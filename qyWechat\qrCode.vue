<template>
  <view>
    <view class="t-c flex-col bg-ff radius-15 mlr-30" :style="{ height: useHeight + 'rpx' }">
      <u-navbar title="个人中心" bgColor="#f3f8fc" placeholder>
        <view class="" slot="left" @click="goBack">
          <u-icon name="arrow-left" size="28" bold color="#000"></u-icon>
        </view>
        <view class="u-nav-slot" slot="center">添加企业微信</view>
      </u-navbar>
      <image :src="qrCode" class="mb-20 img" @longpress="saveCode" mode=""></image>
      <view class="title">
        <text>请扫码添加企业微信，否则将影响学员上课</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      ifBottomRefresh: false,
      useHeight: 0,
      imgHost: getApp().globalData.imgsomeHost,
      qrCode: ''
    };
  },
  onReady() {
    uni.getSystemInfo({
      success: (res) => {
        // 可使用窗口高度，将px转换rpx
        let h = res.windowHeight * (750 / res.windowWidth);
        this.useHeight = h - 30;
      }
    });
  },
  onLoad(e) {
    if (e.qrCode) {
      // this.qrCode = e.qrCode;
      this.qrCode = JSON.parse(decodeURIComponent(e.qrCode));
    }
  },
  onShow() {},
  methods: {
    saveCode() {
      let url = this.qrCode;
      uni.showModal({
        content: '保存二维码？',
        success: (res) => {
          if (res.confirm) {
            uni.downloadFile({
              url,
              success: (res) => {
                console.log(res.tempFilePath);
                // 获取到图片本地地址后再保存图片到相册(因为此方法不支持远程地址)
                uni.saveImageToPhotosAlbum({
                  filePath: res.tempFilePath,
                  success: () => {
                    uni.showToast({
                      title: '保存成功！'
                    });
                  },
                  fail: (err) => {
                    uni.showToast({
                      title: '保存失败！' + JSON.stringify(err.errMsg),
                      icon: 'none'
                    });
                  }
                });
              },
              fail: (err) => {
                uni.showToast({
                  title: '下载失败！' + JSON.stringify(err.errMsg),
                  icon: 'none'
                });
              }
            });
          } else {
            uni.showToast({
              title: '已取消！',
              icon: 'none'
            });
          }
        }
      });
    },
    goBack() {
      uni.reLaunch({
        url: '/pages/index/index'
      });
    }
  }
};
</script>

<style lang="scss">
.tips-title {
  color: #2e896f;
  font-weight: 600;
}
.title {
  color: #666666;
  font-size: 28rpx;
  padding: 0 10rpx;
}
.img {
  width: 500rpx;
  height: 500rpx;
}
</style>

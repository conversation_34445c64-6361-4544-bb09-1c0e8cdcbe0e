<template>
  <view>
    <scroll-view :style="'height: ' + (useHeight - 120) + 'rpx;'" style="background-color: #f3f8fc" scroll-y="true" :scroll-top="scrollTop" @scrolltolower="scrolltolower">
      <view>
        <view class="hear_img">
          <view class="nav-title" @click="goBack">
            <!--#ifdef APP-PLUS  -->
            <image lazy-load src="/static/images/left-icon.png" style="width: 32rpx; height: 32rpx; display: block" mode=""></image>
            <!-- #endif -->
            <view class="title">首页</view>
          </view>
          <image src="https://document.dxznjy.com/app/images/zhujiaoduan/parent-bgc.png" class="wallet-bgc"></image>
          <view class="banner-bgc">
            <image style="width: 690rpx; height: 330rpx" src="https://document.dxznjy.com/app/images/zhujiaoduan/zhujiao_img_banner.png"></image>
          </view>
        </view>
        <!-- 已上课/未上课 -->
        <view class="tab-bg">
          <u-tabs
            :list="list4"
            @click="checkIndex"
            lineWidth="40rpx"
            lineColor="#2E896F"
            :activeStyle="{ color: '#000', fontWeight: 'bold', fontSize: '32rpx' }"
            :inactiveStyle="{ color: '#666', fontSize: '30rpx' }"
          ></u-tabs>
        </view>
        <view class="mb-20 mr-20">
          <u-row justify="center">
            <u-col :span="4">
              <!-- 学员/班级 -->
              <u-tabs
                :list="typeList"
                @click="checkTypeIndex"
                :current="typeIndex"
                lineWidth="40rpx"
                lineColor="transparent"
                :activeStyle="{ color: '#000', fontWeight: 'bold', fontSize: '30rpx', color: '#2E896F' }"
                :inactiveStyle="{ color: '#666', fontSize: '28rpx' }"
              ></u-tabs>
            </u-col>
            <u-col :span="7" customStyle="text-align: right">
              <picker @change="pickerSearchStatus" :value="searchStatusIndex" :range="searchStatusArr" range-key="label">
                <text class="c-00 date-title" style="font-size: 30rpx">
                  {{ searchStatusArr[searchStatusIndex].label ? searchStatusArr[searchStatusIndex].label : '' }}
                </text>
                <uni-icons type="forward" size="16" color="#000" class="pick_right"></uni-icons>
              </picker>
            </u-col>
          </u-row>
        </view>
        <view>
          <view style="padding-bottom: 30rpx">
            <view class="bottom-view">
              <view class="search-view">
                <view class="search-date">
                  <view>
                    <image style="height: 30rpx; width: 30rpx; line-height: 41px" src="/static/images/create_img_date.png"></image>
                    <text style="padding-left: 10rpx; width: 180rpx; color: #999; font-size: 30rpx; height: 80rpx; line-height: 80rpx" @click="searchDateShow">
                      {{ searchDate ? searchDate : '选择日期' }}
                    </text>
                  </view>
                  <uni-icons v-if="searchDate == ''" type="right" color="#999" size="20" @click="searchDateShow"></uni-icons>
                  <uni-icons v-else type="close" color="#999" size="20" @click="cleanSearchDate"></uni-icons>
                </view>
                <!-- <view class="search-date">
                    <input v-model="serchDeliverClass" type="number" maxlength="3" :max="999" :min="1" @input="changeDeliverClass1" class="search-class-input" placeholder="输入班级" />
                    <uni-icons v-if="showClearIcon" type="close" color="#999" size="20" @click="clearIcon"></uni-icons>
                  </view> -->
                <!-- <view class="search-class-text" @click="classSearchFunc">搜索</view> -->
              </view>
            </view>
            <view class="bottom-view">
              <view class="search-view1">
                <view class="search-student">
                  <view style="display: flex; padding-left: 20rpx">
                    <image style="height: 30rpx; width: 30rpx; margin-top: 8rpx" src="/static/images/create_img_seacher.png"></image>
                    <input type="text" v-if="typeIndex == 0" v-model="searchName" placeholder="请输入学员姓名" class="search-student-input" />
                    <input type="text" v-else v-model="searchName" placeholder="请输入班级名称" class="search-student-input" />
                  </view>
                  <view class="search-student-text" @click="nameSearchFunc">搜索</view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view v-if="studylist.data.length > 0" class="list-bg">
          <view v-for="(parentItem, parentIndex) in studylist.data" :key="parentIndex">
            <view class="mb-30 t-l" style="width: 690rpx">
              <text class="date-title">{{ parentItem.date }}</text>
            </view>
            <!-- that.typeIndex == 1 为班级（1v多），0为学员（1v1） -->
            <view v-for="(item, index) in parentItem.data" :key="index" style="position: relative" class="list-item-bg">
              <!-- 退款图标 -->
              <image v-if="item.payStatus == 3" src="https://document.dxznjy.com/applet/refund_icon.png" class="refund_img"></image>

              <view class="flex-between border-bottom pb-25 pt-25">
                <view>
                  <image class="time-pic" src="/static/images/icon_clock.png" mode="aspectFill"></image>
                  <text class="ml-20 f-26">{{ getTimeFormat(item.startTime) }}~{{ getTimeFormat(item.endTime) }}</text>
                </view>
                <view style="display: flex">
                  <view class="tag" v-if="typeIndex == 1">班</view>
                  <view class="tag" v-else-if="!!item.deliverClass">{{ item.deliverClass == 0 ? '班' : item.deliverClass + '班' }}</view>
                  <view class="studyClass">{{ item.hours }}小时</view>
                  <!-- <view class="studyClass">1小时</view> -->
                </view>
              </view>
              <view class="flex-s pr-20" v-if="typeIndex == 0">
                <view class="item-content">学员：{{ item.studentName }}({{ item.studentCode }})</view>
                <!-- 听力检测报告图标 -->
                <image
                  v-if="navIndex === 1 && (item.curriculumName == '拼音法' || item.curriculumName == '拼音法（高年级）')"
                  @click="dictation(item)"
                  style="width: 80rpx; height: 80rpx"
                  src="/static/images/img_dictation_report.png"
                  mode="aspectFill"
                ></image>
                <!-- 语法检测报告图标 -->
                <image
                  v-else-if="navIndex == 1 && !['鼎学能', '珠心算'].includes(item.curriculumName) && !CurriculumCodeArr.includes(item.curriculumCode)"
                  @click="vocabulary(item)"
                  style="width: 80rpx; height: 80rpx"
                  src="/static/images/img_student_report.png"
                  mode="aspectFill"
                ></image>

                <image
                  v-else-if="navIndex == 1 && item.curriculumName == '鼎学能'"
                  @click="homeworkDetail(item)"
                  style="width: 80rpx; height: 80rpx"
                  src="/static/images/view_report.png"
                  mode="aspectFill"
                ></image>
              </view>
              <!-- 1v多 -->
              <view v-if="typeIndex == 1">
                <view class="flex-s pr-20">
                  <view class="item-content">班级名称：{{ item.className || '' }}</view>
                </view>
                <view class="flex-s pr-20">
                  <view class="item-content">班级编号：{{ item.classCode || '' }}</view>
                </view>
                <view class="flex-s pr-20">
                  <view class="item-content">课程类型：{{ item.curriculumName || '' }}</view>
                </view>
                <view class="item-content flex-a-c flex-x-s" style="align-items: start">
                  上课学员：
                  <view style="flex: 1">
                    <u-read-more :toggle="true" :showHeight="80" textIndent="0" closeText="展开" color="#2e896f">
                      <rich-text :nodes="item.studentInfoList.map((student) => student.studentName + '(' + student.studentCode + ')').join('<br/>')"></rich-text>
                    </u-read-more>
                    <!-- <u-row customStyle="margin-bottom: 10px; flex-wrap: wrap; max-height: 200rpx; overflow-y: scroll;">
                      <u-col span="12" v-for="(student, studentIndex) in item.studentInfoList" :key="studentIndex">
                        <view>{{ student.studentName }}({{ student.studentCode }})</view>
                      </u-col>
                      <u-col span="12" v-for="(student, studentIndex) in item.studentInfoList" :key="studentIndex">
                        <view>{{ student.studentName }}({{ student.studentCode }})</view>
                      </u-col>
                      <u-col span="12" v-for="(student, studentIndex) in item.studentInfoList" :key="studentIndex">
                        <view>{{ student.studentName }}({{ student.studentCode }})</view>
                      </u-col>
                    </u-row> -->
                  </view>
                </view>
              </view>
              <!-- 其他1v1 -->
              <!-- 拼音法 -->
              <view v-else-if="item.curriculumName == '拼音法' || item.curriculumName == '拼音法（高年级）'" class="item-content flex-a-c flex-x-s">
                课程：
                <view class="flex-c">
                  <view class="flex-a-c">
                    <text>{{ item.curriculumName }}</text>
                  </view>
                  <!-- <text v-if="item.courseNum > 1" @click="wordMore(item)" class="f-30 ml-20 c-2e8">更多></text> -->
                </view>
              </view>
              <!-- 鼎学能 -->
              <view v-else-if="item.curriculumName == '鼎学能' || CurriculumCodeArr.includes(item.curriculumCode)">
                <view class="flex-s pr-20">
                  <view class="item-content">课程类型：{{ item.curriculumName || '' }}</view>
                </view>
              </view>

              <!-- 非拼音法 -->
              <view v-else>
                <view class="flex-s pr-20">
                  <view class="item-content">课程类型：{{ item.curriculumName || '' }}</view>
                </view>
                <view class="item-content flex-a-c flex-x-s">
                  词库：
                  <view v-if="item.experience">试课词库</view>
                  <view class="flex-c" v-else>
                    <view class="flex-a-c">
                      <text>{{ item.courseName ? getMaxLengthText(item.courseName) : '暂无词库' }}</text>
                    </view>
                    <text v-if="item.courseNum > 1" @click="wordMore(item)" class="f-30 ml-20 c-2e8">更多></text>
                  </view>
                </view>
              </view>
              <view class="item-content flex-center" style="margin-top: 8rpx" v-if="typeIndex == 0">
                <view>家长联系方式：{{ item.parentTel ? telHide(item.parentTel) : '无' }}</view>
                <view class="copy radius-8 m-20 c-ff f-28" @click.stop="copy(item.parentTel)">复制</view>
              </view>
              <view class="flex-between border-top pt-15 mt-20">
                <image v-if="item.experience" class="pic" src="/static/images/shike.png" mode="aspectFill"></image>
                <image v-else class="pic" src="/static/images/img_formal_flag.png" mode="aspectFill"></image>
                <view class="flex-a-c">
                  <view v-if="item.studyStatus == 0 && item.payStatus != 3" class="flex-c">
                    <!-- <view v-if="checkTimeOut(item)" @click="leave(item)" class="border-btn-1 mr-20">请假</view> -->
                    <view v-if="item.type == 2 || (typeIndex == 1 && !item.experience)" @click="showDeleteDialog(item)" class="border-btn mr-20">删除</view>
                  </view>
                  <view v-if="navIndex == 0 && typeIndex != 1 && (item.oneToManyType == 1 || item.needDeliver == 0)" class="border-btn mr-20" @click="editCourse(item)">编辑</view>
                  <view v-if="navIndex == 0 && typeIndex != 1 && (item.oneToManyType == 1 || item.needDeliver == 0)" class="callBtn flex-c mr-20" @click="finishStudy(item)">
                    已完成上课
                  </view>
                  <view v-if="item.studyStatus != 0 && item.payStatus != 3" class="flex-c">
                    <view v-if="!CurriculumCodeArr.includes(item.curriculumCode)" class="border-btn" type="default" size="mini" @click="studtPrint(item)">学习内容打印</view>
                    <view class="ml-20" :class="item.isFeedback ? 'border-btn' : 'border-orange-btn'" type="default" size="mini" @click="open(item)">
                      {{ item.isFeedback ? '查看反馈' : '填写反馈' }}
                    </view>
                  </view>
                  <view class="callBtn flex-c" v-if="navIndex == 0 && typeIndex == 0" @click="callFn(item)">联系推荐人</view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view v-else class="empty-content">
          <image :src="imgHost + '/app/images/zhujiaoduan/zhujiao_img_course_no.png'" class="empty-img"></image>
          <view style="color: #bdbdbd; margin-top: 28rpx">暂无排课计划</view>
        </view>
      </view>
    </scroll-view>

    <view class="course-btn-bg">
      <view class="course-btn" @click="createCourse">创建排课计划</view>
    </view>

    <uni-popup ref="popup2" type="center" catchtouchmove="true">
      <view class="card">
        <view class="tickling mb-30">请假</view>
        <!-- <view class="text-content">课程名称：{{leaves.courseType}}</view> -->
        <view class="text-content">学员名称：{{ leaves.studentName }}</view>
        <view class="text-content">时间：{{ getTimeFormat(leaves.startTime) }}~{{ getTimeFormat(leaves.endTime) }}</view>
        <view class="text-content">请假原因：</view>
        <!-- 请假内容 -->
        <u--textarea class="feedback" v-model="cause" placeholder="请输入" placeholderStyle="color:#b0b0b6" maxlength="210" height="160"></u--textarea>
        <view style="margin-bottom: 20rpx" class="mt-10 ml-20 f-28">备注:请假需提前24小时,如在24小时之内则无法在小程序请假, 需自行联系学管师</view>
        <view class="flex-c mt-40">
          <view class="common-cancel-btn" @click="closeLeave()">取消</view>
          <view class="common-sure-btn ml-40" @click="sureLeave()">确定</view>
        </view>
      </view>
    </uni-popup>

    <uni-popup ref="popup" type="center" catchtouchmove="true">
      <!-- 试课反馈详情 -->
      <view class="card" v-if="trialclass">
        <view v-if="learneShow">
          <view class="text-content flexs mt-30 mb-30">实际时间：</view>
          <dateTime ref="chanelTime" @getStart="changeStart" @getEnd="changeEnd"></dateTime>
          <view class="flex-c mt-40">
            <view class="common-cancel-btn" @click="popupCancel()">取消</view>
          </view>
        </view>
      </view>
      <view class="card" v-else>
        <view class="tickling f-32">反馈</view>
        <view v-if="learneShow">
          <view class="text-content flexs mt-30 mb-30">实际时间：</view>
          <dateTime ref="chanelTime" @getStart="changeStart" @getEnd="changeEnd"></dateTime>
          <view class="flex-c mt-40">
            <view class="common-cancel-btn" @click="popupCancel()">取消</view>
          </view>
        </view>
      </view>
    </uni-popup>

    <uni-popup ref="deleteCourse" type="center">
      <view>
        <image class="cartoom_image" src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
      </view>
      <view class="bg-ff radius-15 mlr-30 delete-bg">
        <view style="font-size: 34rpx; margin-bottom: 40rpx; text-align: center">删除</view>
        <view style="font-size: 32rpx; margin-bottom: 20rpx; text-align: center">学员：{{ deleteItem.studentName }}（{{ deleteItem.studentCode }}）</view>
        <view style="color: #e57126; font-size: 32rpx; text-align: center">
          确认删除{{ deleteItem.date }} {{ getTimeFormat(deleteItem.startTime) }}～{{ getTimeFormat(deleteItem.endTime) }}的排课计划吗？
        </view>
        <view class="flex-center" style="margin-top: 34rpx">
          <view class="common-sure-btn" @click="deleteOption(true)">确定</view>
          <view class="common-cancel-btn ml-20" @click="deleteOption(false)">取消</view>
        </view>
      </view>
    </uni-popup>
    <uni-popup ref="deleteClassCourse" type="center">
      <view>
        <image class="cartoom_image" src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
      </view>
      <view class="bg-ff radius-15 mlr-30 delete-bg">
        <view style="font-size: 34rpx; margin-bottom: 40rpx; text-align: center">删除</view>
        <view style="font-size: 32rpx; margin-bottom: 20rpx; text-align: center">班级名称：{{ deleteItem.className }}</view>
        <view style="font-size: 32rpx; margin-bottom: 20rpx; text-align: center">班级编号：{{ deleteItem.classCode }}</view>
        <view style="color: #e57126; font-size: 32rpx; text-align: center">
          确认删除{{ deleteItem.date }} {{ getTimeFormat(deleteItem.startTime) }}～{{ getTimeFormat(deleteItem.endTime) }}的排课计划吗？
        </view>
        <view class="flex-center" style="margin-top: 34rpx">
          <view class="common-sure-btn" @click="deleteOption(true)">确定</view>
          <view class="common-cancel-btn ml-20" @click="deleteOption(false)">取消</view>
        </view>
      </view>
    </uni-popup>

    <u-datetime-picker
      ref="dateChose"
      :show="showDateChose"
      v-model="searchDateModel"
      mode="date"
      itemHeight="40"
      confirmColor="#2e896f"
      @cancel="searchDateChosecancel"
      @confirm="searchDateChoseSure"
      :immediateChange="true"
    ></u-datetime-picker>
    <!-- 创建排课计划 -->
    <uni-popup ref="createPopup" @change="createPopupShowChange" type="bottom" :is-mask-click="false" :safe-area="false">
      <view class="bg-ff radius-15 pt-25 pb-45 pl-40 pr-40 m-30" @click="selectCyCancel">
        <view class="flex-s" style="margin-bottom: 60rpx">
          <text class="f-30" style="font-weight: 650">创建排课计划</text>
          <image @click="createOption(false)" style="width: 42rpx; height: 42rpx" src="/static/images/icon_close.png" mode="aspectFill"></image>
        </view>
        <view class="tab-bg type-list">
          <u-tabs
            ref="tabs"
            :scrollable="false"
            :current="typeListIndex"
            :list="typeList"
            @click="checkTypeListIndex"
            lineWidth="50rpx"
            lineColor="#2E896F"
            :activeStyle="{ color: '#000', fontWeight: 'bold', fontSize: '32rpx', 'white-space': 'nowrap' }"
            :inactiveStyle="{ color: '#666', fontSize: '30rpx' }"
          ></u-tabs>
        </view>
        <!-- 1v1 -->
        <view v-if="typeListIndex == 0">
          <view class="input-border mb-30 flex-center plr-30" @click="chooseCourseType(true)">
            <image style="width: 38rpx; height: 38rpx" src="/static/images/create_img_user.png" mode="aspectFill"></image>
            <text class="search-dialog-student-input ml-30 text-hide" :class="dialogSearchPeople != null ? 'no-null-text-color' : 'null-text-color'">
              {{ courseType.curriculumId ? courseType.curriculumName : '请选择课程类型' }}
            </text>
            <uni-icons type="right" color="#999999" size="20"></uni-icons>
          </view>
          <view class="input-border mb-30 flex-center plr-30" @click="dialogSearchStudent(false)">
            <image style="width: 38rpx; height: 38rpx" src="/static/images/create_img_user.png" mode="aspectFill"></image>
            <text class="search-dialog-student-input ml-30 text-hide" :class="dialogSearchPeople != null ? 'no-null-text-color' : 'null-text-color'">
              {{ dialogSearchPeople != null ? dialogSearchPeople.studentName + '（' + dialogSearchPeople.studentCode + '）' : '请选择学员' }}
            </text>
            <uni-icons type="right" color="#999999" size="20"></uni-icons>
          </view>

          <view class="input-border mb-30 flex-center plr-30" @click="dialogChoseDate">
            <image style="width: 38rpx; height: 38rpx" src="/static/images/create_img_date.png" mode="aspectFill"></image>
            <text class="search-dialog-student-input ml-30 text-hide" :class="calendarDate.length > 0 ? 'no-null-text-color' : 'null-text-color'">
              {{ calendarDate.length > 0 ? getCalendarDateText() : '请选择日期' }}
            </text>
            <uni-icons type="right" color="#999999" size="20"></uni-icons>
          </view>
          <view class="flex-s mb-30">
            <view class="input-border-s flex-c" :class="createCourseRequest.startTime == '' ? 'null-text-color' : 'no-null-text-color'" @click="showChoseCourseTime(true)">
              {{ createCourseRequest.startTime == '' ? '开始时间' : createCourseRequest.startTime }}
            </view>
            <uni-number-box v-model="createCourseRequest.hour" :min="1" :max="1" @change="numberBoxBindChange()"></uni-number-box>
            <view class="input-border-s flex-c" :class="createCourseRequest.startTime == '' ? 'null-text-color' : 'no-null-text-color'" @click="showChoseCourseTime(null)">
              {{ createCourseRequest.endTime == '' ? '结束时间' : createCourseRequest.endTime }}
            </view>
          </view>
          <view class="input-border mb-30 flex-center plr-30" v-if="courseType.extraSecond == 1">
            <image style="width: 38rpx; height: 38rpx" src="/static/images/create_img_user.png" mode="aspectFill"></image>
            <input type="number" v-model="deliverClass" @input="changeDeliverClass" class="search-dialog-student-input ml-30 text-hide" placeholder="请输入1-999" />
            <text>班</text>
          </view>
          <view class="c-66 f-30 mt-30">剩余可排交付时长：{{ choseSchoolHour ? choseSchoolHour : '0' }}</view>
        </view>
        <!-- 1v多 -->
        <view v-if="typeListIndex == 1">
          <view class="input-border mb-30 flex-center plr-30" @click="chooseClassShow(true)">
            <image style="width: 38rpx; height: 38rpx" src="/static/images/create_img_user.png" mode="aspectFill"></image>
            <text class="search-dialog-student-input ml-30 text-hide" :class="dialogSearchPeople != null ? 'no-null-text-color' : 'null-text-color'">
              {{ classType.className ? `${classType.className}(${classType.type == 2 ? '正式课' : '试课'})` : '请选择班级' }}
            </text>
            <uni-icons type="right" color="#999999" size="20"></uni-icons>
          </view>
          <view class="mb-30" v-if="studentlist.length > 0">
            <selectCy
              :disabled="classType.type == 1"
              ref="selectCyRef"
              :zindex="10"
              svalue="studentCode"
              slabel="studentName"
              placeholder="请选择学员"
              :value="students"
              :options="studentlist"
              @change="changeStudents"
            ></selectCy>
          </view>
          <!-- 不可多选/试课 -->
          <view class="input-border mb-30 flex-center plr-30" @click="dialogChoseDate1" v-if="classType.type == 1">
            <image style="width: 38rpx; height: 38rpx" src="/static/images/create_img_date.png" mode="aspectFill"></image>
            <text class="search-dialog-student-input ml-30 text-hide" :class="calendarDate.length > 0 ? 'no-null-text-color' : 'null-text-color'">
              {{ calendarDate1.length > 0 ? calendarDate1 : '请选择日期' }}
            </text>
            <uni-icons type="right" color="#999999" size="20"></uni-icons>
          </view>
          <!-- 可多选/正式课 -->
          <view class="input-border mb-30 flex-center plr-30" @click="dialogChoseDate" v-else>
            <image style="width: 38rpx; height: 38rpx" src="/static/images/create_img_date.png" mode="aspectFill"></image>
            <text class="search-dialog-student-input ml-30 text-hide" :class="calendarDate.length > 0 ? 'no-null-text-color' : 'null-text-color'">
              {{ calendarDate.length > 0 ? calendarDate.join(',') : '请选择日期' }}
            </text>
            <uni-icons type="right" color="#999999" size="20"></uni-icons>
          </view>
          <view class="flex-s mb-30">
            <view class="input-border-s flex-c" :class="schedulingPlan.startTime == '' ? 'null-text-color' : 'no-null-text-color'" @click="showChoseCourseTime(true)">
              {{ schedulingPlan.startTime == '' ? '开始时间' : schedulingPlan.startTime }}
            </view>
            <uni-number-box v-model="schedulingPlan.hour" :min="1" :step="1" @change="numberBoxBindChange2()"></uni-number-box>
            <view class="input-border-s flex-c" :class="schedulingPlan.startTime == '' ? 'null-text-color' : 'no-null-text-color'" @click="showChoseCourseTime(false)">
              {{ schedulingPlan.endTime == '' ? '结束时间' : schedulingPlan.endTime }}
            </view>
          </view>
        </view>
        <view class="flex-c" style="margin-top: 86rpx">
          <view class="common-cancel-btn" @click="createOption(false)">取消</view>
          <view class="common-sure-btn ml-40" @click="createOption(true)">确定</view>
        </view>
      </view>
    </uni-popup>
    <!-- 修改排课计划 -->
    <uni-popup ref="editPopup" @change="editPopupShowChange" type="bottom" :is-mask-click="false" :safe-area="false">
      <view class="bg-ff radius-15 pt-25 pb-45 pl-40 pr-40 m-30" @click="selectCyCancel">
        <view class="flex-s" style="margin-bottom: 60rpx">
          <text class="f-30" style="font-weight: 650">修改排课计划</text>
          <image @click="editOption(false)" style="width: 42rpx; height: 42rpx" src="/static/images/icon_close.png" mode="aspectFill"></image>
        </view>
        <!-- 1v1 -->
        <view v-if="typeIndex == 0">
          <view class="input-border mb-30 flex-center plr-30" @click="dialogChoseDate1">
            <image style="width: 38rpx; height: 38rpx" src="/static/images/create_img_date.png" mode="aspectFill"></image>
            <text class="search-dialog-student-input ml-30 text-hide" :class="calendarDate.length > 0 ? 'no-null-text-color' : 'null-text-color'">
              {{ calendarDate1.length > 0 ? getCalendarDateText1() : '请选择日期' }}
            </text>
            <uni-icons type="right" color="#999999" size="20"></uni-icons>
          </view>
          <view class="flex-s mb-30">
            <view class="input-border-s flex-c" :class="editCourseRequest.startTime == '' ? 'null-text-color' : 'no-null-text-color'" @click="showChoseCourseTime1(true)">
              {{ editCourseRequest.startTime == '' ? '开始时间' : editCourseRequest.startTime }}
            </view>
            <uni-number-box v-model="editCourseRequest.hour" :min="1" :max="1" @change="numberBoxBindChange1()"></uni-number-box>
            <view class="input-border-s flex-c" :class="editCourseRequest.startTime == '' ? 'null-text-color' : 'no-null-text-color'" @click="showChoseCourseTime1(null)">
              {{ editCourseRequest.endTime == '' ? '结束时间' : editCourseRequest.endTime }}
            </view>
          </view>
          <view class="input-border mb-30 flex-center plr-30" v-if="!!editForm.deliverClass && editForm.deliverClass !== 0">
            <image style="width: 38rpx; height: 38rpx" src="/static/images/create_img_user.png" mode="aspectFill"></image>
            <input type="number" v-model="deliverClass" @input="changeDeliverClass" class="search-dialog-student-input ml-30 text-hide" placeholder="请输入1-999" />
            <text>班</text>
          </view>
        </view>
        <!-- 1v多 -->
        <view v-else>
          <view class="input-border mb-30 flex-center plr-30">
            <image style="width: 38rpx; height: 38rpx" src="/static/images/create_img_user.png" mode="aspectFill"></image>
            <text class="search-dialog-student-input ml-30 text-hide" :class="dialogSearchPeople != null ? 'no-null-text-color' : 'null-text-color'">
              {{ classType.className ? `${classType.className}(${classType.type == 2 ? '正式课' : '试课'})` : '请选择班级' }}
            </text>
          </view>
          <view class="mb-30" v-if="studentlist.length > 0">
            <selectCy
              ref="selectCyRef"
              :zindex="10"
              svalue="studentCode"
              slabel="studentName"
              placeholder="请选择学员"
              :value="students"
              :options="studentlist"
              @change="changeStudents"
            ></selectCy>
          </view>
          <view class="input-border mb-30 flex-center plr-30" @click="dialogChoseDate1">
            <image style="width: 38rpx; height: 38rpx" src="/static/images/create_img_date.png" mode="aspectFill"></image>
            <text class="search-dialog-student-input ml-30 text-hide" :class="calendarDate.length > 0 ? 'no-null-text-color' : 'null-text-color'">
              {{ calendarDate1.length > 0 ? calendarDate1 : '请选择日期' }}
            </text>
            <uni-icons type="right" color="#999999" size="20"></uni-icons>
          </view>
          <view class="flex-s mb-30">
            <view class="input-border-s flex-c" :class="schedulingPlan.startTime == '' ? 'null-text-color' : 'no-null-text-color'" @click="showChoseCourseTime(true)">
              {{ schedulingPlan.startTime == '' ? '开始时间' : schedulingPlan.startTime }}
            </view>
            <uni-number-box v-model="schedulingPlan.hour" :min="1" :step="1" @change="numberBoxBindChange2()"></uni-number-box>
            <view class="input-border-s flex-c" :class="schedulingPlan.startTime == '' ? 'null-text-color' : 'no-null-text-color'" @click="showChoseCourseTime(false)">
              {{ schedulingPlan.endTime == '' ? '结束时间' : schedulingPlan.endTime }}
            </view>
          </view>
        </view>
        <view class="flex-c" style="margin-top: 86rpx">
          <view class="common-cancel-btn" @click="editOption(false)">取消</view>
          <view class="common-sure-btn ml-40" @click="editOption(true)">确定</view>
        </view>
      </view>
    </uni-popup>

    <uni-popup ref="stopTakingPrompt" type="center">
      <view>
        <image class="cartoom_image" src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
      </view>
      <view class="bg-ff radius-15 mlr-30 delete-newbg">
        <view style="font-size: 34rpx; margin-bottom: 40rpx; text-align: center">温馨提示</view>
        <view style="font-size: 32rpx; text-align: center">您选择的排课时间与系统停服时间存在重叠，请确认在该时间段排课么？</view>
        <view style="margin-top: 20rpx"></view>
        <view style="color: #e57126; font-size: 32rpx; text-align: center; margin: 10rpx 0 15rpx">停服时间：</view>
        <view style="height: auto; overflow: auto; line-height: 60rpx; max-height: 180rpx; white-space: nowrap">
          <view style="color: #e57126; font-size: 28rpx; text-align: left; line-height: 50rpx" v-for="(item, index) in offServiceTimeList" :key="index">
            {{ item.split('/')[0] }} -- {{ item.split('/')[1] }}
          </view>
        </view>
        <view class="flex-center" style="margin-top: 34rpx">
          <view class="common-sure-btn" @click="continueAdding()">确定</view>
          <view class="common-cancel-btn ml-20" @click="() => this.$refs.stopTakingPrompt.close()">取消</view>
        </view>
      </view>
    </uni-popup>
    <wu-calendar
      ref="calendar"
      @confirm="calendarConfirm"
      :insert="false"
      mode="multiple"
      :date="calendarDate"
      :useToday="false"
      :startDate="calendarStartDate"
      color="#2E896F"
      :showMonth="false"
      :selected="selectCalendarArr"
      @monthSwitch="monthSwitch"
    ></wu-calendar>
    <wu-calendar
      ref="editcalendar"
      @confirm="calendarConfirm1"
      :insert="false"
      mode="single"
      :date="calendarDate1"
      :useToday="false"
      :startDate="calendarStartDate1"
      color="#2E896F"
      :showMonth="false"
      :selected="selectCalendarArr1"
      @monthSwitch="monthSwitch1"
    ></wu-calendar>

    <u-datetime-picker
      ref="createTimePicker"
      :show="showCreateTimePicker"
      mode="time"
      itemHeight="40"
      confirmColor="#2e896f"
      @cancel="createTimePickerCancel"
      @confirm="createTimePickerSure"
      :immediateChange="true"
      :maxHour="22"
    ></u-datetime-picker>

    <u-datetime-picker
      ref="createTimePicker1"
      :show="showCreateTimePicker1"
      mode="time"
      itemHeight="40"
      confirmColor="#2e896f"
      @cancel="createTimePickerCancel1"
      @confirm="createTimePickerSure1"
      :immediateChange="true"
    ></u-datetime-picker>
    <uni-popup ref="choseStudent" type="bottom" :safe-area="false" :is-mask-click="false">
      <view class="bg-ff radius-15 flex-col pb-40 pt-30 c-00 m-30" style="height: 650rpx">
        <view class="flex-s" style="width: 100%">
          <text class="c-99 f-32 pl-30" @click="choseStudentOption(false)">取消</text>
          <view class="search-class">
            <view style="display: flex">
              <image style="height: 30rpx; width: 30rpx; margin-top: 8rpx" src="/static/images/create_img_seacher.png"></image>
              <input type="text" v-model="choseStudentSearchName" placeholder="请输入学员姓名" class="search-class-input" />
            </view>
            <view class="search-class-text" @click="choseStudentSearch">搜索</view>
          </view>
          <text class="c-2e8 f-32 pr-30" @click="choseStudentOption(true)">确定</text>
        </view>
        <view class="chose-student-picker" v-if="createChoseStudentArr.length > 0">
          <picker-view style="height: 510rpx" :indicator-style="itemHeight" @change="choseStudentChange" :value="dialogSearchPeopleIndex">
            <picker-view-column>
              <view class="chose-student-item" :class="dialogSearchPeopleIndex[0] == index ? 'chose-text-style' : ''" v-for="(item, index) in createChoseStudentArr" :key="index">
                <text :class="item.type == 1 ? 'c-2e8' : 'c-fe5'" v-if="item.type == 1">【上】</text>
                <text :class="item.type == 1 ? 'c-2e8' : 'c-fe5'" v-if="item.type == 3">【代】</text>
                <!--                <text :class="item.type == 1 ? 'c-2e8' : 'c-fe5'">【{{ item.type == 1 ? '上' : item.type == 3 ? '代' : '复' }}】</text> -->
                <text class="c-00">{{ item.studentName }}（{{ item.studentCode }}）</text>
              </view>
            </picker-view-column>
          </picker-view>
        </view>
        <view v-else class="flex-dir-col flex-y-c flex-x-c" style="color: #bdbdbd; height: 550rpx">
          <image :src="imgHost + '/app/images/zhujiaoduan/zhujiao_img_student_no.png'" class="empty-img"></image>
          <view style="margin-top: 28rpx">暂无学员</view>
          <!-- <view style="margin-top: 6rpx">请先在学员管理中添加学员</view> -->
        </view>
      </view>
    </uni-popup>

    <!-- 选择校区弹窗 -->
    <uni-popup ref="popopChooseSchool" type="center">
      <view class="dialogBG">
        <view class="dialog-all">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="dialog-small-close" @click="choseSchoolOption(false)">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="dialog-center">
            <view class="dialog-title bold pb-20">选择校区</view>
            <view
              class="dialog-item"
              @click="chooseSchoollist(item, index)"
              v-for="(item, index) in arraySchool"
              :key="index"
              :class="isactive == index ? 'selected' : 'not-selected'"
            >
              {{ item.label }}
            </view>
            <view class="flex-c mt-40">
              <view class="common-sure-btn" @click="choseSchoolOption(true)">确定</view>
              <view class="common-cancel-btn ml-40" @click="choseSchoolOption(false)">取消</view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
    <!-- 选择课程类型弹窗 -->
    <uni-popup ref="courseShow" type="center">
      <view class="dialogBG">
        <view class="dialog-all">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="dialog-small-close" @click="closeCourse">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="dialog-center">
            <view class="dialog-title bold pb-20">选择课程类型</view>
            <view class="dialog-list">
              <view
                class="dialog-item"
                @click="chooseCourse(item, index)"
                v-for="(item, index) in courseList"
                :key="index"
                :class="isActive == index ? 'selected' : 'not-selected'"
              >
                {{ item.enName }}
              </view>
            </view>
            <view class="flex-c mt-40">
              <view class="common-sure-btn" @click="chooseCourseType(false)">确定</view>
              <view class="common-cancel-btn ml-40" @click="closeCourse">取消</view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
    <!-- 选择班级弹窗 -->
    <uni-popup ref="classShow" type="center">
      <view class="dialogBG">
        <view class="dialog-all">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="dialog-small-close" @click="closeClass">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="dialog-center">
            <view class="dialog-title bold pb-20">选择班级</view>
            <view class="dialog-list">
              <view
                class="dialog-item"
                @click="chooseClass(item, index)"
                v-for="(item, index) in classList"
                :key="index"
                :class="isClassActive == index ? 'selected' : 'not-selected'"
              >
                {{ item.className }}({{ item.type == 2 ? '正式课' : '试课' }})
              </view>
            </view>
            <view class="flex-c mt-40">
              <view class="common-sure-btn" @click="chooseClassShow(false)">确定</view>
              <view class="common-cancel-btn ml-40" @click="closeClass">取消</view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>

    <uni-popup ref="createSuccess" type="center" :is-mask-click="false">
      <view class="bg-ff radius-15 flex-col ptb-40 plr-30 f-32 c-00" style="width: 610rpx">
        <image class="mb-30" style="width: 110rpx; height: 110rpx" src="https://document.dxznjy.com/app/images/zhujiaoduan/create_img_success.png"></image>
        <view class="mb-20">排课成功</view>
        <view class="mb-10">
          {{ dialogSearchPeople ? '学员：' + dialogSearchPeople.studentName + '（' + dialogSearchPeople.studentCode + '）' : '' }}
        </view>
        <view class="mb-15 text-enter" style="width: 621rpx; text-align: center">
          {{ getCalendarDateText() }}
        </view>
        <view class="mb-45 f-30" style="color: #e57126">{{ createCourseRequest.startTime }}{{ createCourseRequest.startTime ? ' ~ ' : '' }}{{ createCourseRequest.endTime }}</view>
        <view class="flex-center" style="justify-content: center">
          <view class="common-cancel-btn" @click="createSuccessOption(false)">暂不排课</view>
          <view class="common-sure-btn ml-40" @click="createSuccessOption(true)">继续排课</view>
        </view>
      </view>
    </uni-popup>

    <uni-popup ref="loadingPopup" type="center" :is-mask-click="false">
      <view class="bg-ff radius-15 t-c">
        <view class="loadingpadding">
          <image src="https://document.dxznjy.com/Assistant/loading.png" class="loadingImg"></image>
          <view class="mt-30">加载中...</view>
          <view>请耐心等候</view>
        </view>
      </view>
    </uni-popup>

    <!-- 二维码 -->
    <u-popup :show="codeShow" :round="24" mode="center" overlay class="pay-content" :safeAreaInsetBottom="false">
      <view>
        <image class="cartoom_image" src="https://document.dxznjy.com/dxSelect/dialog_icon.png" style="left: 170rpx" mode="widthFix"></image>
      </view>
      <view class="orderinfo">
        <view style="height: 50rpx"></view>
        <uni-icons class="close-icon" @click="codeShow = false" type="closeempty" size="18" color="#fff"></uni-icons>
        <view class="flex flex-col" style="height: 310rpx; justify-content: space-around">
          <!-- <image src="../static/qrcode.png" style="width: 300rpx; height: 300rpx" class="mb-20 img" mode=""></image> -->
          <view class="color">拨打学员{{ callForm.studentName }}，推荐人的号码</view>
          <view class="phone">{{ telHide(callForm.referrerPhone) }}</view>
          <view class="btns">
            <view class="btn btn2" @click="callReffer">立即呼叫</view>
            <view class="btn btn1" @click="codeShow = false">取消</view>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
  import dateTime from '@/components/lanxiujuan-dyDateTime/lanxiujuan-dyDateTime.vue';
  import selectCy from './components/select-cy.vue';
  import dayjs from 'dayjs';
  import Config from '@/utils/config';
  export default {
    components: {
      dateTime,
      selectCy
    },
    data() {
      return {
        itemHeight: `height: ${uni.upx2px(80)}px;`,
        imgHost: 'https://document.dxznjy.com/',
        useHeight: 0,
        typeList: [
          {
            name: '学员'
          },
          {
            name: '班级'
          }
        ],
        typeListIndex: 0, // 创建排课计划类型 学员/班级 索引
        list4: [
          {
            name: '未上课'
          },
          {
            name: '已上课'
          }
        ],
        navIndex: 0, // tabs栏索引

        typeIndex: 0, // 主页列表 学员/班级tabs栏 索引

        studylist: {
          data: []
        },
        page: 1,
        scrollTop: 0,
        pageSize: 20,

        searchName: '', //主页搜索姓名

        showDateChose: false,
        searchDate: '',
        searchDateModel: '', //选择日期弹框需要

        searchStatusIndex: 0,
        searchStatusArr: [],
        beforeStudyArray: [
          {
            //1未上课 2已过期
            value: 1,
            label: '未上课'
          },
          {
            value: 2,
            label: '已过期'
          }
        ],
        studyAfterArray: [
          {
            //1已上课未反馈 2已上课已反馈
            value: 0,
            label: '全部'
          },
          {
            value: 1,
            label: '已上课未反馈'
          },
          {
            value: 2,
            label: '已上课已反馈'
          }
        ],
        codeShow: false,
        //创建排课
        calendarStartDate: '', //选择日期截止开始时间
        calendarStartDate1: '', //选择日期截止开始时间
        showCreateTimePicker: false, //显示选择开始时间弹框
        showCreateTimePicker1: false, //显示选择开始时间弹框
        dialogSearchPeople: null,
        dialogSearchPeopleIndex: [0],
        calendarDate: [],
        calendarDate1: '',
        createCourseRequest: {
          hour: 1,
          startTime: '',
          endTime: ''
        },
        editCourseRequest: {
          hour: 1,
          startTime: '',
          endTime: ''
        },
        createChoseStudentArr: [],
        //弹框搜索名称
        choseStudentSearchName: '',
        //选择校区
        choseSchoolItem: null,
        choseSchoolHour: 0,
        arraySchool: [],
        isactive: -1,
        isActive: -1,
        isClassActive: -1,
        selectCalendarArr: [],
        selectCalendarArr1: [],

        //删除
        deleteItem: {},
        //请假
        leaves: {},
        leavelist: '',
        cause: '',

        //查看填写反馈
        backlist: '', // 获取反馈详情列表
        subject: '', // 课程列表详情
        timelist: {
          actualStart: '',
          actualEnd: ''
        },
        feedback: '', // 弹出层文本框输入的内容
        isFeedback: false, // 是否显示反馈详情确定按钮

        learneShow: false,

        trialclass: false, // 是否是试课反馈
        triallist: '', // 试课反馈详情
        Feedback: false, // 是否显示试课反馈详情确定按钮

        //创建弹框显示哦
        isShowCreatePopup: false,
        callForm: {
          studentName: '',
          referrerPhone: ''
        },
        courseList: [],
        courseType: {
          curriculumId: '',
          curriculumName: '',
          extraSecond: 0
        },
        classList: [], // 班级列表
        classType: {}, // 1v多班级
        flag: true,
        offServiceTimeList: [],
        serchDeliverClass: '',
        deliverClass: '',
        showClearIcon: false,
        editForm: {},
        // 1v多排课计划表单
        students: [], // 选择的学生列表
        studentlist: [], // 当前班级学生列表
        schedulingPlan: { startTime: '', endTime: '', hour: 1, studentList: [] }, // 1v多排课计划表单
        CurriculumCodeArr: Config.CurriculumCodeArr //学考通、小四门课程code
      };
    },

    watch: {
      searchName(newName, oldName) {
        if (newName.length == 0) {
          console.log('searchName watch');
          this.getNormalStudyList();
        }
      },
      serchDeliverClass(newName, oldName) {
        if (newName.length > 0) {
          this.showClearIcon = true;
        } else if (newName.length == 0) {
          this.showClearIcon = false;
        }
      },
      choseStudentSearchName(newName, oldName) {
        if (newName.length == 0) {
          this.dialogSearchStudent(true);
        }
      }
    },

    onLoad() {
      uni.showLoading({
        title: '加载中'
      });
      this.searchStatusArr = this.beforeStudyArray;
      this.searchDateModel = dayjs().format('YYYY-MM-DD');
      this.getCourseList(); // 获取课程列表
      this.getClassList(); // 获取班级列表
    },
    onReady() {
      let that = this;
      uni.getSystemInfo({
        //调用uni-app接口获取屏幕高度
        success(res) {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          that.useHeight = h;
        }
      });
      this.$refs.dateChose.setFormatter(this.formatter);
    },

    onShow() {
      this.searchName = '';
      this.searchDate = '';

      uni.removeStorage({
        key: 'logintokenReview'
      });
      this.clickHandle();
      this.calendarStartDate = dayjs().format('YYYY-MM-DD');
      this.calendarStartDate1 = dayjs().format('YYYY-MM-DD');

      this.getNormalStudyList();
    },
    methods: {
      changeDeliverClass1(e) {
        console.log(e);
        let value = e.detail.value * 1;
        if (value === '') return;
        const reg = /^[1-9]\d*$/;
        console.log(!reg.test(value));
        if (!reg.test(value)) {
          uni.showToast({
            title: '请输入1-999之间的数字',
            icon: 'none'
          });
          setTimeout(() => {
            this.serchDeliverClass = '';
          }, 100);
          // this.clearIcon();
          // return
        } else {
          if (value > 999) {
            value = 999;
          } else if (value < 1) {
            value = 1;
          }
          this.serchDeliverClass = value;
        }
      },
      changeDeliverClass(e) {
        console.log(e);
        let value = e.detail.value * 1;
        if (value === '') return;
        const reg = /^[1-9]\d*$/;
        console.log(!reg.test(value));
        if (!reg.test(value)) {
          uni.showToast({
            title: '请输入1-999之间的数字',
            icon: 'none'
          });
          setTimeout(() => {
            this.deliverClass = '';
          }, 100);
        } else {
          if (value > 999) {
            value = 999;
          } else if (value < 1) {
            value = 1;
          }
          this.deliverClass = value;
        }
      },
      clearIcon() {
        this.serchDeliverClass = '';
        this.showClearIcon = false;
      },
      async editCourse(item) {
        // TODO 编辑排课计划
        console.log('🚀 ~ editCourse ~ item:', item);
        // item.classCode = 'ydd00009';
        // item.studentInfoList = [{ studentCode: '625047878' }];
        // item.classCode = 'ydd00009';
        // item.studentInfoList = [{ studentCode: '625047878' }];
        // console.log('🚀 ~ editCourse ~ item:', item, this.typeIndex, this.classList);
        // 一对多编辑
        if (this.typeIndex == 1) {
          let classType = this.classList.find((i) => i.classCode == item.classCode);
          if (!classType) {
            uni.showToast({
              title: '班级不存在',
              icon: 'none'
            });
            return;
          }
          this.classType = classType;
          console.log('🚀 ~ editCourse ~ this.classList.find(', this.classType);
          await this.getStudentList();
          //studentlist
          // console.log('🚀 ~ editCourse ~ studentlist:', this.studentlist);
          // this.studentlist.filter((i) => item.studentInfoList.some((ii)=>ii.studentCode==i.studentCode);
          // item.studentInfoList.some((i)=>i.studentCode==item.studentCode)
          this.schedulingPlan.studentList = this.studentlist.filter((i) => item.studentInfoList.some((ii) => ii.studentCode == i.studentCode));
          this.students = this.schedulingPlan.studentList.map((i) => i.studentCode);

          this.editForm = { ...item };
          let startTime = item.startTime.split(' ');
          let endTime = item.endTime.split(' ');
          this.calendarDate1 = item.date;
          this.schedulingPlan = { ...this.schedulingPlan, hour: item.hours, startTime: startTime[1], endTime: endTime[1] };
          this.$refs.editPopup.open();
          return;
        }
        // 一对一编辑
        this.editForm = { ...item };
        this.deliverClass = this.editForm.deliverClass;
        let startTime = item.startTime.split(' ');
        let endTime = item.endTime.split(' ');
        this.calendarDate1 = item.date;
        this.editCourseRequest = { hour: item.hours, startTime: startTime[1], endTime: endTime[1] };
        console.log(this.editForm, '111111111');
        this.$refs.editPopup.open();
      },
      finishStudy(item) {
        this.subject = item;
        this.timelist = {
          actualStart: '',
          actualEnd: ''
        };
        uni.$http
          .post(`/deliver/app/teacher/updateStudyFinish?studyId=${item.id}`)
          .then((res) => {
            console.log(res);
            if (res.data.success) {
              if (this.subject.experience == false) {
                this.trialclass = false;
                this.backData(); // experience = false 是学习反馈
              } else {
                this.trialclass = true;
                if (item.curriculumName === '鼎英语') {
                  this.flag = false;
                }
                this.trialData(item); // experience = true 是试课反馈
              }
            } else {
              return false;
            }
          })
          .catch((err) => {
            console.log(err);
            return false;
          });
      },
      // 取消选择班级
      closeClass() {
        this.isClassActive = -1;
        this.classType = {}; // 清空班级信息
        this.studentlist = []; // 清空班级学生列表
        this.changeStudents([], []); // 清空选择的学生
        this.$refs.classShow.close();
      },
      // 确定选择班级
      async chooseClass(item, index) {
        this.isClassActive = index;
        this.classType = item;
        console.log('🚀 ~ chooseClass ~ classType:', this.classType);
        this.getStudentList();
        // setTimeout(() => {
        //   this.students = ['label1'];
        //   this.schedulingPlan.students = ['label1'];
        // }, 200);
      },
      // 打开/关闭选择班级弹窗
      chooseClassShow(flag) {
        if (flag) {
          if (this.classList && this.classList.length == 0) {
            uni.showToast({
              title: '暂无可排课班级',
              icon: 'none'
            });
            return;
          }
          this.$refs.classShow.open();
        } else {
          this.$refs.classShow.close();
        }
      },
      // 获取班级
      async getClassList() {
        try {
          let { data } = await uni.$http.get('/deliver/app/teacher/getClassByTeacher');
          // console.log(data.data, '6666');
          // 关闭加载中
          uni.hideLoading();
          this.classList = data.data.filter((i) => {
            // 过滤掉已结束的班级 如果是 试课 且 已结束 则不显示
            if (i.type == 1 && i.isStartCourse == 1) {
              return false;
            }
            return true;
          });
        } catch (error) {
          uni.hideLoading();
        }
      },
      async getCourseList() {
        let { data } = await uni.$http.get('/znyy/curriculum/allNew?curriculumType=0');
        console.log(data.data);
        this.courseList = data.data;
      },
      chooseCourse(item, index) {
        if (this.courseType.curriculumId == item.id) {
          this.courseType.curriculumId = '';
          this.courseType.curriculumName = '';
          this.courseType.extraSecond = 0;
          this.isActive = -1;
        } else {
          this.isActive = index;
          this.courseType.curriculumId = item.id;
          this.courseType.curriculumName = item.enName;
          this.courseType.extraSecond = item.extraSecond;

          this.getStudentHaveDeliverHours();
        }
      },
      closeCourse() {
        this.isActive = -1;
        this.courseType.curriculumName = '';
        this.courseType.curriculumId = '';
        this.courseType.extraSecond = 0;
        this.$refs.courseShow.close();
      },
      chooseCourseType(flag) {
        if (flag) {
          this.$refs.courseShow.open();
        } else {
          this.$refs.courseShow.close();
        }
      },
      // 手机号脱敏('13912345678' 转换成 '139****5678') 第3位开始替换4个
      telHide(value) {
        if (!value) {
          return '';
        } else {
          let data = value.replace(/(\d{3})\d{4}(\d*)/, '$1****$2');
          return data;
        }
      },
      callFn(item) {
        console.log(item);
        this.callForm.studentName = item.studentName;
        this.callForm.referrerPhone = item.referrerPhone;
        this.codeShow = true;
      },
      callReffer() {
        uni.makePhoneCall({
          phoneNumber: this.callForm.referrerPhone
        });
      },
      selectCyCancel() {
        this.$refs.selectCyRef?.close();
        this.$refs.selectCyRef2?.close();
      },
      createPopupShowChange(e) {
        this.isShowCreatePopup = e.show;
      },
      editPopupShowChange(e) {
        this.isShowCreatePopup = e.show;
      },
      getMaxLengthText(str) {
        if (str.length > 12) {
          let val = str.substr(0, 12);
          return val + '...';
        }
        return str;
      },
      getTimeFormat(date) {
        return dayjs(date).format('HH:mm');
      },
      formatter(type, value) {
        if (type === 'year') {
          return `${value}年`;
        }
        if (type === 'month') {
          return `${value}月`;
        }
        if (type === 'day') {
          return `${value}日`;
        }
        return value;
      },
      classSearchFunc() {
        if (this.serchDeliverClass.length == 0) {
          uni.showToast({
            icon: 'none',
            title: '请输入班级',
            duration: 2000
          });
          return;
        }
        this.getNormalStudyList();
      },
      //主页搜索
      nameSearchFunc() {
        // if (this.searchName.length == 0) {
        //   uni.showToast({
        //     icon: 'none',
        //     title: '请输入学员姓名',
        //     duration: 2000
        //   });
        //   return;
        // }
        this.getNormalStudyList();
      },
      pickerSearchStatus(e) {
        this.searchStatusIndex = e.target.value;
        this.getNormalStudyList();
      },
      searchDateShow() {
        this.showDateChose = true;
      },
      searchDateChosecancel() {
        this.showDateChose = false;
      },
      searchDateChoseSure(e) {
        console.log(e);
        if (e) {
          this.searchDateModel = e.value;
          this.searchDate = dayjs(e.value).format('YYYY-MM-DD');
          this.getNormalStudyList();
        }
        this.showDateChose = false;
      },
      cleanSearchDate() {
        this.searchDate = '';
        this.searchDateModel = dayjs().format('YYYY-MM-DD');
        this.getNormalStudyList();
      },
      // 滚动条回到顶部
      clickHandle() {
        this.scrollTop = this.scrollTop === 0 ? 1 : 0;
      },
      getNormalStudyList() {
        this.page = 1;
        this.getStudyList();
      },
      scrolltolower() {
        if (this.page >= this.studylist.totalPage) {
          return false;
        }
        ++this.page;
        this.getStudyList(true);
      },
      async getStudyList(isPage = false) {
        const that = this;
        // that.$refs.loadingPopup.open();
        try {
          // 在请求前记录当前导航状态
          const currentNav = that.navIndex;
          const currentType = that.typeIndex;
          let res;
          // 1v多
          if (that.typeIndex == 1) {
            if (that.navIndex == 1) {
              //已上课
              /**
               * alreadyType 1:已上课 2:未上课
               */
              res = await uni.$http.get(
                `/deliver/app/teacher/getOneToManyTeacherPlanStudyList?date=${that.searchDate}&pageNum=${that.page}&pageSize=${that.pageSize}&searchType=${
                  that.searchStatusArr[that.searchStatusIndex].value
                }&alreadyType=1` + (that.searchName ? '&className=' + that.searchName : '')
              );
            } else {
              //未上课
              /**
               * alreadyType 1:已上课 2:未上课
               */
              res = await uni.$http.get(
                `/deliver/app/teacher/getOneToManyTeacherPlanStudyList?date=${that.searchDate}&pageNum=${that.page}&pageSize=${that.pageSize}&searchType=${
                  that.searchStatusArr[that.searchStatusIndex].value
                }&alreadyType=2` + (that.searchName ? '&className=' + that.searchName : '')
              );
            }
          } else {
            // 1v1
            if (that.navIndex == 1) {
              //已上课
              res = await uni.$http.get(
                `/deliver/app/teacher/getTeacherPlanStudyAlreadyStudyList?date=${that.searchDate}&pageNum=${that.page}&pageSize=${that.pageSize}&searchType=${
                  that.searchStatusArr[that.searchStatusIndex].value
                }&studentName=${that.searchName}`
              );
              //   if (that.navIndex == 0) {
              //     return;
              //   }
            } else {
              //未上课
              res = await uni.$http.get(
                `/deliver/app/teacher/getTeacherPlanStudyNotStudyList?date=${that.searchDate}&pageNum=${that.page}&pageSize=${that.pageSize}&searchType=${
                  that.searchStatusArr[that.searchStatusIndex].value
                }&studentName=${that.searchName}`
              );
              //   if (that.navIndex == 1) {
              //     return;
              //   }
            }
          }
          // 在数据处理前校验状态
          if (currentNav !== that.navIndex || currentType !== that.typeIndex) {
            return; // 状态已变化时丢弃本次请求结果
          }
          // that.$refs.loadingPopup.close();
          if (isPage) {
            let old = that.studylist.data;
            // if (that.typeIndex == 1) return (that.studylist.data = this.studyDateOption(null, res.data.data));
            // that.studylist.data = this.studyDateOption(old, res.data.data.data);
            // that.studylist.data = [...old, ...res.data.data.data]
            this.$set(that.studylist, 'data', this.studyDateOption(old, res.data.data.data));
          } else {
            that.studylist = res.data.data;
            // if (that.typeIndex == 1) return (that.studylist.data = this.studyDateOption(null, res.data.data));
            // that.studylist.data = this.studyDateOption(null, res.data.data.data);
            this.$set(that.studylist, 'data', this.studyDateOption(null, res.data.data.data));
          }
        } catch (e) {
          console.log('🚀 ~ getStudyList ~ e-catch:', e);
          // that.$refs.loadingPopup.close();
          if (!isPage) {
            // 初始化加载失败时显示空状态
            that.studylist = { data: [] };
          }
          uni.showToast({
            title: '数据加载失败',
            icon: 'none'
          });
          // 失败时重置页码
          if (isPage) this.page--;
        }
      },

      studyDateOption(oldData, newData) {
        console.log('🚀 ~ studyDateOption ~ newData:', newData);
        // let dataArr = [];
        // if (oldData) {
        //   dataArr = oldData;
        // }
        // 数据校验
        if (!Array.isArray(newData)) {
          console.error('Invalid newData format:', newData);
          return oldData || [];
        }

        let dataArr = oldData ? [...oldData] : [];

        for (let i = 0; i < newData.length; i++) {
          let haveIndex = this.getIsHaveDate(dataArr, newData[i].date);
          if (haveIndex != -1) {
            dataArr[haveIndex].data.push(newData[i]);
          } else {
            let needData = {
              date: newData[i].date,
              data: [newData[i]]
            };
            // needData.data.push()
            dataArr.push(needData);
          }
        }
        return dataArr;
      },
      getIsHaveDate(arr, date) {
        for (var i = 0; i < arr.length; i++) {
          if (arr[i].date == date) {
            return i;
          }
        }
        return -1;
      },
      checkTypeListIndex(e) {
        this.typeListIndex = e.index;
      },
      checkIndex(e) {
        this.navIndex = e.index;
        if (this.navIndex == 0) {
          this.searchStatusArr = this.beforeStudyArray;
        } else {
          this.searchStatusArr = this.studyAfterArray;
        }
        //清除搜索状态
        this.searchStatusIndex = 0;
        this.studylist = {
          data: []
        };
        this.getNormalStudyList();
      },
      checkTypeIndex(e) {
        this.typeIndex = e.index;
        //清除搜索状态
        this.searchStatusIndex = 0;
        this.studylist = {
          data: []
        };
        this.getNormalStudyList();
      },

      studtPrint(item) {
        uni.navigateTo({
          url: '/studyPrint/studyPrint?studentCode=' + item.studentCode + '&studentName=' + item.studentName
        });
      },

      open(item) {
        this.subject = item;
        this.timelist = {
          actualStart: '',
          actualEnd: ''
        };
        console.log('item', this.subject.experience);
        console.log('item1111', item);
        // 一对多项目跳转到1对多反馈页面
        if (this.typeIndex == 1) {
          console.log('item1111111', item);
          this.trialclass = !this.subject.experience == false;
          this.oneToManyBackData(); // experience = false 是学习反馈
          return;
        }

        if (this.subject.experience == false) {
          this.trialclass = false;
          this.backData(); // experience = false 是学习反馈
        } else {
          this.trialclass = true;
          if (item.curriculumName === '鼎英语') {
            this.flag = false;
          }
          this.trialData(item); // experience = true 是试课反馈
        }
      },

      // 获取反馈详情
      async backData() {
        let that = this;
        that.$refs.loadingPopup.open();
        try {
          let res = await uni.$http.get('/deliver/app/teacher/getFeedbackInfo', {
            // id: 1282826358493421568,
            id: this.subject.id,
            actualStart: this.timelist.actualStart,
            actualEnd: this.timelist.actualEnd,
            type: 1
          });
          that.$refs.loadingPopup.close();
          if (res.data.code == 70001) {
            that.learneShow = true;
            that.$refs.popup.open('center');
          }
          if (res.data.success) {
            that.backlist = res.data.data;
            that.learneShow = false;
            that.isFeedback = that.backlist.feedback === '' || that.backlist.feedback == null || that.backlist.feedback == undefined;
            that.feedback = that.backlist.feedback;
            that.timelist.actualStart = that.backlist.actualStart;
            that.timelist.actualEnd = that.backlist.actualEnd;
            //跳转页面
            if (res.data.data.curriculumName === '拼音法' || res.data.data.curriculumName == '拼音法（高年级）') {
              that.jumpPagePinyin();
            } else if (res.data.data.curriculumName === '鼎学能') {
              that.jumpPageDxn();
            } else if (this.CurriculumCodeArr.includes(res.data.data.curriculumCode)) {
              console.log('🚀 ~ backData ~ curriculumCode:', this.CurriculumCodeArr.includes(res.data.data.curriculumCode));
              that.jumpPageXkt();
            } else if (res.data.data.needDetail) {
              that.jumpPage();
            } else {
              console.log(1111);
              that.jumpElse();
            }
          }
          that.$refs.chanelTime.setLists(that.timelist);
        } catch (e) {
          that.$refs.loadingPopup.close();
        }
      },
      // 获取1对多反馈详情
      async oneToManyBackData() {
        let that = this;
        that.$refs.loadingPopup.open();
        try {
          let isShow = this.subject.isFeedback ? 1 : 0;
          let res = await uni.$http.post(`/deliver/app/teacher/showOneToManyFeedback?classPlanStudyId=${this.subject.id}&isShow=${isShow}`);
          console.log('一对多接口 ~ res:', res);
          that.$refs.loadingPopup.close();
          if (res.data.success) {
            that.backlist = res.data.data;
            //跳转页面
            let sendData = {};
            this.$refs.popup.close();
            sendData.isFirst = !this.subject.isFeedback;
            sendData.subject = this.subject;
            sendData.backlist = this.backlist;
            sendData.classPlanStudyId = res.data.data.classPlanStudyId;
            sendData.type = this.subject.experience == 0 ? 2 : 1;
            console.log('分享数据 ~ sendData:', sendData);
            console.log(sendData, '0909099', encodeURIComponent(JSON.stringify(sendData)));
            console.log('curriculumCode ~ res:', res.data.data.curriculumCode);
            console.log('判断进入那个h5 ~ res:', res.data.data.needDetail);
            // 反馈跳转
            if (res.data.data.curriculumCode === 'MATH') {
              uni.navigateTo({
                url: '/qyWechat/math_study_feedback?sendData=' + encodeURIComponent(JSON.stringify(sendData))
              });
            } else {
              uni.navigateTo({
                url: '/qyWechat/oneToMany_study_feedback?sendData=' + encodeURIComponent(JSON.stringify(sendData))
              });
            }

            return;
          }
          that.$refs.chanelTime.setLists(that.timelist);
        } catch (e) {
          that.$refs.loadingPopup.close();
        }
      },

      jumpElse() {
        let sendData = {};
        this.$refs.popup.close();
        sendData.trialclass = this.trialclass;
        sendData.isFeedback = this.isFeedback;
        sendData.triallist = this.triallist;
        sendData.subject = this.subject;
        sendData.backlist = this.backlist;
        let newstr = '';
        // #ifdef MP-WEIXIN
        newstr = JSON.stringify(sendData);
        // #endif
        // #ifdef APP-PLUS
        newstr = JSON.stringify(sendData).replace(/%/g, '%25');
        // #endif
        uni.navigateTo({
          url: '/qyWechat/onlyFeedBack?sendData=' + encodeURIComponent(JSON.stringify(sendData))
        });
      },
      //跳转页面
      jumpPage() {
        console.log('跳转页面111111');
        let sendData = {};
        this.$refs.popup.close();
        sendData.trialclass = this.trialclass;
        sendData.isFeedback = this.isFeedback;
        sendData.triallist = this.triallist;
        sendData.subject = this.subject;
        sendData.backlist = this.backlist;
        let newstr = '';
        // #ifdef MP-WEIXIN
        newstr = JSON.stringify(sendData);
        // #endif
        // #ifdef APP-PLUS
        newstr = JSON.stringify(sendData).replace(/%/g, '%25');
        // #endif
        uni.navigateTo({
          url: '/pages/index/study_feedback?sendData=' + encodeURIComponent(newstr)
        });
      },
      //跳转拼音法页面
      jumpPagePinyin() {
        let sendData = {};
        this.$refs.popup.close();
        sendData.trialclass = this.trialclass;
        sendData.isFeedback = this.isFeedback;
        sendData.triallist = this.triallist;
        sendData.subject = this.subject;
        sendData.backlist = this.backlist;
        console.log(sendData, '090909', encodeURIComponent(JSON.stringify(sendData)));
        let newstr = '';
        // #ifdef MP-WEIXIN
        newstr = JSON.stringify(sendData);
        // #endif
        // #ifdef APP-PLUS
        newstr = JSON.stringify(sendData).replace(/%/g, '%25');
        // #endif
        uni.navigateTo({
          url: '/qyWechat/pinyin_study_feedback?sendData=' + encodeURIComponent(newstr)
        });
      },
      jumpPageDxn() {
        let sendData = {};
        console.log(22222222);
        this.$refs.popup.close();
        sendData.trialclass = this.trialclass;
        sendData.isFeedback = this.isFeedback;
        sendData.triallist = this.triallist;
        sendData.subject = this.subject;
        sendData.backlist = this.backlist;
        let newstr = '';
        // #ifdef MP-WEIXIN
        newstr = JSON.stringify(sendData);
        // #endif
        // #ifdef APP-PLUS
        newstr = JSON.stringify(sendData).replace(/%/g, '%25');
        // #endif
        uni.navigateTo({
          url: '/share/dxn_study_feedback?sendData=' + encodeURIComponent(newstr)
        });
      },
      // 跳转学考通的反馈
      jumpPageXkt() {
        let sendData = {};
        this.$refs.popup.close();
        sendData.trialclass = this.trialclass;
        sendData.isFeedback = this.isFeedback;
        sendData.triallist = this.triallist;
        sendData.subject = this.subject;
        sendData.backlist = this.backlist;
        console.log(22222222, sendData);
        uni.navigateTo({
          url: '/share/xkt_study_feedback?sendData=' + encodeURIComponent(JSON.stringify(sendData))
        });
      },
      // 试课反馈详情
      async trialData(item) {
        let that = this;
        that.$refs.loadingPopup.open();
        try {
          let res = await uni.$http.post(`/deliver/app/teacher/getExperienceInfo/${that.subject.id}?actualStart=${that.timelist.actualStart}&actualEnd=${that.timelist.actualEnd}`);
          that.$refs.loadingPopup.close();
          if (res.data.code == 70001) {
            that.learneShow = true;
            await that.$refs.popup.open('center');
            that.$refs.chanelTime.setLists(that.timelist);
            return;
          }
          that.triallist = res.data.data;
          if (res.data.success) {
            that.learneShow = false;
            that.Feedback = that.triallist.feedback === '' || that.triallist.feedback == null || that.triallist.feedback == undefined;
            that.feedback = that.triallist.feedback;
            that.Intention = that.triallist.studyIntention;
            that.timelist.actualStart = that.triallist.actualStart;
            that.timelist.actualEnd = that.triallist.actualEnd;
            //跳转页面
            if (res.data.data.curriculumName === '拼音法' || res.data.data.curriculumName === '拼音法（高年级）') {
              that.jumpPagePinyin();
            } else if (res.data.data.curriculumName === '鼎学能') {
              that.jumpPageDxn();
            } else if (this.CurriculumCodeArr.includes(res.data.data.curriculumCode)) {
              console.log('🚀 ~ trialData ~ CurriculumCodeArr.includes(res.data.data.curriculumCode):', this.CurriculumCodeArr.includes(res.data.data.curriculumCode));
              that.jumpPageXkt();
            } else if (res.data.data.needDetail) {
              that.jumpPage();
            } else {
              that.jumpElse();
            }
          }
        } catch {
          that.$refs.loadingPopup.close();
        }
      },

      changeStart(val) {
        this.timelist.actualStart = val;
        if (this.timelist.actualStart != '' && this.timelist.actualEnd != '') {
          this.backData();
        }
      },
      changeEnd(time) {
        this.timelist.actualEnd = time;
        if (this.timelist.actualStart != '' && this.timelist.actualEnd != '') {
          if (this.trialclass) {
            //试课
            this.trialData(this.subject);
          } else {
            //学习反馈
            this.backData();
          }
        }
      },

      popupCancel() {
        this.timelist.actualStart = '';
        this.timelist.actualEnd = '';
        this.$refs.popup.close();
      },

      checkTimeOut(item) {
        console.log(item);
        // 过期 就  true
        // 否则 就  return false;
        let nowTimeStr = new Date().toLocaleDateString();
        let setTimeStr = new Date(item.date).toLocaleDateString();
        if (nowTimeStr === setTimeStr) {
          // 今天
          let today = dayjs().format('YYYY-MM-DD');
          let allStr = item.startTime;
          let nowTimeStamp = Date.now();
          let setTimeStamp = dayjs(allStr).unix() * 1000;
          return nowTimeStamp < setTimeStamp;
        } else {
          // 不是今天
          let nowTimeStamp = Date.now();
          let setTimeStamp = new Date(item.date).getTime();
          return nowTimeStamp < setTimeStamp;
        }
      },

      copy(item) {
        if (item) {
          uni.setClipboardData({
            data: item,
            success: function (res) {
              uni.getClipboardData({
                success: function (res) {
                  uni.showToast({
                    title: '已复制到剪贴板'
                  });
                }
              });
            }
          });
        }
      },

      // 请假弹框打开
      leave(item) {
        this.leaves = item;
        let date = this.leaves.startTime;
        let nowDate = Date.now();
        let setDate = dayjs(date).unix() * 1000;
        let basetime = 24 * 60 * 60 * 1000;
        if (setDate - nowDate < basetime) {
          uni.showToast({
            icon: 'none',
            title: '请假时间不小于24小时',
            duration: 2000
          });
        } else {
          this.restData(); // 请假详情
        }
      },
      // 请假详情
      async restData() {
        let that = this;
        let res = await uni.$http.get('/deliver/app/teacher/getVacationApplyInfo', {
          id: that.leaves.id
        });
        if (res.data.success) {
          that.leavelist = res.data.data;
          that.$refs.popup2.open('center');
        }
        that.isFeedback = that.leavelist.cause === '' || that.leavelist.cause == null || that.leavelist.cause == undefined;
        that.cause = that.leavelist.cause;
      },
      // 请假弹框确定按钮
      sureLeave() {
        this.leaveData();
      },

      // 请假取消按钮
      closeLeave() {
        this.cause = '';
        this.$refs.popup2.close();
      },

      // 请假
      async leaveData(e) {
        let that = this;
        that.$refs.loadingPopup.open();
        try {
          let res = await uni.$http.post('/deliver/app/teacher/addVacationApply?cause=' + encodeURI(that.cause) + '&id=' + that.leaves.id + '&type=' + 1);
          that.$refs.loadingPopup.close();
          if (res.data.success) {
            that.cause = '';
            uni.showToast({
              title: '请假成功',
              duration: 500
            });
            that.$refs.popup2.close();
            that.studyData();
          }
        } catch {
          that.$refs.loadingPopup.close();
        }
      },
      // 1v多改变选择的学生
      changeStudents(item, value) {
        // console.log(item, value, '改变');
        this.students = value;
        this.schedulingPlan.studentList = item;
      },
      // 1v多获取学生列表
      async getStudentList() {
        let { data } = await uni.$http.get('/deliver/app/teacher/getStudentByClass?classId=' + this.classType.id);

        if (data.data && data.data.length <= 0) {
          uni.showToast({
            title: '该班级没有学生',
            icon: 'none'
          });
          this.isClassActive = -1;
          this.classType = {}; // 清空班级信息
          this.studentlist = []; // 清空班级学生列表
          this.changeStudents([], []); // 清空选择的学生
          return;
        }
        // console.log(data.data, '6666');

        this.studentlist = data.data;
        this.students = data.data.map((i) => i.studentCode);
        this.schedulingPlan.studentList = data.data;
      },
      createSuccessOption(isAgain) {
        this.$refs.createSuccess.close();
        if (isAgain) {
          this.createCourse();
        } else {
          this.dialogSearchPeople = null;
          this.dialogSearchPeopleIndex = [0];
          this.calendarDate = [];
          this.createCourseRequest = {
            hour: 1,
            startTime: '',
            endTime: ''
          };
        }
      },
      // 创建排课计划
      async createPlanCourse() {
        let that = this;
        try {
          let res = await that.isConflict();
          if (res.data.data.length > 0) {
            // 排课时间和停服时间有冲突
            that.offServiceTimeList = res.data.data;
            that.$refs.stopTakingPrompt.open();
          } else {
            // 没有冲突 继续创建排课计划
            console.log(res, 'res');
            this.stopTakingOption(); // 创建排课计划
          }
        } catch (e) {
          that.$refs.loadingPopup.close();
        }
      },
      // 判断是否和停服时间冲突
      async isConflict() {
        let that = this;
        that.$refs.loadingPopup.open();
        let data;
        // 1v1排课计划
        if (this.typeListIndex == 0) {
          data = {
            dateList: that.calendarDate,
            startTime: that.createCourseRequest.startTime,
            endTime: that.createCourseRequest.endTime
          };
        } else {
          data = {
            dateList: this.classType.type == 2 ? that.calendarDate : [that.calendarDate1],
            startTime: that.schedulingPlan.startTime,
            endTime: that.schedulingPlan.endTime
          };
        }
        console.log(data, '查看和停服时间是否冲突data');
        //查看和停服时间是否冲突
        let timeList = [];
        for (let i = 0; i < data.dateList.length; i++) {
          timeList.push(data.dateList[i] + ' ' + data.startTime + ':00' + '/' + data.dateList[i] + ' ' + data.endTime + ':00');
        }
        console.log(timeList, 'timeList');

        let res = await uni.$http.post('/znyy/stopServiceTimeConfig/isDuringStopServiceTime', {
          timeList
        });
        that.$refs.loadingPopup.close();
        return res;
      },
      // 添加排课计划
      async stopTakingOption() {
        let that = this;
        // 1v多班级排课计划
        if (this.typeListIndex == 1) {
          let that = this;
          that.$refs.loadingPopup.open();
          try {
            that.schedulingPlan.studentList.forEach((i) => {
              i.id = i.studentDeliverId;
            });

            that.$refs.loadingPopup.open();
            let res;
            // 正课
            if (that.classType.type == 2) {
              let data = {
                dateList: that.calendarDate,
                timeList: [
                  {
                    startTime: that.schedulingPlan.startTime,
                    endTime: that.schedulingPlan.endTime
                  }
                ],

                studentDeliverDtoList: that.schedulingPlan.studentList,
                deliverClassId: that.classType.id,
                teachingType: 3
              };
              console.log(data, '班级排课2');

              res = await uni.$http.post('/deliver/app/teacher/addOneToMoreClassPlanStudy', data);
            } else {
              // 试课
              let data = {
                date: that.calendarDate1,
                time: `${that.schedulingPlan.startTime},${that.schedulingPlan.endTime}`,

                studentDeliverDtoList: that.schedulingPlan.studentList,
                deliverClassId: that.classType.id,
                teachingType: 3
              };
              console.log(data, '班级排课1');
              res = await uni.$http.post('/deliver/app/teacher/addOneToMoreClassExperiencePlanStudy', data);
            }
            that.$refs.loadingPopup.close();
            uni.setStorageSync('lastDeliverClass', this.deliverClass);
            console.log(res);
            if (res.data.success) {
              this.getNormalStudyList(); // 重新获取学习列表
              this.$refs.createSuccess.open();
              this.createOption(false);
              this.closeCourse();
            }
          } catch (e) {
            that.$refs.loadingPopup.close();
          }
          return;
        }
        // 1v1排课计划
        try {
          that.$refs.loadingPopup.open();
          let data = {
            dateList: that.calendarDate,
            schoolMerchantCode: that.choseSchoolItem?.value,
            startTime: that.createCourseRequest.startTime,
            studentCode: that.dialogSearchPeople.studentCode,
            endTime: that.createCourseRequest.endTime,
            curriculumId: that.courseType.curriculumId,
            deliverClass: that.deliverClass
          };
          console.log("🚀 ~ stopTakingOption ~ '/deliver/app/teacher/batchAddPlanCourse':", '/deliver/app/teacher/batchAddPlanCourse');
          let res = await uni.$http.post('/deliver/app/teacher/batchAddPlanCourse', data);
          that.$refs.loadingPopup.close();

          if (res.data.success) {
            this.getNormalStudyList();
            this.$refs.createSuccess.open();
            this.$refs.stopTakingPrompt.close();
            this.$refs.createPopup.close();
          }
        } catch (e) {
          console.log(e);

          that.$refs.loadingPopup.close();
        }
      },
      // 停服时间冲突弹框确定继续添加排课计划
      continueAdding() {
        // 判断是新增还是编辑
        if (this.editForm.id) {
          // 新增
          this.stopTakingOption();
        } else {
          // 编辑
          this.editOption(true, true);
        }
      },
      //获取所选门店剩余可排课时
      async getStudentHaveDeliverHours() {
        let that = this;
        try {
          that.$refs.loadingPopup.open();
          let res = await uni.$http.get(
            '/deliver/app/teacher/selStudentHaveDeliverHours?studentCode=' +
              that.dialogSearchPeople.studentCode +
              '&merchantCode=' +
              that.choseSchoolItem.value +
              '&curriculumId=' +
              that.courseType.curriculumId
          );
          that.$refs.loadingPopup.close();
          console.log('获取学生所属名店剩余课时');
          console.log(res);
          if (res.data && res.data.success) {
            that.choseSchoolHour = res.data.data;
          }
        } catch (e) {
          that.$refs.loadingPopup.close();
        }
      },
      //获取学生所属名店
      async getStudentSchoolList() {
        let that = this;
        try {
          that.$refs.loadingPopup.open();
          let res = await uni.$http.get(
            `/deliver/app/teacher/selStudentSchoolList?studentCode=${that.dialogSearchPeople.studentCode}&curriculumId=${that.courseType.curriculumId}`
          );
          console.log('获取学生所属名店');
          console.log(res);
          that.$refs.loadingPopup.close();
          if (res.data && res.data.success) {
            if (res.data.data.length > 0) {
              if (res.data.data.length <= 1) {
                that.choseSchoolItem = res.data.data[0];
                that.getStudentHaveDeliverHours();
              } else {
                that.arraySchool = res.data.data;
                that.$refs.popopChooseSchool.open();
              }
            } else {
              uni.showToast({
                icon: 'none',
                title: '该学员还没绑定门店'
              });
            }
          }
        } catch (e) {
          that.$refs.loadingPopup.close();
        }
      },
      chooseSchoollist(item, index) {
        this.isactive = index;
        this.choseSchoolItem = item;
      },
      choseSchoolOption(isOk) {
        if (isOk) {
          if (this.choseSchoolItem) {
            this.isactive = -1;
            this.getStudentHaveDeliverHours();
          } else {
            uni.showToast({
              icon: 'none',
              title: '请先选择~'
            });
            return;
          }
        } else {
          that.$refs.loadingPopup.close();
        }
        this.$refs.popopChooseSchool.close();
      },
      //创建排课
      createCourse() {
        this.typeListIndex = 0; // 重置排课计划类型
        this.choseSchoolHour = 0;
        this.dialogSearchPeople = null;
        this.dialogSearchPeopleIndex = [0];
        this.calendarDate = [];
        this.calendarDate1 = '';
        this.createCourseRequest = {
          hour: 1,
          startTime: '',
          endTime: ''
        };
        // 打开创建排课弹框
        this.$refs.createPopup.open();
        // 重新计算tabs
        this.$nextTick(() => {
          this.$refs.tabs.resize();
        });
        // 获取班级列表
        this.getClassList();

        if (uni.getStorageSync('lastDeliverClass')) {
          this.deliverClass = uni.getStorageSync('lastDeliverClass');
        }
      },
      /**
       *
       * @param isSure
       * @param sure  是否跳过停服时间冲突弹框
       */
      async editOption(isSure, sure = false) {
        let that = this;
        if (isSure) {
          // 1v1
          let obj = {
            studyId: this.editForm.id,
            startStudyTime: this.calendarDate1 + ' ' + this.editCourseRequest.startTime,
            endStudyTime: this.calendarDate1 + ' ' + this.editCourseRequest.endTime,
            deliverClass: this.deliverClass
          };
          // 1v多
          let objTwo = {
            date: that.calendarDate1,
            time: `${that.schedulingPlan.startTime},${that.schedulingPlan.endTime}`,

            studentDeliverDtoList: that.schedulingPlan.studentList,
            deliverClassId: that.classType.id,
            teachingType: 3
          };
          that.$refs.loadingPopup.open();
          // 查看停服计划是否冲突
          let timeList = [];
          if (this.typeIndex == 1) {
            timeList.push(this.calendarDate1 + ' ' + that.schedulingPlan.startTime + '/' + this.calendarDate1 + ' ' + that.schedulingPlan.endTime);
          } else {
            timeList.push(obj.startStudyTime + '/' + obj.endStudyTime);
          }
          let res;
          // 跳过
          if (sure) {
            res.data.data = [];
          } else {
            res = await uni.$http.post('/znyy/stopServiceTimeConfig/isDuringStopServiceTime', { timeList });
            that.$refs.loadingPopup.close();
          }
          // 有冲突打开冲突弹框
          if (res.data.data.length > 0) {
            that.offServiceTimeList = res.data.data;
            that.$refs.stopTakingPrompt.open();
          } else {
            // 没有冲突 继续编辑
            // 1v1
            if (this.typeIndex == 0) {
              that.$refs.loadingPopup.open();
              let res = await uni.$http.post('/deliver/web/learnManager/modifyPlanStudy', obj);
              that.$refs.loadingPopup.close();
              console.log(res);
              if (res.data.success) {
                uni.showToast({
                  title: '编辑成功',
                  icon: 'success',
                  mask: true
                });
                uni.setStorageSync('lastDeliverClass', this.deliverClass);
                this.$refs.editPopup.close();
                this.closeCourse();
                this.studylist = { data: [] };
                this.getNormalStudyList();
              } else {
                uni.showToast({
                  title: '编辑失败',
                  icon: 'none',
                  mask: true
                });
              }
            } else {
              // 1v多
              // 试课
              console.log(objTwo, '班级排课1');
              res = await uni.$http.post('/deliver/app/teacher/addOneToMoreClassExperiencePlanStudy', objTwo);
              if (res.data.success) {
                uni.showToast({
                  title: '编辑成功',
                  icon: 'success',
                  mask: true
                });
                uni.setStorageSync('lastDeliverClass', this.deliverClass);
                this.$refs.editPopup.close();
                this.closeCourse();
                this.studylist = { data: [] };
                this.getNormalStudyList();
              } else {
                uni.showToast({
                  title: '编辑失败',
                  icon: 'none',
                  mask: true
                });
              }
            }
          }
        } else {
          this.editForm = {};
          this.editCourseRequest = {
            hour: 1,
            startTime: '',
            endTime: ''
          };
          console.log(this.courseType, 'courseType');
          this.calendarDate1 = '';
          this.deliverClass = '';
          this.$refs.editPopup.close();
        }
      },
      createOption(isSure) {
        if (isSure) {
          // 1v多排课计划
          if (this.typeListIndex == 1) {
            if (!this.classType.className) {
              uni.showToast({
                icon: 'none',
                title: '请选择班级'
              });
              return;
            }
            if (this.schedulingPlan.studentList.length == 0) {
              uni.showToast({
                icon: 'none',
                title: '请选择学员'
              });
              return;
            }
            if ((this.classType.type == 2 && this.calendarDate.length == 0) || (this.classType.type == 1 && this.calendarDate1.length == 0)) {
              uni.showToast({
                icon: 'none',
                title: '请选择日期'
              });
              return;
            }
            if (!this.schedulingPlan.startTime) {
              uni.showToast({
                icon: 'none',
                title: '请选择开始时间'
              });
              return;
            }
            //   if (this.courseType.extraSecond == 1) {
            //     if (this.deliverClass == '') {
            //       uni.showToast({
            //         title: '请输入班级',
            //         icon: 'none'
            //       });
            //       return;
            //     }
            //   }
            // 创建排课计划
            this.createPlanCourse();
            return;
          }
          if (!this.dialogSearchPeople) {
            uni.showToast({
              icon: 'none',
              title: '请选择学员'
            });
            return;
          }
          if (!this.choseSchoolItem) {
            uni.showToast({
              icon: 'none',
              title: '请选择门店~'
            });
            return;
          }
          if (!this.choseSchoolHour) {
            uni.showToast({
              icon: 'none',
              title: '剩余可排时长不足~'
            });
            return;
          }
          if (this.calendarDate.length == 0) {
            uni.showToast({
              icon: 'none',
              title: '请选择日期'
            });
            return;
          }
          if (!this.createCourseRequest.startTime) {
            uni.showToast({
              icon: 'none',
              title: '请选择开始时间'
            });
            return;
          }
          if (this.courseType.extraSecond == 1) {
            if (this.deliverClass == '') {
              uni.showToast({
                title: '请输入班级',
                icon: 'none'
              });
              return;
            }
          }
          // 创建排课计划
          this.createPlanCourse();
          return;
        } else {
          let that = this;
          this.typeListIndex = 0; // 重置排课计划类型
          this.deliverClass = '';
          this.closeCourse();
          // 1v多
          this.closeClass(); // 关闭班级选择弹框
          this.schedulingPlan = { startTime: '', endTime: '', hour: 1, studentList: [] };

          this.$refs.createPopup.close();
        }
      },
      //选择学员
      async dialogSearchStudent(isNormal) {
        let that = this;
        that.$refs.loadingPopup.open();
        try {
          let res = await uni.$http.get('/deliver/app/teacher/selTeacherStudentBindingList?name=');
          that.$refs.loadingPopup.close();
          if (res.data && res.data.success) {
            that.createChoseStudentArr = res.data.data;
            !isNormal && that.$refs.choseStudent.open();
          } else {
            uni.showToast({
              icon: 'none',
              title: '获取学员失败'
            });
          }
        } catch {
          that.$refs.loadingPopup.close();
        }
      },
      choseStudentOption(isSure) {
        if (isSure) {
          if (!this.courseType.curriculumId) {
            uni.showToast({
              icon: 'none',
              title: '请选择课程类型'
            });
            return;
          }
          if (this.createChoseStudentArr.length == 0) {
            uni.showToast({
              icon: 'none',
              title: '请选择学员'
            });
            return;
          }
          this.dialogSearchPeople = this.createChoseStudentArr[this.dialogSearchPeopleIndex];
          this.getStudentSchoolList();
        }
        this.choseStudentSearchName = '';
        this.$refs.choseStudent.close();
      },
      //选择学员中的搜索学员姓名
      async choseStudentSearch() {
        let that = this;
        if (that.choseStudentSearchName.length == 0) {
          uni.showToast({
            icon: 'none',
            title: '请输入学员姓名'
          });
          return;
        }
        that.$refs.loadingPopup.open();
        try {
          let res = await uni.$http.get(`/deliver/app/teacher/selTeacherStudentBindingList?name=${that.choseStudentSearchName}`);
          that.$refs.loadingPopup.close();
          if (res.data && res.data.success) {
            if (res.data.data.length == 0) {
              uni.showToast({
                icon: 'none',
                title: '暂无该学员'
              });
              return;
            }
            this.dialogSearchPeopleIndex = [0];
            that.createChoseStudentArr = res.data.data;
          } else {
            uni.showToast({
              icon: 'none',
              title: '获取学员失败'
            });
          }
        } catch {
          that.$refs.loadingPopup.close();
        }
      },
      choseStudentChange(val) {
        this.dialogSearchPeopleIndex = val.detail.value;
      },
      //选择日期
      dialogChoseDate() {
        // 1v1排课计划
        if (this.typeListIndex == 0) {
          if (this.dialogSearchPeople && this.dialogSearchPeople.studentCode) {
            this.selectCalendarArr = [];
            this.$refs.calendar.open();
            this.getStudentStudyDateList(dayjs().format('YYYY-MM'));
          } else {
            uni.showToast({
              icon: 'none',
              title: '请先选择学员'
            });
          }
          return;
        }
        // 1v多班级排课计划
        this.selectCalendarArr = [];
        this.$refs.calendar.open();
        this.getStudentStudyDateList(dayjs().format('YYYY-MM'));
      },
      dialogChoseDate1() {
        this.selectCalendarArr1 = [];
        this.$refs.editcalendar.open();
        this.getStudentStudyDateList1(dayjs().format('YYYY-MM'));
      },
      //日历确定
      calendarConfirm(e) {
        console.log(e);
        this.calendarDate = e.multiple;
      },
      //日历确定
      calendarConfirm1(e) {
        console.log(e);
        this.calendarDate1 = e.fulldate;
      },
      getCalendarDateText() {
        return this.calendarDate.join(',');
      },
      getCalendarDateText1() {
        return this.calendarDate1;
      },
      showChoseCourseTime(isStart) {
        if (!isStart) {
          uni.showToast({
            icon: 'none',
            title: '请选择开始时间'
          });
          return;
        }
        this.showCreateTimePicker = true;
      },
      showChoseCourseTime1(isStart) {
        if (!isStart) {
          uni.showToast({
            icon: 'none',
            title: '请选择开始时间'
          });
          return;
        }
        this.showCreateTimePicker1 = true;
      },
      createTimePickerCancel() {
        this.showCreateTimePicker = false;
      },
      createTimePickerCancel1() {
        this.showCreateTimePicker1 = false;
      },
      // 选择开始时间
      createTimePickerSure(e) {
        console.log('🚀 ~ createTimePickerSure ~ e:', e);
        this.showCreateTimePicker = false;
        // 1v1排课计划
        if (this.typeListIndex == 0) {
          this.createCourseRequest.startTime = e.value;
          this.createCourseRequest.endTime = this.getAutoEndTime(e.value, this.createCourseRequest.hour);
          return;
        }
        // 1v多班级排课计划
        this.schedulingPlan.startTime = e.value;
        this.schedulingPlan.endTime = this.getAutoEndTime(e.value, this.schedulingPlan.hour);
      },
      createTimePickerSure1(e) {
        console.log(e);
        this.showCreateTimePicker1 = false;
        // 1v1排课计划
        if (!this.editForm.deliverClass) {
          this.editCourseRequest.startTime = e.value + ':00';
          this.editCourseRequest.endTime = this.getAutoEndTime(e.value, this.editCourseRequest.hour);
          console.log(this.editCourseRequest.endTime, 'this.editCourseRequest.endTime');
          this.editCourseRequest.endTime += ':00';
          return;
        }
        // 1v多班级排课计划
        this.schedulingPlan.startTime = e.value + ':00';
        this.schedulingPlan.endTime = this.getAutoEndTime(e.value, this.schedulingPlan.hour);
        console.log(this.schedulingPlan.endTime, 'this.schedulingPlan.endTime');
        this.schedulingPlan.endTime += ':00';
      },
      numberBoxBindChange(e) {
        console.log(e);
        if (this.createCourseRequest.startTime == '') {
          return;
        }
        this.createCourseRequest.hour = e;
        this.createCourseRequest.endTime = this.getAutoEndTime(this.createCourseRequest.startTime, e);
      },
      goBack() {
        plus.runtime.quit();
      },
      numberBoxBindChange1(e) {
        if (this.editCourseRequest.startTime == '') {
          return;
        }
        this.editCourseRequest.hour = e;
        this.editCourseRequest.endTime = this.getAutoEndTime(this.editCourseRequest.startTime, e);
        this.editCourseRequest.endTime += ':00';
      },
      numberBoxBindChange2(e) {
        console.log(this.schedulingPlan.hour);
        if (this.schedulingPlan.startTime == '') {
          return;
        }
        // let hour = (e + '').split('.');
        // console.log('🚀 ~ numberBoxBindChange2 ~ hour:', hour, e);
        // 组件app端有问题，延迟刷新
        this.$nextTick(() => {
          this.schedulingPlan.endTime = this.getAutoEndTime(this.schedulingPlan.startTime, this.schedulingPlan.hour);
          // 只能1v多才会调用这个方法，this.typeListIndex == 0 代表新增排课计划是1v1，说明不是新增是编辑
          if (this.typeListIndex == 0) {
            this.schedulingPlan.endTime += ':00';
          }
        });
      },

      //自动获取结束时间
      getAutoEndTime(startTime, addHour) {
        let timeParts = startTime.split(':');
        let hours = parseInt(timeParts[0], 10) * 1;
        let minutes = parseInt(timeParts[1], 10) * 1;

        addHour = (addHour + '').split('.');
        addHour = addHour.map((i) => i * 1);

        if (addHour.length > 1) {
          minutes += 30;
          if (minutes > 60) {
            minutes -= 60;
            addHour += 1;
          }
        }
        hours += addHour[0];
        // console.log('🚀 ~ getAutoEndTime ~ hours:', hours);

        if (hours >= 24) {
          hours -= 24;
        }
        let endTime = `${hours < 10 ? '0' + hours : hours}:${minutes < 10 ? '0' + minutes : minutes}`;
        return endTime;
      },
      monthSwitch(date) {
        console.log('monthSwitch', date);
        if (this.isShowCreatePopup) {
          this.getStudentStudyDateList(`${date.year}-${date.month}`);
        }
      },
      monthSwitch1(date) {
        console.log('monthSwitch', date);
        if (this.isShowCreatePopup) {
          this.getStudentStudyDateList1(`${date.year}-${date.month}`);
        }
      },
      ///日历数据
      async getStudentStudyDateList(date) {
        let that = this;
        // that.$refs.loadingPopup.open();
        try {
          let res = await uni.$http.get('/deliver/app/teacher/getStudentStudyDateList?date=' + date + '&type=1&studentCode=' + that.dialogSearchPeople.studentCode);
          // that.$refs.loadingPopup.close();
          if (res.data && res.data.success) {
            for (var i = 0; i < res.data.data.length; i++) {
              let data = {
                date: res.data.data[i],
                info: '已排课',
                infoColor: '#2E896F'
              };
              that.selectCalendarArr.push(data);
            }
          }
        } catch {
          // that.$refs.loadingPopup.close();
        }
      },
      ///日历数据
      async getStudentStudyDateList1(date) {
        let that = this;
        // that.$refs.loadingPopup.open();
        try {
          let res = await uni.$http.get('/deliver/app/teacher/getStudentStudyDateList?date=' + date + '&type=1&studentCode=' + that.dialogSearchPeople.studentCode);
          // that.$refs.loadingPopup.close();
          if (res.data && res.data.success) {
            for (var i = 0; i < res.data.data.length; i++) {
              let data = {
                date: res.data.data[i],
                info: '已排课',
                infoColor: '#2E896F'
              };
              that.selectCalendarArr1.push(data);
            }
          }
        } catch {
          // that.$refs.loadingPopup.close();
        }
      },
      //删除班级排课记录
      async deleteClassPlanStudy() {
        let that = this;
        try {
          that.$refs.loadingPopup.open();
          let res = await uni.$http.post(`/deliver/app/teacher/deleteClassPlanStudy`, {
            classStudyId: that.deleteItem.id
            // id: that.deleteItem.id,
          });
          that.$refs.loadingPopup.close();
          // if (res && res.data.success) {
          uni.showToast({
            icon: 'none',
            title: '删除成功'
          });
          this.$refs.deleteClassCourse.close();
          setTimeout(() => {
            this.getNormalStudyList();
          }, 500);
        } catch (e) {
          that.$refs.loadingPopup.close();
        }
      },
      //删除排课记录
      async deletePlanStudy() {
        let that = this;
        try {
          that.$refs.loadingPopup.open();
          let res = await uni.$http.delete(`/deliver/app/teacher/teacherDeletePlanStudy?id=${that.deleteItem.id}`);
          that.$refs.loadingPopup.close();
          if (res && res.data.success) {
            uni.showToast({
              icon: 'none',
              title: '删除成功'
            });
            this.$refs.deleteCourse.close();
            that.getNormalStudyList();
          }
        } catch (e) {
          that.$refs.loadingPopup.close();
        }
      },
      showDeleteDialog(item) {
        this.deleteItem = item;
        // 1v多
        if (this.typeIndex == 1) {
          this.$refs.deleteClassCourse.open();
          return;
        }
        // 1v1
        this.$refs.deleteCourse.open();
      },
      deleteOption(isOk) {
        if (isOk) {
          // 1v多
          if (this.typeIndex == 1) {
            this.deleteClassPlanStudy();
            return;
          }
          this.deletePlanStudy();
        }
        // 1v多
        if (this.typeIndex == 1) {
          this.$refs.deleteClassCourse.close();
          return;
        }
        // 1v1
        this.$refs.deleteCourse.close();
      },

      //词库更多
      wordMore(item) {
        uni.navigateTo({
          url: '/pages/index/wordMore?merchantCode=' + item.merchantCode + '&studentCode=' + item.studentCode
        });
      },
      vocabulary(item) {
        uni.navigateTo({
          url: '/vocabulary/vocabulary?studentCode=' + item.studentCode + '&studentName=' + item.studentName
        });
      },
      dictation(item) {
        uni.navigateTo({
          url: '/vocabulary/dictation?studentCode=' + item.studentCode + '&studentName=' + item.studentName
        });
      },
      async homeworkDetail(item) {
        let data = await uni.$http.get('/paper/wap/coursePractice/practiceCheck?planId=' + item.id);
        let uuid = await uni.$http.post(`/paper/wap/token?token=${uni.getStorageSync('token')}`);
        if (data.data.success && uuid.data.success) {
          const homeDetailData = data.data.data;
          const correctStatus = homeDetailData.isCheck ? 'see' : 'correct';
          const uuidToken = uuid.data.data;
          console.log('🚀 ~ homeworkDetail ~ homeDetailData.reportId:', homeDetailData.reportId);
          if (homeDetailData.reportId == '') {
            uni.showToast({
              icon: 'none',
              title: '该学生作业暂未提交'
            });
          } else {
            uni.navigateTo({
              url:
                '/qyWechat/homeworkDetail?studentCode=' +
                item.studentCode +
                '&reportId=' +
                homeDetailData.reportId +
                '&correctStatus=' +
                correctStatus +
                '&uuid=' +
                uuidToken +
                '&isMessage=true'
            });
          }
        } else {
          console.error('请求失败:', data.data.message || '未知错误');
          uni.showToast({
            icon: 'none',
            title: '请求失败，请重试',
            duration: 2000
          });
        }
      }
    },
    onError(err) {
      uni.showModal({
        title: '应用错误',
        content: err.message,
        showCancel: false
      });
      console.error('Global Error:', err);
    }
  };
</script>

<style lang="scss" scoped>
  .hear_img {
    position: relative;
    width: 750rpx;
    height: 506rpx;
    z-index: 99;
  }

  .wallet-bgc {
    width: 750rpx;
    height: 506rpx;
  }

  .banner-bgc {
    text-align: center;
    width: 750rpx;
    position: absolute;
    top: 180rpx;
  }

  .nav-title {
    position: absolute;
    //#ifdef APP-PLUS
    width: 550rpx;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    left: 300rpx;
    //#endif
    // #ifdef MP-WEIXIN
    left: 50%;
    // #endif
    top: 130rpx;
    font-weight: bold;
    // border: 1px solid red;
    font-size: 34rpx;
    transform: translate(-50%, -50%);
    z-index: 999;

    .title {
      margin-left: 20rpx;
    }
  }

  .bottom-view {
    background-color: #f3f8fc;
    width: 750rpx;
    display: flex;
    justify-content: center;
  }

  .search-view {
    width: 690rpx;
    height: 130rpx;
    background-color: #fff;
    border-radius: 14rpx 14rpx 0 0;
    display: flex;
    justify-content: space-around;
  }

  .search-view1 {
    width: 690rpx;
    height: 110rpx;
    background-color: #fff;
    border-radius: 0 0 14rpx 14rpx;
    display: flex;
    justify-content: center;
  }

  .search-date {
    margin-top: 30rpx;
    width: 650rpx;
    height: 80rpx;
    display: flex;
    border-radius: 8rpx;
    background-color: rgba(200, 200, 200, 0.18);
    justify-content: space-between;
    align-items: center;
    padding: 0 20rpx;
    box-sizing: border-box;
  }

  .search-class {
    width: 390rpx;
    height: 80rpx;
    display: flex;
    border-radius: 8rpx;
    background-color: rgba(200, 200, 200, 0.18);
    justify-content: space-around;
    align-items: center;
  }

  .search-class-input {
    color: #999999;
    margin-left: 6rpx;
    width: 160rpx;
    padding-left: 10rpx;
    height: 40rpx;
    font-size: 30rpx;
  }

  .search-class-text {
    padding: 0 10rpx;
    color: #2e896f;
    font-size: 30rpx;
    border-left: 1rpx solid #d9d9d9;
  }

  .search-student {
    // width: 390rpx;
    width: 650rpx;
    height: 80rpx;
    display: flex;
    border-radius: 8rpx;
    background-color: rgba(200, 200, 200, 0.18);
    justify-content: space-between;
    align-items: center;
  }

  .search-student-input {
    color: #999999;
    margin-left: 6rpx;
    width: 510rpx;
    height: 40rpx;
    font-size: 30rpx;
  }

  .search-student-text {
    padding-right: 20rpx;
    color: #2e896f;
    font-size: 30rpx;
    // border-left: 1rpx solid #d9d9d9;
  }

  .tab-bg {
    background-color: #f3f8fc;
    width: 750rpx;
    height: 64rpx;
    padding-top: 30rpx;
    padding-bottom: 20rpx;
    display: flex;
    justify-content: center;

    &.type-list {
      width: 600rpx;
      overflow: visible;
      background-color: transparent;
      padding-bottom: 60rpx;
      margin-top: -70rpx;
    }
  }

  .list-bg {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #f3f8fc;
    width: 750rpx;
  }

  .list-item-bg {
    width: 690rpx;
    // height: 500rpx;
    background: #ffffff;
    border-radius: 14rpx;
    margin-bottom: 30rpx;
    padding-bottom: 28rpx;
  }

  .time-pic {
    height: 35rpx;
    width: 38rpx;
  }

  .pic {
    height: 50rpx;
    width: 50rpx;
  }

  .flex-between {
    margin-right: 20rpx;
    margin-left: 20rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .flex-center {
    display: flex;
    align-items: center;
    justify-content: start;
  }

  .border-bottom {
    border-bottom: 2rpx dashed #eeeeee;
  }

  .border-top {
    border-top: 2rpx dashed #eeeeee;
  }

  .studyClass {
    width: 90rpx;
    height: 36rpx;
    background: #edbd58;
    border-radius: 4rpx;
    border: 1rpx solid #edbd58;
    font-size: 26rpx;
    color: #ffffff;
    text-align: center;
  }

  .item-content {
    margin-left: 20rpx;
    margin-top: 30rpx;
    font-size: 30rpx;
  }

  .copy {
    background-color: #c8c8c8;
    text-align: center;
    width: 80rpx;
    height: 40rpx;
    line-height: 40rpx;
  }

  .border-btn {
    border-radius: 30rpx;
    border: 1rpx solid #2e896f;
    // padding: 8rpx 16rpx 8rpx 16rpx;
    padding: 12rpx 22rpx 12rpx 22rpx;

    // font-size: 30rpx;
    font-size: 24rpx;
    color: #2e896f;
    transform: scale(0.995);
    /* 解决ios上圆角失效 */
  }

  .border-orange-btn {
    border-radius: 30rpx;
    border: 1rpx solid #e57126;
    padding: 8rpx 20rpx 8rpx 20rpx;
    font-size: 30rpx;
    color: #e57126;
  }

  .loadingImg {
    width: 120rpx;
    height: 120rpx;
  }

  .loadingpadding {
    padding: 85rpx 250rpx;
  }

  .tickling {
    text-align: center;
    font-weight: 700;
    font-size: 30rpx;
  }

  .text-content {
    font-size: 30rpx;
    margin-top: 20rpx;
    margin-left: 20rpx;
  }

  /deep/.u-textarea__field {
    background-color: #f7f7f7;
    border: 1rpx solid #d9d9d9;
    padding: 10rpx;
  }

  /deep/.u-textarea--disabled {
    background-color: #fff !important;
  }

  .button-sp-area {
    display: flex;
    justify-content: space-around;
    padding: 0 60rpx;
  }

  .cancel-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 30rpx;
    width: 160rpx;
    height: 60rpx;
    font-size: 24rpx;
    margin-right: 60rpx;
    background-color: #e7e7e7;
  }

  .mini-btn {
    color: #fff;
    width: 160rpx;
    height: 60rpx;
    font-size: 24rpx;
    line-height: 60rpx;
    text-align: center;
    border-radius: 30rpx;
    background-color: #2e896f;
  }

  /deep/.wu-calendar--fixed {
    z-index: 99999 !important;
  }

  // 弹框的样式设置
  /deep/.card {
    width: 600rpx;
    background-color: #fff;
    border-radius: 14rpx;
    padding: 30rpx;
    overflow-y: scroll;

    .reality {
      display: flex;
      align-items: center;
      margin-top: 10rpx;
      line-height: 40rpx;
      font-size: 25rpx;
    }

    .begin {
      border: 1px solid #999;
      border-radius: 10rpx;
      height: 40rpx;
      line-height: 40rpx;
      width: 220rpx;
      text-align: center;
      overflow: visible;
      transform: scale(0.995);
      /* 解决ios上圆角失效 */
    }

    .finish {
      border: 1px solid #999;
      overflow: visible;
      border-radius: 10rpx;
      height: 40rpx;
      line-height: 40rpx;
      width: 220rpx;
      text-align: center;
      transform: scale(0.995);
      /* 解决ios上圆角失效 */
    }
  }

  .course-btn-bg {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 120rpx;
    background-color: #ffffff;
  }

  .course-btn {
    color: #ffffff;
    font-size: 30rpx;
    width: 586rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 40rpx;
  }

  .delete-bg {
    padding: 45rpx 88rpx 50rpx 88rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .delete-newbg {
    padding: 45rpx 50rpx 50rpx 50rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .common-sure-btn {
    width: 250rpx;
    height: 80rpx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 40rpx;
    font-size: 30rpx;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(0.995);
    /* 解决ios上圆角失效 */
  }

  .common-cancel-btn {
    width: 250rpx;
    height: 80rpx;
    border-radius: 40rpx;
    border: 1rpx solid #2e896f;
    font-size: 30rpx;
    color: #2e896f;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(0.995);
    /* 解决ios上圆角失效 */
  }

  .date-title {
    font-size: 32rpx;
    color: #333333;
    line-height: 44rpx;
    font-weight: 600;
  }

  .empty-content {
    padding: 150rpx 0;
    margin: 0 30rpx 0 30rpx;
    z-index: 9;
    border-radius: 14rpx;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    /* 垂直布局，子视图按列排列 */
    justify-content: center;
    /* 水平居中 */
    align-items: center;
    /* 垂直居中 */
  }

  .empty-img {
    width: 114rpx;
    height: 107rpx;
  }

  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 185rpx;
    z-index: -1;
  }

  .input-border {
    height: 80rpx;
    border-radius: 14rpx;
    border: 1rpx solid rgba(0, 0, 0, 0.2);
  }

  .input-border-s {
    width: 170rpx;
    height: 70rpx;
    font-size: 28rpx;
    border-radius: 14rpx;
    border: 1rpx solid rgba(0, 0, 0, 0.2);
  }

  .null-text-color {
    color: #666666;
  }

  .no-null-text-color {
    color: #000;
  }

  .search-dialog-student-input {
    width: 460rpx;
    height: 40rpx;
    font-size: 28rpx;
  }

  .text-hide {
    text-overflow: ellipsis;
    /* 当文本溢出时显示省略号 */
    white-space: nowrap;
    /* 禁止文本换行 */
    overflow: hidden;
    /* 隐藏溢出部分文本 */
  }

  .text-enter {
    word-wrap: break-word;
  }

  /deep/ .wu-calendar__content {
    margin: 20rpx;
    border-radius: 14rpx;
  }

  .chose-student-picker {
    width: 100%;
    height: 510rpx;
    overflow: hidden;
    transition: height 0.3s;
    padding-top: 40rpx;
  }

  .chose-student-item {
    text-align: center;
    height: 80rpx;
    line-height: 80rpx;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 30upx;
  }

  .chose-text-style {
    font-weight: 600;
    font-size: 32rpx;
  }

  .dialogBG {
    width: 680rpx;
    margin: 0 30rpx 30rpx 30rpx;
    background-color: #fff;
    border-radius: 14rpx;
  }

  .dialog-all {
    width: 670rpx;
    position: relative;
  }

  .dialog-all image {
    width: 100%;
    height: 100%;
  }

  .dialog-center {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 50upx 55upx;
    box-sizing: border-box;
  }

  .dialog-small-close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .dialog-title {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    font-weight: 600;
    justify-content: center;
  }

  .dialog-item {
    box-sizing: border-box;
    font-size: 32upx;
    line-height: 45upx;
    text-align: center;
    margin-top: 40rpx;
  }

  .selected {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #2e896f;
    border: 1px solid #2e896f;
    overflow: visible;
    border-radius: 35rpx;
  }

  .not-selected {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #000;
    border: 1px solid #c8c8c8;
    overflow: visible;
    border-radius: 35rpx;
    transform: scale(0.995);
    /* 解决ios上圆角失效 */
  }

  .refund_img {
    position: absolute;
    top: 105rpx;
    right: 65rpx;
    width: 165rpx;
    height: 130rpx;
  }

  .callBtn {
    // min-width: 150rpx;
    // height: 50rpx;
    padding: 12rpx 22rpx 12rpx 22rpx;
    // padding: 8rpx;
    // line-height: 60rpx;
    text-align: center;
    color: #fff;
    font-size: 24rpx;
    background: #2e896f;
    border-radius: 30rpx;
    border: 1rpx solid #2e896f;
  }

  .orderinfo {
    position: relative;
    width: 670rpx;
    height: 400rpx;
    background: #ffffff;
    border-radius: 24rpx;
    font-size: 30rpx;
    border-radius: 24rpx;
    // padding-top: 160rpx;
    font-size: 28rpx;

    .color {
      color: #333333;
      font-size: 30rpx;
    }

    .phone {
      color: #000000;
      font-size: 50rpx;
      font-weight: 600;
    }

    .btns {
      display: flex;
      justify-content: center;
      margin-top: 20rpx;
    }

    .btn {
      width: 250rpx;
      height: 80rpx;
      border-radius: 40rpx;
      line-height: 80rpx;
      text-align: center;
      font-size: 30rpx;
    }

    .btn1 {
      color: #64a795;
      background-color: #ffffff;
      border: 1px solid #64a795;
      overflow: visible;
      // margin-right: 40rpx;
      margin-left: 40rpx;
    }

    .btn2 {
      background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
      color: #fff;
    }

    .close-icon {
      position: absolute;
      top: 20rpx;
      right: 20rpx;
      width: 42rpx;
      height: 42rpx;
      line-height: 42rpx;
      text-align: center;
      border-radius: 50%;
      background: #b1b1b1;
    }
  }

  .dialog-list {
    height: 450rpx;
    overflow-y: auto;
  }

  .tag {
    background-color: #9d5bf0;
    padding: 0 10rpx;
    height: 36rpx;
    border-radius: 4rpx;
    border: 1rpx solid #9d5bf0;
    font-size: 26rpx;
    color: #ffffff;
    text-align: center;
    margin-right: 10rpx;
  }
  ::v-deep .u-read-more__toggle.u-read-more__toggle {
    justify-content: start;
  }
</style>

<template>
  <view>
    <web-view :src="urlAddress" @message="handleMessage"></web-view>
  </view>
</template>
<script>
  export default {
    name: 'StudyDetails',
    data() {
      return {
        urlAddress: ''
      };
    },
    computed: {
      webViewSrc() {
        let baseDomain = 'math-class';
        return `https://${baseDomain}.dxznjy.com/#/pages/feedback/formal/index`;
      }
    },
    onLoad(e) {
      var token = uni.getStorageSync('token');
      this.urlAddress = this.webViewSrc + `?sendData=${e.sendData || '{}'}&token=${token}`;
      console.log(this.urlAddress);
    },
    onReady() {},
    onShow() {},

    methods: {
      handleMessage(e) {
        console.log('🚀 ~ handleMessage ~ e:', e);
        const { action, data } = e.detail.data;
        // 根据action执行相应的操作，处理data
        if (action === 'yourAction') {
          // 执行你的操作
        }
      }
    }
  };
</script>

<style lang="scss" scoped></style>

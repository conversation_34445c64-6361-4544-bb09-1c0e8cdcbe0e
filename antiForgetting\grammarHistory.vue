<template>
  <view class="contaiter">
    <u-navbar title="往期复习" @leftClick="leftClick"></u-navbar>
    <view class="paddingb">
      <view class="potop">
        <view class="top">
          <text class="ml-60">日期</text>
          <text>复习讲义数</text>
          <text>查看详情</text>
        </view>
      </view>
      <u-empty v-if="historyGralist.length == 0" mode="history" icon="http://cdn.uviewui.com/uview/empty/history.png"></u-empty>
      <view class="history-item" style="padding-bottom: 100rpx" v-else>
        <view class="list" v-for="(item, index) in historyGralist" :key="index">
          <view class="flex-a-c listBorder">
            <view>{{ item.reviewDate }}</view>
            <view class="ml-40">{{ item.reviewNum }}</view>
            <!-- <span class="boxl-50 lh-30 b-g f-18 t-c radius-6 c-81" style="color: #81E2AF;">{{item.reviewNum}}</span> -->
          </view>
          <text>{{ item.studyData }}</text>
          <image src="https://document.dxznjy.com/applet/newimages/xiangqing.png" mode="" @click="goUrl(item.id)" class="list-img"></image>
        </view>
      </view>
      <view class="zanwu" :status="loadingType">没有更多数据了~</view>
      <view class="bottomFixed" v-if="sendType">
        <view class="sendButton" @click="sendMsg">发送</view>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    components: {},
    data() {
      return {
        loadingType: 'more', //加载更多状态
        historyGralist: [],
        pageindex: 1,
        pageSize: 20,
        studentCode: '',
        sendType: false,
        queryData: {},
        isShare: false,
        antiForgetting: null
      };
    },

    methods: {
      leftClick() {
        console.log('leftClick', this.antiForgetting);
        if (this.antiForgetting == 1) {
          uni.navigateBack({
            delta: 1
          });
        } else {
          uni.redirectTo({
            url: '/antiForgetting/index?buttonclickName=' + '&buttonClick=' + encodeURIComponent(this.studentCode) + '&merchantCode=&deliverMerchant=' + '&type=grammar'
          });
        }
      },

      sendMsg() {
        let that = this;
        // that.sendData.isSend = 0;
        // 发文字
        wx.qy.getContext({
          success: () => {
            //    var entry = data.entry, //返回进入小程序的入口类型
            // var shareTicket = data.shareTicket;
            wx.qy.sendChatMessage({
              msgtype: 'miniprogram', //消息类型，必填
              enterChat: false, //为true时表示发送完成之后顺便进入会话，仅移动端3.1.10及以上版本支持该字段
              text: {
                content: '' //文本内容
              },
              image: {
                mediaid: '' //图片的素材id
              },
              video: {
                mediaid: '' //视频的素材id
              },
              file: {
                mediaid: '' //文件的素材id
              },
              news: {
                link: '', //H5消息页面url 必填
                title: '', //H5消息标题
                desc: '', //H5消息摘要
                imgUrl: '' //H5消息封面图片URL
              },
              miniprogram: {
                appid: 'wxce2bd1113a024ff6', //小程序的appid
                title: '往期复习记录', //小程序消息的title
                imgUrl: 'https://document.dxznjy.com/manage/1716255999000', //小程序消息的封面图
                page: `/antiForgetting/history.html?studentCode=${that.studentCode}&isSend=0` //小程序消息打开后的路径，注意要以.html作为后缀，否则在微信端打开会提示找不到页面
              },
              success: (res) => {
                that.$http
                  .get('/deliver/teacher/notify/details', {
                    id: that.queryData.id,
                    isSend: 1,
                    boundInfo: that.queryData.boundInfo,
                    message: that.queryData.msgType
                  })
                  .then(() => {
                    uni.showToast({
                      title: '发送成功'
                    });
                    setTimeout(() => {
                      uni.redirectTo({
                        url: '/qyWechat/notice'
                      });
                    }, 800);
                  })
                  .catch((err) => {
                    uni.showToast({
                      title: '发送失败',
                      icon: 'none'
                    });
                  });
              },
              fail: (err) => {}
            });
          },
          fail: (err) => {}
        });
      },
      async loadMyMember(type = 'add', loading) {
        if (type === 'add') {
          if (this.loadingType === 'nomore') {
            return;
          }
          this.loadingType = 'loading';
        } else {
          this.loadingType = 'more';
        }
        await this.$http
          .get('/dyf/wap/applet/antiForgettingHistory', {
            studentCode: this.studentCode,
            pageNum: this.pageindex,
            pageSize: this.pageSize
          })
          .then((result) => {
            if (result.data.data.data.length == 0) {
              this.loadingType = 'nodata';
            } else {
              if (result.data.data.data.length) {
                this.historyGralist = [...this.historyGralist, ...result.data.data.data];
              }
              this.loadingType = this.pageindex >= result.data.data.totalPage ? 'nomore' : 'more';
            }
            if (type === 'refresh') {
              if (loading == 1) {
                uni.hideLoading();
              } else {
                uni.stopPullDownRefresh();
                this.loadingType = 'nomore';
              }
            }
          });
      },
      goUrl(id) {
        let url = '';
        if (this.sendType) {
          url = `/antiForgetting/hisGraReviewReport?handoutId=${id}&isShare=true&isSend=0`;
        } else {
          url = `/antiForgetting/hisGraReviewReport?handoutId=${id}`;
        }
        uni.navigateTo({
          url: url
        });
      }
    },
    onLoad(options) {
      let that = this;
      that.antiForgetting = options.antiForgetting;
      that.sendType = options.isSend == 1 ? true : false;
      that.isShare = options.isShare == 'true' ? true : false;
      if (options.item) {
        that.queryData = JSON.parse(decodeURIComponent(options.item));
      }
      this.studentCode = options.studentCode;

      this.loadMyMember();
    },
    onPageScroll(e) {
      if (e.scrollTop >= 0) {
        this.headerPosition = 'fixed';
      } else {
        this.headerPosition = 'absolute';
      }
    },
    onPullDownRefresh() {
      this.pageindex = 1;
      this.loadMyMember('refresh');
    },
    //加载更多
    onReachBottom() {
      this.pageindex++;
      this.loadMyMember();
    }
  };
</script>

<style>
  .contaiter {
    /* 		margin-top: 10rpx; */
    /* '		height: 1311rpx;' */
  }
  page {
    background: #f3f8fc !important;
  }
  .paddingb {
    padding: 100rpx 0;
    width: 690rpx;
    /* margin-top: 200rpx; */
    margin-top: 0;
    /* position: relative; */
    margin: 0 auto;
    background: #ffffff;
    border-radius: 14rpx;
    min-height: 600rpx;
  }
  .potop {
    /* position: fixed; */
    top: 0;
    margin-top: 100rpx;
    width: 690rpx;
    height: 100rpx;
    background-color: #fff;
    z-index: 999;
  }
  .top {
    height: 100rpx;
    display: flex;
    align-items: center;
    justify-content: space-around;
    font-size: 30rpx;
    border-bottom: 1px solid #e5e5e5;
    /* padding-left: 20rpx; */
    /* 不放大不缩小固定100rpx */
  }
  .top text:nth-child(2) {
    margin-left: 20rpx;
  }
  .list {
    display: flex;
    align-items: center;
    justify-content: space-around;
    font-size: 30rpx;
    border-bottom: 1px solid #e5e5e5;
    height: 100rpx;
    color: #666;
    /* padding-left: 30rpx; */
  }

  .list-img {
    width: 29rpx;
    height: 34rpx;
    margin-right: 60rpx;
  }

  .list text {
    display: block;
    width: 32%;
    text-align: center;
  }
  .list text:first-child {
    width: 35.5%;
  }

  .zanwu {
    margin: 0 auto;
    margin-top: 20rpx;
    margin-bottom: 20rpx;
    text-align: center;
    font-size: 28rpx;
    color: #b3b7ba;
  }

  /* .timeFn {
  width: 80rpx;
  margin-left: 40rpx;
}

.wordsFn {
  margin-left: 39rpx;
}

.list-timer {
  width: 182rpx !important;
} */

  .detail_btn {
    width: 140rpx;
    height: 58rpx;
    background-color: #007aff;
    text-align: center;
    line-height: 58rpx;
    color: #fff;
    border-radius: 50%;
  }
  .bottomFixed {
    z-index: 999;
    position: fixed;
    bottom: 10rpx;
    width: 690rpx;
    /* background-color: #fff; */
    /* border-radius: 20rpx 20rpx 0 0; */
  }
  .sendButton {
    width: 586rpx;
    height: 90rpx;
    line-height: 90rpx;
    color: #ffffff;
    font-size: 30rpx;
    text-align: center;
    background: #2e896f;
    border-radius: 45rpx;
    margin: 10rpx auto 0;
  }
  .listBorder {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-flow: row wrap;
  }
</style>

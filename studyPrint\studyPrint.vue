<!-- 学习打印页面 -->
<template>
  <view class="study_print_page plr-30 pb-30">
    <view class="bg_box"></view>
    <view class="list_box mt-30">
      <view class="query_row f-30 c-00">
        <view class="row_flex">
          <view>学员：</view>
          <view class="pick f-28 c-99">
            <view v-if="studentCode != ''">{{ studentName }}（{{ studentCode }})</view>
            <picker v-else class="btnchange" @change="bindPickerStudent" :value="studentIndex" :range="studentArray" range-key="realName" name="grade">
              <view :class="studentStyle ? 'c-00' : ''">{{ studentArray[studentIndex].realName }}</view>
              <uni-icons type="forward" size="16" color="#C8C8C8" class="pick_right"></uni-icons>
            </picker>
          </view>
        </view>
        <view class="row_flex mt-30">
          <view>类型：</view>
          <view class="pick f-28 c-99">
            <picker class="btnchange" @change="bindPickerType" :value="typeIndex" :range="typeArray" range-key="label" name="grade">
              <view :class="courseType ? 'c-00' : ''">{{ typeArray[typeIndex].label }}</view>
              <uni-icons type="forward" size="16" color="#C8C8C8" class="pick_right"></uni-icons>
            </picker>
          </view>
        </view>
      </view>
    </view>

    <view class="list_data bg-ff radius-15 plr-30 f-30 c-33" v-if="printList.length > 0">
      <view v-if="typeArray[typeIndex].value !== 'NewGrammar'">
        <view v-for="item in printList">
          <view
            v-if="typeArray[typeIndex].value != 'Word'"
            class="flex-box b-b flex printList_item"
            @click="goContent(item.studentName, item.type, item.printCode, item.submitTime)"
          >
            <view class="flex-c border-b" style="height: 100%">
              <view class="f-28 c-22" style="width: 160upx; height: 100%">{{ item.studentName }}</view>
              <view class="f-28 c-22" style="width: 148upx; height: 100%">{{ typeArray[typeIndex].label }}</view>
              <view class="f-28 c-22" style="width: 300upx; height: 100%">{{ item.submitTime }}</view>
              <image :src="imgHost + 'dxSelect/image/right.png'" class="arrow" mode="widthFix"></image>
            </view>
          </view>
          <view v-else class="flex-box b-b flex printList_item">
            <view class="flex-c border-b">
              <view class="f-30" style="width: 148upx; color: #2e896f" @click="goContent(item.studentName, item.type, item.printCode, item.submitTime, 1)">全天</view>
              <view class="f-30" style="width: 148upx; color: #2e896f" @click="goContent(item.studentName, item.type, item.printCode, item.submitTime, 0)">本次</view>
              <view class="f-30" style="width: 320upx; color: #333333">{{ item.submitTime }}</view>
            </view>
          </view>
        </view>
      </view>
      <view v-else>
        <view class="flex-c pt-30 pb-30" v-for="(item, index) in printList" :key="index" @click="goContentGra(item)">
          <view class="f-28 c-22" style="width: 180upx">{{ item.studentName }}</view>
          <view class="f-28 c-22" style="width: 148upx">{{ item.printType }}</view>
          <view class="f-28 c-22" style="width: 300upx">{{ item.createTime }}</view>
          <image :src="imgHost + 'dxSelect/image/right.png'" class="arrow" mode="widthFix"></image>
        </view>
      </view>
    </view>

    <view v-else class="t-c flex-col mt-30 bg-ff radius-15" :style="{ height: useHeight + 'rpx' }">
      <image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20 img_s" mode="widthFix"></image>
      <view style="color: #bdbdbd">暂无数据</view>
    </view>
  </view>
</template>
<script>
  export default {
    data() {
      return {
        studentCode: '',
        studentName: '',
        type: '',
        studentIndex: 0,
        typeIndex: 0,
        studentArray: [
          {
            studentCode: '',
            realName: '请选择学员'
          }
        ],
        typeArray: [
          {
            value: '',
            label: '请选择类型'
          },
          {
            value: 'Word',
            label: '单词'
          },
          {
            value: 'Reading',
            label: '阅读理解'
          },
          {
            value: 'Grammar',
            label: '语法'
          },
          {
            value: 'NewGrammar',
            label: '新版语法'
          },
          {
            value: 'SuperRead',
            label: '新阅读理解'
          }
        ],
        printList: [], // 课件列表
        pageNum: 1, // 当前页
        pageSize: 20, // 页数
        loadingType: 'nodata', //加载更多状态
        studentStyle: false,
        courseType: false,
        useHeight: 0, //除头部之外高度
        imgHost: 'https://document.dxznjy.com/'
      };
    },
    onLoad(option) {
      console.log('onLoad');
      var that = this;
      if (option != null && option.studentCode != undefined) {
        that.studentCode = option.studentCode;
        that.studentName = option.studentName;
      } else {
        this.getStudentCode();
      }
    },

    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 650;
        }
      });
    },

    onShow() {},

    //加载更多
    onReachBottom() {
      if (this.studentCode != '' && this.type != '') {
        this.pageNum++;
        // this.getDataList()
        if (this.type == 'NewGrammar') {
          this.getNewGrammar();
        } else {
          this.getDataList();
        }
      }
    },

    methods: {
      // 选择学员code
      bindPickerStudent(e) {
        this.studentStyle = true;
        this.studentIndex = e.target.value;
        if (this.studentCode == this.studentArray[this.studentIndex].studentCode) {
          return;
        }
        this.studentCode = this.studentArray[this.studentIndex].studentCode;
        this.printList = [];
      },
      // 选择类型
      bindPickerType(e) {
        this.courseType = true;
        this.typeIndex = e.target.value;
        if (this.type == this.typeArray[this.typeIndex].value) {
          return;
        }
        this.printList = [];
        this.type = this.typeArray[this.typeIndex].value;
      },

      // 查询studentCode
      async getStudentCode() {
        let result = await uni.$http.get('/znyy/review/query/my/student');
        if (result != undefined && result != '') {
          if (result.data.data != null) {
            this.studentArray = [
              {
                studentCode: '',
                realName: '选择学员'
              }
            ];
            if (result.data.data.length > 0) {
              if (result.data.data.length == 1) {
                this.studentArray = [];
                this.index = 0;
                this.studentStyle = true;
                this.studentArray = this.studentArray.concat(result.data.data);
                this.studentCode = this.studentArray[this.index].studentCode;
              } else {
                var that = this;
                that.studentArray = that.studentArray.concat(result.data.data);
              }
            }
          }
        }
      },
      getNewGrammar() {
        this.$http
          .get('/dyf/wap/applet/teacherPrintPage', {
            studentCode: this.studentCode,
            pageNum: this.pageNum,
            pageSize: this.pageSize
          })
          .then((result) => {
            // this.printList = result.data.data.data
            if (result.data.data.data.length > 0) {
              const newList = result.data.data.data;
              this.printList.push(...newList);
            } else {
              this.loadingType = 'nomore';
            }
          });
      },
      // 获取学习内容
      async getDataList(change) {
        let that = this;
        // that.printList = [];
        uni.showLoading();
        await uni.$http
          .get('/znyy/student/print', {
            studentCode: that.studentCode,
            type: that.type,
            pageNum: that.pageNum,
            pageSize: that.pageSize
          })
          .then((result) => {
            uni.hideLoading();
            if (result) {
              if (result.data.data.data.length == 0) {
                that.loadingType = 'nodata';
                uni.showToast({
                  icon: 'none',
                  title: '暂无更多内容了！',
                  duration: 2000
                });
              } else {
                if (result.data.data.data.length > 0) {
                  that.printList = that.printList.concat(result.data.data.data);
                }
                that.loadingType = that.pageNum >= Number(result.data.data.totalPage) ? 'nomore' : 'more';
              }
            }
          });
      },

      // 跳转打印详情
      goContent(studentName, type, printCode, submitTime, isAll) {
        uni.navigateTo({
          url: `/studyPrint/studyContentPrint?studentName=${studentName}&studentCode=${this.studentCode}&type=${
            type === 'Handouts' ? 'Grammar' : type
          }&printCode=${printCode}&submitTime=${submitTime}&allDay=${isAll}`
        });
      },
      // 跳转打印详情 语法
      goContentGra(item) {
        console.log(item.id, '打印语法');
        uni.navigateTo({
          url: `/studyPrint/studyContentPrint?id=${item.id}&studentName=${item.studentName}&studentCode=${item.studentCode}&type=${this.type}`
        });
      }
    },

    watch: {
      studentCode(val) {
        if (val != '' && this.type != '') {
          this.pageNum = 1;
          this.pageSize = 20;
          // this.getDataList(true)
          if (this.type == 'NewGrammar') {
            this.getNewGrammar();
          } else {
            this.getDataList(true);
          }
        }
      },
      type(val) {
        if (val != '' && this.studentCode != '') {
          this.pageNum = 1;
          this.pageSize = 20;
          // this.getDataList(true)
          if (this.type == 'NewGrammar') {
            this.getNewGrammar();
          } else {
            this.getDataList(true);
          }
        }
      }
    }
  };
</script>

<style>
  .study_print_page {
    background-color: #f3f8fc;
  }

  .bg_box {
    width: 690upx;
    height: 332upx;
    background-image: url(http://document.dxznjy.com/dxSelect/studyPrint_img.png);
    background-size: cover;
    background-repeat: no-repeat;
  }

  .list_box {
    border-radius: 14upx;
    background: #ffffff;
    box-shadow: 0 -10rpx 25rpx 15rpx rgba(0, 0, 0, 0.12);
    padding: 30upx;
    font-size: 26upx;
    color: rgba(102, 102, 102, 1);
    position: relative;
  }

  .query_row {
    display: flex;
    flex-wrap: wrap;
  }

  .row_flex {
    display: flex;
    justify-content: start;
    width: 100%;
    align-items: center;
  }

  .pick {
    flex: 1;
    /* width: 500upx; */
    height: 70upx;
    line-height: 70upx;
    border: 1px solid #c8c8c8;
    border-radius: 35upx;
    text-align: left;
    padding: 0 30upx;
    box-sizing: border-box;
    position: relative;
    overflow: visible;
    transform: scale(0.995); /* 解决ios上圆角失效 */
  }

  .list_data {
    overflow-y: scroll;
    margin-top: 30rpx;
  }

  .arrow {
    width: 14rpx;
    margin-left: 5upx;
  }
  .pick_right {
    position: absolute;
    right: 20upx;
    top: 0;
  }
  .printList_item {
    height: 110upx;
    line-height: 110upx;
    border-bottom: 1upx solid #efefef;
  }
  .printList_item:last-child {
    border: none;
  }
  .border-b {
    border-bottom: 1px solid #efefef;
  }
  .img_s {
    width: 160rpx;
  }
</style>

<!-- 当前可用时间 -->
<template>
	<view>
		<view class="adjustment" v-if="datelist.length>0" :style="{height:availableHeight+'rpx'}">
			<view class="adjust" v-for="(item,index) in datelist" :key="item.id">
				<view class="newsItem">
					<view class="displayFlex displayFlexCenter ">
						日期：{{weekList[item.usableWeek-1].value}}
					</view>
					<view>
						<image class="width30" @click="editTime(index)" src="/static/images/edit_icon.png" mode="">
						</image>
						<image class="width30 marginLeft16" @click="deleteTime(index)"
							src="/static/images/delete_icon.png" mode=""></image>
					</view>
				</view>
				<view class="marginTop20 font30" style="line-height: 45upx;">
					开始时间-结束时间：{{item.startTime}}-{{item.endTime}}
				</view>
			</view>
		</view>


		<view v-else :style="{height:nodataHeight+'rpx'}" class="userCardTwo textCenter">
			<image :src="imgHost+'alading/correcting/no_data.png'" class="mb-20 img_s" mode="widthFix"></image>
			<view style="color: #BDBDBD;">暂无数据</view>
		</view>


		<view class="commonTwoBtn background_green_two" style="margin: 40upx 50upx;" @click="addTime">
			添加可用时间
		</view>



		<!-- 新增/编辑弹窗 -->
		<uni-popup ref="addEditTimePopup" type="center" catchtouchmove="true">
			<view class="dialogBG">
				<view class="reviewCard_box positionRelative">
					<view class="cartoom_image">
						<image src="https://document.dxznjy.com/applet/zhujiao/dialog_bg.png" mode="widthFix"></image>
					</view>
					<view class="review_close" @click="closeDialog">
						<uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
					</view>
					<view class="reviewCard">
						<view class="reviewTitle bold">{{addEditType==1?'添加':'编辑'}}可用时间</view>
						<view class="dialogContent">
							<view class="contentListItem">
								<view class="picker_title">请选择日期</view>
								<view class="picker_box">
									<image src="/static/images/rili_icon.png" class="picker_left_icon" mode=""></image>
									<picker class="pickerInner" :value="nowChooseTime.usableWeek-1" :range="weekList"
										range-key="value" @change="bindPickerDate($event,index)">
										<view class="uni-input">
											{{nowChooseTime.usableWeek === undefined ? '请选择' : weekList[nowChooseTime.usableWeek-1].value}}
										</view>
									</picker>
									<image src="/static/images/right.png" class="picker_left_icon" mode=""></image>
								</view>

							</view>

							<view class="contentListItem">
								<view class="picker_title">请选择开始时间与结束时间</view>
								<view class="picker_box">
									<image src="/static/images/time2_icon.png" class="picker_left_icon" mode=""></image>
									<picker class="pickerInner" mode="time" fields="day"
										:value="nowChooseTime.startTime" start="00:00" end="23:59"
										@change="bindPickerStart($event,nowChooseTime,index)">
										<view class="uni-input">
											{{nowChooseTime.startTime === undefined ? '请选择' : nowChooseTime.startTime}}
										</view>
									</picker>
									<image src="/static/images/right.png" class="picker_left_icon" mode=""></image>
								</view>
								<view class="picker_box">
									<image src="/static/images/time2_icon.png" class="picker_left_icon" mode=""></image>
									<picker class="pickerInner" mode="time" fields="day" :value="nowChooseTime.endTime"
										start="00:00" end="23:59" @change="bindPickerEnd($event,nowChooseTime,index)"
										@cancel="remove">
										<view class="uni-input">
											{{nowChooseTime.endTime === undefined ? '请选择' : nowChooseTime.endTime }}
										</view>
									</picker>
									<image src="/static/images/right.png" class="picker_left_icon" mode=""></image>
								</view>
							</view>
						</view>
						<view class="review_btn" @click="confirm">确定</view>
					</view>
				</view>
			</view>
		</uni-popup>
		
		
		<!-- 删除时间弹窗 -->
		<uni-popup ref="deleteTimePopup" type="center" catchtouchmove="true">
			<view class="dialogBG">
				<view class="reviewCard_box positionRelative">
					<view class="cartoom_image">
						<image src="https://document.dxznjy.com/applet/zhujiao/dialog_bg.png" mode="widthFix"></image>
					</view>
					<view class="review_close" @click="closeDialog">
						<uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
					</view>
					<view class="reviewCard t-c">
						<view class="mt-50 f-34">是否删除当前可用时间</view>
						<view class="mt-70 flex-a-c flex-x-a">
							<view class="close_btn" @click="closeDialog">取消</view>
							<view class="confirm_btn " @click="confirm">确定</view>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
		
        <uni-popup ref="loadingPopup" type="center" :is-mask-click="false">
        	<view class="bg-ff radius-15 t-c">
        		<view class="loadingpadding">
        			<image src="https://document.dxznjy.com/Assistant/loading.png" class="loadingImg"></image>
        			<view class="mt-30">加载中...</view>
        			<view>请耐心等候</view>
        		</view>
        	</view>
        </uni-popup>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				datelist: [], // 调课通知列表		
				weekList: [],

				ifBottomRefresh: false,
				availableHeight: 0, // 暂无数据高度
				nodataHeight: 0, // 暂无数据高度
				imgHost: "",
				addEditType: 1, //1新增  2编辑
				chooseIndex: -1, //编辑下标
				nowChooseTime: {},

			};
		},
		onReady() {
			this.weekList = this.$config.WeekList;
			this.imgHost = this.$config.ImgsomeHost;
			uni.getSystemInfo({
				success: (res) => {
					// 可使用窗口高度，将px转换rpx
					let h = (res.windowHeight * (750 / res.windowWidth));
					this.availableHeight = h - 180;
					this.nodataHeight = h - 180;
				}
			})
		},

		onLoad() {
			this.getDateList();
		},
		
		onShow() {
			// this.$refs.deleteTimePopup.open();
		},

		// 触底的事件
		onReachBottom() {
			this.getDateList();
			if (this.status == 'no-more') return
		},

		methods: {
			// 加载事件
			loadingmore(e) {
				uni.showToast({
					icon: 'none',
					title: "当前状态：" + e.detail.status
				})
			},
			
			// 获取可用时间列表
			async getDateList() {
				// this.isloading = true
				let res = await uni.$http.get('/deliver/app/teacher/getAvailableWeek')
				// this.status = res.data.data.data.status;
				if (res.data.success) {
					this.datelist = res.data.data;
				}
			},

			bindPickerDate(e) {
				console.log(this.weekList)
				console.log(this.nowChooseTime)
				this.nowChooseTime.usableWeek = this.weekList[e.detail.value].key;
				this.$forceUpdate();
			},

			// picker的change事件
			bindPickerStart(e, index) {
				console.log(e, index, '打印日期e');
				this.nowChooseTime.startTime = e.detail.value
				this.$forceUpdate()
			},

			bindPickerEnd(e, index) {
				this.nowChooseTime.endTime = e.detail.value
				this.$forceUpdate()
			},


			// 点击编辑
			editTime(index) {
				this.addEditType = 2;
				this.nowChooseTime = this.datelist[index];
				this.chooseIndex = index;
				this.$refs.addEditTimePopup.open();
			},

			// 点击删除
			deleteTime(index) {
				this.$refs.loadingPopup.open();
				this.datelist.splice(index, 1);
				this.getamendTime();
			},

			// 点击添加可用时间
			addTime() {
				this.addEditType = 1;
				this.nowChooseTime = {};
				this.$refs.addEditTimePopup.open();
			},

			// 新增和编辑确定
			confirm() {
				if (this.addEditType == 1) {
					if (JSON.stringify(this.nowChooseTime) == "{}") {
						this.$util.alter("可用时间不能为空");
						return;
					}
					if (!this.nowChooseTime.usableWeek) {
						this.$util.alter("请选择日期");
						return;
					}
					if (!this.nowChooseTime.startTime) {
						this.$util.alter("请选择开始时间");
						return;
					}
					if (!this.nowChooseTime.endTime) {
						this.$util.alter("请选择结束时间");
						return;
					}
					this.datelist.push(this.nowChooseTime);
				} else {
					this.datelist[this.chooseIndex] = this.nowChooseTime;
				}
				this.chooseIndex = -1;
				this.nowChooseTime = {};
				this.$refs.loadingPopup.open();
				this.getamendTime();
			},

			// 更改
			async getamendTime() {
				let nowData = '"' + JSON.stringify(this.datelist).replace(/"/g, "\\\"") + '"';
				let res = await uni.$http.post(`/deliver/app/teacher/updateAvailableWeek`, nowData)
				this.$refs.loadingPopup.close();
				if (res.data.success) {
					this.closeDialog();
					this.getDateList();
				} else {
					// 新增失败将数组中添加的最后一条数据删除
					if (this.addEditType == 1) {
						this.datelist.pop();
					}
				}
			},

			// 关闭弹窗
			closeDialog() {
				this.$refs.addEditTimePopup.close();
				// this.$refs.deleteTimePopup.close();
			},
			

		}
	}
</script>

<style lang="scss" scoped>
	.nav-title {
		position: fixed;
		height: 180rpx;
		width: 100%;
		background-color: #fff;
	}

	.status_bar {
		text-align: center;
		font-size: 40rpx;
		color: #000;
		line-height: 220rpx;

	}

	.icon_img {
		position: absolute;
		left: 10rpx;
	}

	.adjustment {
		padding-top: 20rpx;
		vertical-align: middle;
		overflow-y: auto;
	}

	/deep/.adjust {
		width: 90%;
		background-color: #fff;
		font-size: 32rpx;
		border-radius: 20rpx;
		margin: auto;
		padding: 30upx;
		margin-bottom: 20rpx;
	}

	.newsItem {
		display: flex;
		width: 100%;
		justify-content: space-between;
		font-size: 32upx;
		align-items: center;
	}

	/deep/.round {
		width: 20rpx;
		height: 20rpx;
		margin-right: 20rpx;
		background-color: #2E896F;
		border-radius: 50%;
	}



	.img_s {
		width: 160rpx;
		margin-top: 300upx;
		margin-bottom: 16upx;
	}






	.dialogBG {
		width: 100%;
		height: 100%;
	}

	/* 21天结束复习弹窗样式 */
	.reviewCard_box {
		width: 670rpx;
		/* height: 560rpx; */
		position: relative;
	}



	.reviewCard {
		width: 100%;
		height: 100%;
		background: #FFFFFF;
		border-radius: 24upx;
		padding: 30upx;
		box-sizing: border-box;
		position: relative;
	}


	.cartoom_image {
		width: 280rpx;
		position: absolute;
		top: -244rpx;
		left: 145rpx;
		z-index: -1;
	}

	.cartoom_image image {
		width: 100%;
		height: 100%;
	}


	.review_close {
		position: absolute;
		top: 20rpx;
		right: 20rpx;
		z-index: 1;
	}

	.reviewTitle {
		margin-top: 20rpx;
		width: 100%;
		text-align: center;
		font-size: 34upx;
		display: flex;
		justify-content: center;
	}

	.dialogContent {
		padding: 0 30upx;
		box-sizing: border-box;
		margin: 50upx 0;
		font-size: 32upx;
		line-height: 45upx;
		text-align: center;
	}



	.review_btn {
		width: 240upx;
		height: 80upx;
		background: linear-gradient(180deg, #88CFBA 0%, #1D755C 100%);
		border-radius: 45upx;
		font-size: 30upx;
		color: #FFFFFF;
		line-height: 80upx;
		margin: 20rpx auto 8rpx auto;
		justify-content: center;
		text-align: center;
	}


	.reviewConfirm {
		background: #116254;
		color: #FFFFFF;
	}

	.reviewCancle {
		color: #116254;
		background: #FFFFFF;
	}

	.contentListItem {
		margin-top: 30upx;
	}

	.picker_title {
		color: #303133;
		font-size: 32upx;
		width: 100%;
		text-align: left;
	}

	.picker_box {
		display: flex;
		width: 100%;
		height: 70upx;
		line-height: 70upx;
		border: 1rpx solid #C8C8C8;
		border-radius: 35upx;
		padding: 0 20upx;
		box-sizing: border-box;
		margin-top: 30upx;
		transform: scale(0.995); 
	}

	.picker_left_icon {
		width: 30upx;
		height: 30upx;
		float: left;
		margin-top: 18upx;
	}

	.pickerInner {
		width: 410upx;
		margin: 0 20upx;
		text-align: left;
		display: inline-block;
		color: #999999;
		font-size: 26rpx;
		float: left;
	}
	
	.confirm_btn{
		width: 240upx;
		height: 80upx;
		margin: 0 !important;
		background: linear-gradient(180deg, #88CFBA 0%, #1D755C 100%);
		border-radius: 45upx;
		font-size: 30upx;
		color: #FFFFFF;
		line-height: 80upx;
		margin: 20rpx auto 8rpx auto;
		justify-content: center;
		text-align: center;
	}
	
	.close_btn {
		width: 250upx;
		height: 80upx;
		color: #2E896F;
		font-size: 30upx;
		line-height: 80upx;
		text-align: center;
		border-radius: 45upx;
		box-sizing: border-box;
		border: 1px solid #2E896F;
		overflow: visible;
		transform: scale(0.995); /* 解决ios上圆角失效 */
	}
	
	
	.loadingImg{
		width: 120rpx;
		height: 120rpx;
	}
	
	
	.loadingpadding{
		padding: 85rpx 250rpx;
	}
</style>
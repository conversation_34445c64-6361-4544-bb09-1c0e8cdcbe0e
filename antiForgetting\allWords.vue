<template>
  <view>
    <!-- <view class="positioning" @click="goback">
			<uni-icons type="left" size="24" color="#000"></uni-icons>
		</view> -->
    <view class="center-banner">
      <view :class="topShow ? 'center_boxs' : 'center-box'" :style="{ height: useHeight + 'rpx' }">
        <image src="https://document.dxznjy.com/applet/newimages/fuxi@2x_new.png" mode="widthFix" class="niuimg" style="width: 434rpx"></image>
        <!-- 单词 -->
        <view class="f-32 green" :class="topShow ? 'words-tops' : 'words-top'" v-if="selectedType == 'word'">
          <view class="remind mb-20 c-00">今日共有{{ wordList.totalWordCount || 0 }}词需复习</view>
          <view v-if="wordList.todayWordCount != 0" class="mb-20" @click="getWordPreview(1)">当日{{ wordList.todayWordCount || 0 }}词</view>
          <view v-if="wordList.beforeWordCount != 0" @click="getWordPreview(2)">往期{{ wordList.beforeWordCount || 0 }}词</view>
        </view>
        <!-- 语法 -->
        <view class="f-32 green" :class="topShow ? 'words-tops' : 'words-top'" v-if="selectedType == 'grammar'">
          <view class="remind mb-20 c-00">今日共有{{ wordList.totalNum || 0 }}个语法知识点需复习</view>
          <view v-if="wordList.todayNum != 0" class="mb-20" @click="getGraPreview(1)">当日{{ wordList.todayNum || 0 }}个语法知识点</view>
          <view v-if="wordList.formerNum != 0" @click="getGraPreview(2)">往期{{ wordList.formerNum || 0 }}个语法知识点</view>
        </view>
        <view class="plr-50" :class="topShow ? 'start-tops' : 'start_top'">
          <button class="start" @click="startReview(0)">开始复习</button>
          <view class="temporarily mt-30" @click="closeReview()">暂不复习</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        wordList: {}, // 单词
        studentCode: '', //学员Code
        topShow: false, //判断向上高度
        useHeight: 0,
        selectedType: 'word'
      };
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          if (h < 1500) {
            this.topShow = true;
          }

          this.useHeight = h - 190;
        }
      });
    },
    onLoad(options) {
      console.log(options,'mmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm')
      this.studentCode = options.studentCode;
      this.deliverMerchant = options.deliverMerchant;
      this.merchantCode = options.merchantCode;
      this.selectedType = options.selectedType||this.selectedType;
    },
    onShow() {
      this.getWords();
    },
    methods: {
      // 获得单词数量
      async getWords() {
        uni.showLoading({
          title: '加载中'
        });
        // this.studentCode = '6231217888';
        if (this.selectedType == 'grammar') {
          let res = await uni.$http.get('/dyf/wap/applet/todayReviewStatistics', {
            studentCode: this.studentCode
          });
          uni.hideLoading();
          if (res.data.success) {
            this.wordList = res.data.data;
          }
        } else {
          let res = await uni.$http.get('/znyy/review/query/fun/word/count', {
            studentCode: this.studentCode
          });
          uni.hideLoading();
          if (res.data.success) {
            this.wordList = res.data.data;
          }
        }
      },

      // 单词页
      getWordPreview(type) {
        console.log('111111111');
        uni.navigateTo({
          url: '/antiForgetting/wordPreview?studentCode=' + this.studentCode + '&type=' + type
        });
      },
      getGraPreview(type) {
        uni.navigateTo({
          url: '/antiForgetting/grammarPreview?studentCode=' + this.studentCode + '&type=' + type
        });
      },
      closeReview() {
        uni.navigateBack();
      },
      // goback(){
      // 	uni.navigateBack()
      // },
      // 开始复习
      startReview(type) {
        console.log(type, '11111');
        console.log(this.selectedType, '22222');
        if (type == 0) {
          if (this.wordCount == 0) {
            this.$util.alter('暂无可复习单词！');
          } else {
            if (this.selectedType == 'word' || this.selectedType === undefined) {
              console.log('开始复习');
              uni.navigateTo({
                url: '/antiForgetting/list?studentCode=' + this.studentCode + '&merchantCode=' + this.merchantCode + '&deliverMerchant=' + this.deliverMerchant
              });
            }
            if (this.selectedType == 'grammar') {
              uni.navigateTo({
                url: '/antiForgetting/grammarList?studentCode=' + this.studentCode
              });
            }
          }
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .positioning {
    position: fixed;
    top: 100rpx;
    left: 30rpx;
  }
  .center-banner {
    margin: 0 auto;
    margin-bottom: 50rpx;
    width: 690rpx;
    height: 1408rpx;
    background: #ffffff;
    border-radius: 20rpx;
    padding-top: 180rpx;
  }
  .center-box {
    width: 100%;
    text-align: center;
    margin-top: 200rpx;
    margin-bottom: 50rpx;
  }

  .center_boxs {
    width: 100%;
    text-align: center;
    // margin-top: 260rpx;
  }

  .words-tops {
    margin-top: 60rpx;
  }

  .words-top {
    margin-top: 120rpx;
  }

  .start {
    border-radius: 50rpx;
    color: #fff;
    font-size: 30rpx;
    height: 90rpx;
    background: #2e896f;
    line-height: 90rpx;
  }

  .start_top {
    margin-top: 208rpx;
  }

  .start-tops {
    margin-top: 148rpx;
  }

  .temporarily {
    font-size: 30rpx;
    color: #999;
    text-align: center;
  }

  .green {
    color: #2e896f;
  }
  .remind {
    font-weight: bold;
    font-size: 32rpx;
  }
  .niuimg {
    // margin-top: 180rpx;
  }
</style>

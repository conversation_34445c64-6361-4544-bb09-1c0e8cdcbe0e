<template>
  <view class="bg-ff read-content" :style="{ height: useHeight + 'rpx' }">
    <view class="plr-15 bg-ff radius-20 ptb-30" >
      <view class="midTxt">
        <image src="https://document.dxznjy.com/course/7bd71437aeed4c62a3fa36ae89190573.png" mode="scaleToFill" style="width: 750rpx; height: 634rpx"></image>
      </view>
    </view>
    <view class="botBtn  pr-35 ptb-30"  :style="{marginTop: marginTop + 'rpx'}">
        <view class="btn_b b_r f-34" @click="goToday">今日复习</view>
        <view class="btn_b b_l f-34" @click="goPast">往期复习</view>
      </view>
  </view>
</template>

<script>
  export default {
    data() {
      return { useHeight: 0, studentCode: '', merchantCode: '',marginTop:0 };
    },
    onLoad(options) {
      this.studentCode = options.studentCode;
      this.merchantCode = options.merchantCode;
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          this.useHeight = res.windowHeight * (750 / res.windowWidth);
          if(this.useHeight-1230>0){
            this.marginTop=this.useHeight-1230
          }
        }
      });
    },
    methods: {
      async goToday() {
        let { data } = await uni.$http.get('/znyy/superReadReview/getReviewNumber?studentCode=' + this.studentCode + '&merchantCode=' + this.merchantCode);
        if (data.success) {
          let a = data.data.todayReviewNum - 0 + (data.data.lastReviewNum - 0);
          if (a > 0) {
            uni.navigateTo({
              url: `/ReadForget/todayForget?studentCode=${this.studentCode}&merchantCode=${this.merchantCode}`
            });
          } else {
            uni.showToast({
              icon: 'none',
              title: '今日无复习的课程'
            });
          }
        }
      },
      goPast() {
        uni.navigateTo({
          url: `/ReadForget/pastForget?studentCode=${this.studentCode}&merchantCode=${this.merchantCode}`
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .midTxt {
    margin-top: 50%;
  }
  .read-content{
   background: #fff;
   overflow-y: scroll;
  }
  .botBtn {
    display: flex;
    flex-direction: row-reverse;
  }
  .btn_b {
    width: 328rpx;
    height: 92rpx;
    border-radius: 60rpx;
    line-height: 92rpx;
    text-align: center;
  }
  .b_l {
    background-color: #fff;
    color: #4e9f87;
    border: 1px solid #7baea0;
  }
  .b_r {
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
    color: #ffffff;
    margin-left: 32rpx;
  }
</style>

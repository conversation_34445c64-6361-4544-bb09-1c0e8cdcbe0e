<template>
  <view style="background-color: #2e896f">
    <view>
      <view v-if="all">
        <image :src="path" mode="widthFix" @longpress="longPress('painter')" style="width: 100%"></image>
        <image :src="path1" mode="widthFix" @longpress="longPress('painter1')" style="width: 100%"></image>
      </view>
      <image v-else :src="path" mode="widthFix" @longpress="longPress('painter')" style="width: 100%"></image>

      <!--评估-->
      <Painter
        v-if="trialclass && all"
        isCanvasToTempFilePath
        ref="painter"
        @success="path = $event"
        custom-style="position: fixed; left: 200%"
        css="width: 750rpx; padding-bottom: 40rpx"
      >
        <PainterImage 
          src="https://document.dxznjy.com/app/images/zhujiaoduan/feedback_bg.png" 
          css="z-index:-1; position: absolute; top:-90rpx; width: 100%;"
        >
        </PainterImage>
        <PainterView css="position: absolute;top:70rpx;left:297rpx;z-index:2;width:100%;color:#FFFFFF;font-size:34rpx;">
          <PainterText text="学习反馈" />
        </PainterView>
        <PainterView 
          css="z-index:1; position:relative; margin-top:160rpx; padding:32rpx; box-sizing:border-box; background:#fff; border:30rpx solid #2e896f;"
        >
          <PainterView>
            <PainterView css="width: 100%; margin-bottom: 24rpx; flex-direction: row; display: flex;">
              <PainterText :text="'课程类型：'" css="text-align: right; font-size: 28rpx; margin-right: 16rpx; line-height: 40rpx; color: rgba(51,51,51,1);" />
              <PainterText :text="backlist.curriculumName" css="text-align: left; font-size: 28rpx; line-height: 40rpx; color: rgba(51,51,51,1);"></PainterText>
            </PainterView>
            <PainterView css="width: 100%; margin-bottom: 24rpx; flex-direction: row; display: flex;">
              <PainterText :text="'日期：'" css="text-align: right; font-size: 28rpx; margin-right: 70rpx; line-height: 40rpx; color: rgba(51,51,51,1);" />
              <PainterText :text="backlist.dateTime" css="text-align: left; font-size: 28rpx; line-height: 40rpx; color: rgba(51,51,51,1);"></PainterText>
            </PainterView>
            <PainterView css="width: 100%; margin-bottom: 24rpx; flex-direction: row; display: flex;">
              <PainterText :text="'姓名：'" css="text-align: right; font-size: 28rpx; margin-right: 70rpx; line-height: 40rpx; color: rgba(51,51,51,1);" />
              <PainterText :text="backlist.studentName" css="text-align: left; font-size: 28rpx; line-height: 40rpx; color: rgba(51,51,51,1);"></PainterText>
            </PainterView>
            <PainterView css="width: 100%; margin-bottom: 24rpx; flex-direction: row; display: flex;">
              <PainterText :text="'年级：'" css="text-align: right; font-size: 28rpx; margin-right: 70rpx; line-height: 40rpx; color: rgba(51,51,51,1);" />
              <PainterText :text="backlist.gradeName" css="text-align: left; font-size: 28rpx; line-height: 40rpx; color: rgba(51,51,51,1);"></PainterText>
            </PainterView>
            <PainterView css="width: 100%; margin-bottom: 48rpx; flex-direction: row; display: flex;">
              <PainterText :text="'学员编号：'" css="text-align: right; font-size: 28rpx; margin-right: 16rpx; line-height: 40rpx; color: rgba(51,51,51,1);" />
              <PainterText :text="backlist.studentCode" css="text-align: left; font-size: 28rpx; line-height: 40rpx; color: rgba(51,51,51,1);"></PainterText>
            </PainterView>
          
              <PainterText :text="'评估课报告及反馈'" css="margin-bottom: 2rpx;text-align: center;font-size: 30rpx;display: block; color: #2F896F; font-weight: 600; " />
              <PainterView css="text-align: center; margin-bottom: 22rpx;">
                <PainterText :text="'得分：'" css="vertical-align: bottom;font-size: 28rpx; color: #333333;margin-right: 16rpx;" />
                <PainterText
                  :text="backlist.extendProperty.assessmentFeedbackDto.sum"
                  css="font-weight: 600;vertical-align: bottom;font-size: 40rpx; color: rgba(54,149,122,1.000000);"
                ></PainterText>
                <PainterText :text="'分'" css="font-size: 28rpx; vertical-align: bottom;"></PainterText>
              </PainterView>
              <PainterView css="text-align: center; margin-bottom: 32rpx;">
                <PainterText :text="'正确题数：'" css="color: rgba(51,51,51,1); font-size: 28rpx; margin-right: 16rpx; " />
                <PainterText :text="backlist.extendProperty.assessmentFeedbackDto.correct" css="color: rgba(51,51,51,1); font-size: 28rpx; margin-right: 38rpx;"></PainterText>
                <PainterText :text="'错误题数：'" css="color: rgba(51,51,51,1); font-size: 28rpx; margin-right: 16rpx; " />
                <PainterText :text="backlist.extendProperty.assessmentFeedbackDto.error" css="color: rgba(51,51,51,1); font-size: 28rpx; "></PainterText>
              </PainterView>
              <!--雷达图-->
              <PainterView css="margin-bottom: 24rpx; width: 100%; height: 500rpx; background: linear-gradient(90deg, #FFFFFF 0%, #EEFEEE 100%, #FFFFFF 0%);">
                <PainterImage :src="backlist.reviewRadaImg" css="object-fit: contain; width: 100%;"></PainterImage>
              </PainterView>
              <PainterText text="评估反馈：" css="font-weight: 600; color: #333333; font-size: 28rpx; display: block; margin-bottom: 24rpx; line-height: 40rpx" />
              <PainterView css="margin-bottom: 24rpx; color: #555555; padding: 30rpx; box-sizing: border-box; background: #F9FAFB; width: 100%; min-height:100rpx;">
                <PainterText :text="backlist.extendProperty.assessmentFeedbackDto.comment" css="font-size: 24rpx; display: block; line-height: 42rpx;" />
              </PainterView>
              <PainterText text="未来规划：" css="font-weight: 600; color: #333333; font-size: 28rpx; display: block; margin-bottom: 24rpx; line-height: 40rpx" />
              <PainterView css="margin-bottom: 48rpx; color: #555555; padding: 30rpx; box-sizing: border-box; background: #F9FAFB; width: 100%; min-height:100rpx;">
                <PainterText text="根据科学的评估分析，您家孩子的专注力一般，学习效率有待提升。通过注意力课程的专项训练，会让孩子的学习能力更上一层楼，学习的效率更高，达到输入即输出的目的，课堂听课听到就会，并灵活运用。专注力的提高可以帮助学生更好的专注于学习，更容易被认可，激发学生学习的内驱力。" css="font-size: 24rpx; display: block; line-height: 42rpx;" />
              </PainterView>
            </PainterView>
        </PainterView>
      </Painter>

      <!--试课-->
      <Painter
        v-if="trialclass && all"
        isCanvasToTempFilePath
        ref="painter1"
        @success="path1 = $event"
        custom-style="position: fixed; left: 200%"
        css="width: 750rpx; padding-bottom: 40rpx"
      >
        <PainterView css="position: relative; margin-top: 20rpx; padding: 32rpx; box-sizing: border-box; background: #fff; border:30rpx solid #2e896f;">
          <PainterView>
            <!--试课/体验课-->
            <PainterView v-if="backlist.experienceRadaImg">
              <PainterText :text="'试课报告及反馈：'" css="margin-bottom: 32rpx;text-align: center;font-size: 30rpx;display: block; color: #2F896F; font-weight: 600; " />
              
              <PainterView css="margin-bottom: 24rpx; width: 85%;">
                <PainterText text="上课时间：" css="margin-right: 16rpx; font-size: 28rpx;"/>
                <PainterText :text="backlist.extendProperty.experienceFeedbackDto.time" css="font-size: 28rpx;"/>
              </PainterView>
              <PainterView css="margin-bottom: 24rpx; width: 85%;">
                <PainterText text="上课用时：" css="margin-right: 16rpx; font-size: 28rpx;"/>
                <PainterText :text="backlist.extendProperty.experienceFeedbackDto.costTime" css="font-size: 28rpx;"/>
              </PainterView>
              
              <PainterView css="margin-bottom: 24rpx; width: 90%;">
                <PainterText :text="'突出项：'" css="font-size: 28rpx; display: block; margin-bottom: 16rpx; "/>
                <PainterText v-for="(item, index) in backlist.extendProperty.experienceFeedbackDto.highLights" :text="item" css="margin-right: 40rpx; line-height: 40rpx; font-weight: 600; font-size: 28rpx; color: #2F896F;" />
              </PainterView>

              <PainterView css="margin-bottom: 24rpx; width: 90%;">
                <PainterText :text="'推荐提升项：'" css="font-size: 28rpx; display: block; margin-bottom: 16rpx; "/>
                <PainterText v-for="(item, index) in backlist.extendProperty.experienceFeedbackDto.shortComing" :text="item" css="margin-right: 40rpx; line-height: 40rpx; font-weight: 600; font-size: 28rpx; color: #EC7A52;" />
              </PainterView>

              <PainterView css="margin-bottom: 40rpx; ">
                <PainterText :text="'正确题数：'" css="font-size: 28rpx; margin-right: 16rpx;" />
                <PainterText :text="backlist.extendProperty.experienceFeedbackDto.correctNums" css="font-size: 28rpx; margin-right: 38rpx;" />
                <PainterText :text="'错误题数：'" css="font-size: 28rpx; margin-right: 16rpx;" />
                <PainterText :text="backlist.extendProperty.experienceFeedbackDto.errorNums" css="font-size: 28rpx;" />
              </PainterView>
              <!--雷达图-->
              <PainterView css="margin-bottom: 24rpx; width: 100%; height: 500rpx; background: linear-gradient(90deg, #FFFFFF 0%, #EEFEEE 100%, #FFFFFF 0%);">
                <PainterImage :src="backlist.experienceRadaImg" css="object-fit: contain;width: 100%;"></PainterImage>
              </PainterView>
              <PainterText text="学员学习状况反馈" css="font-weight: 600; color: #333333; font-size: 28rpx; display: block; margin-bottom: 24rpx; line-height: 40rpx" />
              <PainterView css="margin-bottom: 62rpx; color: #555555; padding: 30rpx; box-sizing: border-box; background: #F9FAFB; width: 100%; min-height:100rpx;">
                <PainterText :text="backlist.feedback" css="font-size: 24rpx; display: block; line-height: 42rpx;" />
              </PainterView>
            </PainterView>
          </PainterView>
        </PainterView>
      </Painter>

	    <!-- 体验课 -->
      <Painter
        v-if="trialclass && !all"
        isCanvasToTempFilePath
        ref="painter"
        @success="path = $event"
        custom-style="position: fixed; left: 200%"
        css="width: 750rpx; padding-bottom: 40rpx"
      >
        <PainterImage 
          src="https://document.dxznjy.com/app/images/zhujiaoduan/feedback_bg.png" 
          css="z-index:-1; position: absolute; top:-90rpx; width: 100%;"
        >
        </PainterImage>
        <PainterView css="position: absolute;top:70rpx;left:297rpx;z-index:2;width:100%;color:#FFFFFF;font-size:34rpx;">
          <PainterText text="学习反馈" />
        </PainterView>
        <PainterView 
          css="z-index:1; position:relative; margin-top:160rpx; padding:32rpx; box-sizing:border-box; background:#fff; border:30rpx solid #2e896f;"
        >
          <PainterView>
            <PainterView css="width: 100%; margin-bottom: 24rpx; flex-direction: row; display: flex;">
              <PainterText :text="'课程类型：'" css="text-align: right; font-size: 28rpx; margin-right: 16rpx; line-height: 40rpx; color: rgba(51,51,51,1);" />
              <PainterText :text="backlist.curriculumName" css="text-align: left; font-size: 28rpx; line-height: 40rpx; color: rgba(51,51,51,1);"></PainterText>
            </PainterView>
            <PainterView css="width: 100%; margin-bottom: 24rpx; flex-direction: row; display: flex;">
              <PainterText :text="'日期：'" css="text-align: right; font-size: 28rpx; margin-right: 70rpx; line-height: 40rpx; color: rgba(51,51,51,1);" />
              <PainterText :text="backlist.dateTime" css="text-align: left; font-size: 28rpx; line-height: 40rpx; color: rgba(51,51,51,1);"></PainterText>
            </PainterView>
            <PainterView css="width: 100%; margin-bottom: 24rpx; flex-direction: row; display: flex;">
              <PainterText :text="'姓名：'" css="text-align: right; font-size: 28rpx; margin-right: 70rpx; line-height: 40rpx; color: rgba(51,51,51,1);" />
              <PainterText :text="backlist.studentName" css="text-align: left; font-size: 28rpx; line-height: 40rpx; color: rgba(51,51,51,1);"></PainterText>
            </PainterView>
            <PainterView css="width: 100%; margin-bottom: 24rpx; flex-direction: row; display: flex;">
              <PainterText :text="'年级：'" css="text-align: right; font-size: 28rpx; margin-right: 70rpx; line-height: 40rpx; color: rgba(51,51,51,1);" />
              <PainterText :text="backlist.gradeName" css="text-align: left; font-size: 28rpx; line-height: 40rpx; color: rgba(51,51,51,1);"></PainterText>
            </PainterView>
            <PainterView css="width: 100%; margin-bottom: 48rpx; flex-direction: row; display: flex;">
              <PainterText :text="'学员编号：'" css="text-align: right; font-size: 28rpx; margin-right: 16rpx; line-height: 40rpx; color: rgba(51,51,51,1);" />
              <PainterText :text="backlist.studentCode" css="text-align: left; font-size: 28rpx; line-height: 40rpx; color: rgba(51,51,51,1);"></PainterText>
            </PainterView>

            <!--试课/体验课-->
            <PainterView >
              <PainterText :text="'试课报告及反馈：'" css="margin-bottom: 32rpx;text-align: center;font-size: 30rpx;display: block; color: #2F896F; font-weight: 600; " />

              <PainterView css="margin-bottom: 24rpx; width: 85%;">
                <PainterText text="上课时间：" css="margin-right: 16rpx; font-size: 28rpx;"/>
                <PainterText :text="backlist.extendProperty.experienceFeedbackDto.time" css="font-size: 28rpx;"/>
              </PainterView>
              <PainterView css="margin-bottom: 24rpx; width: 85%;">
                <PainterText text="上课用时：" css="margin-right: 16rpx; font-size: 28rpx;"/>
                <PainterText :text="backlist.extendProperty.experienceFeedbackDto.costTime" css="font-size: 28rpx;"/>
              </PainterView>
              
              <PainterView css="margin-bottom: 24rpx; width: 90%;">
                <PainterText :text="'突出项：'" css="font-size: 28rpx; display: block; margin-bottom: 16rpx; "/>
                <PainterText v-for="(item, index) in backlist.extendProperty.experienceFeedbackDto.highLights" :text="item" css="margin-right: 40rpx; line-height: 40rpx; font-weight: 600; font-size: 28rpx; color: #2F896F;" />
              </PainterView>

              <PainterView css="margin-bottom: 24rpx; width: 90%;">
                <PainterText :text="'推荐提升项：'" css="font-size: 28rpx; display: block; margin-bottom: 16rpx; "/>
                <PainterText v-for="(item, index) in backlist.extendProperty.experienceFeedbackDto.shortComing" :text="item" css="margin-right: 40rpx; line-height: 40rpx; font-weight: 600; font-size: 28rpx; color: #EC7A52;" />
              </PainterView>

              <PainterView css="margin-bottom: 40rpx; ">
                <PainterText :text="'正确题数：'" css="font-size: 28rpx; margin-right: 16rpx;" />
                <PainterText :text="backlist.extendProperty.experienceFeedbackDto.correctNums" css="font-size: 28rpx; margin-right: 38rpx;" />
                <PainterText :text="'错误题数：'" css="font-size: 28rpx; margin-right: 16rpx;" />
                <PainterText :text="backlist.extendProperty.experienceFeedbackDto.errorNums" css="font-size: 28rpx;" />
              </PainterView>
              <!--雷达图-->
              <PainterView css="margin-bottom: 24rpx; width: 100%; height: 500rpx; background: linear-gradient(90deg, #FFFFFF 0%, #EEFEEE 100%, #FFFFFF 0%);">
                <PainterImage :src="backlist.experienceRadaImg" css="object-fit: contain;width: 100%;"></PainterImage>
              </PainterView>
              <PainterText text="学员学习状况反馈" css="font-weight: 600; color: #333333; font-size: 28rpx; display: block; margin-bottom: 24rpx; line-height: 40rpx" />
              <PainterView css="margin-bottom: 62rpx; color: #555555; padding: 30rpx; box-sizing: border-box; background: #F9FAFB; width: 100%; min-height:100rpx;">
                <PainterText :text="backlist.feedback" css="font-size: 24rpx; display: block; line-height: 42rpx;" />
              </PainterView>
            </PainterView>
          </PainterView>
        </PainterView>
      </Painter>

      <!--正课-->
      <Painter
        v-if="!trialclass"
        isCanvasToTempFilePath
        ref="painter"
        @success="path = $event"
        custom-style="position: fixed; left: 200%"
        css="width: 750rpx; padding-bottom: 40rpx"
      >
        <PainterImage 
          src="https://document.dxznjy.com/app/images/zhujiaoduan/feedback_bg.png" 
          css="z-index:-1; position: absolute; top:-90rpx; width: 100%;"
        >
        </PainterImage>
        <PainterView css="position:absolute; top:70rpx; left:297rpx; z-index:2; width:100%; color:#FFFFFF; font-size:34rpx;">
          <PainterText text="学习反馈" />
        </PainterView>
        <PainterView 
          css="z-index:1; position:relative; margin-top:160rpx; padding:32rpx; box-sizing:border-box; background:#fff; border:30rpx solid #2e896f;"
        >
          <PainterView>
            <PainterView css="width: 100%; margin-bottom: 24rpx; flex-direction: row; display: flex;">
              <PainterText :text="'课程类型：'" css="text-align: right; font-size: 28rpx; margin-right: 16rpx; line-height: 40rpx; color: rgba(51,51,51,1);" />
              <PainterText :text="backlist.curriculumName" css="text-align: left; font-size: 28rpx; line-height: 40rpx; color: rgba(51,51,51,1);"></PainterText>
            </PainterView>
            <PainterView css="width: 100%; margin-bottom: 24rpx; flex-direction: row; display: flex;">
              <PainterText :text="'日期：'" css="text-align: right; font-size: 28rpx; margin-right: 70rpx; line-height: 40rpx; color: rgba(51,51,51,1);" />
              <PainterText :text="backlist.dateTime" css="text-align: left; font-size: 28rpx; line-height: 40rpx; color: rgba(51,51,51,1);"></PainterText>
            </PainterView>
            <PainterView css="width: 100%; margin-bottom: 24rpx; flex-direction: row; display: flex;">
              <PainterText :text="'姓名：'" css="text-align: right; font-size: 28rpx; margin-right: 70rpx; line-height: 40rpx; color: rgba(51,51,51,1);" />
              <PainterText :text="backlist.studentName" css="text-align: left; font-size: 28rpx; line-height: 40rpx; color: rgba(51,51,51,1);"></PainterText>
            </PainterView>
            <PainterView css="width: 100%; margin-bottom: 24rpx; flex-direction: row; display: flex;">
              <PainterText :text="'年级：'" css="text-align: right; font-size: 28rpx; margin-right: 70rpx; line-height: 40rpx; color: rgba(51,51,51,1);" />
              <PainterText :text="backlist.gradeName" css="text-align: left; font-size: 28rpx; line-height: 40rpx; color: rgba(51,51,51,1);"></PainterText>
            </PainterView>
            <PainterView css="width: 100%; margin-bottom: 24rpx; flex-direction: row; display: flex;">
              <PainterText :text="'学员编号：'" css="text-align: right; font-size: 28rpx; margin-right: 16rpx; line-height: 40rpx; color: rgba(51,51,51,1);" />
              <PainterText :text="backlist.studentCode" css="text-align: left; font-size: 28rpx; line-height: 40rpx; color: rgba(51,51,51,1);"></PainterText>
            </PainterView>
            <PainterView css="width: 100%; margin-bottom: 24rpx; flex-direction: row; display: flex;">
              <PainterText :text="'时间：'" css="text-align: right; font-size: 28rpx; margin-right: 70rpx; line-height: 40rpx; color: rgba(51,51,51,1);" />
              <PainterText :text="backlist.studyTime" css="text-align: left; font-size: 28rpx; line-height: 40rpx; color: rgba(51,51,51,1);"></PainterText>
            </PainterView>
            <PainterView css="width: 100%; margin-bottom: 24rpx; flex-direction: row; display: flex;">
              <PainterText :text="'上课用时：'" css="text-align: right; font-size: 28rpx; margin-right: 16rpx; line-height: 40rpx; color: rgba(51,51,51,1);" />
              <PainterText :text="backlist.extendProperty.costTime" css="text-align: left; font-size: 28rpx; line-height: 40rpx; color: rgba(51,51,51,1);"></PainterText>
            </PainterView>
            <PainterView css="width: 100%; margin-bottom: 24rpx; flex-direction: row; display: flex;">
              <PainterText :text="'已购鼎学能课时：'" css="text-align: right; font-size: 28rpx; margin-right: 16rpx; line-height: 40rpx; color: rgba(51,51,51,1);" />
              <PainterText
                :text="backlist.extendProperty.haveCourseTime + '小时'"
                css="text-align: left; font-size: 28rpx; line-height: 40rpx; color: rgba(51,51,51,1);"
              ></PainterText>
            </PainterView>
            <PainterView css="width: 100%; margin-bottom: 24rpx; flex-direction: row; display: flex;">
              <PainterText :text="'剩余鼎学能课时：'" css="text-align: right; font-size: 28rpx; margin-right: 16rpx; line-height: 40rpx; color: rgba(51,51,51,1);" />
              <PainterText
                :text="backlist.extendProperty.surplusCourseTime + '小时'"
                css="text-align: left; font-size: 28rpx; line-height: 40rpx; color: rgba(51,51,51,1);"
              ></PainterText>
            </PainterView>
            <PainterView css="margin-bottom: 24rpx;">
              <PainterText :text="'突出项：'" css="color: rgba(51,51,51,1); font-size: 28rpx; display: block; margin-bottom: 16rpx; " />
              <PainterText :text="'' + backlist.extendProperty.highLights" css="line-height: 40rpx; font-weight: 600; font-size: 28rpx; color: #2F896F;" />
            </PainterView>
            <PainterView css="margin-bottom: 24rpx;">
              <PainterText :text="'推荐提升项：'" css="color: rgba(51,51,51,1); display: block; line-height: 40rpx; font-size: 28rpx; margin-bottom: 16rpx; " />
              <PainterText :text="'' + backlist.extendProperty.shortComing" css="font-size: 28rpx; font-weight: 600;color: #EC7A52;" />
            </PainterView>
            <PainterView css="width: 100%; margin-bottom: 24rpx; flex-direction: row; display: flex;">
              <PainterText :text="'正确题数：'" css="text-align: right; font-size: 28rpx; margin-right: 16rpx; line-height: 40rpx; color: rgba(51,51,51,1);" />
              <PainterText :text="backlist.extendProperty.correctNums" css="text-align: left; font-size: 28rpx; line-height: 40rpx; color: rgba(51,51,51,1);"></PainterText>
            </PainterView>
            <PainterView css="width: 100%; margin-bottom: 48rpx; flex-direction: row; display: flex;">
              <PainterText :text="'错误题数：'" css="text-align: right; font-size: 28rpx; margin-right: 16rpx; line-height: 40rpx; color: rgba(51,51,51,1);" />
              <PainterText :text="backlist.extendProperty.errorNums" css="text-align: left; font-size: 28rpx; line-height: 40rpx; color: rgba(51,51,51,1);"></PainterText>
            </PainterView>
            <!--雷达图-->
            <PainterImage :src="backlist.formalRadaImg" css="object-fit: contain;width: 100%;"></PainterImage>
            <PainterText text="教练评语" css="font-weight: 600; color: #333333; font-size: 28rpx; display: block; margin-bottom: 24rpx; line-height: 40rpx" />
            <PainterView css="margin-bottom: 54rpx; color: #555555; padding: 30rpx; box-sizing: border-box; background: #F9FAFB; width: 100%; min-height:100rpx;">
              <PainterText :text="backlist.feedback" css="font-size: 24rpx; display: block; line-height: 42rpx;" />
            </PainterView>
          </PainterView>
        </PainterView>
      </Painter>

    </view>
  </view>
</template>

<script>
  import PainterText from './components/lime-painter/components/l-painter-text/l-painter-text.vue';
  import PainterImage from './components/lime-painter/components/l-painter-image/l-painter-image.vue';
  import PainterView from './components/lime-painter/components/l-painter-view/l-painter-view.vue';
  import Painter from './components/lime-painter/components/l-painter/l-painter.vue';
  export default {
    components: {
      PainterText,
      Painter,
      PainterImage,
      PainterView
    },
    data() {
      return {
        all: false, //是否评估课和体验课同时存在
        path: '', //生成海报
        path1: '', //体验加评估
        backlist: '', // 获取反馈详情
        //反馈详情列表
        trialclass: false, // 是否是试课反馈
        isStudy: false, // 学习还是复习

        feedback: '', // 弹出层文本框输入的内容
        Intention: '', // 学习意愿
        timelist: {
          actualStart: '',
          actualEnd: ''
        }
      };
    },
    onLoad(e) {
      console.log("[Shine] onLoad.");

      let sendData = JSON.parse(decodeURIComponent(e.sendData));
      if (sendData != null && sendData != undefined) {
        this.trialclass = sendData.trialclass;
        this.isStudy = sendData.isStudy;
        this.backlist = sendData.detailsData;
        if (this.backlist != null && this.backlist != undefined) {
          this.Intention = this.backlist.studyIntention;
          this.feedback = this.backlist.feedback;
          this.timelist.actualStart = this.backlist.actualStart;
          this.timelist.actualEnd = this.backlist.actualEnd;
        }
        if (this.backlist.experienceRadaImg && this.backlist.reviewRadaImg) {
          this.all = true;
        } else {
          this.all = false;
        }
      }

      console.log("[Shine] backlist:", this.backlist);
    },
    methods: {
      //数值转化为时分秒，入参为秒
      secondsToTimeFormat(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const remainingSeconds = seconds % 60;
        const formattedHours = String(hours).padStart(2, '0');
        const formattedMinutes = String(minutes).padStart(2, '0');
        const formattedSeconds = String(remainingSeconds).padStart(2, '0');
        return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
      },

      //长按
      longPress(ref) {
        let that = this;
        uni.showModal({
          content: '保存图片',
          success: (res) => {
			  console.log(9999);
            if (res.confirm) {
              that.$refs[ref].canvasToTempFilePathSync({
                fileType: 'jpg',
                pathType: 'url',
                quality: 1,
                success: (res) => {
					console.log(2222222);
                  // that.path = res.tempFilePath;
                  // let path = ;
                  that.saveLocal(res.tempFilePath)
                },
                fail: (err) => {
                  console.log(err);
                  uni.showModal({
                    title: '提示',
                    content: '生成海报失败,请重试',
                    showCancel: false
                  });
                }
              });
            } else {
              uni.showToast({
                title: '已取消！',
                icon: 'none'
              });
            }
          }
        });
      },

      //保存本地
      saveLocal(path) {
        // #ifdef APP-PLUS
        uni.saveImageToPhotosAlbum({
              filePath: path,
              success: (res) => {
                uni.showToast({
                  icon: 'none',
                  title: '保存成功'
                });
                console.log(res);
                //分享
                // wx.showShareImageMenu({
                //     path: res.path,
                //     success(msg) {
                //         console.log(msg);
                //     },
                //     fail(err) {
                //         console.log("11111");
                //         console.log(err);
                //     }
                // })
              },
              fail(err) {
                uni.showToast({
                  icon: 'none',
                  title: '保存失败'
                });
              }
            });
            // #endif
         // #ifdef MP-WEIXIN
        uni.authorize({
          scope: 'scope.writePhotosAlbum',
          success: () => {
            uni.saveImageToPhotosAlbum({
              filePath: path,
              success: (res) => {
                uni.showToast({
                  icon: 'none',
                  title: '保存成功'
                });
                console.log(res);
                //分享
                // wx.showShareImageMenu({
                //     path: res.path,
                //     success(msg) {
                //         console.log(msg);
                //     },
                //     fail(err) {
                //         console.log("11111");
                //         console.log(err);
                //     }
                // })
              },
              fail(err) {
                uni.showToast({
                  icon: 'none',
                  title: '保存失败'
                });
              }
            });
          },
          fail() {
            uni.showModal({
              title: '保存失败',
              content: '您没有授权，无法保存到相册',
              showCancel: false
            });
          }
        });
        // #endif
      },

      Back() {
        uni.navigateBack();
      }
    }
  };
</script>

<template>
  <view>
    <!-- <view class="header2">
      <uni-icons v-if="!isShare" type="arrowleft" class="backIcon" size="20" color="#000" @click="back()"></uni-icons>
    </view> -->
    <view>
      <view class="" style="overflow: hidden;overflow-y: scroll;" :style="'height: ' + bodyHeight + 'rpx;'">
        <view class="postion_relative">
          <image class="rate_bgImageG" src="https://document.dxznjy.com/applet/interesting/grammarBG.png" mode=""></image>
          <view class="review_rate">
            <view class="header">
              <text class="fuxitext">{{ showData.reportDate }} 复习报告</text>
            </view>
            <view class="review_taskG">——— 本场作业完成情况统计 ———</view>
            <view class="rate_numG">
              {{ showData.completionRate }}
              <text>%</text>
              <view class="review_rate_text">完成率</view>
            </view>
            <view class="student-name-code">
              <view>学生姓名：{{ showData.studentName }}</view>
              <view style="margin-top: 14rpx">学生编号：{{ urlStundentCode }}</view>
            </view>
            <view class="over_review_census" v-if="isType == 0">
              <view>
                <view>
                  <view class="flex-a-c flex-x-s">
                    <view class="box-10 radius-allh" style="background-color: #9b8469"></view>
                    <view style="color: #9b8469" class="f-24 m-10">需复习</view>
                  </view>
                  <view class="review_litleTitle review_color11">
                    <view style="color: #c97513" class="f-30">{{ showData.needReviewNum }}个 知识点</view>
                  </view>
                </view>
                <view>
                  <view class="flex-a-c flex-x-s">
                    <view class="box-10 radius-allh" style="background-color: #90c0b0"></view>
                    <view style="color: #90c0b0" class="f-24 m-10">已复习</view>
                  </view>
                  <view class="review_litleTitle review_color22">
                    <view style="color: #078970" class="f-30">{{ showData.reviewedNum }}个 知识点</view>
                  </view>
                </view>
                <view>
                  <view class="flex-a-c flex-x-s">
                    <view class="box-10 radius-allh" style="background-color: #949fbd"></view>
                    <view style="color: #949fbd" class="f-24 m-10">未复习</view>
                  </view>
                  <view class="review_litleTitle review_color33">
                    <view style="color: #365197" class="f-30">{{ showData.notReviewedNum }}个 知识点</view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view class="study_records bg-f3" :style="'min-height: ' + recordHeight + 'rpx;'">
          <view class="study_record_titles pl-40">今日完成知识点记录</view>

          <view class="listBorder">
            <span>复习语法</span>
            <span>阶段</span>
            <span>复习时间</span>
          </view>
          <view class="listData pb-20" v-for="(item, index) in showData.recordList" :key="index">
            <span style="width: 130rpx">{{ truncatedKnowledgeName(item.knowledgeName) }}</span>
            <span style="margin-left: -80rpx">{{ item.phase }}</span>
            <span style="width: 50rpx">{{ item.useTime }}s</span>
          </view>
        </view>

        <view class="reviewBottomGra" v-if="!isShare">
          <view class="reviewLeft_btn_1 reviewBtn1" @click="goUrl(`/antiForgetting/grammarHistory?studentCode=${showData.studentCode}&antiForgetting=2`)">往期复习</view>
          <!-- <button class="reviewRight_btn reviewBtn" hover-class="none" open-type="share">分享给家长</button> -->
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  var innerAudioContext;
  export default {
    data() {
      return {
        src: 'http://*************:5000/v/b88b6ff1fa28125e', //音频地址
        showData: {}, //复习字段
        echartsList: 0, //echarts数据
        wrongList: [], //错误列表
        reviewId: null, //复习id
        postImage: getApp().globalData.postHost, //图片地址
        recordHeight: 0, //记录高度
        bodyHeight: 0, //body高度
        urlStundentCode: '', //url学生编号
        recordInnerHeight: 250, //记录高度
        studyTime: 10, //复习时间
        isplay: false, //是否播放
        isShare: false, //是否分享
        isType: 0, //1刚复习  0复习报告
        handoutId: null //讲义id
      };
    },

    onShareAppMessage() {
      return {
        title: '21天抗遗忘打卡',
        imageUrl: '', //分享封面
        path: '/antiForgetting/hisGraReviewReport?handoutId=' + this.handoutId + '&isShare=true&isType=0'
      };
    },
    onReady() {
			uni.getSystemInfo({
				success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let nowScreenHeight = this.$util.pxTorpx();
          console.log(nowScreenHeight,'11111111111111111111111')
          // nowScreenHeight = res.windowHeight * (750 / res.windowWidth);
          // if(nowScreenHeight<1348){
          //   nowScreenHeight=1648
          // }
          this.bodyHeight = nowScreenHeight;
          this.recordHeight = nowScreenHeight - 940;
          this.recordInnerHeight = nowScreenHeight - 1000;
				}
			});
		},
    onLoad(options) {
      this.studyTime = options.studyTime;
      this.handoutId = options.handoutId;
      this.isShare = options.isShare == 'true' ? true : false;
      this.isType = options.isType == '1' ? 1 : 0;
    
      // uni.showLoading({
      // 	title: '加载中...'
      // });
      this.getReviewReport(this.handoutId);
      let that = this;
      innerAudioContext = uni.createInnerAudioContext();
      innerAudioContext.onPlay(() => {
        console.log('开始播放');
      });
      innerAudioContext.onStop(function () {
        console.log('播放结束');
        that.isplay = false;
      });
      innerAudioContext.onPause(function () {
        console.log('播放暂停');
        that.isplay = false;
      });
      innerAudioContext.onError((res) => {
        console.log(res.errMsg);
        console.log(res.errCode);
        that.isplay = false;
      });
    },
    methods: {
      async getReviewReport(id) {
        let result = await this.$http.get('/dyf/wap/applet/antiForgettingQuery?reportId=' + id);
        if (result) {
          if (result.data.data) {
            this.showData = result.data.data;
            if (this.showData.totalWordCount == 0) {
              this.isType = 0;
            }
            this.showData.totalWordCount = Number(this.showData.totalWordCount) + Number(this.showData.wordCount);
            this.urlStundentCode = result.data.data.studentCode;
            this.echartsList = result.data.data.rate;
          }
        }
        setTimeout(function () {
          uni.hideLoading();
        }, 200);
      },
      truncatedKnowledgeName(name) {
        if (name.length > 4) {
          return name.slice(0, 3) + '....';
        }
        return name;
      },
      //返回上一页
      back() {
        if (this.isShare) {
          uni.switchTab({
            url: '/pages/index/index'
          });
        } else {
          uni.navigateBack({
            delta: 1
          });
        }
      },
      goUrl(url) {
        uni.redirectTo({
          url: url
        });
      },

      //获取海报图片
      shareReviewReport() {
        // console.log("分享海报");
        let that = this;
        uni.request({
          url: that.postImage + 'api/link',
          data: {
            'zt#syh_rightRate': that.showData.rate,
            'aRig_zt#syh_alreadyReview': that.showData.reviewWordVM.length,
            'aRig_zt#syh_noReview': that.showData.wordCount - that.showData.reviewWordVM.length,
            'aRig_zt#syh_needReview': that.showData.wordCount,
            accessKey: 'ApfrIzxCoK1DwNZO',
            secretKey: 'EJCwlrnv6QZ0PCdvrWGi',
            posterId: 7
          },
          method: 'POST',
          header: {
            'Content-Type': 'application/json'
          },
          success: (res) => {
            // console.log(res.data);
            let urlString = res.data.url;
            that.src = that.postImage + 'v/' + urlString.split('/v/')[1];
            uni.navigateTo({
              url: '/interestModule/playbill?url=' + that.src
            });
          }
        });
      }
    }
  };
</script>

<style lang="less">
  page {
    height: 100%;
  }

  .header2 {
    margin-left: -95rpx;
    height: 172rpx;
    background-color: #fff;
  }
  .postion_relative{
    position: relative;
  }
  .student-name-code {
    width: 100%;
    text-align: left;
    position: absolute;
    font-family: PingFang-SC, PingFang-SC;
    color: #555555;
    bottom: 260rpx;
    font-size: 28rpx;
    right: -289rpx;
  }

  .review_litleTitle-1 {
    // width: 290rpx;
    // height: 160rpx;
    border-radius: 14rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
  }

  .review_litleTitle {
    width: 300rpx;
    height: 64rpx;
    line-height: 64rpx;
    border-radius: 8rpx;
  }

  .study_records {
    // width: 710rpx;
    padding-top: 190rpx;
    margin-top: 40rpx;
    box-sizing: border-box;
    margin: 0 auto;
  }

  .study_record_titles {
    font-size: 32rpx;
    font-family: AlibabaPuHuiTiM;
    color: #428a6f;
    font-weight: bold;
    margin: 20rpx 0;
  }

  .header {
    padding-top: 40rpx;
    margin-left: -280rpx;
    font-size: 40rpx;
    box-sizing: border-box;
    text-align: center;
    position: relative;
  }

  .backIcon {
    position: absolute;
    left: 30rpx;
    top: 90rpx;
  }

  .lake_page {
    margin-top: 120rpx;
    display: flex;
    flex-direction: column;
    align-items: center;

    image {
      width: 200rpx;
      height: 160rpx;
      margin-bottom: 30rpx;
    }
  }

  .lake_page-text {
    text-align: center;
    font-size: 30rpx;
    font-family: SourceHanSansSC-Regular, SourceHanSansSC;
    font-weight: 400;
    color: #666666;
    line-height: 45rpx;
  }

  .borderTop {
    border-bottom: 1px dashed #ddd;
  }

  .recordLists {
    width: 100%;
    padding: 30rpx 0;
  }

  .over_review_census {
    margin: 0 auto;
    display: flex;
    justify-content: flex-start;
    padding-top: 230rpx;
    padding-left: 285rpx;
  }

  .recordTransts {
    margin-top: 3rpx;
    margin-left: 35rpx;
    font-size: 28rpx;
    font-family: AlibabaPuHuiTiR;
    color: #999999;
  }

  .fuxitext {
    color: #ffffff;
    margin-left: 185rpx;
    // padding-top: 20rpx;
  }

  .review_taskG {
    width: 100%;
    text-align: center;
    position: absolute;
    top: 140rpx;
    font-family: 'syhtM';
    font-size: 28rpx;
  }

  .rate_numG {
    font-family: 'syhtM';
    font-size: 42rpx;
    color: #2ab3d8;
    position: absolute;
    z-index: 1;
    width: 100%;
    text-align: center;
    top: 72%;
    left: -152rpx;
  }

  .listBorder {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-flow: row wrap;
    font-size: 30rpx;
    height: 100rpx;
    background: #ebf4fb;
    margin: 0 30rpx;
    border-radius: 8rpx;
  }

  .listData {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-flow: row wrap;
    font-size: 30rpx;
    height: 100rpx;
    margin: 0 30rpx;
    border-radius: 8rpx;
    border-bottom: 2rpx solid #e7eaed;
  }

  .poButtons {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .grammarBg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 68%;
  }

  .rate_bgImageG {
    position: absolute;
    left: 0;
    height: 907rpx;
    width: 750rpx;
  }

  .reviewLeft_btn_1 {
    background: #ffffff;
    border: 2rpx solid #1d755c;
  }

  .reviewBtn1 {
    width: 100%;
    height: 80rpx;
    border-radius: 40rpx;
    display: inline-flex;
    justify-content: center;
    line-height: 80rpx;
    font-size: 32rpx;
    color: #1d755c;
    font-family: 'syhtM';
  }

  .review_color11 {
    background: #feefe4;
    border: 2rpx solid #e3af73;
    background: #fcf2e9;
    opacity: 0.5;
  }

  .review_color22 {
    background: #dafce2;
    border: 2rpx solid #80f0b8;
    background: rgba(230, 251, 236, 0.5);
    opacity: 0.5;
  }

  .review_color33 {
    background: #dbf4fa;
    border: 2rpx solid #bfc4f8;
    background: rgba(230, 232, 253, 0.5);
    opacity: 0.5;
  }

  .box-10 {
    width: 10rpx;
    height: 10rpx;
  }

  .radius-allh {
    border-radius: 50%;
  }
  .reviewBottomGra {
    width: 100%;
    height: 140rpx;
    position: fixed;
    display: flex;
    justify-content: space-between;
    margin-top: 20rpx;
    bottom: 0;
    z-index: 2;
    background: #ffffff;
    border-top-left-radius: 35rpx;
    border-top-right-radius: 35rpx;
    padding: 30rpx 55rpx;
    box-sizing: border-box;
  }
</style>

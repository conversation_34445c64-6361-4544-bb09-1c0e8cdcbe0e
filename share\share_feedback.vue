<template>
  <view style="min-height: 100vh; background-color: #2e896f">
    <view>
      <image :src="path" mode="widthFix" @longpress="longPress()" style="width: 100%"></image>
      <Painter isCanvasToTempFilePath ref="painter" @success="path = $event" custom-style="position: fixed; left: 200%" css="width: 750rpx; padding-bottom: 20rpx">
        <PainterImage src="https://document.dxznjy.com/app/images/zhujiaoduan/feedback_bg.png" css="position: absolute;top:-50rpx;object-fit: contain;width: 100%;"></PainterImage>
        <PainterView css="position: absolute;top:100rpx;z-index:2;width:100%;text-align:center;color:#FFFFFF;font-size:32rpx;">
          <PainterText v-if="isStudy && trialclass" text="鼎英语体验结束反馈" />
          <PainterText v-if="isStudy && !trialclass" text="学习反馈" />
          <PainterText v-if="!isStudy" text="复习反馈" />
        </PainterView>

        <PainterView css="position: relative;margin-top: 190rpx; padding: 40rpx 0; box-sizing: border-box; background: #fff;border:20rpx solid #2e896f;">
          <!-- 试课反馈 -->
          <PainterView v-if="isStudy && trialclass">
            <PainterView v-if="backlist.moduleType == 1">
              <PainterText :text="'日期：' + backlist.dateTime" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'姓名：' + backlist.studentName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'年级：' + backlist.gradeName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'学员编号：' + backlist.studentCode" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />

              <PainterText :text="'试学学时：' + backlist.studyHour + '小时'" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'词汇测试水平：' + backlist.vocabularyLevel" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'首测词汇量：' + backlist.expWords + '个'" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />

              <PainterText :text="'记忆率：' + backlist.wordsRate + '%'" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'体验词库：' + backlist.studyBooks" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'记忆情况：' + backlist.memoryTime + '分钟记住' + backlist.memoryNum + '个单词'" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterView css="margin-bottom: 20rpx;background: rgba(238, 238, 238, 1);width: 630rpx;height:1rpx"></PainterView>
              <PainterText :text="'体验后学习意愿：' + Intention" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'复习时间：' + getReviewTime()" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
            </PainterView>
            <PainterView v-else-if="backlist.moduleType == 3">
              <PainterText :text="'课程类型：' + backlist.curriculumName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'日期：' + backlist.dateTime" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'姓名：' + backlist.studentName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'年级：' + backlist.gradeName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'学员编号：' + backlist.studentCode" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'时间：' + backlist.studyTime" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'实际时间：'" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="backlist.actualStart + '--' + backlist.actualEnd" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'学习学时：' + backlist.studyHour + '小时'" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'所学课程类型：' + backlist.superReadCourseStatistics.courseName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'所学课程名称个数：' + mapArray(backlist.superReadCourseStatistics.courseList)" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'学习进度：' + backlist.superReadCourseStatistics.learningProgress + '%'" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText
                :text="'学习关卡正确率：' + rateMapArray(backlist.superReadCourseStatistics.checkpointList)"
                css="font-size: 28rpx;display: block; margin-bottom: 20rpx; "
              />
              <PainterView css="margin-bottom: 20rpx;background: rgba(238, 238, 238, 1);width: 630rpx;height:1rpx"></PainterView>
            </PainterView>
            <!-- // 听力反馈 -->
            <PainterView v-else-if="backlist.moduleType == 4">
              <PainterText :text="'日期：' + backlist.dateTime" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'姓名：' + backlist.studentName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'年级：' + backlist.gradeName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'学员编号：' + backlist.studentCode" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'实际时间：'" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="backlist.actualStart + '--' + backlist.actualEnd" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'学习学时：' + backlist.studyHour + '小时'" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'课程类型：' + backlist.listeningStatisticsDto.courseName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'课程名称：' + listenName(backlist.listeningStatisticsDto.courseList)" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'学习进度：' + listenProgress(backlist.listeningStatisticsDto.progressList)" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText
                :text="'听力名称(正确率)：' + listenArray(backlist.listeningStatisticsDto.listeningList)"
                css="font-size: 28rpx;display: block; margin-bottom: 20rpx; "
              />
              <PainterView css="margin-bottom: 20rpx;background: rgba(238, 238, 238, 1);width: 630rpx;height:1rpx"></PainterView>
            </PainterView>
            <PainterView v-else-if="backlist.curriculumName == '珠心算'">
              <PainterText :text="'日期：' + backlist.dateTime" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'姓名：' + backlist.studentName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'年级：' + backlist.gradeName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'学员编号：' + backlist.studentCode" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'实际时间：'" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="backlist.actualStart + '--' + backlist.actualEnd" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />

              <PainterView css="margin-bottom: 20rpx;background: rgba(238, 238, 238, 1);width: 630rpx;height:1rpx"></PainterView>
            </PainterView>
            <!-- 1对多 -->
            <PainterView v-else-if="backlist.curriculumName.includes('1V') || backlist.curriculumName.includes('1对')">
              <PainterText :text="'日期：' + backlist.dateTime" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'姓名：' + backlist.studentName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'年级：' + backlist.gradeName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'学员编号：' + backlist.studentCode" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'实际时间：'" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="backlist.actualStart + '--' + backlist.actualEnd" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />

              <PainterView css="margin-bottom: 20rpx;background: rgba(238, 238, 238, 1);width: 630rpx;height:1rpx"></PainterView>
            </PainterView>
            <PainterView v-else>
              <PainterText :text="'日期：' + backlist.dateTime" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'姓名：' + backlist.studentName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'年级：' + backlist.gradeName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'时间：' + backlist.studyTime" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'学习学时：' + backlist.studyHour + '小时'" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'所学语法：' + backlist.grammarName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'学习进度：' + backlist.learningProgress + '%'" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterView css="width: 630rpx;">
                <PainterText :text="'复习知识点：' + backlist.reviewKnowledgeNum + '个'" css="font-size: 28rpx;display: inline-block;width:50%;margin-bottom: 20rpx; " />
                <PainterText :text="'新学知识点：' + backlist.newKnowledgeNum + '个'" css="font-size: 28rpx;display: inline-block;width:50%;margin-bottom: 20rpx; " />
              </PainterView>

              <PainterText :text="'语法点：' + backlist.grammarNum + '个'" css="font-size: 28rpx;display: block;margin-bottom: 20rpx; " />
              <PainterText :text="'结业检测：' + backlist.graduationNum + '个'" css="font-size: 28rpx;display: block;margin-bottom: 20rpx; " />
              <PainterText :text="'复习知识点（正确率）：' + backlist.reviewKnowledgeText" css="font-size: 28rpx;display: block;margin-bottom: 20rpx; " />
              <PainterText :text="'新学知识点（正确率）：' + backlist.newKnowledgeText" css="font-size: 28rpx;display: block;margin-bottom: 20rpx; " />
              <PainterText :text="'语法点（正确率）：' + backlist.grammarText" css="font-size: 28rpx;display: block;margin-bottom: 20rpx; " />
              <PainterText :text="'结业检测（正确率）：' + backlist.graduationText" css="font-size: 28rpx;display: block;margin-bottom: 20rpx; " />
            </PainterView>

            <PainterText text="学员学习状况反馈：" css="font-size: 28rpx;display: block; margin-bottom: 20rpx ;line-height: 1.8em; " />

            <PainterView
              css=" margin-bottom: 20rpx;margin-left: 24rpx;margin-right: 24rpx;padding: 24rpx;box-sizing: border-box;background: rgba(153, 153, 153, 0.08);border-radius: 8rpx;min-height:280rpx"
            >
              <PainterText :text="feedback" css="font-size: 28rpx;display: block;line-height: 1.8em" />
            </PainterView>
          </PainterView>

          <!-- 学习反馈  -->
          <PainterView v-else-if="isStudy && !trialclass">
            <PainterView v-if="backlist.moduleType == 1">
              <PainterText :text="'日期：' + backlist.dateTime" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'姓名：' + backlist.studentName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'年级：' + backlist.gradeName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'时间：' + backlist.studyTime" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'学习学时：' + backlist.studyHour + '小时'" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'已购鼎英语学时：' + backlist.totalCourseHours + '小时'" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'剩余鼎英语学时：' + backlist.leaveCourseHours + '小时'" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterView v-if="backlist.isWord" css="width: 630rpx;">
                <PainterText text="所学词库：" css="font-size: 28rpx;margin-bottom: 20rpx;line-height: 1.8em" />
                <PainterText v-if="backlist.isWord" :text="backlist.studyBooks.replace(/,/g, '\n')" css="font-size: 28rpx;white-space: pre-line;margin-bottom: 20rpx;" />
              </PainterView>
              <PainterView css="width: 630rpx;">
                <PainterText v-if="backlist.isWord" :text="'复习词汇：' + backlist.reviewWords" css="font-size: 28rpx;display: inline-block;width:50%;margin-bottom: 20rpx;" />
                <PainterText v-if="backlist.isWord" :text="'复习遗忘词汇：' + backlist.forgetWords" css="font-size: 28rpx;display: inline-block;width:50%;margin-bottom: 20rpx; " />
              </PainterView>
              <PainterText v-if="backlist.isWord" :text="'复习遗忘率：' + backlist.forgetRate + '%'" css="font-size: 28rpx;display: block;margin-bottom: 20rpx; " />
              <PainterView css="width: 630rpx;">
                <PainterText v-if="backlist.isWord" :text="'学新词汇：' + backlist.newWords + '个'" css="font-size: 28rpx;display: inline-block;width:50%;margin-bottom: 20rpx; " />
                <PainterText
                  v-if="backlist.isWord"
                  :text="'学新遗忘词汇：' + backlist.newForget + '个'"
                  css="font-size: 28rpx;display: inline-block;width:50%;margin-bottom: 20rpx; "
                />
              </PainterView>

              <PainterText v-if="backlist.isWord" :text="'学新遗忘率：' + backlist.newForgetRate + '%'" css="font-size: 28rpx;display: block;margin-bottom: 20rpx; " />

              <PainterView v-if="backlist.isWord" css="width: 630rpx;">
                <PainterText text="学习进度：" css="font-size: 28rpx;margin-bottom: 20rpx;line-height: 1.8em" />
                <PainterText v-if="backlist.isWord" :text="backlist.learnSchedule.replace(/,/g, '\n')" css="font-size: 28rpx;white-space: pre-line;margin-bottom: 20rpx;" />
              </PainterView>

              <PainterView v-if="backlist.isWord" css="width: 630rpx;">
                <PainterText :text="'今日共识记词汇'" css="font-size: 28rpx;margin-bottom: 20rpx; " />
                <PainterText :text="'(复习遗忘词汇+学新词汇)：'" css="font-size: 24rpx;margin-bottom: 20rpx;color:#999999" />
                <PainterText :text="backlist.todayWords + '个'" css="font-size: 28rpx;margin-bottom: 20rpx; " />
              </PainterView>
            </PainterView>
            <PainterView v-else-if="backlist.moduleType == 3">
              <PainterText :text="'课程类型：' + backlist.curriculumName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'日期：' + backlist.dateTime" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'姓名：' + backlist.studentName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'年级：' + backlist.gradeName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'学员编号：' + backlist.studentCode" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'时间：' + backlist.studyTime" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'实际时间：'" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="backlist.actualStart + '--' + backlist.actualEnd" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'学习学时：' + backlist.studyHour + '小时'" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'所学课程类型：' + backlist.superReadCourseStatistics.courseName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'所学课程名称个数：' + mapArray(backlist.superReadCourseStatistics.courseList)" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'学习进度：' + backlist.superReadCourseStatistics.learningProgress + '%'" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText
                :text="'学习关卡正确率：' + rateMapArray(backlist.superReadCourseStatistics.checkpointList)"
                css="font-size: 28rpx;display: block; margin-bottom: 20rpx; "
              />
              <PainterView css="margin-bottom: 20rpx;background: rgba(238, 238, 238, 1);width: 630rpx;height:1rpx"></PainterView>
            </PainterView>
            <!-- // 听力反馈 -->
            <PainterView v-else-if="backlist.moduleType == 4">
              <PainterText :text="'日期：' + backlist.dateTime" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'姓名：' + backlist.studentName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'年级：' + backlist.gradeName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'学员编号：' + backlist.studentCode" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'实际时间：'" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="backlist.actualStart + '--' + backlist.actualEnd" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'学习学时：' + backlist.studyHour + '小时'" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'课程类型：' + backlist.listeningStatisticsDto.courseName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'课程名称：' + listenName(backlist.listeningStatisticsDto.courseList)" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'学习进度：' + listenProgress(backlist.listeningStatisticsDto.progressList)" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText
                :text="'听力名称(正确率)：' + listenArray(backlist.listeningStatisticsDto.listeningList)"
                css="font-size: 28rpx;display: block; margin-bottom: 20rpx; "
              />
              <PainterView css="margin-bottom: 20rpx;background: rgba(238, 238, 238, 1);width: 630rpx;height:1rpx"></PainterView>
            </PainterView>
            <PainterView v-else-if="backlist.curriculumName == '珠心算'">
              <PainterText :text="'日期：' + backlist.dateTime" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'姓名：' + backlist.studentName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'年级：' + backlist.gradeName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'学员编号：' + backlist.studentCode" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'实际时间：'" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="backlist.actualStart + '--' + backlist.actualEnd" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />

              <PainterView css="margin-bottom: 20rpx;background: rgba(238, 238, 238, 1);width: 630rpx;height:1rpx"></PainterView>
            </PainterView>
            <!-- // 一对多 -->
            <PainterView v-else-if="backlist.curriculumName.includes('1V') || backlist.curriculumName.includes('1对')">
              <PainterText :text="'日期：' + backlist.dateTime" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'姓名：' + backlist.studentName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'年级：' + backlist.gradeName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'学员编号：' + backlist.studentCode" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'实际时间：'" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="backlist.actualStart + '--' + backlist.actualEnd" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterView css="margin-bottom: 20rpx;background: rgba(238, 238, 238, 1);width: 630rpx;height:1rpx"></PainterView>
            </PainterView>
            <!-- todo 授课视频需要添加 -->
            <PainterView v-else-if="backlist.curriculumName == '学考通'">
              <PainterText :text="'日期：' + backlist.dateTime" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'姓名：' + backlist.studentName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'年级：' + backlist.gradeName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'学员编号：' + backlist.studentCode" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'实际时间：'" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="backlist.actualStart + '--' + backlist.actualEnd" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterView css="margin-bottom: 20rpx;background: rgba(238, 238, 238, 1);width: 630rpx;height:1rpx"></PainterView>
            </PainterView>
            <!-- //语法 -->
            <PainterView v-else>
              <!-- <PainterImage src="https://documsent.dxznjy.com/course/1bad910d87b8402abc07af9b61277085.png" css="font-size: 30rpx; margin-bottom: 30rpx;width: 100%;"></PainterImage> -->
              <PainterText :text="'日期：' + backlist.dateTime" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'姓名：' + backlist.studentName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'年级：' + backlist.gradeName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'时间：' + backlist.studyTime" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'学习学时：' + backlist.studyHour + '小时'" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'所学语法：' + backlist.grammarName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterText :text="'学习进度：' + backlist.learningProgress + '%'" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
              <PainterView css="width: 630rpx;">
                <PainterText :text="'复习知识点：' + backlist.reviewKnowledgeNum + '个'" css="font-size: 28rpx;display: inline-block;width:50%;margin-bottom: 20rpx; " />
                <PainterText :text="'新学知识点：' + backlist.newKnowledgeNum + '个'" css="font-size: 28rpx;display: inline-block;width:50%;margin-bottom: 20rpx; " />
              </PainterView>

              <PainterText :text="'语法点：' + backlist.grammarNum + '个'" css="font-size: 28rpx;display: block;margin-bottom: 20rpx; " />
              <PainterText :text="'结业检测：' + backlist.graduationNum + '个'" css="font-size: 28rpx;display: block;margin-bottom: 20rpx; " />
              <PainterText :text="'复习知识点（正确率）：' + backlist.reviewKnowledgeText" css="font-size: 28rpx;display: block;margin-bottom: 20rpx; " />
              <PainterText :text="'新学知识点（正确率）：' + backlist.newKnowledgeText" css="font-size: 28rpx;display: block;margin-bottom: 20rpx; " />
              <PainterText :text="'语法点（正确率）：' + backlist.grammarText" css="font-size: 28rpx;display: block;margin-bottom: 20rpx; " />
              <PainterText :text="'结业检测（正确率）：' + backlist.graduationText" css="font-size: 28rpx;display: block;margin-bottom: 20rpx; " />
            </PainterView>

            <PainterText text="教练评语：" css="font-size: 28rpx;display: block;margin-bottom: 20rpx;line-height: 1.8em" />
            <PainterView css="padding: 24rpx;box-sizing: border-box;background: rgba(153, 153, 153, 0.08);border-radius: 8rpx;width: 630rpx;min-height:481rpx;">
              <PainterText :text="feedback" css="font-size: 28rpx;display: block;line-height: 1.8em" />
            </PainterView>
          </PainterView>

          <!-- 复习反馈 -->
          <PainterView v-else-if="!isStudy">
            <PainterText :text="'日期：' + backlist.dateTime" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
            <PainterText :text="'姓名：' + backlist.studentName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
            <PainterText :text="'年级：' + backlist.gradeName" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
            <PainterView css="width: 630rpx;line-height: 1.8em;margin-bottom: 20rpx;">
              <PainterText text="实际时间：" css="font-size: 28rpx;" />
              <PainterText :text="timelist.actualStart + '~' + timelist.actualEnd" css="font-size: 26rpx;margin-top:4rpx" />
            </PainterView>
            <PainterText :text="'复习词汇：' + backlist.reviewWords" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
            <PainterText :text="'遗忘词汇：' + backlist.forgetWords" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
            <PainterText :text="'遗忘率：' + backlist.forgetRate + '%'" css="font-size: 28rpx;display: block; margin-bottom: 20rpx; " />
            <PainterView css="margin-bottom: 20rpx;background: rgba(238, 238, 238, 1);width: 630rpx;height:1rpx"></PainterView>
            <PainterText text="教练评语：" css="font-size: 28rpx;display: block;margin-bottom: 20rpx;line-height: 1.8em" />
            <PainterView css="padding: 24rpx;box-sizing: border-box;background: rgba(153, 153, 153, 0.08);border-radius: 8rpx;width: 630rpx;min-height:481rpx;">
              <PainterText :text="feedback" css="font-size: 28rpx;display: block;line-height: 1.8em" />
            </PainterView>
          </PainterView>
        </PainterView>
      </Painter>
    </view>
  </view>
</template>

<script>
  import PainterText from '@/share/components/lime-painter/components/l-painter-text/l-painter-text.vue';
  import PainterImage from '@/share/components/lime-painter/components/l-painter-image/l-painter-image.vue';
  import PainterView from '@/share/components/lime-painter/components/l-painter-view/l-painter-view.vue';
  import Painter from '@/share/components/lime-painter/components/l-painter/l-painter.vue';
  export default {
    components: {
      PainterText,
      Painter,
      PainterImage,
      PainterView
    },
    data() {
      return {
        path: '', //生成海报
        backlist: '', // 获取反馈详情
        //反馈详情列表
        trialclass: false, // 是否是试课反馈
        isStudy: false, // 学习还是复习

        feedback: '', // 弹出层文本框输入的内容
        Intention: '', // 学习意愿
        timelist: {
          actualStart: '',
          actualEnd: ''
        }
      };
    },
    onLoad(e) {
      let sendData = JSON.parse(decodeURIComponent(e.sendData));
      if (sendData != null && sendData != undefined) {
        this.trialclass = sendData.trialclass;
        this.isStudy = sendData.isStudy;
        this.backlist = sendData.detailsData;
        if (this.backlist != null && this.backlist != undefined) {
          this.Intention = this.backlist.studyIntention;
          this.feedback = this.backlist.feedback;
          this.timelist.actualStart = this.backlist.actualStart;
          this.timelist.actualEnd = this.backlist.actualEnd;
        }
      }
      if (this.backlist.reviewKnowledgeList?.length) {
        this.backlist.reviewKnowledgeText = this.backlist.reviewKnowledgeList.reduce((e, i) => i.name + `(${i.accuracyRate || '--'}%)  `, '');
      } else {
        this.backlist.reviewKnowledgeText = '--';
      }
      if (this.backlist.newKnowledgeList?.length) {
        console.log(this.backlist.newKnowledgeList);
        this.backlist.newKnowledgeText = this.backlist.newKnowledgeList.map((i) => i.name + `(${i.accuracyRate || '--'}%)`).join(',');
        // this.backlist.newKnowledgeText = this.backlist.newKnowledgeList.reduce((e, i) => i.name + `(${i.accuracyRate || '--'}%)  `, '');
        console.log(this.backlist.newKnowledgeText);
      } else {
        this.backlist.newKnowledgeText = '--';
      }
      if (this.backlist.grammarList?.length) {
        this.backlist.grammarText = this.backlist.grammarList.reduce((e, i) => i.name + `(${i.accuracyRate || '--'}%)  `, '');
      } else {
        this.backlist.grammarText = '--';
      }
      if (this.backlist.graduationList?.length) {
        this.backlist.graduationText = this.backlist.graduationList.reduce((e, i) => i.name + `(${i.accuracyRate || '--'}%)  `, '');
      } else {
        this.backlist.graduationText = '--';
      }
    },
    methods: {
      rateMapArray(arr) {
        if (arr && arr.length > 0) {
          return arr.map((i) => i.checkpointName + '（' + i.accuracyRate + '%' + '）').join(',');
        } else {
          return '-';
        }
      },
      mapArray(arr) {
        if (arr && arr.length > 0) {
          return arr.map((i) => i.courseName + '（' + i.checkpointNum + '）').join(',');
        } else {
          return '-';
        }
      },
      getReviewTime() {
        let str = '';
        if (this.backlist.reviewTimeArr) {
          for (let i = 0; i < this.backlist.reviewTimeArr.length; i++) {
            if (this.backlist.reviewTimeArr[i]) {
              str += this.backlist.reviewTimeArr[i];
              if (i != this.backlist.reviewTimeArr.length - 1) {
                str += '、';
              }
            }
          }
        }
        return str ? str : '无';
      },
      //听力数据处理
      listenProgress(progressList) {
        return progressList.map((item) => `${parseFloat(item.learningProgress)}%`).join(' , ');
      },
      listenName(progressList) {
        return progressList.map((item) => item.courseName).join(' , ');
      },
      listenArray(arr) {
        if (arr && arr.length > 0) {
          return arr.map((i) => i.listeningName + '（' + i.accuracyRate + '%）').join(' , ');
        } else {
          return '-';
        }
      },
      //长按
      longPress() {
        let that = this;
        uni.showModal({
          content: '保存图片',
          success: (res) => {
            if (res.confirm) {
              that.$refs.painter.canvasToTempFilePathSync({
                fileType: 'jpg',
                pathType: 'url',
                quality: 1,
                success: (res) => {
                  console.log(res);
                  that.path = res.tempFilePath;
                  that.saveLocal();
                },
                fail: (err) => {
                  console.log(err);
                  uni.showModal({
                    title: '提示',
                    content: '生成海报失败,请重试',
                    showCancel: false
                  });
                }
              });
            } else {
              uni.showToast({
                title: '已取消！',
                icon: 'none'
              });
            }
          }
        });
      },

      //保存本地
      saveLocal() {
        // #ifdef APP-PLUS
        uni.saveImageToPhotosAlbum({
          filePath: this.path,
          success: (res) => {
            uni.showToast({
              icon: 'none',
              title: '保存成功'
            });
            console.log(res);
            //分享
            // wx.showShareImageMenu({
            //     path: res.path,
            //     success(msg) {
            //         console.log(msg);
            //     },
            //     fail(err) {
            //         console.log("11111");
            //         console.log(err);
            //     }
            // })
          },
          fail(err) {
            uni.showToast({
              icon: 'none',
              title: '保存失败'
            });
          }
        });
        // #endif
        // #ifdef MP-WEIXIN
        uni.authorize({
          scope: 'scope.writePhotosAlbum',
          success: () => {
            uni.saveImageToPhotosAlbum({
              filePath: this.path,
              success: (res) => {
                uni.showToast({
                  icon: 'none',
                  title: '保存成功'
                });
                console.log(res);
                //分享
                // wx.showShareImageMenu({
                //     path: res.path,
                //     success(msg) {
                //         console.log(msg);
                //     },
                //     fail(err) {
                //         console.log("11111");
                //         console.log(err);
                //     }
                // })
              },
              fail(err) {
                uni.showToast({
                  icon: 'none',
                  title: '保存失败'
                });
              }
            });
          },
          fail() {
            uni.showModal({
              title: '保存失败',
              content: '您没有授权，无法保存到相册',
              showCancel: false
            });
          }
        });
        // #endif
      },

      Back() {
        uni.navigateBack();
      }
    }
  };
</script>

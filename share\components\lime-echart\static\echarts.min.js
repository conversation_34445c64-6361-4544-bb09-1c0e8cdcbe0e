!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).echarts={})}(this,function(t){"use strict";var w=function(t,e){return(w=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}))(t,e)};function a(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}w(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}var S=function(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1},p=new function(){this.browser=new S,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow="undefined"!=typeof window};"object"==typeof wx&&"function"==typeof wx.getSystemInfoSync?(p.wxa=!0,p.touchEventsSupported=!0):"undefined"==typeof document&&"undefined"!=typeof self?p.worker=!0:"undefined"==typeof navigator||0===navigator.userAgent.indexOf("Node.js")?(p.node=!0,p.svgSupported=!0):(J=navigator.userAgent,ae=(Gt=p).browser,rt=J.match(/Firefox\/([\d.]+)/),U=J.match(/MSIE\s([\d.]+)/)||J.match(/Trident\/.+?rv:(([\d.]+))/),Q=J.match(/Edge?\/([\d.]+)/),J=/micromessenger/i.test(J),rt&&(ae.firefox=!0,ae.version=rt[1]),U&&(ae.ie=!0,ae.version=U[1]),Q&&(ae.edge=!0,ae.version=Q[1],ae.newEdge=18<+Q[1].split(".")[0]),J&&(ae.weChat=!0),Gt.svgSupported="undefined"!=typeof SVGRect,Gt.touchEventsSupported="ontouchstart"in window&&!ae.ie&&!ae.edge,Gt.pointerEventsSupported="onpointerdown"in window&&(ae.edge||ae.ie&&11<=+ae.version),Gt.domSupported="undefined"!=typeof document,rt=document.documentElement.style,Gt.transform3dSupported=(ae.ie&&"transition"in rt||ae.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in rt)&&!("OTransition"in rt),Gt.transformSupported=Gt.transform3dSupported||ae.ie&&9<=+ae.version);var Z="12px sans-serif";var T,C,D=function(t){var e={};if("undefined"!=typeof JSON)for(var n=0;n<t.length;n++){var i=String.fromCharCode(n+32),r=(t.charCodeAt(n)-20)/100;e[i]=r}return e}("007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N"),H={createCanvas:function(){return"undefined"!=typeof document&&document.createElement("canvas")},measureText:function(t,e){if(T||(n=H.createCanvas(),T=n&&n.getContext("2d")),T)return C!==e&&(C=T.font=e||Z),T.measureText(t);t=t||"",e=e||Z;var n=/((?:\d+)?\.?\d*)px/.exec(e),i=n&&+n[1]||12,r=0;if(0<=e.indexOf("mono"))r=i*t.length;else for(var o=0;o<t.length;o++){var a=D[t[o]];r+=null==a?i:a*i}return{width:r}},loadImage:function(t,e,n){var i=new Image;return i.onload=e,i.onerror=n,i.src=t,i}};function L(t){for(var e in H)t[e]&&(H[e]=t[e])}var A=lt(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],function(t,e){return t["[object "+e+"]"]=!0,t},{}),W=lt(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],function(t,e){return t["[object "+e+"Array]"]=!0,t},{}),G=Object.prototype.toString,U=Array.prototype,Y=U.forEach,j=U.filter,K=U.slice,$=U.map,Q=function(){}.constructor,J=Q?Q.prototype:null,tt="__proto__",et=2311;function nt(){return et++}function it(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];"undefined"!=typeof console&&console.error.apply(console,t)}function b(t){if(null==t||"object"!=typeof t)return t;var e=t,n=G.call(t);if("[object Array]"===n){if(!At(t))for(var e=[],i=0,r=t.length;i<r;i++)e[i]=b(t[i])}else if(W[n]){if(!At(t)){var o=t.constructor;if(o.from)e=o.from(t);else{e=new o(t.length);for(i=0,r=t.length;i<r;i++)e[i]=t[i]}}}else if(!A[n]&&!At(t)&&!vt(t))for(var a in e={},t)t.hasOwnProperty(a)&&a!==tt&&(e[a]=b(t[a]));return e}function d(t,e,n){if(!R(e)||!R(t))return n?b(e):t;for(var i in e){var r,o;e.hasOwnProperty(i)&&i!==tt&&(r=t[i],!R(o=e[i])||!R(r)||F(o)||F(r)||vt(o)||vt(r)||yt(o)||yt(r)||At(o)||At(r)?!n&&i in t||(t[i]=b(e[i])):d(r,o,n))}return t}function P(t,e){if(Object.assign)Object.assign(t,e);else for(var n in e)e.hasOwnProperty(n)&&n!==tt&&(t[n]=e[n]);return t}function B(t,e,n){for(var i=ct(e),r=0;r<i.length;r++){var o=i[r];(n?null!=e[o]:null==t[o])&&(t[o]=e[o])}return t}var rt=H.createCanvas;function k(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var n=0,i=t.length;n<i;n++)if(t[n]===e)return n}return-1}function ot(t,e){var n,i=t.prototype;function r(){}for(n in r.prototype=e.prototype,t.prototype=new r,i)i.hasOwnProperty(n)&&(t.prototype[n]=i[n]);(t.prototype.constructor=t).superClass=e}function at(t,e,n){if(t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,Object.getOwnPropertyNames)for(var i=Object.getOwnPropertyNames(e),r=0;r<i.length;r++){var o=i[r];"constructor"!==o&&(n?null!=e[o]:null==t[o])&&(t[o]=e[o])}else B(t,e,n)}function st(t){return!!t&&"string"!=typeof t&&"number"==typeof t.length}function O(t,e,n){if(t&&e)if(t.forEach&&t.forEach===Y)t.forEach(e,n);else if(t.length===+t.length)for(var i=0,r=t.length;i<r;i++)e.call(n,t[i],i,t);else for(var o in t)t.hasOwnProperty(o)&&e.call(n,t[o],o,t)}function z(t,e,n){if(!t)return[];if(!e)return Tt(t);if(t.map&&t.map===$)return t.map(e,n);for(var i=[],r=0,o=t.length;r<o;r++)i.push(e.call(n,t[r],r,t));return i}function lt(t,e,n,i){if(t&&e){for(var r=0,o=t.length;r<o;r++)n=e.call(i,n,t[r],r,t);return n}}function ut(t,e,n){if(!t)return[];if(!e)return Tt(t);if(t.filter&&t.filter===j)return t.filter(e,n);for(var i=[],r=0,o=t.length;r<o;r++)e.call(n,t[r],r,t)&&i.push(t[r]);return i}function ht(t,e,n){if(t&&e)for(var i=0,r=t.length;i<r;i++)if(e.call(n,t[i],i,t))return t[i]}function ct(t){if(!t)return[];if(Object.keys)return Object.keys(t);var e,n=[];for(e in t)t.hasOwnProperty(e)&&n.push(e);return n}var pt=J&&I(J.bind)?J.call.bind(J.bind):function(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];return function(){return t.apply(e,n.concat(K.call(arguments)))}};function dt(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return function(){return t.apply(this,e.concat(K.call(arguments)))}}function F(t){return Array.isArray?Array.isArray(t):"[object Array]"===G.call(t)}function I(t){return"function"==typeof t}function V(t){return"string"==typeof t}function ft(t){return"[object String]"===G.call(t)}function gt(t){return"number"==typeof t}function R(t){var e=typeof t;return"function"==e||!!t&&"object"==e}function yt(t){return!!A[G.call(t)]}function mt(t){return!!W[G.call(t)]}function vt(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function _t(t){return null!=t.colorStops}function xt(t){return null!=t.image}function wt(t){return"[object RegExp]"===G.call(t)}function bt(t){return t!=t}function St(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var n=0,i=t.length;n<i;n++)if(null!=t[n])return t[n]}function N(t,e){return null!=t?t:e}function Mt(t,e,n){return null!=t?t:null!=e?e:n}function Tt(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return K.apply(t,e)}function Ct(t){var e;return"number"==typeof t?[t,t,t,t]:2===(e=t.length)?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function kt(t,e){if(!t)throw new Error(e)}function It(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var Dt="__ec_primitive__";function Lt(t){t[Dt]=!0}function At(t){return t[Dt]}Ot.prototype.delete=function(t){var e=this.has(t);return e&&delete this.data[t],e},Ot.prototype.has=function(t){return this.data.hasOwnProperty(t)},Ot.prototype.get=function(t){return this.data[t]},Ot.prototype.set=function(t,e){return this.data[t]=e,this},Ot.prototype.keys=function(){return ct(this.data)},Ot.prototype.forEach=function(t){var e,n=this.data;for(e in n)n.hasOwnProperty(e)&&t(n[e],e)};var Pt=Ot;function Ot(){this.data={}}var Rt="function"==typeof Map;Et.prototype.hasKey=function(t){return this.data.has(t)},Et.prototype.get=function(t){return this.data.get(t)},Et.prototype.set=function(t,e){return this.data.set(t,e),e},Et.prototype.each=function(n,i){this.data.forEach(function(t,e){n.call(i,t,e)})},Et.prototype.keys=function(){var t=this.data.keys();return Rt?Array.from(t):t},Et.prototype.removeKey=function(t){this.data.delete(t)};var Nt=Et;function Et(t){var n=F(t),i=(this.data=new(Rt?Map:Pt),this);function e(t,e){n?i.set(t,e):i.set(e,t)}t instanceof Et?t.each(e):t&&O(t,e)}function E(t){return new Nt(t)}function Bt(t,e){for(var n=new t.constructor(t.length+e.length),i=0;i<t.length;i++)n[i]=t[i];for(var r=t.length,i=0;i<e.length;i++)n[i+r]=e[i];return n}function zt(t,e){var n,t=Object.create?Object.create(t):((n=function(){}).prototype=t,new n);return e&&P(t,e),t}function Ft(t){t=t.style;t.webkitUserSelect="none",t.userSelect="none",t.webkitTapHighlightColor="rgba(0,0,0,0)",t["-webkit-touch-callout"]="none"}function Vt(t,e){return t.hasOwnProperty(e)}function Ht(){}var Wt=180/Math.PI,Gt=Object.freeze({__proto__:null,HashMap:Nt,RADIAN_TO_DEGREE:Wt,assert:kt,bind:pt,clone:b,concatArray:Bt,createCanvas:rt,createHashMap:E,createObject:zt,curry:dt,defaults:B,disableUserSelect:Ft,each:O,eqNaN:bt,extend:P,filter:ut,find:ht,guid:nt,hasOwn:Vt,indexOf:k,inherits:ot,isArray:F,isArrayLike:st,isBuiltInObject:yt,isDom:vt,isFunction:I,isGradientObject:_t,isImagePatternObject:xt,isNumber:gt,isObject:R,isPrimitive:At,isRegExp:wt,isString:V,isStringSafe:ft,isTypedArray:mt,keys:ct,logError:it,map:z,merge:d,mergeAll:function(t,e){for(var n=t[0],i=1,r=t.length;i<r;i++)n=d(n,t[i],e);return n},mixin:at,noop:Ht,normalizeCssArray:Ct,reduce:lt,retrieve:St,retrieve2:N,retrieve3:Mt,setAsPrimitive:Lt,slice:Tt,trim:It});function Ut(t,e){return[t=null==t?0:t,e=null==e?0:e]}function qt(t){return[t[0],t[1]]}function Xt(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t}function Yt(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t}function jt(t){return Math.sqrt(Zt(t))}function Zt(t){return t[0]*t[0]+t[1]*t[1]}function Kt(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t}function $t(t,e){var n=jt(e);return 0===n?(t[0]=0,t[1]=0):(t[0]=e[0]/n,t[1]=e[1]/n),t}function Qt(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}var Jt=Qt;function te(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}var ee=te;function ne(t,e,n,i){return t[0]=e[0]+i*(n[0]-e[0]),t[1]=e[1]+i*(n[1]-e[1]),t}function ie(t,e,n){var i=e[0],e=e[1];return t[0]=n[0]*i+n[2]*e+n[4],t[1]=n[1]*i+n[3]*e+n[5],t}function re(t,e,n){return t[0]=Math.min(e[0],n[0]),t[1]=Math.min(e[1],n[1]),t}function oe(t,e,n){return t[0]=Math.max(e[0],n[0]),t[1]=Math.max(e[1],n[1]),t}var ae=Object.freeze({__proto__:null,add:Xt,applyTransform:ie,clone:qt,copy:function(t,e){return t[0]=e[0],t[1]=e[1],t},create:Ut,dist:Jt,distSquare:ee,distance:Qt,distanceSquare:te,div:function(t,e,n){return t[0]=e[0]/n[0],t[1]=e[1]/n[1],t},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},len:jt,lenSquare:Zt,length:jt,lengthSquare:Zt,lerp:ne,max:oe,min:re,mul:function(t,e,n){return t[0]=e[0]*n[0],t[1]=e[1]*n[1],t},negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t},normalize:$t,scale:Kt,scaleAndAdd:function(t,e,n,i){return t[0]=e[0]+n[0]*i,t[1]=e[1]+n[1]*i,t},set:function(t,e,n){return t[0]=e,t[1]=n,t},sub:Yt}),se=function(t,e){this.target=t,this.topTarget=e&&e.topTarget},le=(ue.prototype._dragStart=function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent||e.__hostTarget;e&&((this._draggingTarget=e).dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new se(e,t),"dragstart",t.event))},ue.prototype._drag=function(t){var e,n,i,r,o=this._draggingTarget;o&&(e=t.offsetX,n=t.offsetY,i=e-this._x,r=n-this._y,this._x=e,this._y=n,o.drift(i,r,t),this.handler.dispatchToElement(new se(o,t),"drag",t.event),i=this.handler.findHover(e,n,o).target,r=this._dropTarget,o!==(this._dropTarget=i))&&(r&&i!==r&&this.handler.dispatchToElement(new se(r,t),"dragleave",t.event),i)&&i!==r&&this.handler.dispatchToElement(new se(i,t),"dragenter",t.event)},ue.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new se(e,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new se(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},ue);function ue(t){(this.handler=t).on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}ce.prototype.on=function(t,e,n,i){this._$handlers||(this._$handlers={});var r=this._$handlers;if("function"==typeof e&&(i=n,n=e,e=null),n&&t){var o=this._$eventProcessor;null!=e&&o&&o.normalizeQuery&&(e=o.normalizeQuery(e)),r[t]||(r[t]=[]);for(var a=0;a<r[t].length;a++)if(r[t][a].h===n)return this;o={h:n,query:e,ctx:i||this,callAtLast:n.zrEventfulCallAtLast},e=r[t].length-1,i=r[t][e];i&&i.callAtLast?r[t].splice(e,0,o):r[t].push(o)}return this},ce.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},ce.prototype.off=function(t,e){var n=this._$handlers;if(n)if(t)if(e){if(n[t]){for(var i=[],r=0,o=n[t].length;r<o;r++)n[t][r].h!==e&&i.push(n[t][r]);n[t]=i}n[t]&&0===n[t].length&&delete n[t]}else delete n[t];else this._$handlers={};return this},ce.prototype.trigger=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(this._$handlers){var i=this._$handlers[t],r=this._$eventProcessor;if(i)for(var o=e.length,a=i.length,s=0;s<a;s++){var l=i[s];if(!r||!r.filter||null==l.query||r.filter(t,l.query))switch(o){case 0:l.h.call(l.ctx);break;case 1:l.h.call(l.ctx,e[0]);break;case 2:l.h.call(l.ctx,e[0],e[1]);break;default:l.h.apply(l.ctx,e)}}r&&r.afterTrigger&&r.afterTrigger(t)}return this},ce.prototype.triggerWithContext=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(this._$handlers){var i=this._$handlers[t],r=this._$eventProcessor;if(i)for(var o=e.length,a=e[o-1],s=i.length,l=0;l<s;l++){var u=i[l];if(!r||!r.filter||null==u.query||r.filter(t,u.query))switch(o){case 0:u.h.call(a);break;case 1:u.h.call(a,e[0]);break;case 2:u.h.call(a,e[0],e[1]);break;default:u.h.apply(a,e.slice(1,o-1))}}r&&r.afterTrigger&&r.afterTrigger(t)}return this};var he=ce;function ce(t){t&&(this._$eventProcessor=t)}var pe=Math.log(2);function de(t,e,n,i,r,o){var a,s=i+"-"+r,l=t.length;if(o.hasOwnProperty(s))return o[s];if(1===e)return a=Math.round(Math.log((1<<l)-1&~r)/pe),t[n][a];for(var u=i|1<<n,h=n+1;i&1<<h;)h++;for(var c=0,p=0,d=0;p<l;p++){var f=1<<p;f&r||(c+=(d%2?-1:1)*t[n][p]*de(t,e-1,h,u,r|f,o),d++)}return o[s]=c}function fe(t,e){var n=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],i={},r=de(n,8,0,0,0,i);if(0!==r){for(var o=[],a=0;a<8;a++)for(var s=0;s<8;s++)null==o[s]&&(o[s]=0),o[s]+=((a+s)%2?-1:1)*de(n,7,0===a?1:0,1<<a,1<<s,i)/r*e[a];return function(t,e,n){var i=e*o[6]+n*o[7]+1;t[0]=(e*o[0]+n*o[1]+o[2])/i,t[1]=(e*o[3]+n*o[4]+o[5])/i}}}var ge="___zrEVENTSAVED";function ye(t,e,n,i,r){if(e.getBoundingClientRect&&p.domSupported&&!me(e)){var o=e[ge]||(e[ge]={}),e=function(t,e,n){for(var i=n?"invTrans":"trans",r=e[i],o=e.srcCoords,a=[],s=[],l=!0,u=0;u<4;u++){var h=t[u].getBoundingClientRect(),c=2*u,p=h.left,h=h.top;a.push(p,h),l=l&&o&&p===o[c]&&h===o[1+c],s.push(t[u].offsetLeft,t[u].offsetTop)}return l&&r?r:(e.srcCoords=a,e[i]=n?fe(s,a):fe(a,s))}(function(t,e){var n=e.markers;if(!n){n=e.markers=[];for(var i=["left","right"],r=["top","bottom"],o=0;o<4;o++){var a=document.createElement("div"),s=a.style,l=o%2,u=(o>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[l]+":0",r[u]+":0",i[1-l]+":auto",r[1-u]+":auto",""].join("!important;"),t.appendChild(a),n.push(a)}}return n}(e,o),o,r);if(e)return e(t,n,i),1}}function me(t){return"CANVAS"===t.nodeName.toUpperCase()}var ve=/([&<>"'])/g,_e={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function xe(t){return null==t?"":(t+"").replace(ve,function(t,e){return _e[e]})}var we=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,be=[],Se=p.browser.firefox&&+p.browser.version.split(".")[0]<39;function Me(t,e,n,i){return n=n||{},i?Te(t,e,n):Se&&null!=e.layerX&&e.layerX!==e.offsetX?(n.zrX=e.layerX,n.zrY=e.layerY):null!=e.offsetX?(n.zrX=e.offsetX,n.zrY=e.offsetY):Te(t,e,n),n}function Te(t,e,n){if(p.domSupported&&t.getBoundingClientRect){var i,r=e.clientX,e=e.clientY;if(me(t))return i=t.getBoundingClientRect(),n.zrX=r-i.left,void(n.zrY=e-i.top);if(ye(be,t,r,e))return n.zrX=be[0],void(n.zrY=be[1])}n.zrX=n.zrY=0}function Ce(t){return t||window.event}function ke(t,e,n){var i;return null==(e=Ce(e)).zrX&&((i=e.type)&&0<=i.indexOf("touch")?(i=("touchend"!==i?e.targetTouches:e.changedTouches)[0])&&Me(t,i,e,n):(Me(t,e,e,n),i=function(t){var e=t.wheelDelta;if(e)return e;var n=t.deltaX,t=t.deltaY;return null!=n&&null!=t?3*(0!==t?Math.abs(t):Math.abs(n))*(0<t||!(t<0)&&0<n?-1:1):e}(e),e.zrDelta=i?i/120:-(e.detail||0)/3),t=e.button,null==e.which&&void 0!==t&&we.test(e.type))&&(e.which=1&t?1:2&t?3:4&t?2:0),e}var Ie=function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0},De=(Le.prototype.recognize=function(t,e,n){return this._doTrack(t,e,n),this._recognize(t)},Le.prototype.clear=function(){return this._track.length=0,this},Le.prototype._doTrack=function(t,e,n){var i=t.touches;if(i){for(var r={points:[],touches:[],target:e,event:t},o=0,a=i.length;o<a;o++){var s=i[o],l=Me(n,s,{});r.points.push([l.zrX,l.zrY]),r.touches.push(s)}this._track.push(r)}},Le.prototype._recognize=function(t){for(var e in Pe)if(Pe.hasOwnProperty(e)){e=Pe[e](this._track,t);if(e)return e}},Le);function Le(){this._track=[]}function Ae(t){var e=t[1][0]-t[0][0],t=t[1][1]-t[0][1];return Math.sqrt(e*e+t*t)}var Pe={pinch:function(t,e){var n,i=t.length;if(i)return n=(t[i-1]||{}).points,(i=(t[i-2]||{}).points||n)&&1<i.length&&n&&1<n.length?(i=Ae(n)/Ae(i),isFinite(i)||(i=1),e.pinchScale=i,i=[(n[0][0]+n[1][0])/2,(n[0][1]+n[1][1])/2],e.pinchX=i[0],e.pinchY=i[1],{type:"pinch",target:t[0].target,event:e}):void 0}};function Oe(){return[1,0,0,1,0,0]}function Re(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function Ne(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function Ee(t,e,n){var i=e[0]*n[0]+e[2]*n[1],r=e[1]*n[0]+e[3]*n[1],o=e[0]*n[2]+e[2]*n[3],a=e[1]*n[2]+e[3]*n[3],s=e[0]*n[4]+e[2]*n[5]+e[4],n=e[1]*n[4]+e[3]*n[5]+e[5];return t[0]=i,t[1]=r,t[2]=o,t[3]=a,t[4]=s,t[5]=n,t}function Be(t,e,n){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+n[0],t[5]=e[5]+n[1],t}function ze(t,e,n,i){void 0===i&&(i=[0,0]);var r=e[0],o=e[2],a=e[4],s=e[1],l=e[3],e=e[5],u=Math.sin(n),n=Math.cos(n);return t[0]=r*n+s*u,t[1]=-r*u+s*n,t[2]=o*n+l*u,t[3]=-o*u+n*l,t[4]=n*(a-i[0])+u*(e-i[1])+i[0],t[5]=n*(e-i[1])-u*(a-i[0])+i[1],t}function Fe(t,e,n){var i=n[0],n=n[1];return t[0]=e[0]*i,t[1]=e[1]*n,t[2]=e[2]*i,t[3]=e[3]*n,t[4]=e[4]*i,t[5]=e[5]*n,t}function Ve(t,e){var n=e[0],i=e[2],r=e[4],o=e[1],a=e[3],e=e[5],s=n*a-o*i;return s?(t[0]=a*(s=1/s),t[1]=-o*s,t[2]=-i*s,t[3]=n*s,t[4]=(i*e-a*r)*s,t[5]=(o*r-n*e)*s,t):null}var He=Object.freeze({__proto__:null,clone:function(t){var e=Oe();return Ne(e,t),e},copy:Ne,create:Oe,identity:Re,invert:Ve,mul:Ee,rotate:ze,scale:Fe,translate:Be}),M=(e.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},e.prototype.clone=function(){return new e(this.x,this.y)},e.prototype.set=function(t,e){return this.x=t,this.y=e,this},e.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},e.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},e.prototype.scale=function(t){this.x*=t,this.y*=t},e.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},e.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},e.prototype.dot=function(t){return this.x*t.x+this.y*t.y},e.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},e.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},e.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},e.prototype.distance=function(t){var e=this.x-t.x,t=this.y-t.y;return Math.sqrt(e*e+t*t)},e.prototype.distanceSquare=function(t){var e=this.x-t.x,t=this.y-t.y;return e*e+t*t},e.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},e.prototype.transform=function(t){var e,n;if(t)return e=this.x,n=this.y,this.x=t[0]*e+t[2]*n+t[4],this.y=t[1]*e+t[3]*n+t[5],this},e.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},e.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},e.set=function(t,e,n){t.x=e,t.y=n},e.copy=function(t,e){t.x=e.x,t.y=e.y},e.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},e.lenSquare=function(t){return t.x*t.x+t.y*t.y},e.dot=function(t,e){return t.x*e.x+t.y*e.y},e.add=function(t,e,n){t.x=e.x+n.x,t.y=e.y+n.y},e.sub=function(t,e,n){t.x=e.x-n.x,t.y=e.y-n.y},e.scale=function(t,e,n){t.x=e.x*n,t.y=e.y*n},e.scaleAndAdd=function(t,e,n,i){t.x=e.x+n.x*i,t.y=e.y+n.y*i},e.lerp=function(t,e,n,i){var r=1-i;t.x=r*e.x+i*n.x,t.y=r*e.y+i*n.y},e);function e(t,e){this.x=t||0,this.y=e||0}var We=Math.min,Ge=Math.max,Ue=new M,qe=new M,Xe=new M,Ye=new M,je=new M,Ze=new M,q=(Ke.prototype.union=function(t){var e=We(t.x,this.x),n=We(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=Ge(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=Ge(t.y+t.height,this.y+this.height)-n:this.height=t.height,this.x=e,this.y=n},Ke.prototype.applyTransform=function(t){Ke.applyTransform(this,this,t)},Ke.prototype.calculateTransform=function(t){var e=t.width/this.width,n=t.height/this.height,i=Oe();return Be(i,i,[-this.x,-this.y]),Fe(i,i,[e,n]),Be(i,i,[t.x,t.y]),i},Ke.prototype.intersect=function(t,e){if(!t)return!1;t instanceof Ke||(t=Ke.create(t));var n,i,r,o,a,s,l,u,h=this,c=h.x,p=h.x+h.width,d=h.y,h=h.y+h.height,f=t.x,g=t.x+t.width,y=t.y,t=t.y+t.height,m=!(p<f||g<c||h<y||t<d);return e&&(n=1/0,i=0,r=Math.abs(p-f),o=Math.abs(g-c),a=Math.abs(h-y),s=Math.abs(t-d),l=Math.min(r,o),u=Math.min(a,s),p<f||g<c?i<l&&(i=l,r<o?M.set(Ze,-r,0):M.set(Ze,o,0)):l<n&&(n=l,r<o?M.set(je,r,0):M.set(je,-o,0)),h<y||t<d?i<u&&(i=u,a<s?M.set(Ze,0,-a):M.set(Ze,0,s)):l<n&&(n=l,a<s?M.set(je,0,a):M.set(je,0,-s))),e&&M.copy(e,m?je:Ze),m},Ke.prototype.contain=function(t,e){var n=this;return t>=n.x&&t<=n.x+n.width&&e>=n.y&&e<=n.y+n.height},Ke.prototype.clone=function(){return new Ke(this.x,this.y,this.width,this.height)},Ke.prototype.copy=function(t){Ke.copy(this,t)},Ke.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},Ke.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},Ke.prototype.isZero=function(){return 0===this.width||0===this.height},Ke.create=function(t){return new Ke(t.x,t.y,t.width,t.height)},Ke.copy=function(t,e){t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height},Ke.applyTransform=function(t,e,n){var i,r,o,a;n?n[1]<1e-5&&-1e-5<n[1]&&n[2]<1e-5&&-1e-5<n[2]?(i=n[0],r=n[3],o=n[4],a=n[5],t.x=e.x*i+o,t.y=e.y*r+a,t.width=e.width*i,t.height=e.height*r,t.width<0&&(t.x+=t.width,t.width=-t.width),t.height<0&&(t.y+=t.height,t.height=-t.height)):(Ue.x=Xe.x=e.x,Ue.y=Ye.y=e.y,qe.x=Ye.x=e.x+e.width,qe.y=Xe.y=e.y+e.height,Ue.transform(n),Ye.transform(n),qe.transform(n),Xe.transform(n),t.x=We(Ue.x,qe.x,Xe.x,Ye.x),t.y=We(Ue.y,qe.y,Xe.y,Ye.y),o=Ge(Ue.x,qe.x,Xe.x,Ye.x),a=Ge(Ue.y,qe.y,Xe.y,Ye.y),t.width=o-t.x,t.height=a-t.y):t!==e&&Ke.copy(t,e)},Ke);function Ke(t,e,n,i){n<0&&(t+=n,n=-n),i<0&&(e+=i,i=-i),this.x=t,this.y=e,this.width=n,this.height=i}var $e="silent";function Qe(){Ie(this.event)}a(en,Je=he),en.prototype.dispose=function(){},en.prototype.setCursor=function(){};var Je,tn=en;function en(){var t=null!==Je&&Je.apply(this,arguments)||this;return t.handler=null,t}var nn,rn=function(t,e){this.x=t,this.y=e},on=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],an=new q(0,0,0,0),sn=(a(ln,nn=he),ln.prototype.setHandlerProxy=function(e){this.proxy&&this.proxy.dispose(),e&&(O(on,function(t){e.on&&e.on(t,this[t],this)},this),e.handler=this),this.proxy=e},ln.prototype.mousemove=function(t){var e=t.zrX,n=t.zrY,i=hn(this,e,n),r=this._hovered,o=r.target,i=(o&&!o.__zr&&(o=(r=this.findHover(r.x,r.y)).target),this._hovered=i?new rn(e,n):this.findHover(e,n)),e=i.target,n=this.proxy;n.setCursor&&n.setCursor(e?e.cursor:"default"),o&&e!==o&&this.dispatchToElement(r,"mouseout",t),this.dispatchToElement(i,"mousemove",t),e&&e!==o&&this.dispatchToElement(i,"mouseover",t)},ln.prototype.mouseout=function(t){var e=t.zrEventControl;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&this.trigger("globalout",{type:"globalout",event:t})},ln.prototype.resize=function(){this._hovered=new rn(0,0)},ln.prototype.dispatch=function(t,e){t=this[t];t&&t.call(this,e)},ln.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},ln.prototype.setCursorStyle=function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},ln.prototype.dispatchToElement=function(t,e,n){var i=(t=t||{}).target;if(!i||!i.silent){for(var r="on"+e,o={type:e,event:n,target:(t=t).target,topTarget:t.topTarget,cancelBubble:!1,offsetX:n.zrX,offsetY:n.zrY,gestureEvent:n.gestureEvent,pinchX:n.pinchX,pinchY:n.pinchY,pinchScale:n.pinchScale,wheelDelta:n.zrDelta,zrByTouch:n.zrByTouch,which:n.which,stop:Qe};i&&(i[r]&&(o.cancelBubble=!!i[r].call(i,o)),i.trigger(e,o),i=i.__hostTarget||i.parent,!o.cancelBubble););o.cancelBubble||(this.trigger(e,o),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer(function(t){"function"==typeof t[r]&&t[r].call(t,o),t.trigger&&t.trigger(e,o)}))}},ln.prototype.findHover=function(t,e,n){var i=this.storage.getDisplayList(),r=new rn(t,e);if(un(i,r,t,e,n),this._pointerSize&&!r.target){for(var o=[],a=this._pointerSize,s=a/2,l=new q(t-s,e-s,a,a),u=i.length-1;0<=u;u--){var h=i[u];h===n||h.ignore||h.ignoreCoarsePointer||h.parent&&h.parent.ignoreCoarsePointer||(an.copy(h.getBoundingRect()),h.transform&&an.applyTransform(h.transform),an.intersect(l)&&o.push(h))}if(o.length)for(var c=Math.PI/12,p=2*Math.PI,d=0;d<s;d+=4)for(var f=0;f<p;f+=c)if(un(o,r,t+d*Math.cos(f),e+d*Math.sin(f),n),r.target)return r}return r},ln.prototype.processGesture=function(t,e){this._gestureMgr||(this._gestureMgr=new De);var n=this._gestureMgr,i=("start"===e&&n.clear(),n.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom));"end"===e&&n.clear(),i&&(e=i.type,t.gestureEvent=e,(n=new rn).target=i.target,this.dispatchToElement(n,e,i.event))},ln);function ln(t,e,n,i,r){var o=nn.call(this)||this;return o._hovered=new rn(0,0),o.storage=t,o.painter=e,o.painterRoot=i,o._pointerSize=r,n=n||new tn,o.proxy=null,o.setHandlerProxy(n),o._draggingMgr=new le(o),o}function un(t,e,n,i,r){for(var o=t.length-1;0<=o;o--){var a=t[o],s=void 0;if(a!==r&&!a.ignore&&(s=function(t,e,n){if(t[t.rectHover?"rectContain":"contain"](e,n)){for(var i=t,r=void 0,o=!1;i;){if(!(o=i.ignoreClip?!0:o)){var a=i.getClipPath();if(a&&!a.contain(e,n))return!1}i.silent&&(r=!0);a=i.__hostTarget,i=a||i.parent}return!r||$e}return!1}(a,n,i))&&(e.topTarget||(e.topTarget=a),s!==$e)){e.target=a;break}}}function hn(t,e,n){t=t.painter;return e<0||e>t.getWidth()||n<0||n>t.getHeight()}O(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(a){sn.prototype[a]=function(t){var e,n,i=t.zrX,r=t.zrY,o=hn(this,i,r);if("mouseup"===a&&o||(n=(e=this.findHover(i,r)).target),"mousedown"===a)this._downEl=n,this._downPoint=[t.zrX,t.zrY],this._upEl=n;else if("mouseup"===a)this._upEl=n;else if("click"===a){if(this._downEl!==this._upEl||!this._downPoint||4<Jt(this._downPoint,[t.zrX,t.zrY]))return;this._downPoint=null}this.dispatchToElement(e,a,t)}});var cn=32,pn=7;function dn(t,e,n,i){var r=e+1;if(r===n)return 1;if(i(t[r++],t[e])<0){for(;r<n&&i(t[r],t[r-1])<0;)r++;var o=t,a=e,s=r;for(s--;a<s;){var l=o[a];o[a++]=o[s],o[s--]=l}}else for(;r<n&&0<=i(t[r],t[r-1]);)r++;return r-e}function fn(t,e,n,i,r){for(i===e&&i++;i<n;i++){for(var o,a=t[i],s=e,l=i;s<l;)r(a,t[o=s+l>>>1])<0?l=o:s=1+o;var u=i-s;switch(u){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;0<u;)t[s+u]=t[s+u-1],u--}t[s]=a}}function gn(t,e,n,i,r,o){var a=0,s=0,l=1;if(0<o(t,e[n+r])){for(s=i-r;l<s&&0<o(t,e[n+r+l]);)(l=1+((a=l)<<1))<=0&&(l=s);s<l&&(l=s),a+=r,l+=r}else{for(s=r+1;l<s&&o(t,e[n+r-l])<=0;)(l=1+((a=l)<<1))<=0&&(l=s);i=a,a=r-(l=s<l?s:l),l=r-i}for(a++;a<l;){var u=a+(l-a>>>1);0<o(t,e[n+u])?a=u+1:l=u}return l}function yn(t,e,n,i,r,o){var a=0,s=0,l=1;if(o(t,e[n+r])<0){for(s=r+1;l<s&&o(t,e[n+r-l])<0;)(l=1+((a=l)<<1))<=0&&(l=s);var u=a,a=r-(l=s<l?s:l),l=r-u}else{for(s=i-r;l<s&&0<=o(t,e[n+r+l]);)(l=1+((a=l)<<1))<=0&&(l=s);s<l&&(l=s),a+=r,l+=r}for(a++;a<l;){var h=a+(l-a>>>1);o(t,e[n+h])<0?l=h:a=h+1}return l}function mn(L,A){var P,O,R=pn,N=0,E=[];function e(t){var e=P[t],n=O[t],i=P[t+1],r=O[t+1],t=(O[t]=n+r,t===N-3&&(P[t+1]=P[t+2],O[t+1]=O[t+2]),N--,yn(L[i],L,e,n,0,A));if(e+=t,0!=(n-=t)&&0!==(r=gn(L[e+n-1],L,i,r,r-1,A)))if(n<=r){var o=e,a=n,t=i,s=r,l=0;for(l=0;l<a;l++)E[l]=L[o+l];var u=0,h=t,c=o;if(L[c++]=L[h++],0==--s)for(l=0;l<a;l++)L[c+l]=E[u+l];else if(1===a){for(l=0;l<s;l++)L[c+l]=L[h+l];L[c+s]=E[u]}else{for(var p,d,f,g=R;;){d=p=0,f=!1;do{if(A(L[h],E[u])<0){if(L[c++]=L[h++],d++,(p=0)==--s){f=!0;break}}else if(L[c++]=E[u++],p++,d=0,1==--a){f=!0;break}}while((p|d)<g);if(f)break;do{if(0!==(p=yn(L[h],E,u,a,0,A))){for(l=0;l<p;l++)L[c+l]=E[u+l];if(c+=p,u+=p,(a-=p)<=1){f=!0;break}}if(L[c++]=L[h++],0==--s){f=!0;break}if(0!==(d=gn(E[u],L,h,s,0,A))){for(l=0;l<d;l++)L[c+l]=L[h+l];if(c+=d,h+=d,0===(s-=d)){f=!0;break}}if(L[c++]=E[u++],1==--a){f=!0;break}}while(g--,pn<=p||pn<=d);if(f)break;g<0&&(g=0),g+=2}if((R=g)<1&&(R=1),1===a){for(l=0;l<s;l++)L[c+l]=L[h+l];L[c+s]=E[u]}else{if(0===a)throw new Error;for(l=0;l<a;l++)L[c+l]=E[u+l]}}}else{var y=e,m=n,v=i,_=r,x=0;for(x=0;x<_;x++)E[x]=L[v+x];var w=y+m-1,b=_-1,S=v+_-1,M=0,T=0;if(L[S--]=L[w--],0==--m)for(M=S-(_-1),x=0;x<_;x++)L[M+x]=E[x];else if(1===_){for(T=(S-=m)+1,M=(w-=m)+1,x=m-1;0<=x;x--)L[T+x]=L[M+x];L[S]=E[b]}else{for(var C=R;;){var k=0,I=0,D=!1;do{if(A(E[b],L[w])<0){if(L[S--]=L[w--],k++,(I=0)==--m){D=!0;break}}else if(L[S--]=E[b--],I++,k=0,1==--_){D=!0;break}}while((k|I)<C);if(D)break;do{if(0!==(k=m-yn(E[b],L,y,m,m-1,A))){for(m-=k,T=(S-=k)+1,M=(w-=k)+1,x=k-1;0<=x;x--)L[T+x]=L[M+x];if(0===m){D=!0;break}}if(L[S--]=E[b--],1==--_){D=!0;break}if(0!==(I=_-gn(L[w],E,0,_,_-1,A))){for(_-=I,T=(S-=I)+1,M=(b-=I)+1,x=0;x<I;x++)L[T+x]=E[M+x];if(_<=1){D=!0;break}}if(L[S--]=L[w--],0==--m){D=!0;break}}while(C--,pn<=k||pn<=I);if(D)break;C<0&&(C=0),C+=2}if((R=C)<1&&(R=1),1===_){for(T=(S-=m)+1,M=(w-=m)+1,x=m-1;0<=x;x--)L[T+x]=L[M+x];L[S]=E[b]}else{if(0===_)throw new Error;for(M=S-(_-1),x=0;x<_;x++)L[M+x]=E[x]}}}}return P=[],O=[],{mergeRuns:function(){for(;1<N;){var t=N-2;if(1<=t&&O[t-1]<=O[t]+O[t+1]||2<=t&&O[t-2]<=O[t]+O[t-1])O[t-1]<O[t+1]&&t--;else if(O[t]>O[t+1])break;e(t)}},forceMergeRuns:function(){for(;1<N;){var t=N-2;0<t&&O[t-1]<O[t+1]&&t--,e(t)}},pushRun:function(t,e){P[N]=t,O[N]=e,N+=1}}}function vn(t,e,n,i){var r=(i=i||t.length)-(n=n||0);if(!(r<2)){var o=0;if(r<cn)fn(t,n,i,n+(o=dn(t,n,i,e)),e);else{var a,s=mn(t,e),l=function(t){for(var e=0;cn<=t;)e|=1&t,t>>=1;return t+e}(r);do{}while((o=dn(t,n,i,e))<l&&(fn(t,n,n+(a=l<(a=r)?l:r),n+o,e),o=a),s.pushRun(n,o),s.mergeRuns(),n+=o,0!==(r-=o));s.forceMergeRuns()}}}var _n=1,xn=4,wn=!1;function bn(){wn||(wn=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function Sn(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}Tn.prototype.traverse=function(t,e){for(var n=0;n<this._roots.length;n++)this._roots[n].traverse(t,e)},Tn.prototype.getDisplayList=function(t,e){e=e||!1;var n=this._displayList;return!t&&n.length||this.updateDisplayList(e),n},Tn.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,n=this._displayList,i=0,r=e.length;i<r;i++)this._updateAndAddDisplayable(e[i],null,t);n.length=this._displayListLen,vn(n,Sn)},Tn.prototype._updateAndAddDisplayable=function(t,e,n){if(!t.ignore||n){t.beforeUpdate(),t.update(),t.afterUpdate();var i=t.getClipPath();if(t.ignoreClip)e=null;else if(i){e=e?e.slice():[];for(var r=i,o=t;r;)r.parent=o,r.updateTransform(),e.push(r),r=(o=r).getClipPath()}if(t.childrenRef){for(var a=t.childrenRef(),s=0;s<a.length;s++){var l=a[s];t.__dirty&&(l.__dirty|=_n),this._updateAndAddDisplayable(l,e,n)}t.__dirty=0}else{i=t;e&&e.length?i.__clipPaths=e:i.__clipPaths&&0<i.__clipPaths.length&&(i.__clipPaths=[]),isNaN(i.z)&&(bn(),i.z=0),isNaN(i.z2)&&(bn(),i.z2=0),isNaN(i.zlevel)&&(bn(),i.zlevel=0),this._displayList[this._displayListLen++]=i}i=t.getDecalElement&&t.getDecalElement(),i=(i&&this._updateAndAddDisplayable(i,e,n),t.getTextGuideLine()),i=(i&&this._updateAndAddDisplayable(i,e,n),t.getTextContent());i&&this._updateAndAddDisplayable(i,e,n)}},Tn.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},Tn.prototype.delRoot=function(t){if(t instanceof Array)for(var e=0,n=t.length;e<n;e++)this.delRoot(t[e]);else{var i=k(this._roots,t);0<=i&&this._roots.splice(i,1)}},Tn.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},Tn.prototype.getRoots=function(){return this._roots},Tn.prototype.dispose=function(){this._displayList=null,this._roots=null};var Mn=Tn;function Tn(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=Sn}var Cn=p.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){return setTimeout(t,16)},kn={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),-(n*Math.pow(2,10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)))},elasticOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),n*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/.4)+1)},elasticInOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),(t*=2)<1?n*Math.pow(2,10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)*-.5:n*Math.pow(2,-10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)*.5+1)},backIn:function(t){return t*t*(2.70158*t-1.70158)},backOut:function(t){return--t*t*(2.70158*t+1.70158)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((1+e)*t-e)*.5:.5*((t-=2)*t*((1+e)*t+e)+2)},bounceIn:function(t){return 1-kn.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*kn.bounceIn(2*t):.5*kn.bounceOut(2*t-1)+.5}},In=Math.pow,Dn=Math.sqrt,Ln=1e-8,An=Dn(3),Pn=1/3,On=Ut(),Rn=Ut(),Nn=Ut();function En(t){return-Ln<t&&t<Ln}function Bn(t){return Ln<t||t<-Ln}function zn(t,e,n,i,r){var o=1-r;return o*o*(o*t+3*r*e)+r*r*(r*i+3*o*n)}function Fn(t,e,n,i,r){var o=1-r;return 3*(((e-t)*o+2*(n-e)*r)*o+(i-n)*r*r)}function Vn(t,e,n,i,r,o){var a,s,i=i+3*(e-n)-t,n=3*(n-2*e+t),e=3*(e-t),t=t-r,r=n*n-3*i*e,l=n*e-9*i*t,t=e*e-3*n*t,u=0;return En(r)&&En(l)?En(n)?o[0]=0:0<=(a=-e/n)&&a<=1&&(o[u++]=a):En(e=l*l-4*r*t)?(s=-(t=l/r)/2,0<=(a=-n/i+t)&&a<=1&&(o[u++]=a),0<=s&&s<=1&&(o[u++]=s)):0<e?(e=r*n+1.5*i*(-l-(t=Dn(e))),0<=(a=(-n-((t=(t=r*n+1.5*i*(-l+t))<0?-In(-t,Pn):In(t,Pn))+(e=e<0?-In(-e,Pn):In(e,Pn))))/(3*i))&&a<=1&&(o[u++]=a)):(t=(2*r*n-3*i*l)/(2*Dn(r*r*r)),e=Math.acos(t)/3,a=(-n-2*(l=Dn(r))*(t=Math.cos(e)))/(3*i),s=(-n+l*(t+An*Math.sin(e)))/(3*i),r=(-n+l*(t-An*Math.sin(e)))/(3*i),0<=a&&a<=1&&(o[u++]=a),0<=s&&s<=1&&(o[u++]=s),0<=r&&r<=1&&(o[u++]=r)),u}function Hn(t,e,n,i,r){var o,a=6*n-12*e+6*t,i=9*e+3*i-3*t-9*n,n=3*e-3*t,e=0;return En(i)?Bn(a)&&0<=(o=-n/a)&&o<=1&&(r[e++]=o):En(t=a*a-4*i*n)?r[0]=-a/(2*i):0<t&&(t=(-a-(n=Dn(t)))/(2*i),0<=(o=(-a+n)/(2*i))&&o<=1&&(r[e++]=o),0<=t)&&t<=1&&(r[e++]=t),e}function Wn(t,e,n,i,r,o){var a=(e-t)*r+t,e=(n-e)*r+e,n=(i-n)*r+n,s=(e-a)*r+a,e=(n-e)*r+e,r=(e-s)*r+s;o[0]=t,o[1]=a,o[2]=s,o[3]=r,o[4]=r,o[5]=e,o[6]=n,o[7]=i}function Gn(t,e,n,i,r,o,a,s,l,u,h){var c,p,d,f,g=.005,y=1/0;On[0]=l,On[1]=u;for(var m=0;m<1;m+=.05)Rn[0]=zn(t,n,r,a,m),Rn[1]=zn(e,i,o,s,m),(d=ee(On,Rn))<y&&(c=m,y=d);for(var y=1/0,v=0;v<32&&!(g<1e-4);v++)p=c+g,Rn[0]=zn(t,n,r,a,f=c-g),Rn[1]=zn(e,i,o,s,f),d=ee(Rn,On),0<=f&&d<y?(c=f,y=d):(Nn[0]=zn(t,n,r,a,p),Nn[1]=zn(e,i,o,s,p),f=ee(Nn,On),p<=1&&f<y?(c=p,y=f):g*=.5);return h&&(h[0]=zn(t,n,r,a,c),h[1]=zn(e,i,o,s,c)),Dn(y)}function Un(t,e,n,i){var r=1-i;return r*(r*t+2*i*e)+i*i*n}function qn(t,e,n,i){return 2*((1-i)*(e-t)+i*(n-e))}function Xn(t,e,n){n=t+n-2*e;return 0==n?.5:(t-e)/n}function Yn(t,e,n,i,r){var o=(e-t)*i+t,e=(n-e)*i+e,i=(e-o)*i+o;r[0]=t,r[1]=o,r[2]=i,r[3]=i,r[4]=e,r[5]=n}function jn(t,e,n,i,r,o,a,s,l){var u,h=.005,c=1/0;On[0]=a,On[1]=s;for(var p=0;p<1;p+=.05)Rn[0]=Un(t,n,r,p),Rn[1]=Un(e,i,o,p),(y=ee(On,Rn))<c&&(u=p,c=y);for(var c=1/0,d=0;d<32&&!(h<1e-4);d++){var f=u-h,g=u+h,y=(Rn[0]=Un(t,n,r,f),Rn[1]=Un(e,i,o,f),ee(Rn,On));0<=f&&y<c?(u=f,c=y):(Nn[0]=Un(t,n,r,g),Nn[1]=Un(e,i,o,g),f=ee(Nn,On),g<=1&&f<c?(u=g,c=f):h*=.5)}return l&&(l[0]=Un(t,n,r,u),l[1]=Un(e,i,o,u)),Dn(c)}var Zn=/cubic-bezier\(([0-9,\.e ]+)\)/;function Kn(t){t=t&&Zn.exec(t);if(t){var e,t=t[1].split(","),n=+It(t[0]),i=+It(t[1]),r=+It(t[2]),o=+It(t[3]);if(!isNaN(n+i+r+o))return e=[],function(t){return t<=0?0:1<=t?1:Vn(0,n,r,1,t,e)&&zn(0,i,o,1,e[0])}}}Qn.prototype.step=function(t,e){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),!this._paused){var n=this._life,i=t-this._startTime-this._pausedTime,r=i/n,o=(r<0&&(r=0),r=Math.min(r,1),this.easingFunc),o=o?o(r):r;if(this.onframe(o),1===r){if(!this.loop)return!0;this._startTime=t-i%n,this._pausedTime=0,this.onrestart()}return!1}this._pausedTime+=e},Qn.prototype.pause=function(){this._paused=!0},Qn.prototype.resume=function(){this._paused=!1},Qn.prototype.setEasing=function(t){this.easing=t,this.easingFunc=I(t)?t:kn[t]||Kn(t)};var $n=Qn;function Qn(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||Ht,this.ondestroy=t.ondestroy||Ht,this.onrestart=t.onrestart||Ht,t.easing&&this.setEasing(t.easing)}var Jn=function(t){this.value=t},ti=(ei.prototype.insert=function(t){t=new Jn(t);return this.insertEntry(t),t},ei.prototype.insertEntry=function(t){this.head?((this.tail.next=t).prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},ei.prototype.remove=function(t){var e=t.prev,n=t.next;e?e.next=n:this.head=n,n?n.prev=e:this.tail=e,t.next=t.prev=null,this._len--},ei.prototype.len=function(){return this._len},ei.prototype.clear=function(){this.head=this.tail=null,this._len=0},ei);function ei(){this._len=0}ii.prototype.put=function(t,e){var n,i,r=this._list,o=this._map,a=null;return null==o[t]&&(i=r.len(),n=this._lastRemovedEntry,i>=this._maxSize&&0<i&&(i=r.head,r.remove(i),delete o[i.key],a=i.value,this._lastRemovedEntry=i),n?n.value=e:n=new Jn(e),n.key=t,r.insertEntry(n),o[t]=n),a},ii.prototype.get=function(t){var t=this._map[t],e=this._list;if(null!=t)return t!==e.tail&&(e.remove(t),e.insertEntry(t)),t.value},ii.prototype.clear=function(){this._list.clear(),this._map={}},ii.prototype.len=function(){return this._list.len()};var ni=ii;function ii(t){this._list=new ti,this._maxSize=10,this._map={},this._maxSize=t}var ri={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function oi(t){return(t=Math.round(t))<0?0:255<t?255:t}function ai(t){return t<0?0:1<t?1:t}function si(t){return t.length&&"%"===t.charAt(t.length-1)?oi(parseFloat(t)/100*255):oi(parseInt(t,10))}function li(t){return t.length&&"%"===t.charAt(t.length-1)?ai(parseFloat(t)/100):ai(parseFloat(t))}function ui(t,e,n){return n<0?n+=1:1<n&&--n,6*n<1?t+(e-t)*n*6:2*n<1?e:3*n<2?t+(e-t)*(2/3-n)*6:t}function hi(t,e,n){return t+(e-t)*n}function ci(t,e,n,i,r){return t[0]=e,t[1]=n,t[2]=i,t[3]=r,t}function pi(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}var di=new ni(20),fi=null;function gi(t,e){fi&&pi(fi,e),fi=di.put(t,fi||e.slice())}function yi(t,e){if(t){e=e||[];var n=di.get(t);if(n)return pi(e,n);n=(t+="").replace(/ /g,"").toLowerCase();if(n in ri)return pi(e,ri[n]),gi(t,e),e;var i=n.length;if("#"===n.charAt(0))return 4===i||5===i?0<=(r=parseInt(n.slice(1,4),16))&&r<=4095?(ci(e,(3840&r)>>4|(3840&r)>>8,240&r|(240&r)>>4,15&r|(15&r)<<4,5===i?parseInt(n.slice(4),16)/15:1),gi(t,e),e):void ci(e,0,0,0,1):7===i||9===i?0<=(r=parseInt(n.slice(1,7),16))&&r<=16777215?(ci(e,(16711680&r)>>16,(65280&r)>>8,255&r,9===i?parseInt(n.slice(7),16)/255:1),gi(t,e),e):void ci(e,0,0,0,1):void 0;var r=n.indexOf("("),o=n.indexOf(")");if(-1!==r&&o+1===i){var i=n.substr(0,r),a=n.substr(r+1,o-(r+1)).split(","),s=1;switch(i){case"rgba":if(4!==a.length)return 3===a.length?ci(e,+a[0],+a[1],+a[2],1):ci(e,0,0,0,1);s=li(a.pop());case"rgb":return 3<=a.length?(ci(e,si(a[0]),si(a[1]),si(a[2]),3===a.length?s:li(a[3])),gi(t,e),e):void ci(e,0,0,0,1);case"hsla":return 4!==a.length?void ci(e,0,0,0,1):(a[3]=li(a[3]),mi(a,e),gi(t,e),e);case"hsl":return 3!==a.length?void ci(e,0,0,0,1):(mi(a,e),gi(t,e),e);default:return}}ci(e,0,0,0,1)}}function mi(t,e){var n=(parseFloat(t[0])%360+360)%360/360,i=li(t[1]),r=li(t[2]),i=r<=.5?r*(i+1):r+i-r*i,r=2*r-i;return ci(e=e||[],oi(255*ui(r,i,n+1/3)),oi(255*ui(r,i,n)),oi(255*ui(r,i,n-1/3)),1),4===t.length&&(e[3]=t[3]),e}function vi(t,e){var n=yi(t);if(n){for(var i=0;i<3;i++)n[i]=e<0?n[i]*(1-e)|0:(255-n[i])*e+n[i]|0,255<n[i]?n[i]=255:n[i]<0&&(n[i]=0);return bi(n,4===n.length?"rgba":"rgb")}}function _i(t,e,n){var i,r,o;if(e&&e.length&&0<=t&&t<=1)return n=n||[],t=t*(e.length-1),i=Math.floor(t),o=Math.ceil(t),r=e[i],e=e[o],n[0]=oi(hi(r[0],e[0],o=t-i)),n[1]=oi(hi(r[1],e[1],o)),n[2]=oi(hi(r[2],e[2],o)),n[3]=ai(hi(r[3],e[3],o)),n}var xi=_i;function wi(t,e,n){var i,r,o,a;if(e&&e.length&&0<=t&&t<=1)return t=t*(e.length-1),i=Math.floor(t),r=Math.ceil(t),a=yi(e[i]),e=yi(e[r]),a=bi([oi(hi(a[0],e[0],o=t-i)),oi(hi(a[1],e[1],o)),oi(hi(a[2],e[2],o)),ai(hi(a[3],e[3],o))],"rgba"),n?{color:a,leftIndex:i,rightIndex:r,value:t}:a}var n=wi;function bi(t,e){var n;if(t&&t.length)return n=t[0]+","+t[1]+","+t[2],"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(n+=","+t[3]),e+"("+n+")"}function Si(t,e){t=yi(t);return t?(.299*t[0]+.587*t[1]+.114*t[2])*t[3]/255+(1-t[3])*e:0}var Mi=new ni(100);function Ti(t){var e;return V(t)?((e=Mi.get(t))||(e=vi(t,-.1),Mi.put(t,e)),e):_t(t)?((e=P({},t)).colorStops=z(t.colorStops,function(t){return{offset:t.offset,color:vi(t.color,-.1)}}),e):t}xi=Object.freeze({__proto__:null,fastLerp:_i,fastMapToColor:xi,lerp:wi,lift:vi,liftColor:Ti,lum:Si,mapToColor:n,modifyAlpha:function(t,e){if((t=yi(t))&&null!=e)return t[3]=ai(e),bi(t,"rgba")},modifyHSL:function(t,e,n,i){var r=yi(t);if(t)return r=function(t){var e,n,i,r,o,a,s,l,u,h;if(t)return h=t[0]/255,e=t[1]/255,n=t[2]/255,s=Math.min(h,e,n),r=((i=Math.max(h,e,n))+s)/2,0==(u=i-s)?a=o=0:(a=r<.5?u/(i+s):u/(2-i-s),s=((i-h)/6+u/2)/u,l=((i-e)/6+u/2)/u,u=((i-n)/6+u/2)/u,h===i?o=u-l:e===i?o=1/3+s-u:n===i&&(o=2/3+l-s),o<0&&(o+=1),1<o&&--o),h=[360*o,a,r],null!=t[3]&&h.push(t[3]),h}(r),null!=e&&(r[0]=(t=e,(t=Math.round(t))<0?0:360<t?360:t)),null!=n&&(r[1]=li(n)),null!=i&&(r[2]=li(i)),bi(mi(r),"rgba")},parse:yi,random:function(){return bi([Math.round(255*Math.random()),Math.round(255*Math.random()),Math.round(255*Math.random())],"rgb")},stringify:bi,toHex:function(t){if(t=yi(t))return((1<<24)+(t[0]<<16)+(t[1]<<8)+ +t[2]).toString(16).slice(1)}});p.hasGlobalWindow&&I(window.btoa);var Ci=Array.prototype.slice;function ki(t,e,n){return(e-t)*n+t}function Ii(t,e,n,i){for(var r=e.length,o=0;o<r;o++)t[o]=ki(e[o],n[o],i);return t}function Di(t,e,n,i){for(var r=e.length,o=0;o<r;o++)t[o]=e[o]+n[o]*i;return t}function Li(t,e,n,i){for(var r=e.length,o=r&&e[0].length,a=0;a<r;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=e[a][s]+n[a][s]*i}return t}function Ai(t){if(st(t)){var e=t.length;if(st(t[0])){for(var n=[],i=0;i<e;i++)n.push(Ci.call(t[i]));return n}return Ci.call(t)}return t}function Pi(t){return t[0]=Math.floor(t[0])||0,t[1]=Math.floor(t[1])||0,t[2]=Math.floor(t[2])||0,t[3]=null==t[3]?1:t[3],"rgba("+t.join(",")+")"}function Oi(t){return 4===t||5===t}function Ri(t){return 1===t||2===t}var Ni=[0,0,0,0],Ei=(Bi.prototype.isFinished=function(){return this._finished},Bi.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},Bi.prototype.needsAnimate=function(){return 1<=this.keyframes.length},Bi.prototype.getAdditiveTrack=function(){return this._additiveTrack},Bi.prototype.addKeyframe=function(t,e,n){this._needsSort=!0;var i,r=this.keyframes,o=r.length,a=!1,s=6,l=e,u=(st(e)?(1==(s=i=st((i=e)&&i[0])?2:1)&&!gt(e[0])||2==i&&!gt(e[0][0]))&&(a=!0):gt(e)&&!bt(e)?s=0:V(e)?isNaN(+e)?(i=yi(e))&&(l=i,s=3):s=0:_t(e)&&((u=P({},l)).colorStops=z(e.colorStops,function(t){return{offset:t.offset,color:yi(t.color)}}),"linear"===e.type?s=4:"radial"===e.type&&(s=5),l=u),0===o?this.valType=s:s===this.valType&&6!==s||(a=!0),this.discrete=this.discrete||a,{time:t,value:l,rawValue:e,percent:0});return n&&(u.easing=n,u.easingFunc=I(n)?n:kn[n]||Kn(n)),r.push(u),u},Bi.prototype.prepare=function(t,e){for(var n=this.keyframes,i=(this._needsSort&&n.sort(function(t,e){return t.time-e.time}),this.valType),r=n.length,o=n[r-1],a=this.discrete,s=Ri(i),l=Oi(i),u=0;u<r;u++){var h=n[u],c=h.value,p=o.value;if(h.percent=h.time/t,!a)if(s&&u!==r-1){x=_=v=m=y=g=f=d=h=void 0;var d=p,f=i,g=h=c,y=d;if(g.push&&y.push){var h=g.length,m=y.length;if(h!==m)if(m<h)g.length=m;else for(var v=h;v<m;v++)g.push(1===f?y[v]:Ci.call(y[v]));for(var _=g[0]&&g[0].length,v=0;v<g.length;v++)if(1===f)isNaN(g[v])&&(g[v]=y[v]);else for(var x=0;x<_;x++)isNaN(g[v][x])&&(g[v][x]=y[v][x])}}else if(l){T=M=S=b=w=h=d=void 0;for(var d=c.colorStops,h=p.colorStops,w=d.length,b=h.length,S=b<w?h:d,h=Math.min(w,b),M=S[h-1]||{color:[0,0,0,0],offset:0},T=h;T<Math.max(w,b);T++)S.push({offset:M.offset,color:M.color.slice()})}}if(!a&&5!==i&&e&&this.needsAnimate()&&e.needsAnimate()&&i===e.valType&&!e._finished){this._additiveTrack=e;for(var C=n[0].value,u=0;u<r;u++)0===i?n[u].additiveValue=n[u].value-C:3===i?n[u].additiveValue=Di([],n[u].value,C,-1):Ri(i)&&(n[u].additiveValue=(1===i?Di:Li)([],n[u].value,C,-1))}},Bi.prototype.step=function(t,e){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var n,i,r,o,a=null!=this._additiveTrack,s=a?"additiveValue":"value",l=this.valType,u=this.keyframes,h=u.length,c=this.propName,p=3===l,d=this._lastFr,f=Math.min;if(1===h)n=i=u[0];else{if(e<0)g=0;else if(e<this._lastFrP){for(var g=f(d+1,h-1);0<=g&&!(u[g].percent<=e);g--);g=f(g,h-2)}else{for(g=d;g<h&&!(u[g].percent>e);g++);g=f(g-1,h-2)}i=u[g+1],n=u[g]}n&&i&&(this._lastFr=g,this._lastFrP=e,d=i.percent-n.percent,r=0==d?1:f((e-n.percent)/d,1),i.easingFunc&&(r=i.easingFunc(r)),f=a?this._additiveValue:p?Ni:t[c],(Ri(l)||p)&&(f=f||(this._additiveValue=[])),this.discrete?t[c]=(r<1?n:i).rawValue:Ri(l)?(1===l?Ii:function(t,e,n,i){for(var r=e.length,o=r&&e[0].length,a=0;a<r;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=ki(e[a][s],n[a][s],i)}})(f,n[s],i[s],r):Oi(l)?(d=n[s],o=i[s],t[c]={type:(l=4===l)?"linear":"radial",x:ki(d.x,o.x,r),y:ki(d.y,o.y,r),colorStops:z(d.colorStops,function(t,e){e=o.colorStops[e];return{offset:ki(t.offset,e.offset,r),color:Pi(Ii([],t.color,e.color,r))}}),global:o.global},l?(t[c].x2=ki(d.x2,o.x2,r),t[c].y2=ki(d.y2,o.y2,r)):t[c].r=ki(d.r,o.r,r)):p?(Ii(f,n[s],i[s],r),a||(t[c]=Pi(f))):(l=ki(n[s],i[s],r),a?this._additiveValue=l:t[c]=l),a)&&this._addToTarget(t)}},Bi.prototype._addToTarget=function(t){var e=this.valType,n=this.propName,i=this._additiveValue;0===e?t[n]=t[n]+i:3===e?(yi(t[n],Ni),Di(Ni,Ni,i,1),t[n]=Pi(Ni)):1===e?Di(t[n],t[n],i,1):2===e&&Li(t[n],t[n],i,1)},Bi);function Bi(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}i.prototype.getMaxTime=function(){return this._maxTime},i.prototype.getDelay=function(){return this._delay},i.prototype.getLoop=function(){return this._loop},i.prototype.getTarget=function(){return this._target},i.prototype.changeTarget=function(t){this._target=t},i.prototype.when=function(t,e,n){return this.whenWithKeys(t,e,ct(e),n)},i.prototype.whenWithKeys=function(t,e,n,i){for(var r=this._tracks,o=0;o<n.length;o++){var a=n[o];if(!(l=r[a])){var s,l=r[a]=new Ei(a),u=void 0,h=this._getAdditiveTrack(a);if(h?(u=(s=(s=h.keyframes)[s.length-1])&&s.value,3===h.valType&&(u=u&&Pi(u))):u=this._target[a],null==u)continue;0<t&&l.addKeyframe(0,Ai(u),i),this._trackKeys.push(a)}l.addKeyframe(t,Ai(e[a]),i)}return this._maxTime=Math.max(this._maxTime,t),this},i.prototype.pause=function(){this._clip.pause(),this._paused=!0},i.prototype.resume=function(){this._clip.resume(),this._paused=!1},i.prototype.isPaused=function(){return!!this._paused},i.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},i.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var e=t.length,n=0;n<e;n++)t[n].call(this)},i.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var n=0;n<e.length;n++)e[n].call(this)},i.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,n=0;n<e.length;n++)t[e[n]].setFinished()},i.prototype._getAdditiveTrack=function(t){var e,n=this._additiveAnimators;if(n)for(var i=0;i<n.length;i++){var r=n[i].getTrack(t);r&&(e=r)}return e},i.prototype.start=function(t){if(!(0<this._started)){this._started=1;for(var e,o=this,a=[],n=this._maxTime||0,i=0;i<this._trackKeys.length;i++){var r=this._trackKeys[i],s=this._tracks[r],r=this._getAdditiveTrack(r),l=s.keyframes,u=l.length;s.prepare(n,r),s.needsAnimate()&&(!this._allowDiscrete&&s.discrete?((r=l[u-1])&&(o._target[s.propName]=r.rawValue),s.setFinished()):a.push(s))}return a.length||this._force?(e=new $n({life:n,loop:this._loop,delay:this._delay||0,onframe:function(t){o._started=2;var e=o._additiveAnimators;if(e){for(var n=!1,i=0;i<e.length;i++)if(e[i]._clip){n=!0;break}n||(o._additiveAnimators=null)}for(i=0;i<a.length;i++)a[i].step(o._target,t);var r=o._onframeCbs;if(r)for(i=0;i<r.length;i++)r[i](o._target,t)},ondestroy:function(){o._doneCallback()}}),this._clip=e,this.animation&&this.animation.addClip(e),t&&e.setEasing(t)):this._doneCallback(),this}},i.prototype.stop=function(t){var e;this._clip&&(e=this._clip,t&&e.onframe(1),this._abortedCallback())},i.prototype.delay=function(t){return this._delay=t,this},i.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},i.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},i.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},i.prototype.getClip=function(){return this._clip},i.prototype.getTrack=function(t){return this._tracks[t]},i.prototype.getTracks=function(){var e=this;return z(this._trackKeys,function(t){return e._tracks[t]})},i.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var n=this._tracks,i=this._trackKeys,r=0;r<t.length;r++){var o=n[t[r]];o&&!o.isFinished()&&(e?o.step(this._target,1):1===this._started&&o.step(this._target,0),o.setFinished())}for(var a=!0,r=0;r<i.length;r++)if(!n[i[r]].isFinished()){a=!1;break}return a&&this._abortedCallback(),a},i.prototype.saveTo=function(t,e,n){if(t){e=e||this._trackKeys;for(var i=0;i<e.length;i++){var r=e[i],o=this._tracks[r];o&&!o.isFinished()&&(o=(o=o.keyframes)[n?0:o.length-1])&&(t[r]=Ai(o.rawValue))}}},i.prototype.__changeFinalValue=function(t,e){e=e||ct(t);for(var n=0;n<e.length;n++){var i,r=e[n],o=this._tracks[r];o&&1<(i=o.keyframes).length&&(i=i.pop(),o.addKeyframe(i.time,t[r]),o.prepare(this._maxTime,o.getAdditiveTrack()))}};var zi=i;function i(t,e,n,i){this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,(this._loop=e)&&i?it("Can' use additive animation on looped animation."):(this._additiveAnimators=i,this._allowDiscrete=n)}function Fi(){return(new Date).getTime()}a(Wi,Vi=he),Wi.prototype.addClip=function(t){t.animation&&this.removeClip(t),this._head?((this._tail.next=t).prev=this._tail,t.next=null,this._tail=t):this._head=this._tail=t,t.animation=this},Wi.prototype.addAnimator=function(t){t.animation=this;t=t.getClip();t&&this.addClip(t)},Wi.prototype.removeClip=function(t){var e,n;t.animation&&(e=t.prev,n=t.next,e?e.next=n:this._head=n,n?n.prev=e:this._tail=e,t.next=t.prev=t.animation=null)},Wi.prototype.removeAnimator=function(t){var e=t.getClip();e&&this.removeClip(e),t.animation=null},Wi.prototype.update=function(t){for(var e=Fi()-this._pausedTime,n=e-this._time,i=this._head;i;)var r=i.next,i=(i.step(e,n)&&(i.ondestroy(),this.removeClip(i)),r);this._time=e,t||(this.trigger("frame",n),this.stage.update&&this.stage.update())},Wi.prototype._startLoop=function(){var e=this;this._running=!0,Cn(function t(){e._running&&(Cn(t),e._paused||e.update())})},Wi.prototype.start=function(){this._running||(this._time=Fi(),this._pausedTime=0,this._startLoop())},Wi.prototype.stop=function(){this._running=!1},Wi.prototype.pause=function(){this._paused||(this._pauseStart=Fi(),this._paused=!0)},Wi.prototype.resume=function(){this._paused&&(this._pausedTime+=Fi()-this._pauseStart,this._paused=!1)},Wi.prototype.clear=function(){for(var t=this._head;t;){var e=t.next;t.prev=t.next=t.animation=null,t=e}this._head=this._tail=null},Wi.prototype.isFinished=function(){return null==this._head},Wi.prototype.animate=function(t,e){e=e||{},this.start();t=new zi(t,e.loop);return this.addAnimator(t),t};var Vi,Hi=Wi;function Wi(t){var e=Vi.call(this)||this;return e._running=!1,e._time=0,e._pausedTime=0,e._pauseStart=0,e._paused=!1,e.stage=(t=t||{}).stage||{},e}var Gi,Ui=p.domSupported,qi=(Gi={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},{mouse:n=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],touch:["touchstart","touchend","touchmove"],pointer:z(n,function(t){var e=t.replace("mouse","pointer");return Gi.hasOwnProperty(e)?e:t})}),Xi=["mousemove","mouseup"],Yi=["pointermove","pointerup"],ji=!1;function Zi(t){t=t.pointerType;return"pen"===t||"touch"===t}function Ki(t){t&&(t.zrByTouch=!0)}function $i(t,e){for(var n=e,i=!1;n&&9!==n.nodeType&&!(i=n.domBelongToZr||n!==e&&n===t.painterRoot);)n=n.parentNode;return i}var Qi=function(t,e){this.stopPropagation=Ht,this.stopImmediatePropagation=Ht,this.preventDefault=Ht,this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY},Ji={mousedown:function(t){t=ke(this.dom,t),this.__mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=ke(this.dom,t);var e=this.__mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||this.__togglePointerCapture(!0),this.trigger("mousemove",t)},mouseup:function(t){t=ke(this.dom,t),this.__togglePointerCapture(!1),this.trigger("mouseup",t)},mouseout:function(t){$i(this,(t=ke(this.dom,t)).toElement||t.relatedTarget)||(this.__pointerCapturing&&(t.zrEventControl="no_globalout"),this.trigger("mouseout",t))},wheel:function(t){ji=!0,t=ke(this.dom,t),this.trigger("mousewheel",t)},mousewheel:function(t){ji||(t=ke(this.dom,t),this.trigger("mousewheel",t))},touchstart:function(t){Ki(t=ke(this.dom,t)),this.__lastTouchMoment=new Date,this.handler.processGesture(t,"start"),Ji.mousemove.call(this,t),Ji.mousedown.call(this,t)},touchmove:function(t){Ki(t=ke(this.dom,t)),this.handler.processGesture(t,"change"),Ji.mousemove.call(this,t)},touchend:function(t){Ki(t=ke(this.dom,t)),this.handler.processGesture(t,"end"),Ji.mouseup.call(this,t),+new Date-+this.__lastTouchMoment<300&&Ji.click.call(this,t)},pointerdown:function(t){Ji.mousedown.call(this,t)},pointermove:function(t){Zi(t)||Ji.mousemove.call(this,t)},pointerup:function(t){Ji.mouseup.call(this,t)},pointerout:function(t){Zi(t)||Ji.mouseout.call(this,t)}},tr=(O(["click","dblclick","contextmenu"],function(e){Ji[e]=function(t){t=ke(this.dom,t),this.trigger(e,t)}}),{pointermove:function(t){Zi(t)||tr.mousemove.call(this,t)},pointerup:function(t){tr.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}});function er(i,r){var o=r.domHandlers;p.pointerEventsSupported?O(qi.pointer,function(e){ir(r,e,function(t){o[e].call(i,t)})}):(p.touchEventsSupported&&O(qi.touch,function(n){ir(r,n,function(t){var e;o[n].call(i,t),(e=r).touching=!0,null!=e.touchTimer&&(clearTimeout(e.touchTimer),e.touchTimer=null),e.touchTimer=setTimeout(function(){e.touching=!1,e.touchTimer=null},700)})}),O(qi.mouse,function(e){ir(r,e,function(t){t=Ce(t),r.touching||o[e].call(i,t)})}))}function nr(i,r){function t(n){ir(r,n,function(t){var e;t=Ce(t),$i(i,t.target)||(e=t,t=ke(i.dom,new Qi(i,e),!0),r.domHandlers[n].call(i,t))},{capture:!0})}p.pointerEventsSupported?O(Yi,t):p.touchEventsSupported||O(Xi,t)}function ir(t,e,n,i){t.mounted[e]=n,t.listenerOpts[e]=i,t.domTarget.addEventListener(e,n,i)}function rr(t){var e,n,i,r,o,a=t.mounted;for(e in a)a.hasOwnProperty(e)&&(n=t.domTarget,r=a[i=e],o=t.listenerOpts[e],n.removeEventListener(i,r,o));t.mounted={}}var or,ar=function(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e},sr=(a(lr,or=he),lr.prototype.dispose=function(){rr(this._localHandlerScope),Ui&&rr(this._globalHandlerScope)},lr.prototype.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},lr.prototype.__togglePointerCapture=function(t){var e;this.__mayPointerCapture=null,Ui&&+this.__pointerCapturing^+t&&(this.__pointerCapturing=t,e=this._globalHandlerScope,t?nr(this,e):rr(e))},lr);function lr(t,e){var n=or.call(this)||this;return n.__pointerCapturing=!1,n.dom=t,n.painterRoot=e,n._localHandlerScope=new ar(t,Ji),Ui&&(n._globalHandlerScope=new ar(document,tr)),er(n,n._localHandlerScope),n}var n=1,ur=n=p.hasGlobalWindow?Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1):n,hr="#333",cr="#ccc",pr=Re;function dr(t){return 5e-5<t||t<-5e-5}var fr=[],gr=[],yr=Oe(),mr=Math.abs,vr=(_r.prototype.getLocalTransform=function(t){return _r.getLocalTransform(this,t)},_r.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},_r.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},_r.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},_r.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},_r.prototype.needLocalTransform=function(){return dr(this.rotation)||dr(this.x)||dr(this.y)||dr(this.scaleX-1)||dr(this.scaleY-1)||dr(this.skewX)||dr(this.skewY)},_r.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),n=this.transform;e||t?(n=n||Oe(),e?this.getLocalTransform(n):pr(n),t&&(e?Ee(n,t,n):Ne(n,t)),this.transform=n,this._resolveGlobalScaleRatio(n)):n&&(pr(n),this.invTransform=null)},_r.prototype._resolveGlobalScaleRatio=function(t){var e,n,i=this.globalScaleRatio;null!=i&&1!==i&&(this.getGlobalScale(fr),n=((fr[1]-(n=fr[1]<0?-1:1))*i+n)/fr[1]||0,t[0]*=i=((fr[0]-(e=fr[0]<0?-1:1))*i+e)/fr[0]||0,t[1]*=i,t[2]*=n,t[3]*=n),this.invTransform=this.invTransform||Oe(),Ve(this.invTransform,t)},_r.prototype.getComputedTransform=function(){for(var t=this,e=[];t;)e.push(t),t=t.parent;for(;t=e.pop();)t.updateTransform();return this.transform},_r.prototype.setLocalTransform=function(t){var e,n,i,r;t&&(r=t[0]*t[0]+t[1]*t[1],i=t[2]*t[2]+t[3]*t[3],e=Math.atan2(t[1],t[0]),n=Math.PI/2+e-Math.atan2(t[3],t[2]),i=Math.sqrt(i)*Math.cos(n),r=Math.sqrt(r),this.skewX=n,this.skewY=0,this.rotation=-e,this.x=+t[4],this.y=+t[5],this.scaleX=r,this.scaleY=i,this.originX=0,this.originY=0)},_r.prototype.decomposeTransform=function(){var t,e,n;this.transform&&(e=this.parent,t=this.transform,e&&e.transform&&(e.invTransform=e.invTransform||Oe(),Ee(gr,e.invTransform,t),t=gr),e=this.originX,n=this.originY,(e||n)&&(yr[4]=e,yr[5]=n,Ee(gr,t,yr),gr[4]-=e,gr[5]-=n,t=gr),this.setLocalTransform(t))},_r.prototype.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1])):(t[0]=1,t[1]=1),t},_r.prototype.transformCoordToLocal=function(t,e){t=[t,e],e=this.invTransform;return e&&ie(t,t,e),t},_r.prototype.transformCoordToGlobal=function(t,e){t=[t,e],e=this.transform;return e&&ie(t,t,e),t},_r.prototype.getLineScale=function(){var t=this.transform;return t&&1e-10<mr(t[0]-1)&&1e-10<mr(t[3]-1)?Math.sqrt(mr(t[0]*t[3]-t[2]*t[1])):1},_r.prototype.copyTransform=function(t){for(var e=this,n=t,i=0;i<xr.length;i++){var r=xr[i];e[r]=n[r]}},_r.getLocalTransform=function(t,e){e=e||[];var n=t.originX||0,i=t.originY||0,r=t.scaleX,o=t.scaleY,a=t.anchorX,s=t.anchorY,l=t.rotation||0,u=t.x,h=t.y,c=t.skewX?Math.tan(t.skewX):0,t=t.skewY?Math.tan(-t.skewY):0;return n||i||a||s?(e[4]=-(a=n+a)*r-c*(s=i+s)*o,e[5]=-s*o-t*a*r):e[4]=e[5]=0,e[0]=r,e[3]=o,e[1]=t*r,e[2]=c*o,l&&ze(e,e,l),e[4]+=n+u,e[5]+=i+h,e},_r.initDefaultProps=((n=_r.prototype).scaleX=n.scaleY=n.globalScaleRatio=1,void(n.x=n.y=n.originX=n.originY=n.skewX=n.skewY=n.rotation=n.anchorX=n.anchorY=0)),_r);function _r(){}var xr=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];var wr={};function br(t,e){var n=wr[e=e||Z],i=(n=n||(wr[e]=new ni(500))).get(t);return null==i&&(i=H.measureText(t,e).width,n.put(t,i)),i}function Sr(t,e,n,i){t=br(t,e),e=kr(e),n=Tr(0,t,n),i=Cr(0,e,i);return new q(n,i,t,e)}function Mr(t,e,n,i){var r=((t||"")+"").split("\n");if(1===r.length)return Sr(r[0],e,n,i);for(var o=new q(0,0,0,0),a=0;a<r.length;a++){var s=Sr(r[a],e,n,i);0===a?o.copy(s):o.union(s)}return o}function Tr(t,e,n){return"right"===n?t-=e:"center"===n&&(t-=e/2),t}function Cr(t,e,n){return"middle"===n?t-=e/2:"bottom"===n&&(t-=e),t}function kr(t){return br("国",t)}function Ir(t,e){return"string"==typeof t?0<=t.lastIndexOf("%")?parseFloat(t)/100*e:parseFloat(t):t}function Dr(t,e,n){var i=e.position||"inside",r=null!=e.distance?e.distance:5,o=n.height,a=n.width,s=o/2,l=n.x,u=n.y,h="left",c="top";if(i instanceof Array)l+=Ir(i[0],n.width),u+=Ir(i[1],n.height),c=h=null;else switch(i){case"left":l-=r,u+=s,h="right",c="middle";break;case"right":l+=r+a,u+=s,c="middle";break;case"top":l+=a/2,u-=r,h="center",c="bottom";break;case"bottom":l+=a/2,u+=o+r,h="center";break;case"inside":l+=a/2,u+=s,h="center",c="middle";break;case"insideLeft":l+=r,u+=s,c="middle";break;case"insideRight":l+=a-r,u+=s,h="right",c="middle";break;case"insideTop":l+=a/2,u+=r,h="center";break;case"insideBottom":l+=a/2,u+=o-r,h="center",c="bottom";break;case"insideTopLeft":l+=r,u+=r;break;case"insideTopRight":l+=a-r,u+=r,h="right";break;case"insideBottomLeft":l+=r,u+=o-r,c="bottom";break;case"insideBottomRight":l+=a-r,u+=o-r,h="right",c="bottom"}return(t=t||{}).x=l,t.y=u,t.align=h,t.verticalAlign=c,t}var Lr,Ar="__zr_normal__",Pr=xr.concat(["ignore"]),Or=lt(xr,function(t,e){return t[e]=!0,t},{ignore:!1}),Rr={},Nr=new q(0,0,0,0),n=(r.prototype._init=function(t){this.attr(t)},r.prototype.drift=function(t,e,n){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var i=this.transform;(i=i||(this.transform=[1,0,0,1,0,0]))[4]+=t,i[5]+=e,this.decomposeTransform(),this.markRedraw()},r.prototype.beforeUpdate=function(){},r.prototype.afterUpdate=function(){},r.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},r.prototype.updateInnerText=function(t){var e,n,i,r,o,a,s,l,u,h,c=this._textContent;!c||c.ignore&&!t||(this.textConfig||(this.textConfig={}),l=(t=this.textConfig).local,i=n=void 0,r=!1,(e=c.innerTransformable).parent=l?this:null,h=!1,e.copyTransform(c),null!=t.position&&(u=Nr,t.layoutRect?u.copy(t.layoutRect):u.copy(this.getBoundingRect()),l||u.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(Rr,t,u):Dr(Rr,t,u),e.x=Rr.x,e.y=Rr.y,n=Rr.align,i=Rr.verticalAlign,o=t.origin)&&null!=t.rotation&&(s=a=void 0,s="center"===o?(a=.5*u.width,.5*u.height):(a=Ir(o[0],u.width),Ir(o[1],u.height)),h=!0,e.originX=-e.x+a+(l?0:u.x),e.originY=-e.y+s+(l?0:u.y)),null!=t.rotation&&(e.rotation=t.rotation),(o=t.offset)&&(e.x+=o[0],e.y+=o[1],h||(e.originX=-o[0],e.originY=-o[1])),a=null==t.inside?"string"==typeof t.position&&0<=t.position.indexOf("inside"):t.inside,s=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),h=u=l=void 0,a&&this.canBeInsideText()?(l=t.insideFill,u=t.insideStroke,null!=l&&"auto"!==l||(l=this.getInsideTextFill()),null!=u&&"auto"!==u||(u=this.getInsideTextStroke(l),h=!0)):(l=t.outsideFill,u=t.outsideStroke,null!=l&&"auto"!==l||(l=this.getOutsideFill()),null!=u&&"auto"!==u||(u=this.getOutsideStroke(l),h=!0)),(l=l||"#000")===s.fill&&u===s.stroke&&h===s.autoStroke&&n===s.align&&i===s.verticalAlign||(r=!0,s.fill=l,s.stroke=u,s.autoStroke=h,s.align=n,s.verticalAlign=i,c.setDefaultTextStyle(s)),c.__dirty|=_n,r&&c.dirtyStyle(!0))},r.prototype.canBeInsideText=function(){return!0},r.prototype.getInsideTextFill=function(){return"#fff"},r.prototype.getInsideTextStroke=function(t){return"#000"},r.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?cr:hr},r.prototype.getOutsideStroke=function(t){for(var e=this.__zr&&this.__zr.getBackgroundColor(),n="string"==typeof e&&yi(e),i=(n=n||[255,255,255,1])[3],r=this.__zr.isDarkMode(),o=0;o<3;o++)n[o]=n[o]*i+(r?0:255)*(1-i);return n[3]=1,bi(n,"rgba")},r.prototype.traverse=function(t,e){},r.prototype.attrKV=function(t,e){"textConfig"===t?this.setTextConfig(e):"textContent"===t?this.setTextContent(e):"clipPath"===t?this.setClipPath(e):"extra"===t?(this.extra=this.extra||{},P(this.extra,e)):this[t]=e},r.prototype.hide=function(){this.ignore=!0,this.markRedraw()},r.prototype.show=function(){this.ignore=!1,this.markRedraw()},r.prototype.attr=function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(R(t))for(var n=ct(t),i=0;i<n.length;i++){var r=n[i];this.attrKV(r,t[r])}return this.markRedraw(),this},r.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,n=0;n<this.animators.length;n++){var i=this.animators[n],r=i.__fromStateTransition;i.getLoop()||r&&r!==Ar||(r=(r=i.targetName)?e[r]:e,i.saveTo(r))}},r.prototype._innerSaveToNormal=function(t){var e=(e=this._normalState)||(this._normalState={});t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,Pr)},r.prototype._savePrimaryToNormal=function(t,e,n){for(var i=0;i<n.length;i++){var r=n[i];null==t[r]||r in e||(e[r]=this[r])}},r.prototype.hasState=function(){return 0<this.currentStates.length},r.prototype.getState=function(t){return this.states[t]},r.prototype.ensureState=function(t){var e=this.states;return e[t]||(e[t]={}),e[t]},r.prototype.clearStates=function(t){this.useState(Ar,!1,t)},r.prototype.useState=function(t,e,n,i){var r=t===Ar,o=this.hasState();if(o||!r){var a,o=this.currentStates,s=this.stateTransition;if(!(0<=k(o,t))||!e&&1!==o.length){if((a=(a=this.stateProxy&&!r?this.stateProxy(t):a)||this.states&&this.states[t])||r)return r||this.saveCurrentToNormalState(a),(o=!!(a&&a.hoverLayer||i))&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,a,this._normalState,e,!n&&!this.__inHover&&s&&0<s.duration,s),i=this._textContent,s=this._textGuide,i&&i.useState(t,e,n,o),s&&s.useState(t,e,n,o),r?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!o&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~_n),a;it("State "+t+" not exists.")}}},r.prototype.useStates=function(t,e,n){if(t.length){var i=[],r=this.currentStates,o=t.length,a=o===r.length;if(a)for(var s=0;s<o;s++)if(t[s]!==r[s]){a=!1;break}if(!a){for(s=0;s<o;s++){var l=t[s],u=void 0;(u=(u=this.stateProxy?this.stateProxy(l,t):u)||this.states[l])&&i.push(u)}var h=i[o-1],h=!!(h&&h.hoverLayer||n),n=(h&&this._toggleHoverLayerFlag(!0),this._mergeStates(i)),c=this.stateTransition,n=(this.saveCurrentToNormalState(n),this._applyStateObj(t.join(","),n,this._normalState,!1,!e&&!this.__inHover&&c&&0<c.duration,c),this._textContent),c=this._textGuide;n&&n.useStates(t,e,h),c&&c.useStates(t,e,h),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!h&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~_n)}}else this.clearStates()},r.prototype.isSilent=function(){for(var t=this.silent,e=this.parent;!t&&e;){if(e.silent){t=!0;break}e=e.parent}return t},r.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},r.prototype.removeState=function(t){var e,t=k(this.currentStates,t);0<=t&&((e=this.currentStates.slice()).splice(t,1),this.useStates(e))},r.prototype.replaceState=function(t,e,n){var i=this.currentStates.slice(),t=k(i,t),r=0<=k(i,e);0<=t?r?i.splice(t,1):i[t]=e:n&&!r&&i.push(e),this.useStates(i)},r.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},r.prototype._mergeStates=function(t){for(var e,n={},i=0;i<t.length;i++){var r=t[i];P(n,r),r.textConfig&&P(e=e||{},r.textConfig)}return e&&(n.textConfig=e),n},r.prototype._applyStateObj=function(t,e,n,i,r,o){for(var a=!(e&&i),s=(e&&e.textConfig?(this.textConfig=P({},(i?this:n).textConfig),P(this.textConfig,e.textConfig)):a&&n.textConfig&&(this.textConfig=n.textConfig),{}),l=!1,u=0;u<Pr.length;u++){var h=Pr[u],c=r&&Or[h];e&&null!=e[h]?c?(l=!0,s[h]=e[h]):this[h]=e[h]:a&&null!=n[h]&&(c?(l=!0,s[h]=n[h]):this[h]=n[h])}if(!r)for(u=0;u<this.animators.length;u++){var p=this.animators[u],d=p.targetName;p.getLoop()||p.__changeFinalValue(d?(e||n)[d]:e||n)}l&&this._transitionState(t,s,o)},r.prototype._attachComponent=function(t){var e;t.__zr&&!t.__hostTarget||t!==this&&((e=this.__zr)&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this)},r.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},r.prototype.getClipPath=function(){return this._clipPath},r.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},r.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},r.prototype.getTextContent=function(){return this._textContent},r.prototype.setTextContent=function(t){var e=this._textContent;e!==t&&(e&&e!==t&&this.removeTextContent(),t.innerTransformable=new vr,this._attachComponent(t),this._textContent=t,this.markRedraw())},r.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),P(this.textConfig,t),this.markRedraw()},r.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},r.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},r.prototype.getTextGuideLine=function(){return this._textGuide},r.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},r.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},r.prototype.markRedraw=function(){this.__dirty|=_n;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},r.prototype.dirty=function(){this.markRedraw()},r.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,n=this._textGuide;e&&(e.__inHover=t),n&&(n.__inHover=t)},r.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.addAnimator(e[n]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},r.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.removeAnimator(e[n]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},r.prototype.animate=function(t,e,n){var i=t?this[t]:this,i=new zi(i,e,n);return t&&(i.targetName=t),this.addAnimator(i,t),i},r.prototype.addAnimator=function(n,t){var e=this.__zr,i=this;n.during(function(){i.updateDuringAnimation(t)}).done(function(){var t=i.animators,e=k(t,n);0<=e&&t.splice(e,1)}),this.animators.push(n),e&&e.animation.addAnimator(n),e&&e.wakeUp()},r.prototype.updateDuringAnimation=function(t){this.markRedraw()},r.prototype.stopAnimation=function(t,e){for(var n=this.animators,i=n.length,r=[],o=0;o<i;o++){var a=n[o];t&&t!==a.scope?r.push(a):a.stop(e)}return this.animators=r,this},r.prototype.animateTo=function(t,e,n){Br(this,t,e,n)},r.prototype.animateFrom=function(t,e,n){Br(this,t,e,n,!0)},r.prototype._transitionState=function(t,e,n,i){for(var r=Br(this,e,n,i),o=0;o<r.length;o++)r[o].__fromStateTransition=t},r.prototype.getBoundingRect=function(){return null},r.prototype.getPaintRect=function(){return null},r.initDefaultProps=((Lr=r.prototype).type="element",Lr.name="",Lr.ignore=Lr.silent=Lr.isGroup=Lr.draggable=Lr.dragging=Lr.ignoreClip=Lr.__inHover=!1,Lr.__dirty=_n,void(Object.defineProperty&&(Er("position","_legacyPos","x","y"),Er("scale","_legacyScale","scaleX","scaleY"),Er("origin","_legacyOrigin","originX","originY")))),r);function r(t){this.id=et++,this.animators=[],this.currentStates=[],this.states={},this._init(t)}function Er(t,e,n,i){function r(e,t){Object.defineProperty(t,0,{get:function(){return e[n]},set:function(t){e[n]=t}}),Object.defineProperty(t,1,{get:function(){return e[i]},set:function(t){e[i]=t}})}Object.defineProperty(Lr,t,{get:function(){var t;return this[e]||(t=this[e]=[],r(this,t)),this[e]},set:function(t){this[n]=t[0],this[i]=t[1],this[e]=t,r(this,t)}})}function Br(t,e,n,i,r){function o(){u=!0,--l<=0&&(u?h&&h():c&&c())}function a(){--l<=0&&(u?h&&h():c&&c())}var s=[],l=(!function t(e,n,i,r,o,a,s,l){var u=ct(r);var h=o.duration;var c=o.delay;var p=o.additive;var d=o.setToFinal;var f=!R(a);var g=e.animators;var y=[];for(var m=0;m<u.length;m++){var v=u[m],_=r[v];null!=_&&null!=i[v]&&(f||a[v])?!R(_)||st(_)||_t(_)?y.push(v):n?l||(i[v]=_,e.updateDuringAnimation(n)):t(e,v,i[v],_,o,a&&a[v],s,l):l||(i[v]=_,e.updateDuringAnimation(n),y.push(v))}var x=y.length;if(!p&&x)for(var w,b=0;b<g.length;b++)(S=g[b]).targetName===n&&S.stopTracks(y)&&(w=k(g,S),g.splice(w,1));o.force||(y=ut(y,function(t){return!Vr(r[t],i[t])}),x=y.length);if(0<x||o.force&&!s.length){var S,M=void 0,T=void 0,C=void 0;if(l){T={},d&&(M={});for(b=0;b<x;b++){v=y[b];T[v]=i[v],d?M[v]=r[v]:i[v]=r[v]}}else if(d){C={};for(b=0;b<x;b++){v=y[b];C[v]=Ai(i[v]),Fr(i,r,v)}}(S=new zi(i,!1,!1,p?ut(g,function(t){return t.targetName===n}):null)).targetName=n,o.scope&&(S.scope=o.scope),d&&M&&S.whenWithKeys(0,M,y),C&&S.whenWithKeys(0,C,y),S.whenWithKeys(null==h?500:h,l?T:r,y).delay(c||0),e.addAnimator(S,n),s.push(S)}}(t,"",t,e,n=n||{},i,s,r),s.length),u=!1,h=n.done,c=n.aborted;l||h&&h(),0<s.length&&n.during&&s[0].during(function(t,e){n.during(e)});for(var p=0;p<s.length;p++){var d=s[p];d.done(o),d.aborted(a),n.force&&d.duration(n.duration),d.start(n.easing)}return s}function zr(t,e,n){for(var i=0;i<n;i++)t[i]=e[i]}function Fr(t,e,n){if(st(e[n]))if(st(t[n])||(t[n]=[]),mt(e[n])){var i=e[n].length;t[n].length!==i&&(t[n]=new e[n].constructor(i),zr(t[n],e[n],i))}else{var r=e[n],o=t[n],a=r.length;if(st(r[0]))for(var s=r[0].length,l=0;l<a;l++)o[l]?zr(o[l],r[l],s):o[l]=Array.prototype.slice.call(r[l]);else zr(o,r,a);o.length=r.length}else t[n]=e[n]}function Vr(t,e){return t===e||st(t)&&st(e)&&function(t,e){var n=t.length;if(n!==e.length)return!1;for(var i=0;i<n;i++)if(t[i]!==e[i])return!1;return!0}(t,e)}at(n,he),at(n,vr);a(Gr,Hr=n),Gr.prototype.childrenRef=function(){return this._children},Gr.prototype.children=function(){return this._children.slice()},Gr.prototype.childAt=function(t){return this._children[t]},Gr.prototype.childOfName=function(t){for(var e=this._children,n=0;n<e.length;n++)if(e[n].name===t)return e[n]},Gr.prototype.childCount=function(){return this._children.length},Gr.prototype.add=function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},Gr.prototype.addBefore=function(t,e){var n;return t&&t!==this&&t.parent!==this&&e&&e.parent===this&&0<=(e=(n=this._children).indexOf(e))&&(n.splice(e,0,t),this._doAdd(t)),this},Gr.prototype.replace=function(t,e){t=k(this._children,t);return 0<=t&&this.replaceAt(e,t),this},Gr.prototype.replaceAt=function(t,e){var n=this._children,i=n[e];return t&&t!==this&&t.parent!==this&&t!==i&&(n[e]=t,i.parent=null,(n=this.__zr)&&i.removeSelfFromZr(n),this._doAdd(t)),this},Gr.prototype._doAdd=function(t){t.parent&&t.parent.remove(t);var e=(t.parent=this).__zr;e&&e!==t.__zr&&t.addSelfToZr(e),e&&e.refresh()},Gr.prototype.remove=function(t){var e=this.__zr,n=this._children,i=k(n,t);return i<0||(n.splice(i,1),t.parent=null,e&&t.removeSelfFromZr(e),e&&e.refresh()),this},Gr.prototype.removeAll=function(){for(var t=this._children,e=this.__zr,n=0;n<t.length;n++){var i=t[n];e&&i.removeSelfFromZr(e),i.parent=null}return t.length=0,this},Gr.prototype.eachChild=function(t,e){for(var n=this._children,i=0;i<n.length;i++){var r=n[i];t.call(e,r,i)}return this},Gr.prototype.traverse=function(t,e){for(var n=0;n<this._children.length;n++){var i=this._children[n],r=t.call(e,i);i.isGroup&&!r&&i.traverse(t,e)}return this},Gr.prototype.addSelfToZr=function(t){Hr.prototype.addSelfToZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].addSelfToZr(t)},Gr.prototype.removeSelfFromZr=function(t){Hr.prototype.removeSelfFromZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].removeSelfFromZr(t)},Gr.prototype.getBoundingRect=function(t){for(var e=new q(0,0,0,0),n=t||this._children,i=[],r=null,o=0;o<n.length;o++){var a,s=n[o];s.ignore||s.invisible||(a=s.getBoundingRect(),(s=s.getLocalTransform(i))?(q.applyTransform(e,a,s),(r=r||e.clone()).union(e)):(r=r||a.clone()).union(a))}return r||e};var Hr,Wr=Gr;function Gr(t){var e=Hr.call(this)||this;return e.isGroup=!0,e._children=[],e.attr(t),e}Wr.prototype.type="group";var Ur={},qr={};o.prototype.add=function(t){!this._disposed&&t&&(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},o.prototype.remove=function(t){!this._disposed&&t&&(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},o.prototype.configLayer=function(t,e){this._disposed||(this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh())},o.prototype.setBackgroundColor=function(t){this._disposed||(this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=function(t){if(t){if("string"==typeof t)return Si(t,1)<.4;if(t.colorStops){for(var e=t.colorStops,n=0,i=e.length,r=0;r<i;r++)n+=Si(e[r].color,1);return(n/=i)<.4}}return!1}(t))},o.prototype.getBackgroundColor=function(){return this._backgroundColor},o.prototype.setDarkMode=function(t){this._darkMode=t},o.prototype.isDarkMode=function(){return this._darkMode},o.prototype.refreshImmediately=function(t){this._disposed||(t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1)},o.prototype.refresh=function(){this._disposed||(this._needsRefresh=!0,this.animation.start())},o.prototype.flush=function(){this._disposed||this._flush(!1)},o.prototype._flush=function(t){var e,n=Fi(),t=(this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately()),Fi());e?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:t-n})):0<this._sleepAfterStill&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill)&&this.animation.stop()},o.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},o.prototype.wakeUp=function(){this._disposed||(this.animation.start(),this._stillFrameAccum=0)},o.prototype.refreshHover=function(){this._needsRefreshHover=!0},o.prototype.refreshHoverImmediately=function(){this._disposed||(this._needsRefreshHover=!1,this.painter.refreshHover&&"canvas"===this.painter.getType()&&this.painter.refreshHover())},o.prototype.resize=function(t){this._disposed||(this.painter.resize((t=t||{}).width,t.height),this.handler.resize())},o.prototype.clearAnimation=function(){this._disposed||this.animation.clear()},o.prototype.getWidth=function(){if(!this._disposed)return this.painter.getWidth()},o.prototype.getHeight=function(){if(!this._disposed)return this.painter.getHeight()},o.prototype.setCursorStyle=function(t){this._disposed||this.handler.setCursorStyle(t)},o.prototype.findHover=function(t,e){if(!this._disposed)return this.handler.findHover(t,e)},o.prototype.on=function(t,e,n){return this._disposed||this.handler.on(t,e,n),this},o.prototype.off=function(t,e){this._disposed||this.handler.off(t,e)},o.prototype.trigger=function(t,e){this._disposed||this.handler.trigger(t,e)},o.prototype.clear=function(){if(!this._disposed){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof Wr&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()}},o.prototype.dispose=function(){var t;this._disposed||(this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,this._disposed=!0,t=this.id,delete qr[t])};var Xr,Yr=o;function o(t,e,n){var i,r=this,o=(this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,n=n||{},this.dom=e,this.id=t,new Mn),a=n.renderer||"canvas",a=(Ur[a]||(a=ct(Ur)[0]),n.useDirtyRect=null!=n.useDirtyRect&&n.useDirtyRect,new Ur[a](e,o,n,t)),e=n.ssr||a.ssrOnly,t=(this.storage=o,this.painter=a,p.node||p.worker||e?null:new sr(a.getViewportRoot(),a.root)),s=n.useCoarsePointer;(null==s||"auto"===s?p.touchEventsSupported:!!s)&&(i=N(n.pointerSize,44)),this.handler=new sn(o,a,t,a.root,i),this.animation=new Hi({stage:{update:e?null:function(){return r._flush(!0)}}}),e||this.animation.start()}function jr(t,e){t=new Yr(et++,t,e);return qr[t.id]=t}function Zr(t,e){Ur[t]=e}function Kr(t){Xr=t}var $r=Object.freeze({__proto__:null,dispose:function(t){t.dispose()},disposeAll:function(){for(var t in qr)qr.hasOwnProperty(t)&&qr[t].dispose();qr={}},getElementSSRData:function(t){if("function"==typeof Xr)return Xr(t)},getInstance:function(t){return qr[t]},init:jr,registerPainter:Zr,registerSSRDataGetter:Kr,version:"5.6.0"}),Qr=20;function Jr(t,e,n,i){var r=e[0],e=e[1],o=n[0],n=n[1],a=e-r,s=n-o;if(0==a)return 0==s?o:(o+n)/2;if(i)if(0<a){if(t<=r)return o;if(e<=t)return n}else{if(r<=t)return o;if(t<=e)return n}else{if(t===r)return o;if(t===e)return n}return(t-r)/a*s+o}function to(t,e){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%"}return V(t)?t.replace(/^\s+|\s+$/g,"").match(/%$/)?parseFloat(t)/100*e:parseFloat(t):null==t?NaN:+t}function eo(t,e,n){return null==e&&(e=10),e=Math.min(Math.max(0,e),Qr),t=(+t).toFixed(e),n?t:+t}function no(t){if(t=+t,isNaN(t))return 0;if(1e-14<t)for(var e=1,n=0;n<15;n++,e*=10)if(Math.round(t*e)/e===t)return n;return io(t)}function io(t){var t=t.toString().toLowerCase(),e=t.indexOf("e"),n=0<e?+t.slice(e+1):0,e=0<e?e:t.length,t=t.indexOf(".");return Math.max(0,(t<0?0:e-1-t)-n)}function ro(t,e){var n=Math.log,i=Math.LN10,t=Math.floor(n(t[1]-t[0])/i),n=Math.round(n(Math.abs(e[1]-e[0]))/i),e=Math.min(Math.max(-t+n,0),20);return isFinite(e)?e:20}function oo(t){var e=2*Math.PI;return(t%e+e)%e}function ao(t){return-1e-4<t&&t<1e-4}var so=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d{1,2})(?::(\d{1,2})(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function lo(t){var e,n;return t instanceof Date?t:V(t)?(e=so.exec(t))?e[8]?(n=+e[4]||0,"Z"!==e[8].toUpperCase()&&(n-=+e[8].slice(0,3)),new Date(Date.UTC(+e[1],+(e[2]||1)-1,+e[3]||1,n,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0))):new Date(+e[1],+(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0):new Date(NaN):null==t?new Date(NaN):new Date(Math.round(t))}function uo(t){return Math.pow(10,ho(t))}function ho(t){var e;return 0===t?0:(e=Math.floor(Math.log(t)/Math.LN10),10<=t/Math.pow(10,e)&&e++,e)}function co(t,e){var n=ho(t),i=Math.pow(10,n),r=t/i,e=e?r<1.5?1:r<2.5?2:r<4?3:r<7?5:10:r<1?1:r<2?2:r<3?3:r<5?5:10;return t=e*i,-20<=n?+t.toFixed(n<0?-n:0):t}function po(t){var e=parseFloat(t);return e==t&&(0!==e||!V(t)||t.indexOf("x")<=0)?e:NaN}function fo(t){return!isNaN(po(t))}function go(t,e){return null==t?e:null==e?t:t*e/function t(e,n){return 0===n?e:t(n,e%n)}(t,e)}function f(t){throw new Error(t)}function yo(t,e,n){return(e-t)*n+t}var mo="series\0";function vo(t){return t instanceof Array?t:null==t?[]:[t]}function _o(t,e,n){if(t){t[e]=t[e]||{},t.emphasis=t.emphasis||{},t.emphasis[e]=t.emphasis[e]||{};for(var i=0,r=n.length;i<r;i++){var o=n[i];!t.emphasis[e].hasOwnProperty(o)&&t[e].hasOwnProperty(o)&&(t.emphasis[e][o]=t[e][o])}}}var xo=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function wo(t){return!R(t)||F(t)||t instanceof Date?t:t.value}function bo(t,n,e){var o,a,s,l,r,u,i,h,c,p,d="normalMerge"===e,f="replaceMerge"===e,g="replaceAll"===e,y=(t=t||[],n=(n||[]).slice(),E()),e=(O(n,function(t,e){R(t)||(n[e]=null)}),function(t,e,n){var i=[];if("replaceAll"!==n)for(var r=0;r<t.length;r++){var o=t[r];o&&null!=o.id&&e.set(o.id,r),i.push({existing:"replaceMerge"===n||ko(o)?null:o,newOption:null,keyInfo:null,brandNew:null})}return i}(t,y,e));return(d||f)&&(o=e,a=t,s=y,O(l=n,function(t,e){var n,i,r;t&&null!=t.id&&(n=Mo(t.id),null!=(i=s.get(n)))&&(kt(!(r=o[i]).newOption,'Duplicated option on id "'+n+'".'),r.newOption=t,r.existing=a[i],l[e]=null)})),d&&(r=e,O(u=n,function(t,e){if(t&&null!=t.name)for(var n=0;n<r.length;n++){var i=r[n].existing;if(!r[n].newOption&&i&&(null==i.id||null==t.id)&&!ko(t)&&!ko(i)&&So("name",i,t))return r[n].newOption=t,void(u[e]=null)}})),d||f?(h=e,c=f,O(n,function(t){if(t){for(var e,n=0;(e=h[n])&&(e.newOption||ko(e.existing)||e.existing&&null!=t.id&&!So("id",t,e.existing));)n++;e?(e.newOption=t,e.brandNew=c):h.push({newOption:t,brandNew:c,existing:null,keyInfo:null}),n++}})):g&&(i=e,O(n,function(t){i.push({newOption:t,brandNew:!0,existing:null,keyInfo:null})})),t=e,p=E(),O(t,function(t){var e=t.existing;e&&p.set(e.id,t)}),O(t,function(t){var e=t.newOption;kt(!e||null==e.id||!p.get(e.id)||p.get(e.id)===t,"id duplicates: "+(e&&e.id)),e&&null!=e.id&&p.set(e.id,t),t.keyInfo||(t.keyInfo={})}),O(t,function(t,e){var n=t.existing,i=t.newOption,r=t.keyInfo;if(R(i)){if(r.name=null!=i.name?Mo(i.name):n?n.name:mo+e,n)r.id=Mo(n.id);else if(null!=i.id)r.id=Mo(i.id);else for(var o=0;r.id="\0"+r.name+"\0"+o++,p.get(r.id););p.set(r.id,t)}}),e}function So(t,e,n){e=To(e[t],null),n=To(n[t],null);return null!=e&&null!=n&&e===n}function Mo(t){return To(t,"")}function To(t,e){return null==t?e:V(t)?t:gt(t)||ft(t)?t+"":e}function Co(t){t=t.name;return!(!t||!t.indexOf(mo))}function ko(t){return t&&null!=t.id&&0===Mo(t.id).indexOf("\0_ec_\0")}function Io(t,r,o){O(t,function(t){var e,n,i=t.newOption;R(i)&&(t.keyInfo.mainType=r,t.keyInfo.subType=(e=r,i=i,t=t.existing,n=o,i.type||(t?t.subType:n.determineSubType(e,i))))})}function Do(e,t){return null!=t.dataIndexInside?t.dataIndexInside:null!=t.dataIndex?F(t.dataIndex)?z(t.dataIndex,function(t){return e.indexOfRawIndex(t)}):e.indexOfRawIndex(t.dataIndex):null!=t.name?F(t.name)?z(t.name,function(t){return e.indexOfName(t)}):e.indexOfName(t.name):void 0}function Lo(){var e="__ec_inner_"+Ao++;return function(t){return t[e]||(t[e]={})}}var Ao=Math.round(9*Math.random());function Po(n,t,i){var t=Oo(t,i),e=t.mainTypeSpecified,r=t.queryOptionMap,o=t.others,a=i?i.defaultMainType:null;return!e&&a&&r.set(a,{}),r.each(function(t,e){t=No(n,e,t,{useDefault:a===e,enableAll:!i||null==i.enableAll||i.enableAll,enableNone:!i||null==i.enableNone||i.enableNone});o[e+"Models"]=t.models,o[e+"Model"]=t.models[0]}),o}function Oo(t,i){var e=V(t)?((e={})[t+"Index"]=0,e):t,r=E(),o={},a=!1;return O(e,function(t,e){var n;"dataIndex"===e||"dataIndexInside"===e?o[e]=t:(n=(e=e.match(/^(\w+)(Index|Id|Name)$/)||[])[1],e=(e[2]||"").toLowerCase(),!n||!e||i&&i.includeMainTypes&&k(i.includeMainTypes,n)<0||(a=a||!!n,(r.get(n)||r.set(n,{}))[e]=t))}),{mainTypeSpecified:a,queryOptionMap:r,others:o}}var Ro={useDefault:!0,enableAll:!1,enableNone:!1};function No(t,e,n,i){i=i||Ro;var r=n.index,o=n.id,n=n.name,a={models:null,specified:null!=r||null!=o||null!=n};return a.specified?"none"===r||!1===r?(kt(i.enableNone,'`"none"` or `false` is not a valid value on index option.'),a.models=[]):("all"===r&&(kt(i.enableAll,'`"all"` is not a valid value on index option.'),r=o=n=null),a.models=t.queryComponents({mainType:e,index:r,id:o,name:n})):(r=void 0,a.models=i.useDefault&&(r=t.getComponent(e))?[r]:[]),a}function Eo(t,e,n){t.setAttribute?t.setAttribute(e,n):t[e]=n}var Bo=".",zo="___EC__COMPONENT__CONTAINER___",Fo="___EC__EXTENDED_CLASS___";function Vo(t){var e={main:"",sub:""};return t&&(t=t.split(Bo),e.main=t[0]||"",e.sub=t[1]||""),e}function Ho(t){(t.$constructor=t).extend=function(t){var e,n,i,r=this;function o(){return n.apply(this,arguments)||this}return I(i=r)&&/^class\s/.test(Function.prototype.toString.call(i))?(a(o,n=r),e=o):ot(e=function(){(t.$constructor||r).apply(this,arguments)},this),P(e.prototype,t),e[Fo]=!0,e.extend=this.extend,e.superCall=Uo,e.superApply=qo,e.superClass=r,e}}function Wo(t,e){t.extend=e.extend}var Go=Math.round(10*Math.random());function Uo(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];return this.superClass.prototype[e].apply(t,n)}function qo(t,e,n){return this.superClass.prototype[e].apply(t,n)}function Xo(t){var r={};t.registerClass=function(t){var e,n=t.type||t.prototype.type;return n&&(kt(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(e=n),'componentType "'+e+'" illegal'),(e=Vo(t.prototype.type=n)).sub?e.sub!==zo&&(function(t){var e=r[t.main];e&&e[zo]||(e=r[t.main]={___EC__COMPONENT__CONTAINER___:!0});return e}(e)[e.sub]=t):r[e.main]=t),t},t.getClass=function(t,e,n){var i=r[t];if(i&&i[zo]&&(i=e?i[e]:null),n&&!i)throw new Error(e?"Component "+t+"."+(e||"")+" is used but not imported.":t+".type should be specified.");return i},t.getClassesByMainType=function(t){var t=Vo(t),n=[],t=r[t.main];return t&&t[zo]?O(t,function(t,e){e!==zo&&n.push(t)}):n.push(t),n},t.hasClass=function(t){t=Vo(t);return!!r[t.main]},t.getAllClassMainTypes=function(){var n=[];return O(r,function(t,e){n.push(e)}),n},t.hasSubTypes=function(t){t=Vo(t),t=r[t.main];return t&&t[zo]}}function Yo(a,s){for(var t=0;t<a.length;t++)a[t][1]||(a[t][1]=a[t][0]);return s=s||!1,function(t,e,n){for(var i={},r=0;r<a.length;r++){var o=a[r][1];e&&0<=k(e,o)||n&&k(n,o)<0||null!=(o=t.getShallow(o,s))&&(i[a[r][0]]=o)}return i}}var jo=Yo([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]]),Zo=(Ko.prototype.getAreaStyle=function(t,e){return jo(this,t,e)},Ko);function Ko(){}var $o=new ni(50);function Qo(t,e,n,i,r){return t?"string"==typeof t?(e&&e.__zrImageSrc===t||!n||(n={hostEl:n,cb:i,cbPayload:r},(i=$o.get(t))?ta(e=i.image)||i.pending.push(n):((e=H.loadImage(t,Jo,Jo)).__zrImageSrc=t,$o.put(t,e.__cachedImgObj={image:e,pending:[n]}))),e):t:e}function Jo(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var n=t.pending[e],i=n.cb;i&&i(this,n.cbPayload),n.hostEl.dirty()}t.pending.length=0}function ta(t){return t&&t.width&&t.height}var ea=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function na(t,e,n,i,r){if(!e)return"";var o=(t+"").split("\n");r=ia(e,n,i,r);for(var a=0,s=o.length;a<s;a++)o[a]=ra(o[a],r);return o.join("\n")}function ia(t,e,n,i){for(var r=P({},i=i||{}),o=(r.font=e,n=N(n,"..."),r.maxIterations=N(i.maxIterations,2),r.minChar=N(i.minChar,0)),a=(r.cnCharWidth=br("国",e),r.ascCharWidth=br("a",e)),s=(r.placeholder=N(i.placeholder,""),t=Math.max(0,t-1)),l=0;l<o&&a<=s;l++)s-=a;i=br(n,e);return s<i&&(n="",i=0),s=t-i,r.ellipsis=n,r.ellipsisWidth=i,r.contentWidth=s,r.containerWidth=t,r}function ra(t,e){var n=e.containerWidth,i=e.font,r=e.contentWidth;if(!n)return"";var o=br(t,i);if(!(o<=n)){for(var a=0;;a++){if(o<=r||a>=e.maxIterations){t+=e.ellipsis;break}var s=0===a?function(t,e,n,i){for(var r=0,o=0,a=t.length;o<a&&r<e;o++){var s=t.charCodeAt(o);r+=0<=s&&s<=127?n:i}return o}(t,r,e.ascCharWidth,e.cnCharWidth):0<o?Math.floor(t.length*r/o):0,o=br(t=t.substr(0,s),i)}""===t&&(t=e.placeholder)}return t}var oa=function(){},aa=function(t){this.tokens=[],t&&(this.tokens=t)},sa=function(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[]};function la(t,e){var n=new sa;if(null!=t&&(t+=""),t){for(var i,r=e.width,o=e.height,a=e.overflow,s="break"!==a&&"breakAll"!==a||null==r?null:{width:r,accumWidth:0,breakAll:"breakAll"===a},l=ea.lastIndex=0;null!=(i=ea.exec(t));){var u=i.index;l<u&&ua(n,t.substring(l,u),e,s),ua(n,i[2],e,s,i[1]),l=ea.lastIndex}l<t.length&&ua(n,t.substring(l,t.length),e,s);var h,c=[],p=0,d=0,f=e.padding,g="truncate"===a,y="truncate"===e.lineOverflow;t:for(var m=0;m<n.lines.length;m++){for(var v=n.lines[m],_=0,x=0,w=0;w<v.tokens.length;w++){var b=(D=v.tokens[w]).styleName&&e.rich[D.styleName]||{},S=D.textPadding=b.padding,M=S?S[1]+S[3]:0,T=D.font=b.font||e.font,C=(D.contentHeight=kr(T),N(b.height,D.contentHeight));if(D.innerHeight=C,S&&(C+=S[0]+S[2]),D.height=C,D.lineHeight=Mt(b.lineHeight,e.lineHeight,C),D.align=b&&b.align||e.align,D.verticalAlign=b&&b.verticalAlign||"middle",y&&null!=o&&p+D.lineHeight>o){0<w?(v.tokens=v.tokens.slice(0,w),A(v,x,_),n.lines=n.lines.slice(0,m+1)):n.lines=n.lines.slice(0,m);break t}var k,S=b.width,I=null==S||"auto"===S;"string"==typeof S&&"%"===S.charAt(S.length-1)?(D.percentWidth=S,c.push(D),D.contentWidth=br(D.text,T)):(I&&(S=(S=b.backgroundColor)&&S.image)&&(k=void 0,ta(S="string"==typeof(h=S)?(k=$o.get(h))&&k.image:h))&&(D.width=Math.max(D.width,S.width*C/S.height)),null!=(k=g&&null!=r?r-x:null)&&k<D.width?!I||k<M?(D.text="",D.width=D.contentWidth=0):(D.text=na(D.text,k-M,T,e.ellipsis,{minChar:e.truncateMinChar}),D.width=D.contentWidth=br(D.text,T)):D.contentWidth=br(D.text,T)),D.width+=M,x+=D.width,b&&(_=Math.max(_,D.lineHeight))}A(v,x,_)}n.outerWidth=n.width=N(r,d),n.outerHeight=n.height=N(o,p),n.contentHeight=p,n.contentWidth=d,f&&(n.outerWidth+=f[1]+f[3],n.outerHeight+=f[0]+f[2]);for(m=0;m<c.length;m++){var D,L=(D=c[m]).percentWidth;D.width=parseInt(L,10)/100*n.width}}return n;function A(t,e,n){t.width=e,t.lineHeight=n,p+=n,d=Math.max(d,e)}}function ua(t,e,n,i,r){var o,a,s=""===e,l=r&&n.rich[r]||{},u=t.lines,h=l.font||n.font,c=!1;i?(n=(t=l.padding)?t[1]+t[3]:0,null!=l.width&&"auto"!==l.width?(t=Ir(l.width,i.width)+n,0<u.length&&t+i.accumWidth>i.width&&(o=e.split("\n"),c=!0),i.accumWidth=t):(t=ca(e,h,i.width,i.breakAll,i.accumWidth),i.accumWidth=t.accumWidth+n,a=t.linesWidths,o=t.lines)):o=e.split("\n");for(var p=0;p<o.length;p++){var d,f,g=o[p],y=new oa;y.styleName=r,y.text=g,y.isLineHolder=!g&&!s,"number"==typeof l.width?y.width=l.width:y.width=a?a[p]:br(g,h),p||c?u.push(new aa([y])):1===(f=(d=(u[u.length-1]||(u[0]=new aa)).tokens).length)&&d[0].isLineHolder?d[0]=y:!g&&f&&!s||d.push(y)}}var ha=lt(",&?/;] ".split(""),function(t,e){return t[e]=!0,t},{});function ca(t,e,n,i,r){for(var o,a=[],s=[],l="",u="",h=0,c=0,p=0;p<t.length;p++){var d,f,g=t.charAt(p);"\n"===g?(u&&(l+=u,c+=h),a.push(l),s.push(c),u=l="",c=h=0):(d=br(g,e),f=!(i||(f=void 0,!(32<=(f=(f=o=g).charCodeAt(0))&&f<=591||880<=f&&f<=4351||4608<=f&&f<=5119||7680<=f&&f<=8303))||!!ha[o]),(a.length?n<c+d:n<r+c+d)?c?(l||u)&&(c=f?(l||(l=u,u="",c=h=0),a.push(l),s.push(c-h),u+=g,l="",h+=d):(u&&(l+=u,u="",h=0),a.push(l),s.push(c),l=g,d)):f?(a.push(u),s.push(h),u=g,h=d):(a.push(g),s.push(d)):(c+=d,f?(u+=g,h+=d):(u&&(l+=u,u="",h=0),l+=g)))}return a.length||l||(l=t,u="",h=0),u&&(l+=u),l&&(a.push(l),s.push(c)),1===a.length&&(c+=r),{accumWidth:c,lines:a,linesWidths:s}}var pa,da="__zr_style_"+Math.round(10*Math.random()),fa={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},ga={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}},ya=(fa[da]=!0,["z","z2","invisible"]),ma=["invisible"],n=(a(s,pa=n),s.prototype._init=function(t){for(var e=ct(t),n=0;n<e.length;n++){var i=e[n];"style"===i?this.useStyle(t[i]):pa.prototype.attrKV.call(this,i,t[i])}this.style||this.useStyle({})},s.prototype.beforeBrush=function(){},s.prototype.afterBrush=function(){},s.prototype.innerBeforeBrush=function(){},s.prototype.innerAfterBrush=function(){},s.prototype.shouldBePainted=function(t,e,n,i){var r=this.transform;if(this.ignore||this.invisible||0===this.style.opacity||this.culling&&function(t,e,n){va.copy(t.getBoundingRect()),t.transform&&va.applyTransform(t.transform);return _a.width=e,_a.height=n,!va.intersect(_a)}(this,t,e)||r&&!r[0]&&!r[3])return!1;if(n&&this.__clipPaths)for(var o=0;o<this.__clipPaths.length;++o)if(this.__clipPaths[o].isZeroArea())return!1;if(i&&this.parent)for(var a=this.parent;a;){if(a.ignore)return!1;a=a.parent}return!0},s.prototype.contain=function(t,e){return this.rectContain(t,e)},s.prototype.traverse=function(t,e){t.call(e,this)},s.prototype.rectContain=function(t,e){t=this.transformCoordToLocal(t,e);return this.getBoundingRect().contain(t[0],t[1])},s.prototype.getPaintRect=function(){var t,e,n,i,r,o=this._paintRect;return this._paintRect&&!this.__dirty||(r=this.transform,t=this.getBoundingRect(),e=(i=this.style).shadowBlur||0,n=i.shadowOffsetX||0,i=i.shadowOffsetY||0,o=this._paintRect||(this._paintRect=new q(0,0,0,0)),r?q.applyTransform(o,t,r):o.copy(t),(e||n||i)&&(o.width+=2*e+Math.abs(n),o.height+=2*e+Math.abs(i),o.x=Math.min(o.x,o.x+n-e),o.y=Math.min(o.y,o.y+i-e)),r=this.dirtyRectTolerance,o.isZero())||(o.x=Math.floor(o.x-r),o.y=Math.floor(o.y-r),o.width=Math.ceil(o.width+1+2*r),o.height=Math.ceil(o.height+1+2*r)),o},s.prototype.setPrevPaintRect=function(t){t?(this._prevPaintRect=this._prevPaintRect||new q(0,0,0,0),this._prevPaintRect.copy(t)):this._prevPaintRect=null},s.prototype.getPrevPaintRect=function(){return this._prevPaintRect},s.prototype.animateStyle=function(t){return this.animate("style",t)},s.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():this.markRedraw()},s.prototype.attrKV=function(t,e){"style"!==t?pa.prototype.attrKV.call(this,t,e):this.style?this.setStyle(e):this.useStyle(e)},s.prototype.setStyle=function(t,e){return"string"==typeof t?this.style[t]=e:P(this.style,t),this.dirtyStyle(),this},s.prototype.dirtyStyle=function(t){t||this.markRedraw(),this.__dirty|=2,this._rect&&(this._rect=null)},s.prototype.dirty=function(){this.dirtyStyle()},s.prototype.styleChanged=function(){return!!(2&this.__dirty)},s.prototype.styleUpdated=function(){this.__dirty&=-3},s.prototype.createStyle=function(t){return zt(fa,t)},s.prototype.useStyle=function(t){t[da]||(t=this.createStyle(t)),this.__inHover?this.__hoverStyle=t:this.style=t,this.dirtyStyle()},s.prototype.isStyleObject=function(t){return t[da]},s.prototype._innerSaveToNormal=function(t){pa.prototype._innerSaveToNormal.call(this,t);var e=this._normalState;t.style&&!e.style&&(e.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(t,e,ya)},s.prototype._applyStateObj=function(t,e,n,i,r,o){pa.prototype._applyStateObj.call(this,t,e,n,i,r,o);var a,s=!(e&&i);if(e&&e.style?r?i?a=e.style:(a=this._mergeStyle(this.createStyle(),n.style),this._mergeStyle(a,e.style)):(a=this._mergeStyle(this.createStyle(),(i?this:n).style),this._mergeStyle(a,e.style)):s&&(a=n.style),a)if(r){var l=this.style;if(this.style=this.createStyle(s?{}:l),s)for(var u=ct(l),h=0;h<u.length;h++)(p=u[h])in a&&(a[p]=a[p],this.style[p]=l[p]);for(var c=ct(a),h=0;h<c.length;h++){var p=c[h];this.style[p]=this.style[p]}this._transitionState(t,{style:a},o,this.getAnimationStyleProps())}else this.useStyle(a);for(var d=this.__inHover?ma:ya,h=0;h<d.length;h++){p=d[h];e&&null!=e[p]?this[p]=e[p]:s&&null!=n[p]&&(this[p]=n[p])}},s.prototype._mergeStates=function(t){for(var e,n=pa.prototype._mergeStates.call(this,t),i=0;i<t.length;i++){var r=t[i];r.style&&this._mergeStyle(e=e||{},r.style)}return e&&(n.style=e),n},s.prototype._mergeStyle=function(t,e){return P(t,e),t},s.prototype.getAnimationStyleProps=function(){return ga},s.initDefaultProps=((n=s.prototype).type="displayable",n.invisible=!1,n.z=0,n.z2=0,n.zlevel=0,n.culling=!1,n.cursor="pointer",n.rectHover=!1,n.incremental=!1,n._rect=null,n.dirtyRectTolerance=0,void(n.__dirty=2|_n)),s);function s(t){return pa.call(this,t)||this}var va=new q(0,0,0,0),_a=new q(0,0,0,0);var xa=Math.min,wa=Math.max,ba=Math.sin,Sa=Math.cos,Ma=2*Math.PI,Ta=Ut(),Ca=Ut(),ka=Ut();function Ia(t,e,n,i,r,o){r[0]=xa(t,n),r[1]=xa(e,i),o[0]=wa(t,n),o[1]=wa(e,i)}var Da=[],La=[];var X={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},Aa=[],Pa=[],Oa=[],Ra=[],Na=[],Ea=[],Ba=Math.min,za=Math.max,Fa=Math.cos,Va=Math.sin,Ha=Math.abs,Wa=Math.PI,Ga=2*Wa,Ua="undefined"!=typeof Float32Array,qa=[];function Xa(t){return Math.round(t/Wa*1e8)/1e8%2*Wa}l.prototype.increaseVersion=function(){this._version++},l.prototype.getVersion=function(){return this._version},l.prototype.setScale=function(t,e,n){0<(n=n||0)&&(this._ux=Ha(n/ur/t)||0,this._uy=Ha(n/ur/e)||0)},l.prototype.setDPR=function(t){this.dpr=t},l.prototype.setContext=function(t){this._ctx=t},l.prototype.getContext=function(){return this._ctx},l.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},l.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},l.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(X.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},l.prototype.lineTo=function(t,e){var n=Ha(t-this._xi),i=Ha(e-this._yi),r=n>this._ux||i>this._uy;return this.addData(X.L,t,e),this._ctx&&r&&this._ctx.lineTo(t,e),r?(this._xi=t,this._yi=e,this._pendingPtDist=0):(r=n*n+i*i)>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=r),this},l.prototype.bezierCurveTo=function(t,e,n,i,r,o){return this._drawPendingPt(),this.addData(X.C,t,e,n,i,r,o),this._ctx&&this._ctx.bezierCurveTo(t,e,n,i,r,o),this._xi=r,this._yi=o,this},l.prototype.quadraticCurveTo=function(t,e,n,i){return this._drawPendingPt(),this.addData(X.Q,t,e,n,i),this._ctx&&this._ctx.quadraticCurveTo(t,e,n,i),this._xi=n,this._yi=i,this},l.prototype.arc=function(t,e,n,i,r,o){this._drawPendingPt(),qa[0]=i,qa[1]=r,s=o,(l=Xa((a=qa)[0]))<0&&(l+=Ga),h=l-a[0],u=a[1],u+=h,!s&&Ga<=u-l?u=l+Ga:s&&Ga<=l-u?u=l-Ga:!s&&u<l?u=l+(Ga-Xa(l-u)):s&&l<u&&(u=l-(Ga-Xa(u-l))),a[0]=l,a[1]=u;var a,s,l,u,h=(r=qa[1])-(i=qa[0]);return this.addData(X.A,t,e,n,n,i,h,0,o?0:1),this._ctx&&this._ctx.arc(t,e,n,i,r,o),this._xi=Fa(r)*n+t,this._yi=Va(r)*n+e,this},l.prototype.arcTo=function(t,e,n,i,r){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,n,i,r),this},l.prototype.rect=function(t,e,n,i){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,n,i),this.addData(X.R,t,e,n,i),this},l.prototype.closePath=function(){this._drawPendingPt(),this.addData(X.Z);var t=this._ctx,e=this._x0,n=this._y0;return t&&t.closePath(),this._xi=e,this._yi=n,this},l.prototype.fill=function(t){t&&t.fill(),this.toStatic()},l.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},l.prototype.len=function(){return this._len},l.prototype.setData=function(t){var e=t.length;this.data&&this.data.length===e||!Ua||(this.data=new Float32Array(e));for(var n=0;n<e;n++)this.data[n]=t[n];this._len=e},l.prototype.appendPath=function(t){for(var e=(t=t instanceof Array?t:[t]).length,n=0,i=this._len,r=0;r<e;r++)n+=t[r].len();Ua&&this.data instanceof Float32Array&&(this.data=new Float32Array(i+n));for(r=0;r<e;r++)for(var o=t[r].data,a=0;a<o.length;a++)this.data[i++]=o[a];this._len=i},l.prototype.addData=function(t,e,n,i,r,o,a,s,l){if(this._saveData){var u=this.data;this._len+arguments.length>u.length&&(this._expandData(),u=this.data);for(var h=0;h<arguments.length;h++)u[this._len++]=arguments[h]}},l.prototype._drawPendingPt=function(){0<this._pendingPtDist&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},l.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},l.prototype.toStatic=function(){var t;this._saveData&&(this._drawPendingPt(),(t=this.data)instanceof Array)&&(t.length=this._len,Ua)&&11<this._len&&(this.data=new Float32Array(t))},l.prototype.getBoundingRect=function(){Oa[0]=Oa[1]=Na[0]=Na[1]=Number.MAX_VALUE,Ra[0]=Ra[1]=Ea[0]=Ea[1]=-Number.MAX_VALUE;for(var t,e=this.data,n=0,i=0,r=0,o=0,a=0;a<this._len;){var E=e[a++],B=1===a;switch(B&&(r=n=e[a],o=i=e[a+1]),E){case X.M:n=r=e[a++],i=o=e[a++],Na[0]=r,Na[1]=o,Ea[0]=r,Ea[1]=o;break;case X.L:Ia(n,i,e[a],e[a+1],Na,Ea),n=e[a++],i=e[a++];break;case X.C:W=H=m=y=V=g=f=d=p=c=F=z=h=u=l=s=void 0;var s=n,l=i,u=e[a++],h=e[a++],z=e[a++],F=e[a++],c=e[a],p=e[a+1],d=Na,f=Ea,g=Hn,V=zn,y=g(s,u,z,c,Da);d[0]=1/0,d[1]=1/0,f[0]=-1/0,f[1]=-1/0;for(var m=0;m<y;m++){var H=V(s,u,z,c,Da[m]);d[0]=xa(H,d[0]),f[0]=wa(H,f[0])}for(y=g(l,h,F,p,La),m=0;m<y;m++){var W=V(l,h,F,p,La[m]);d[1]=xa(W,d[1]),f[1]=wa(W,f[1])}d[0]=xa(s,d[0]),f[0]=wa(s,f[0]),d[0]=xa(c,d[0]),f[0]=wa(c,f[0]),d[1]=xa(l,d[1]),f[1]=wa(l,f[1]),d[1]=xa(p,d[1]),f[1]=wa(p,f[1]),n=e[a++],i=e[a++];break;case X.Q:g=n,P=i,M=e[a++],x=e[a++],S=e[a],v=e[a+1],b=Na,T=Ea,t=w=t=_=void 0,_=Un,t=wa(xa((w=Xn)(g,M,S),1),0),w=wa(xa(w(P,x,v),1),0),M=_(g,M,S,t),t=_(P,x,v,w),b[0]=xa(g,S,M),b[1]=xa(P,v,t),T[0]=wa(g,S,M),T[1]=wa(P,v,t),n=e[a++],i=e[a++];break;case X.A:var v,_=e[a++],x=e[a++],w=e[a++],b=e[a++],S=e[a++],M=e[a++]+S,T=(a+=1,!e[a++]),C=(B&&(r=Fa(S)*w+_,o=Va(S)*b+x),N=v=U=G=R=O=P=A=L=D=I=k=C=void 0,_),k=x,I=w,D=b,L=S,A=M,P=T,O=Na,R=Ea,G=re,U=oe;if((v=Math.abs(L-A))%Ma<1e-4&&1e-4<v)O[0]=C-I,O[1]=k-D,R[0]=C+I,R[1]=k+D;else{Ta[0]=Sa(L)*I+C,Ta[1]=ba(L)*D+k,Ca[0]=Sa(A)*I+C,Ca[1]=ba(A)*D+k,G(O,Ta,Ca),U(R,Ta,Ca),(L%=Ma)<0&&(L+=Ma),(A%=Ma)<0&&(A+=Ma),A<L&&!P?A+=Ma:L<A&&P&&(L+=Ma),P&&(v=A,A=L,L=v);for(var N=0;N<A;N+=Math.PI/2)L<N&&(ka[0]=Sa(N)*I+C,ka[1]=ba(N)*D+k,G(O,ka,O),U(R,ka,R))}n=Fa(M)*w+_,i=Va(M)*b+x;break;case X.R:Ia(r=n=e[a++],o=i=e[a++],r+e[a++],o+e[a++],Na,Ea);break;case X.Z:n=r,i=o}re(Oa,Oa,Na),oe(Ra,Ra,Ea)}return 0===a&&(Oa[0]=Oa[1]=Ra[0]=Ra[1]=0),new q(Oa[0],Oa[1],Ra[0]-Oa[0],Ra[1]-Oa[1])},l.prototype._calculateLength=function(){for(var t=this.data,e=this._len,n=this._ux,i=this._uy,r=0,o=0,a=0,s=0,l=(this._pathSegLen||(this._pathSegLen=[]),this._pathSegLen),u=0,h=0,c=0;c<e;){var p=t[c++],d=1===c,f=(d&&(a=r=t[c],s=o=t[c+1]),-1);switch(p){case X.M:r=a=t[c++],o=s=t[c++];break;case X.L:var g=t[c++],y=(_=t[c++])-o;(Ha(k=g-r)>n||Ha(y)>i||c===e-1)&&(f=Math.sqrt(k*k+y*y),r=g,o=_);break;case X.C:var m=t[c++],v=t[c++],g=t[c++],_=t[c++],x=t[c++],w=t[c++],f=function(t,e,n,i,r,o,a,s,l){for(var u=t,h=e,c=0,p=1/l,d=1;d<=l;d++){var f=d*p,g=zn(t,n,r,a,f),f=zn(e,i,o,s,f),y=g-u,m=f-h;c+=Math.sqrt(y*y+m*m),u=g,h=f}return c}(r,o,m,v,g,_,x,w,10),r=x,o=w;break;case X.Q:f=function(t,e,n,i,r,o,a){for(var s=t,l=e,u=0,h=1/a,c=1;c<=a;c++){var p=c*h,d=Un(t,n,r,p),p=Un(e,i,o,p),f=d-s,g=p-l;u+=Math.sqrt(f*f+g*g),s=d,l=p}return u}(r,o,m=t[c++],v=t[c++],g=t[c++],_=t[c++],10),r=g,o=_;break;case X.A:var x=t[c++],w=t[c++],b=t[c++],S=t[c++],M=t[c++],T=t[c++],C=T+M;c+=1,d&&(a=Fa(M)*b+x,s=Va(M)*S+w),f=za(b,S)*Ba(Ga,Math.abs(T)),r=Fa(C)*b+x,o=Va(C)*S+w;break;case X.R:a=r=t[c++],s=o=t[c++];f=2*t[c++]+2*t[c++];break;case X.Z:var k=a-r,y=s-o;f=Math.sqrt(k*k+y*y),r=a,o=s}0<=f&&(u+=l[h++]=f)}return this._pathLen=u},l.prototype.rebuildPath=function(t,e){var n,i,r,o,a,s,l,u,h=this.data,E=this._ux,B=this._uy,z=this._len,c=e<1,p=0,d=0,f=0;if(!c||(this._pathSegLen||this._calculateLength(),a=this._pathSegLen,s=e*this._pathLen))t:for(var g=0;g<z;){var y=h[g++],F=1===g;switch(F&&(n=r=h[g],i=o=h[g+1]),y!==X.L&&0<f&&(t.lineTo(l,u),f=0),y){case X.M:n=r=h[g++],i=o=h[g++],t.moveTo(r,o);break;case X.L:var m=h[g++],v=h[g++],_=Ha(m-r),x=Ha(v-o);if(E<_||B<x){if(c){if(s<p+(N=a[d++])){var w=(s-p)/N;t.lineTo(r*(1-w)+m*w,o*(1-w)+v*w);break t}p+=N}t.lineTo(m,v),r=m,o=v,f=0}else{_=_*_+x*x;f<_&&(l=m,u=v,f=_)}break;case X.C:var b=h[g++],S=h[g++],M=h[g++],T=h[g++],x=h[g++],_=h[g++];if(c){if(s<p+(N=a[d++])){Wn(r,b,M,x,w=(s-p)/N,Aa),Wn(o,S,T,_,w,Pa),t.bezierCurveTo(Aa[1],Pa[1],Aa[2],Pa[2],Aa[3],Pa[3]);break t}p+=N}t.bezierCurveTo(b,S,M,T,x,_),r=x,o=_;break;case X.Q:b=h[g++],S=h[g++],M=h[g++],T=h[g++];if(c){if(s<p+(N=a[d++])){Yn(r,b,M,w=(s-p)/N,Aa),Yn(o,S,T,w,Pa),t.quadraticCurveTo(Aa[1],Pa[1],Aa[2],Pa[2]);break t}p+=N}t.quadraticCurveTo(b,S,M,T),r=M,o=T;break;case X.A:var C=h[g++],k=h[g++],I=h[g++],D=h[g++],L=h[g++],A=h[g++],P=h[g++],V=!h[g++],H=D<I?I:D,O=.001<Ha(I-D),R=L+A,W=!1;if(c&&(s<p+(N=a[d++])&&(R=L+A*(s-p)/N,W=!0),p+=N),O&&t.ellipse?t.ellipse(C,k,I,D,P,L,R,V):t.arc(C,k,H,L,R,V),W)break t;F&&(n=Fa(L)*I+C,i=Va(L)*D+k),r=Fa(R)*I+C,o=Va(R)*D+k;break;case X.R:n=r=h[g],i=o=h[g+1],m=h[g++],v=h[g++];var N,A=h[g++],O=h[g++];if(c){if(s<p+(N=a[d++])){P=s-p;t.moveTo(m,v),t.lineTo(m+Ba(P,A),v),0<(P-=A)&&t.lineTo(m+A,v+Ba(P,O)),0<(P-=O)&&t.lineTo(m+za(A-P,0),v+O),0<(P-=A)&&t.lineTo(m,v+za(O-P,0));break t}p+=N}t.rect(m,v,A,O);break;case X.Z:if(c){if(s<p+(N=a[d++])){w=(s-p)/N;t.lineTo(r*(1-w)+n*w,o*(1-w)+i*w);break t}p+=N}t.closePath(),r=n,o=i}}},l.prototype.clone=function(){var t=new l,e=this.data;return t.data=e.slice?e.slice():Array.prototype.slice.call(e),t._len=this._len,t},l.CMD=X,l.initDefaultProps=((pu=l.prototype)._saveData=!0,pu._ux=0,pu._uy=0,pu._pendingPtDist=0,void(pu._version=0));var Ya=l;function l(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}function ja(t,e,n,i,r,o,a){var s;if(0!==r)return s=0,!(e+(r=r)<a&&i+r<a||a<e-r&&a<i-r||t+r<o&&n+r<o||o<t-r&&o<n-r)&&(t===n?Math.abs(o-t)<=r/2:(o=(s=(e-i)/(t-n))*o-a+(t*i-n*e)/(t-n))*o/(s*s+1)<=r/2*r/2)}var Za=2*Math.PI;function Ka(t){return(t%=Za)<0&&(t+=Za),t}var $a=2*Math.PI;function Qa(t,e,n,i,r,o){return e<o&&i<o||o<e&&o<i||i===e?0:(n=(o=(o-e)/(i-e))*(n-t)+t)===r?1/0:r<n?1!=o&&0!=o?i<e?1:-1:i<e?.5:-.5:0}var Ja=Ya.CMD,ts=2*Math.PI,es=1e-4;var ns=[-1,-1,-1],is=[-1,-1];function rs(t,e,n,i,r,o,a,s,l,u){if(e<u&&i<u&&o<u&&s<u||u<e&&u<i&&u<o&&u<s)return 0;var h=Vn(e,i,o,s,u,ns);if(0===h)return 0;for(var c,p=0,d=-1,f=void 0,g=void 0,y=0;y<h;y++){var m=ns[y],v=0===m||1===m?.5:1;zn(t,n,r,a,m)<l||(d<0&&(d=Hn(e,i,o,s,is),is[1]<is[0]&&1<d&&(c=void 0,c=is[0],is[0]=is[1],is[1]=c),f=zn(e,i,o,s,is[0]),1<d)&&(g=zn(e,i,o,s,is[1])),2===d?m<is[0]?p+=f<e?v:-v:m<is[1]?p+=g<f?v:-v:p+=s<g?v:-v:m<is[0]?p+=f<e?v:-v:p+=s<f?v:-v)}return p}function os(t,e,n,i,r,o,a,s){if(e<s&&i<s&&o<s||s<e&&s<i&&s<o)return 0;c=ns,h=(l=e)-2*(u=i)+(h=o),u=2*(u-l),l-=s=s,s=0,En(h)?Bn(u)&&0<=(p=-l/u)&&p<=1&&(c[s++]=p):En(l=u*u-4*h*l)?0<=(p=-u/(2*h))&&p<=1&&(c[s++]=p):0<l&&(d=(-u-(l=Dn(l)))/(2*h),0<=(p=(-u+l)/(2*h))&&p<=1&&(c[s++]=p),0<=d)&&d<=1&&(c[s++]=d);var l,u,h,c,p,d,f=s;if(0===f)return 0;var g=Xn(e,i,o);if(0<=g&&g<=1){for(var y=0,m=Un(e,i,o,g),v=0;v<f;v++){var _=0===ns[v]||1===ns[v]?.5:1;Un(t,n,r,ns[v])<a||(ns[v]<g?y+=m<e?_:-_:y+=o<m?_:-_)}return y}return _=0===ns[0]||1===ns[0]?.5:1,Un(t,n,r,ns[0])<a?0:o<e?_:-_}function as(t,e,n,i,r){for(var o,a=t.data,s=t.len(),l=0,u=0,h=0,c=0,p=0,d=0;d<s;){var f=a[d++],g=1===d;switch(f===Ja.M&&1<d&&(n||(l+=Qa(u,h,c,p,i,r))),g&&(c=u=a[d],p=h=a[d+1]),f){case Ja.M:u=c=a[d++],h=p=a[d++];break;case Ja.L:if(n){if(ja(u,h,a[d],a[d+1],e,i,r))return!0}else l+=Qa(u,h,a[d],a[d+1],i,r)||0;u=a[d++],h=a[d++];break;case Ja.C:if(n){if(function(t,e,n,i,r,o,a,s,l,u,h){if(0!==l)return!(e+(l=l)<h&&i+l<h&&o+l<h&&s+l<h||h<e-l&&h<i-l&&h<o-l&&h<s-l||t+l<u&&n+l<u&&r+l<u&&a+l<u||u<t-l&&u<n-l&&u<r-l&&u<a-l)&&Gn(t,e,n,i,r,o,a,s,u,h,null)<=l/2}(u,h,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],e,i,r))return!0}else l+=rs(u,h,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],i,r)||0;u=a[d++],h=a[d++];break;case Ja.Q:if(n){if(function(t,e,n,i,r,o,a,s,l){if(0!==a)return!(e+(a=a)<l&&i+a<l&&o+a<l||l<e-a&&l<i-a&&l<o-a||t+a<s&&n+a<s&&r+a<s||s<t-a&&s<n-a&&s<r-a)&&jn(t,e,n,i,r,o,s,l,null)<=a/2}(u,h,a[d++],a[d++],a[d],a[d+1],e,i,r))return!0}else l+=os(u,h,a[d++],a[d++],a[d],a[d+1],i,r)||0;u=a[d++],h=a[d++];break;case Ja.A:var y=a[d++],m=a[d++],v=a[d++],_=a[d++],x=a[d++],w=a[d++],b=(d+=1,!!(1-a[d++])),S=Math.cos(x)*v+y,M=Math.sin(x)*_+m,T=(g?(c=S,p=M):l+=Qa(u,h,S,M,i,r),(i-y)*_/v+y);if(n){if(function(t,e,n,i,r,o,a,s,l){if(0!==a)return a=a,s-=t,l-=e,!(n<(t=Math.sqrt(s*s+l*l))-a||t+a<n)&&(Math.abs(i-r)%$a<1e-4||((r=o?(e=i,i=Ka(r),Ka(e)):(i=Ka(i),Ka(r)))<i&&(r+=$a),(t=Math.atan2(l,s))<0&&(t+=$a),i<=t&&t<=r)||i<=t+$a&&t+$a<=r)}(y,m,_,x,x+w,b,e,T,r))return!0}else l+=function(t,e,n,i,r,o,a,s){if(n<(s-=e)||s<-n)return 0;var e=Math.sqrt(n*n-s*s);if(ns[0]=-e,ns[1]=e,(n=Math.abs(i-r))<1e-4)return 0;if(ts-1e-4<=n)return r=ts,h=o?1:-1,a>=ns[i=0]+t&&a<=ns[1]+t?h:0;r<i&&(e=i,i=r,r=e),i<0&&(i+=ts,r+=ts);for(var l=0,u=0;u<2;u++){var h,c=ns[u];a<c+t&&(h=o?1:-1,i<=(c=(c=Math.atan2(s,c))<0?ts+c:c)&&c<=r||i<=c+ts&&c+ts<=r)&&(l+=h=c>Math.PI/2&&c<1.5*Math.PI?-h:h)}return l}(y,m,_,x,x+w,b,T,r);u=Math.cos(x+w)*v+y,h=Math.sin(x+w)*_+m;break;case Ja.R:c=u=a[d++],p=h=a[d++];if(S=c+a[d++],M=p+a[d++],n){if(ja(c,p,S,p,e,i,r)||ja(S,p,S,M,e,i,r)||ja(S,M,c,M,e,i,r)||ja(c,M,c,p,e,i,r))return!0}else l=(l+=Qa(S,p,S,M,i,r))+Qa(c,M,c,p,i,r);break;case Ja.Z:if(n){if(ja(u,h,c,p,e,i,r))return!0}else l+=Qa(u,h,c,p,i,r);u=c,h=p}}return n||(t=h,o=p,Math.abs(t-o)<es)||(l+=Qa(u,h,c,p,i,r)||0),0!==l}var ss,ls=B({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},fa),us={style:B({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},ga.style)},hs=xr.concat(["invisible","culling","z","z2","zlevel","parent"]),cs=(a(u,ss=n),u.prototype.update=function(){var e=this,t=(ss.prototype.update.call(this),this.style);if(t.decal){var n,i=this._decalEl=this._decalEl||new u,r=(i.buildPath===u.prototype.buildPath&&(i.buildPath=function(t){e.buildPath(t,e.shape)}),i.silent=!0,i.style);for(n in t)r[n]!==t[n]&&(r[n]=t[n]);r.fill=t.fill?t.decal:null,r.decal=null,r.shadowColor=null,t.strokeFirst&&(r.stroke=null);for(var o=0;o<hs.length;++o)i[hs[o]]=this[hs[o]];i.__dirty|=_n}else this._decalEl&&(this._decalEl=null)},u.prototype.getDecalElement=function(){return this._decalEl},u.prototype._init=function(t){var e=ct(t),n=(this.shape=this.getDefaultShape(),this.getDefaultStyle());n&&this.useStyle(n);for(var i=0;i<e.length;i++){var r=e[i],o=t[r];"style"===r?this.style?P(this.style,o):this.useStyle(o):"shape"===r?P(this.shape,o):ss.prototype.attrKV.call(this,r,o)}this.style||this.useStyle({})},u.prototype.getDefaultStyle=function(){return null},u.prototype.getDefaultShape=function(){return{}},u.prototype.canBeInsideText=function(){return this.hasFill()},u.prototype.getInsideTextFill=function(){var t,e=this.style.fill;if("none"!==e){if(V(e))return.5<(t=Si(e,0))?hr:.2<t?"#eee":cr;if(e)return cr}return hr},u.prototype.getInsideTextStroke=function(t){var e=this.style.fill;if(V(e)){var n=this.__zr;if(!(!n||!n.isDarkMode())==Si(t,0)<.4)return e}},u.prototype.buildPath=function(t,e,n){},u.prototype.pathUpdated=function(){this.__dirty&=~xn},u.prototype.getUpdatedPathProxy=function(t){return this.path||this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,t),this.path},u.prototype.createPathProxy=function(){this.path=new Ya(!1)},u.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return!(null==e||"none"===e||!(0<t.lineWidth))},u.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},u.prototype.getBoundingRect=function(){var t,e,n=this._rect,i=this.style,r=!n;return r&&(t=!1,this.path||(t=!0,this.createPathProxy()),e=this.path,(t||this.__dirty&xn)&&(e.beginPath(),this.buildPath(e,this.shape,!1),this.pathUpdated()),n=e.getBoundingRect()),this._rect=n,this.hasStroke()&&this.path&&0<this.path.len()?(t=this._rectStroke||(this._rectStroke=n.clone()),(this.__dirty||r)&&(t.copy(n),e=i.strokeNoScale?this.getLineScale():1,r=i.lineWidth,this.hasFill()||(i=this.strokeContainThreshold,r=Math.max(r,null==i?4:i)),1e-10<e)&&(t.width+=r/e,t.height+=r/e,t.x-=r/e/2,t.y-=r/e/2),t):n},u.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect(),r=this.style;if(t=n[0],e=n[1],i.contain(t,e)){n=this.path;if(this.hasStroke()){i=r.lineWidth,r=r.strokeNoScale?this.getLineScale():1;if(1e-10<r&&(this.hasFill()||(i=Math.max(i,this.strokeContainThreshold)),as(n,i/r,!0,t,e)))return!0}if(this.hasFill())return as(n,0,!1,t,e)}return!1},u.prototype.dirtyShape=function(){this.__dirty|=xn,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},u.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},u.prototype.animateShape=function(t){return this.animate("shape",t)},u.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():"shape"===t?this.dirtyShape():this.markRedraw()},u.prototype.attrKV=function(t,e){"shape"===t?this.setShape(e):ss.prototype.attrKV.call(this,t,e)},u.prototype.setShape=function(t,e){var n=(n=this.shape)||(this.shape={});return"string"==typeof t?n[t]=e:P(n,t),this.dirtyShape(),this},u.prototype.shapeChanged=function(){return!!(this.__dirty&xn)},u.prototype.createStyle=function(t){return zt(ls,t)},u.prototype._innerSaveToNormal=function(t){ss.prototype._innerSaveToNormal.call(this,t);var e=this._normalState;t.shape&&!e.shape&&(e.shape=P({},this.shape))},u.prototype._applyStateObj=function(t,e,n,i,r,o){ss.prototype._applyStateObj.call(this,t,e,n,i,r,o);var a,s=!(e&&i);if(e&&e.shape?r?i?a=e.shape:(a=P({},n.shape),P(a,e.shape)):(a=P({},(i?this:n).shape),P(a,e.shape)):s&&(a=n.shape),a)if(r){this.shape=P({},this.shape);for(var l={},u=ct(a),h=0;h<u.length;h++){var c=u[h];"object"==typeof a[c]?this.shape[c]=a[c]:l[c]=a[c]}this._transitionState(t,{shape:l},o)}else this.shape=a,this.dirtyShape()},u.prototype._mergeStates=function(t){for(var e,n=ss.prototype._mergeStates.call(this,t),i=0;i<t.length;i++){var r=t[i];r.shape&&this._mergeStyle(e=e||{},r.shape)}return e&&(n.shape=e),n},u.prototype.getAnimationStyleProps=function(){return us},u.prototype.isZeroArea=function(){return!1},u.extend=function(n){a(r,i=u),r.prototype.getDefaultStyle=function(){return b(n.style)},r.prototype.getDefaultShape=function(){return b(n.shape)};var i,t,e=r;function r(t){var e=i.call(this,t)||this;return n.init&&n.init.call(e,t),e}for(t in n)"function"==typeof n[t]&&(e.prototype[t]=n[t]);return e},u.initDefaultProps=((pu=u.prototype).type="path",pu.strokeContainThreshold=5,pu.segmentIgnoreThreshold=0,pu.subPixelOptimize=!1,pu.autoBatch=!1,void(pu.__dirty=2|_n|xn)),u);function u(t){return ss.call(this,t)||this}var ps,ds=B({strokeFirst:!0,font:Z,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},ls),fs=(a(gs,ps=n),gs.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return null!=e&&"none"!==e&&0<t.lineWidth},gs.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},gs.prototype.createStyle=function(t){return zt(ds,t)},gs.prototype.setBoundingRect=function(t){this._rect=t},gs.prototype.getBoundingRect=function(){var t,e=this.style;return this._rect||(null!=(t=e.text)?t+="":t="",(t=Mr(t,e.font,e.textAlign,e.textBaseline)).x+=e.x||0,t.y+=e.y||0,this.hasStroke()&&(e=e.lineWidth,t.x-=e/2,t.y-=e/2,t.width+=e,t.height+=e),this._rect=t),this._rect},gs.initDefaultProps=void(gs.prototype.dirtyRectTolerance=10),gs);function gs(){return null!==ps&&ps.apply(this,arguments)||this}fs.prototype.type="tspan";var ys=B({x:0,y:0},fa),ms={style:B({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},ga.style)};a(xs,vs=n),xs.prototype.createStyle=function(t){return zt(ys,t)},xs.prototype._getSize=function(t){var e,n=this.style,i=n[t];return null!=i?i:(i=(i=n.image)&&"string"!=typeof i&&i.width&&i.height?n.image:this.__image)?null==(e=n[n="width"===t?"height":"width"])?i[t]:i[t]/i[n]*e:0},xs.prototype.getWidth=function(){return this._getSize("width")},xs.prototype.getHeight=function(){return this._getSize("height")},xs.prototype.getAnimationStyleProps=function(){return ms},xs.prototype.getBoundingRect=function(){var t=this.style;return this._rect||(this._rect=new q(t.x||0,t.y||0,this.getWidth(),this.getHeight())),this._rect};var vs,_s=xs;function xs(){return null!==vs&&vs.apply(this,arguments)||this}_s.prototype.type="image";var ws=Math.round;function bs(t,e,n){var i,r,o;if(e)return i=e.x1,r=e.x2,o=e.y1,e=e.y2,t.x1=i,t.x2=r,t.y1=o,t.y2=e,(n=n&&n.lineWidth)&&(ws(2*i)===ws(2*r)&&(t.x1=t.x2=Ms(i,n,!0)),ws(2*o)===ws(2*e))&&(t.y1=t.y2=Ms(o,n,!0)),t}function Ss(t,e,n){var i,r,o;if(e)return i=e.x,r=e.y,o=e.width,e=e.height,t.x=i,t.y=r,t.width=o,t.height=e,(n=n&&n.lineWidth)&&(t.x=Ms(i,n,!0),t.y=Ms(r,n,!0),t.width=Math.max(Ms(i+o,n,!1)-t.x,0===o?0:1),t.height=Math.max(Ms(r+e,n,!1)-t.y,0===e?0:1)),t}function Ms(t,e,n){var i;return e?((i=ws(2*t))+ws(e))%2==0?i/2:(i+(n?1:-1))/2:t}var Ts,Cs=function(){this.x=0,this.y=0,this.width=0,this.height=0},ks={},Is=(a(Ds,Ts=cs),Ds.prototype.getDefaultShape=function(){return new Cs},Ds.prototype.buildPath=function(t,e){var n,i,r,o,a,s,l,u,h,c,p,d,f,g;this.subPixelOptimize?(n=(a=Ss(ks,e,this.style)).x,i=a.y,r=a.width,o=a.height,a.r=e.r,e=a):(n=e.x,i=e.y,r=e.width,o=e.height),e.r?(a=t,p=(e=e).x,d=e.y,f=e.width,g=e.height,e=e.r,f<0&&(p+=f,f=-f),g<0&&(d+=g,g=-g),"number"==typeof e?s=l=u=h=e:e instanceof Array?1===e.length?s=l=u=h=e[0]:2===e.length?(s=u=e[0],l=h=e[1]):3===e.length?(s=e[0],l=h=e[1],u=e[2]):(s=e[0],l=e[1],u=e[2],h=e[3]):s=l=u=h=0,f<s+l&&(s*=f/(c=s+l),l*=f/c),f<u+h&&(u*=f/(c=u+h),h*=f/c),g<l+u&&(l*=g/(c=l+u),u*=g/c),g<s+h&&(s*=g/(c=s+h),h*=g/c),a.moveTo(p+s,d),a.lineTo(p+f-l,d),0!==l&&a.arc(p+f-l,d+l,l,-Math.PI/2,0),a.lineTo(p+f,d+g-u),0!==u&&a.arc(p+f-u,d+g-u,u,0,Math.PI/2),a.lineTo(p+h,d+g),0!==h&&a.arc(p+h,d+g-h,h,Math.PI/2,Math.PI),a.lineTo(p,d+s),0!==s&&a.arc(p+s,d+s,s,Math.PI,1.5*Math.PI)):t.rect(n,i,r,o)},Ds.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},Ds);function Ds(t){return Ts.call(this,t)||this}Is.prototype.type="rect";var Ls,As={fill:"#000"},Ps={style:B({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},ga.style)},Os=(a(Rs,Ls=n),Rs.prototype.childrenRef=function(){return this._children},Rs.prototype.update=function(){Ls.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var t=0;t<this._children.length;t++){var e=this._children[t];e.zlevel=this.zlevel,e.z=this.z,e.z2=this.z2,e.culling=this.culling,e.cursor=this.cursor,e.invisible=this.invisible}},Rs.prototype.updateTransform=function(){var t=this.innerTransformable;t?(t.updateTransform(),t.transform&&(this.transform=t.transform)):Ls.prototype.updateTransform.call(this)},Rs.prototype.getLocalTransform=function(t){var e=this.innerTransformable;return e?e.getLocalTransform(t):Ls.prototype.getLocalTransform.call(this,t)},Rs.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),Ls.prototype.getComputedTransform.call(this)},Rs.prototype._updateSubTexts=function(){var t;this._childCursor=0,Fs(t=this.style),O(t.rich,Fs),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},Rs.prototype.addSelfToZr=function(t){Ls.prototype.addSelfToZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].__zr=t},Rs.prototype.removeSelfFromZr=function(t){Ls.prototype.removeSelfFromZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].__zr=null},Rs.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var t=new q(0,0,0,0),e=this._children,n=[],i=null,r=0;r<e.length;r++){var o=e[r],a=o.getBoundingRect(),o=o.getLocalTransform(n);o?(t.copy(a),t.applyTransform(o),(i=i||t.clone()).union(t)):(i=i||a.clone()).union(a)}this._rect=i||t}return this._rect},Rs.prototype.setDefaultTextStyle=function(t){this._defaultStyle=t||As},Rs.prototype.setTextContent=function(t){},Rs.prototype._mergeStyle=function(t,e){var n,i;return e&&(n=e.rich,i=t.rich||n&&{},P(t,e),n&&i?(this._mergeRich(i,n),t.rich=i):i&&(t.rich=i)),t},Rs.prototype._mergeRich=function(t,e){for(var n=ct(e),i=0;i<n.length;i++){var r=n[i];t[r]=t[r]||{},P(t[r],e[r])}},Rs.prototype.getAnimationStyleProps=function(){return Ps},Rs.prototype._getOrCreateChild=function(t){var e=this._children[this._childCursor];return e&&e instanceof t||(e=new t),(this._children[this._childCursor++]=e).__zr=this.__zr,e.parent=this,e},Rs.prototype._updatePlainTexts=function(){for(var t,e=this.style,n=e.font||Z,i=e.padding,r=function(t,e){null!=t&&(t+="");var n,i=e.overflow,r=e.padding,o=e.font,a="truncate"===i,s=kr(o),l=N(e.lineHeight,s),u=!!e.backgroundColor,h="truncate"===e.lineOverflow,c=e.width,i=(n=null==c||"break"!==i&&"breakAll"!==i?t?t.split("\n"):[]:t?ca(t,e.font,c,"breakAll"===i,0).lines:[]).length*l,p=N(e.height,i);if(p<i&&h&&(h=Math.floor(p/l),n=n.slice(0,h)),t&&a&&null!=c)for(var d=ia(c,o,e.ellipsis,{minChar:e.truncateMinChar,placeholder:e.placeholder}),f=0;f<n.length;f++)n[f]=ra(n[f],d);for(var h=p,g=0,f=0;f<n.length;f++)g=Math.max(br(n[f],o),g);return null==c&&(c=g),t=g,r&&(h+=r[0]+r[2],t+=r[1]+r[3],c+=r[1]+r[3]),{lines:n,height:p,outerWidth:t=u?c:t,outerHeight:h,lineHeight:l,calculatedLineHeight:s,contentWidth:g,contentHeight:i,width:c}}(Gs(e),e),o=Us(e),a=!!e.backgroundColor,s=r.outerHeight,l=r.outerWidth,u=r.contentWidth,h=r.lines,c=r.lineHeight,p=this._defaultStyle,d=e.x||0,f=e.y||0,g=e.align||p.align||"left",y=e.verticalAlign||p.verticalAlign||"top",m=d,v=Cr(f,r.contentHeight,y),_=((o||i)&&(t=Tr(d,l,g),f=Cr(f,s,y),o)&&this._renderBackground(e,e,t,f,l,s),v+=c/2,i&&(m=Ws(d,g,i),"top"===y?v+=i[0]:"bottom"===y&&(v-=i[2])),0),o=!1,x=(Hs(("fill"in e?e:(o=!0,p)).fill)),w=(Vs("stroke"in e?e.stroke:a||p.autoStroke&&!o?null:(_=2,p.stroke))),b=0<e.textShadowBlur,S=null!=e.width&&("truncate"===e.overflow||"break"===e.overflow||"breakAll"===e.overflow),M=r.calculatedLineHeight,T=0;T<h.length;T++){var C=this._getOrCreateChild(fs),k=C.createStyle();C.useStyle(k),k.text=h[T],k.x=m,k.y=v,g&&(k.textAlign=g),k.textBaseline="middle",k.opacity=e.opacity,k.strokeFirst=!0,b&&(k.shadowBlur=e.textShadowBlur||0,k.shadowColor=e.textShadowColor||"transparent",k.shadowOffsetX=e.textShadowOffsetX||0,k.shadowOffsetY=e.textShadowOffsetY||0),k.stroke=w,k.fill=x,w&&(k.lineWidth=e.lineWidth||_,k.lineDash=e.lineDash,k.lineDashOffset=e.lineDashOffset||0),k.font=n,zs(k,e),v+=c,S&&C.setBoundingRect(new q(Tr(k.x,e.width,k.textAlign),Cr(k.y,M,k.textBaseline),u,M))}},Rs.prototype._updateRichTexts=function(){for(var t=this.style,e=la(Gs(t),t),n=e.width,i=e.outerWidth,r=e.outerHeight,o=t.padding,a=t.x||0,s=t.y||0,l=this._defaultStyle,u=t.align||l.align,l=t.verticalAlign||l.verticalAlign,a=Tr(a,i,u),u=Cr(s,r,l),h=a,c=u,p=(o&&(h+=o[3],c+=o[0]),h+n),d=(Us(t)&&this._renderBackground(t,t,a,u,i,r),!!t.backgroundColor),f=0;f<e.lines.length;f++){for(var g=e.lines[f],y=g.tokens,m=y.length,v=g.lineHeight,_=g.width,x=0,w=h,b=p,S=m-1,M=void 0;x<m&&(!(M=y[x]).align||"left"===M.align);)this._placeToken(M,t,v,c,w,"left",d),_-=M.width,w+=M.width,x++;for(;0<=S&&"right"===(M=y[S]).align;)this._placeToken(M,t,v,c,b,"right",d),_-=M.width,b-=M.width,S--;for(w+=(n-(w-h)-(p-b)-_)/2;x<=S;)M=y[x],this._placeToken(M,t,v,c,w+M.width/2,"center",d),w+=M.width,x++;c+=v}},Rs.prototype._placeToken=function(t,e,n,i,r,o,a){var s=e.rich[t.styleName]||{},l=(s.text=t.text,t.verticalAlign),u=i+n/2;"top"===l?u=i+t.height/2:"bottom"===l&&(u=i+n-t.height/2);!t.isLineHolder&&Us(s)&&this._renderBackground(s,e,"right"===o?r-t.width:"center"===o?r-t.width/2:r,u-t.height/2,t.width,t.height);var l=!!s.backgroundColor,i=t.textPadding,n=(i&&(r=Ws(r,o,i),u-=t.height/2-i[0]-t.innerHeight/2),this._getOrCreateChild(fs)),i=n.createStyle(),h=(n.useStyle(i),this._defaultStyle),c=!1,p=0,d=Hs(("fill"in s?s:"fill"in e?e:(c=!0,h)).fill),l=Vs("stroke"in s?s.stroke:"stroke"in e?e.stroke:l||a||h.autoStroke&&!c?null:(p=2,h.stroke)),a=0<s.textShadowBlur||0<e.textShadowBlur,c=(i.text=t.text,i.x=r,i.y=u,a&&(i.shadowBlur=s.textShadowBlur||e.textShadowBlur||0,i.shadowColor=s.textShadowColor||e.textShadowColor||"transparent",i.shadowOffsetX=s.textShadowOffsetX||e.textShadowOffsetX||0,i.shadowOffsetY=s.textShadowOffsetY||e.textShadowOffsetY||0),i.textAlign=o,i.textBaseline="middle",i.font=t.font||Z,i.opacity=Mt(s.opacity,e.opacity,1),zs(i,s),l&&(i.lineWidth=Mt(s.lineWidth,e.lineWidth,p),i.lineDash=N(s.lineDash,e.lineDash),i.lineDashOffset=e.lineDashOffset||0,i.stroke=l),d&&(i.fill=d),t.contentWidth),h=t.contentHeight;n.setBoundingRect(new q(Tr(i.x,c,i.textAlign),Cr(i.y,h,i.textBaseline),c,h))},Rs.prototype._renderBackground=function(t,e,n,i,r,o){var a,s,l,u,h=t.backgroundColor,c=t.borderWidth,p=t.borderColor,d=h&&h.image,f=h&&!d,g=t.borderRadius,y=this,g=((f||t.lineHeight||c&&p)&&((a=this._getOrCreateChild(Is)).useStyle(a.createStyle()),a.style.fill=null,(l=a.shape).x=n,l.y=i,l.width=r,l.height=o,l.r=g,a.dirtyShape()),f?((u=a.style).fill=h||null,u.fillOpacity=N(t.fillOpacity,1)):d&&((s=this._getOrCreateChild(_s)).onload=function(){y.dirtyStyle()},(l=s.style).image=h.image,l.x=n,l.y=i,l.width=r,l.height=o),c&&p&&((u=a.style).lineWidth=c,u.stroke=p,u.strokeOpacity=N(t.strokeOpacity,1),u.lineDash=t.borderDash,u.lineDashOffset=t.borderDashOffset||0,a.strokeContainThreshold=0,a.hasFill())&&a.hasStroke()&&(u.strokeFirst=!0,u.lineWidth*=2),(a||s).style);g.shadowBlur=t.shadowBlur||0,g.shadowColor=t.shadowColor||"transparent",g.shadowOffsetX=t.shadowOffsetX||0,g.shadowOffsetY=t.shadowOffsetY||0,g.opacity=Mt(t.opacity,e.opacity,1)},Rs.makeFont=function(t){var e,n="";return(n=null!=(e=t).fontSize||e.fontFamily||e.fontWeight?[t.fontStyle,t.fontWeight,"string"!=typeof(e=t.fontSize)||-1===e.indexOf("px")&&-1===e.indexOf("rem")&&-1===e.indexOf("em")?isNaN(+e)?"12px":e+"px":e,t.fontFamily||"sans-serif"].join(" "):n)&&It(n)||t.textFont||t.font},Rs);function Rs(t){var e=Ls.call(this)||this;return e.type="text",e._children=[],e._defaultStyle=As,e.attr(t),e}var Ns={left:!0,right:1,center:1},Es={top:1,bottom:1,middle:1},Bs=["fontStyle","fontWeight","fontSize","fontFamily"];function zs(t,e){for(var n=0;n<Bs.length;n++){var i=Bs[n],r=e[i];null!=r&&(t[i]=r)}}function Fs(t){var e;t&&(t.font=Os.makeFont(t),e=t.align,t.align=null==(e="middle"===e?"center":e)||Ns[e]?e:"left",e=t.verticalAlign,t.verticalAlign=null==(e="center"===e?"middle":e)||Es[e]?e:"top",t.padding)&&(t.padding=Ct(t.padding))}function Vs(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function Hs(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function Ws(t,e,n){return"right"===e?t-n[1]:"center"===e?t+n[3]/2-n[1]/2:t+n[3]}function Gs(t){t=t.text;return null!=t&&(t+=""),t}function Us(t){return!!(t.backgroundColor||t.lineHeight||t.borderWidth&&t.borderColor)}var qs=Lo(),Xs=1,Ys={},js=Lo(),Zs=Lo(),Ks=0,$s=1,Qs=2,Js=["emphasis","blur","select"],tl=["normal","emphasis","blur","select"],el="highlight",nl="downplay",il="select",rl="unselect",ol="toggleSelect";function al(t){return null!=t&&"none"!==t}function sl(t,e,n){t.onHoverStateChange&&(t.hoverState||0)!==n&&t.onHoverStateChange(e),t.hoverState=n}function ll(t){sl(t,"emphasis",Qs)}function ul(t){t.hoverState===Qs&&sl(t,"normal",Ks)}function hl(t){sl(t,"blur",$s)}function cl(t){t.hoverState===$s&&sl(t,"normal",Ks)}function pl(t){t.selected=!0}function dl(t){t.selected=!1}function fl(t,e,n){e(t,n)}function gl(t,e,n){fl(t,e,n),t.isGroup&&t.traverse(function(t){fl(t,e,n)})}function yl(t,e,n){var i=0<=k(t.currentStates,e),r=t.style.opacity,t=i?null:function(t,e,n,i){for(var r=t.style,o={},a=0;a<e.length;a++){var s=e[a],l=r[s];o[s]=null==l?i&&i[s]:l}for(a=0;a<t.animators.length;a++){var u=t.animators[a];u.__fromStateTransition&&u.__fromStateTransition.indexOf(n)<0&&"style"===u.targetName&&u.saveTo(o,e)}return o}(t,["opacity"],e,{opacity:1}),e=(n=n||{}).style||{};return null==e.opacity&&(n=P({},n),e=P({opacity:i?r:.1*t.opacity},e),n.style=e),n}function ml(t,e){var n,i,r,o,a,s=this.states[t];if(this.style){if("emphasis"===t)return n=this,i=s,e=(e=e)&&0<=k(e,"select"),a=!1,n instanceof cs&&(r=js(n),o=e&&r.selectFill||r.normalFill,e=e&&r.selectStroke||r.normalStroke,al(o)||al(e))&&("inherit"===(r=(i=i||{}).style||{}).fill?(a=!0,i=P({},i),(r=P({},r)).fill=o):!al(r.fill)&&al(o)?(a=!0,i=P({},i),(r=P({},r)).fill=Ti(o)):!al(r.stroke)&&al(e)&&(a||(i=P({},i),r=P({},r)),r.stroke=Ti(e)),i.style=r),i&&null==i.z2&&(a||(i=P({},i)),o=n.z2EmphasisLift,i.z2=n.z2+(null!=o?o:10)),i;if("blur"===t)return yl(this,t,s);if("select"===t)return e=this,(r=s)&&null==r.z2&&(r=P({},r),a=e.z2SelectLift,r.z2=e.z2+(null!=a?a:9)),r}return s}function vl(t){t.stateProxy=ml;var e=t.getTextContent(),t=t.getTextGuideLine();e&&(e.stateProxy=ml),t&&(t.stateProxy=ml)}function _l(t,e){Cl(t,e)||t.__highByOuter||gl(t,ll)}function xl(t,e){Cl(t,e)||t.__highByOuter||gl(t,ul)}function wl(t,e){t.__highByOuter|=1<<(e||0),gl(t,ll)}function bl(t,e){(t.__highByOuter&=~(1<<(e||0)))||gl(t,ul)}function Sl(t){gl(t,cl)}function Ml(t){gl(t,pl)}function Tl(t){gl(t,dl)}function Cl(t,e){return t.__highDownSilentOnTouch&&e.zrByTouch}function kl(r){var e=r.getModel(),o=[],a=[];e.eachComponent(function(t,e){var n=Zs(e),t="series"===t,i=t?r.getViewOfSeriesModel(e):r.getViewOfComponentModel(e);t||a.push(i),n.isBlured&&(i.group.traverse(function(t){cl(t)}),t)&&o.push(e),n.isBlured=!1}),O(a,function(t){t&&t.toggleBlurSeries&&t.toggleBlurSeries(o,!1,e)})}function Il(t,o,a,s){var l,u,h,n=s.getModel();function c(t,e){for(var n=0;n<e.length;n++){var i=t.getItemGraphicEl(e[n]);i&&Sl(i)}}a=a||"coordinateSystem",null!=t&&o&&"none"!==o&&(l=n.getSeriesByIndex(t),(u=l.coordinateSystem)&&u.master&&(u=u.master),h=[],n.eachSeries(function(t){var e=l===t,n=t.coordinateSystem,n=(n=n&&n.master?n.master:n)&&u?n===u:e;if(!("series"===a&&!e||"coordinateSystem"===a&&!n||"series"===o&&e)){if(s.getViewOfSeriesModel(t).group.traverse(function(t){t.__highByOuter&&e&&"self"===o||hl(t)}),st(o))c(t.getData(),o);else if(R(o))for(var i=ct(o),r=0;r<i.length;r++)c(t.getData(i[r]),o[i[r]]);h.push(t),Zs(t).isBlured=!0}}),n.eachComponent(function(t,e){"series"!==t&&(t=s.getViewOfComponentModel(e))&&t.toggleBlurSeries&&t.toggleBlurSeries(h,!0,n)}))}function Dl(t,e,n){var i;null!=t&&null!=e&&(t=n.getModel().getComponent(t,e))&&(Zs(t).isBlured=!0,i=n.getViewOfComponentModel(t))&&i.focusBlurEnabled&&i.group.traverse(function(t){hl(t)})}function Ll(t,e,n,i){var r={focusSelf:!1,dispatchers:null};if(null==t||"series"===t||null==e||null==n)return r;t=i.getModel().getComponent(t,e);if(!t)return r;e=i.getViewOfComponentModel(t);if(!e||!e.findHighDownDispatchers)return r;for(var o,a=e.findHighDownDispatchers(n),s=0;s<a.length;s++)if("self"===qs(a[s]).focus){o=!0;break}return{focusSelf:o,dispatchers:a}}function Al(i){O(i.getAllData(),function(t){var e=t.data,n=t.type;e.eachItemGraphicEl(function(t,e){(i.isSelected(e,n)?Ml:Tl)(t)})})}function Pl(t,e,n){Bl(t,!0),gl(t,vl);t=qs(t),null!=e?(t.focus=e,t.blurScope=n):t.focus&&(t.focus=null)}function Ol(t,e,n,i){i?Bl(t,!1):Pl(t,e,n)}var Rl=["emphasis","blur","select"],Nl={itemStyle:"getItemStyle",lineStyle:"getLineStyle",areaStyle:"getAreaStyle"};function El(t,e,n,i){n=n||"itemStyle";for(var r=0;r<Rl.length;r++){var o=Rl[r],a=e.getModel([o,n]);t.ensureState(o).style=i?i(a):a[Nl[n]]()}}function Bl(t,e){var e=!1===e,n=t;t.highDownSilentOnTouch&&(n.__highDownSilentOnTouch=t.highDownSilentOnTouch),e&&!n.__highDownDispatcher||(n.__highByOuter=n.__highByOuter||0,n.__highDownDispatcher=!e)}function zl(t){return!(!t||!t.__highDownDispatcher)}function Fl(t){t=t.type;return t===il||t===rl||t===ol}function Vl(t){t=t.type;return t===el||t===nl}var Hl=Ya.CMD,Wl=[[],[],[]],Gl=Math.sqrt,Ul=Math.atan2;var ql=Math.sqrt,Xl=Math.sin,Yl=Math.cos,jl=Math.PI;function Zl(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}function Kl(t,e){return(t[0]*e[0]+t[1]*e[1])/(Zl(t)*Zl(e))}function $l(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(Kl(t,e))}function Ql(t,e,n,i,r,o,a,s,l,u,h){var l=l*(jl/180),c=Yl(l)*(t-n)/2+Xl(l)*(e-i)/2,p=-1*Xl(l)*(t-n)/2+Yl(l)*(e-i)/2,d=c*c/(a*a)+p*p/(s*s),d=(1<d&&(a*=ql(d),s*=ql(d)),(r===o?-1:1)*ql((a*a*(s*s)-a*a*(p*p)-s*s*(c*c))/(a*a*(p*p)+s*s*(c*c)))||0),r=d*a*p/s,d=d*-s*c/a,t=(t+n)/2+Yl(l)*r-Xl(l)*d,n=(e+i)/2+Xl(l)*r+Yl(l)*d,e=$l([1,0],[(c-r)/a,(p-d)/s]),i=[(c-r)/a,(p-d)/s],c=[(-1*c-r)/a,(-1*p-d)/s],r=$l(i,c);Kl(i,c)<=-1&&(r=jl),(r=1<=Kl(i,c)?0:r)<0&&(p=Math.round(r/jl*1e6)/1e6,r=2*jl+p%2*jl),h.addData(u,t,n,a,s,e,r,l,o)}var Jl=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,tu=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;a(iu,eu=cs),iu.prototype.applyTransform=function(t){};var eu,nu=iu;function iu(){return null!==eu&&eu.apply(this,arguments)||this}function ru(t){return null!=t.setData}function ou(t,e){var S=function(t){var e=new Ya;if(t){var n,i=0,r=0,o=i,a=r,s=Ya.CMD,l=t.match(Jl);if(l){for(var u=0;u<l.length;u++){for(var h=l[u],c=h.charAt(0),p=void 0,d=h.match(tu)||[],f=d.length,g=0;g<f;g++)d[g]=parseFloat(d[g]);for(var y=0;y<f;){var m=void 0,v=void 0,_=void 0,x=void 0,w=void 0,b=void 0,S=void 0,M=i,T=r,C=void 0,k=void 0;switch(c){case"l":i+=d[y++],r+=d[y++],p=s.L,e.addData(p,i,r);break;case"L":i=d[y++],r=d[y++],p=s.L,e.addData(p,i,r);break;case"m":i+=d[y++],r+=d[y++],p=s.M,e.addData(p,i,r),o=i,a=r,c="l";break;case"M":i=d[y++],r=d[y++],p=s.M,e.addData(p,i,r),o=i,a=r,c="L";break;case"h":i+=d[y++],p=s.L,e.addData(p,i,r);break;case"H":i=d[y++],p=s.L,e.addData(p,i,r);break;case"v":r+=d[y++],p=s.L,e.addData(p,i,r);break;case"V":r=d[y++],p=s.L,e.addData(p,i,r);break;case"C":p=s.C,e.addData(p,d[y++],d[y++],d[y++],d[y++],d[y++],d[y++]),i=d[y-2],r=d[y-1];break;case"c":p=s.C,e.addData(p,d[y++]+i,d[y++]+r,d[y++]+i,d[y++]+r,d[y++]+i,d[y++]+r),i+=d[y-2],r+=d[y-1];break;case"S":m=i,v=r,C=e.len(),k=e.data,n===s.C&&(m+=i-k[C-4],v+=r-k[C-3]),p=s.C,M=d[y++],T=d[y++],i=d[y++],r=d[y++],e.addData(p,m,v,M,T,i,r);break;case"s":m=i,v=r,C=e.len(),k=e.data,n===s.C&&(m+=i-k[C-4],v+=r-k[C-3]),p=s.C,M=i+d[y++],T=r+d[y++],i+=d[y++],r+=d[y++],e.addData(p,m,v,M,T,i,r);break;case"Q":M=d[y++],T=d[y++],i=d[y++],r=d[y++],p=s.Q,e.addData(p,M,T,i,r);break;case"q":M=d[y++]+i,T=d[y++]+r,i+=d[y++],r+=d[y++],p=s.Q,e.addData(p,M,T,i,r);break;case"T":m=i,v=r,C=e.len(),k=e.data,n===s.Q&&(m+=i-k[C-4],v+=r-k[C-3]),i=d[y++],r=d[y++],p=s.Q,e.addData(p,m,v,i,r);break;case"t":m=i,v=r,C=e.len(),k=e.data,n===s.Q&&(m+=i-k[C-4],v+=r-k[C-3]),i+=d[y++],r+=d[y++],p=s.Q,e.addData(p,m,v,i,r);break;case"A":_=d[y++],x=d[y++],w=d[y++],b=d[y++],S=d[y++],Ql(M=i,T=r,i=d[y++],r=d[y++],b,S,_,x,w,p=s.A,e);break;case"a":_=d[y++],x=d[y++],w=d[y++],b=d[y++],S=d[y++],Ql(M=i,T=r,i+=d[y++],r+=d[y++],b,S,_,x,w,p=s.A,e)}}"z"!==c&&"Z"!==c||(p=s.Z,e.addData(p),i=o,r=a),n=p}e.toStatic()}}return e}(t),t=P({},e);return t.buildPath=function(t){var e;ru(t)?(t.setData(S.data),(e=t.getContext())&&t.rebuildPath(e,1)):S.rebuildPath(e=t,1)},t.applyTransform=function(t){var e=S,n=t;if(n){for(var i,r,o,a,s=e.data,l=e.len(),u=Hl.M,h=Hl.C,c=Hl.L,p=Hl.R,d=Hl.A,f=Hl.Q,g=0,y=0;g<l;){switch(i=s[g++],y=g,r=0,i){case u:case c:r=1;break;case h:r=3;break;case f:r=2;break;case d:var m=n[4],v=n[5],_=Gl(n[0]*n[0]+n[1]*n[1]),x=Gl(n[2]*n[2]+n[3]*n[3]),w=Ul(-n[1]/x,n[0]/_);s[g]*=_,s[g++]+=m,s[g]*=x,s[g++]+=v,s[g++]*=_,s[g++]*=x,s[g++]+=w,s[g++]+=w,y=g+=2;break;case p:a[0]=s[g++],a[1]=s[g++],ie(a,a,n),s[y++]=a[0],s[y++]=a[1],a[0]+=s[g++],a[1]+=s[g++],ie(a,a,n),s[y++]=a[0],s[y++]=a[1]}for(o=0;o<r;o++){var b=Wl[o];b[0]=s[g++],b[1]=s[g++],ie(b,b,n),s[y++]=b[0],s[y++]=b[1]}}e.increaseVersion()}this.dirtyShape()},t}var au,su=function(){this.cx=0,this.cy=0,this.r=0},lu=(a(uu,au=cs),uu.prototype.getDefaultShape=function(){return new su},uu.prototype.buildPath=function(t,e){t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI)},uu);function uu(t){return au.call(this,t)||this}lu.prototype.type="circle";var hu,cu=function(){this.cx=0,this.cy=0,this.rx=0,this.ry=0},pu=(a(du,hu=cs),du.prototype.getDefaultShape=function(){return new cu},du.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=e.rx,e=e.ry,o=.5522848*r,a=.5522848*e;t.moveTo(n-r,i),t.bezierCurveTo(n-r,i-a,n-o,i-e,n,i-e),t.bezierCurveTo(n+o,i-e,n+r,i-a,n+r,i),t.bezierCurveTo(n+r,i+a,n+o,i+e,n,i+e),t.bezierCurveTo(n-o,i+e,n-r,i+a,n-r,i),t.closePath()},du);function du(t){return hu.call(this,t)||this}pu.prototype.type="ellipse";var fu=Math.PI,gu=2*fu,yu=Math.sin,mu=Math.cos,vu=Math.acos,_u=Math.atan2,xu=Math.abs,wu=Math.sqrt,bu=Math.max,Su=Math.min,Mu=1e-4;function Tu(t,e,n,i,r,o,a){var s=t-n,l=e-i,a=(a?o:-o)/wu(s*s+l*l),l=a*l,a=-a*s,s=t+l,t=e+a,e=n+l,n=i+a,i=(s+e)/2,u=(t+n)/2,h=e-s,c=n-t,p=h*h+c*c,o=r-o,s=s*n-e*t,n=(c<0?-1:1)*wu(bu(0,o*o*p-s*s)),e=(s*c-h*n)/p,t=(-s*h-c*n)/p,d=(s*c+h*n)/p,s=(-s*h+c*n)/p,h=e-i,c=t-u,n=d-i,p=s-u;return n*n+p*p<h*h+c*c&&(e=d,t=s),{cx:e,cy:t,x0:-l,y0:-a,x1:e*(r/o-1),y1:t*(r/o-1)}}function Cu(t,e){var n,i,r,o,a,s,l,u,h,c,p,d,f,g,y,m,v,_,x,w,b,S,M,T,C,k,I,D,L,A,P=bu(e.r,0),O=bu(e.r0||0,0),R=0<P;(R||0<O)&&(R||(P=O,O=0),P<O&&(R=P,P=O,O=R),R=e.startAngle,n=e.endAngle,isNaN(R)||isNaN(n)||(i=e.cx,r=e.cy,o=!!e.clockwise,m=xu(n-R),Mu<(a=gu<m&&m%gu)&&(m=a),Mu<P?gu-Mu<m?(t.moveTo(i+P*mu(R),r+P*yu(R)),t.arc(i,r,P,R,n,!o),Mu<O&&(t.moveTo(i+O*mu(n),r+O*yu(n)),t.arc(i,r,O,n,R,o))):(S=b=w=x=_=v=c=h=k=C=T=M=u=l=s=a=void 0,p=P*mu(R),d=P*yu(R),f=O*mu(n),g=O*yu(n),(y=Mu<m)&&((e=e.cornerRadius)&&(a=(e=function(t){if(F(t)){var e=t.length;if(!e)return t;e=1===e?[t[0],t[0],0,0]:2===e?[t[0],t[0],t[1],t[1]]:3===e?t.concat(t[2]):t}else e=[t,t,t,t];return e}(e))[0],s=e[1],l=e[2],u=e[3]),e=xu(P-O)/2,M=Su(e,l),T=Su(e,u),C=Su(e,a),k=Su(e,s),v=h=bu(M,T),_=c=bu(C,k),Mu<h||Mu<c)&&(x=P*mu(n),w=P*yu(n),b=O*mu(R),S=O*yu(R),m<fu)&&(e=function(t,e,n,i,r,o,a,s){var l=(s=s-o)*(n=n-t)-(a=a-r)*(i=i-e);if(!(l*l<Mu))return[t+(l=(a*(e-o)-s*(t-r))/l)*n,e+l*i]}(p,d,b,S,x,w,f,g))&&(M=p-e[0],T=d-e[1],C=x-e[0],k=w-e[1],m=1/yu(vu((M*C+T*k)/(wu(M*M+T*T)*wu(C*C+k*k)))/2),M=wu(e[0]*e[0]+e[1]*e[1]),v=Su(h,(P-M)/(1+m)),_=Su(c,(O-M)/(m-1))),y?Mu<v?(I=Su(l,v),D=Su(u,v),L=Tu(b,S,p,d,P,I,o),A=Tu(x,w,f,g,P,D,o),t.moveTo(i+L.cx+L.x0,r+L.cy+L.y0),v<h&&I===D?t.arc(i+L.cx,r+L.cy,v,_u(L.y0,L.x0),_u(A.y0,A.x0),!o):(0<I&&t.arc(i+L.cx,r+L.cy,I,_u(L.y0,L.x0),_u(L.y1,L.x1),!o),t.arc(i,r,P,_u(L.cy+L.y1,L.cx+L.x1),_u(A.cy+A.y1,A.cx+A.x1),!o),0<D&&t.arc(i+A.cx,r+A.cy,D,_u(A.y1,A.x1),_u(A.y0,A.x0),!o))):(t.moveTo(i+p,r+d),t.arc(i,r,P,R,n,!o)):t.moveTo(i+p,r+d),Mu<O&&y?Mu<_?(I=Su(a,_),L=Tu(f,g,x,w,O,-(D=Su(s,_)),o),A=Tu(p,d,b,S,O,-I,o),t.lineTo(i+L.cx+L.x0,r+L.cy+L.y0),_<c&&I===D?t.arc(i+L.cx,r+L.cy,_,_u(L.y0,L.x0),_u(A.y0,A.x0),!o):(0<D&&t.arc(i+L.cx,r+L.cy,D,_u(L.y0,L.x0),_u(L.y1,L.x1),!o),t.arc(i,r,O,_u(L.cy+L.y1,L.cx+L.x1),_u(A.cy+A.y1,A.cx+A.x1),o),0<I&&t.arc(i+A.cx,r+A.cy,I,_u(A.y1,A.x1),_u(A.y0,A.x0),!o))):(t.lineTo(i+f,r+g),t.arc(i,r,O,n,R,o)):t.lineTo(i+f,r+g)):t.moveTo(i,r),t.closePath()))}var ku,Iu=function(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0,this.cornerRadius=0},Du=(a(Lu,ku=cs),Lu.prototype.getDefaultShape=function(){return new Iu},Lu.prototype.buildPath=function(t,e){Cu(t,e)},Lu.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},Lu);function Lu(t){return ku.call(this,t)||this}Du.prototype.type="sector";var Au,Pu=function(){this.cx=0,this.cy=0,this.r=0,this.r0=0},Ou=(a(Ru,Au=cs),Ru.prototype.getDefaultShape=function(){return new Pu},Ru.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=2*Math.PI;t.moveTo(n+e.r,i),t.arc(n,i,e.r,0,r,!1),t.moveTo(n+e.r0,i),t.arc(n,i,e.r0,0,r,!0)},Ru);function Ru(t){return Au.call(this,t)||this}function Nu(t,e,n){var i=e.smooth,r=e.points;if(r&&2<=r.length){if(i)for(var o=function(t,e,n,i){var r,o,a=[],s=[],l=[],u=[];if(i){for(var h=[1/0,1/0],c=[-1/0,-1/0],p=0,d=t.length;p<d;p++)re(h,h,t[p]),oe(c,c,t[p]);re(h,h,i[0]),oe(c,c,i[1])}for(p=0,d=t.length;p<d;p++){var f=t[p];if(n)r=t[p?p-1:d-1],o=t[(p+1)%d];else{if(0===p||p===d-1){a.push(qt(t[p]));continue}r=t[p-1],o=t[p+1]}Yt(s,o,r),Kt(s,s,e);var g=Qt(f,r),y=Qt(f,o),m=g+y,m=(0!==m&&(g/=m,y/=m),Kt(l,s,-g),Kt(u,s,y),Xt([],f,l)),g=Xt([],f,u);i&&(oe(m,m,h),re(m,m,c),oe(g,g,h),re(g,g,c)),a.push(m),a.push(g)}return n&&a.push(a.shift()),a}(r,i,n,e.smoothConstraint),a=(t.moveTo(r[0][0],r[0][1]),r.length),s=0;s<(n?a:a-1);s++){var l=o[2*s],u=o[2*s+1],h=r[(s+1)%a];t.bezierCurveTo(l[0],l[1],u[0],u[1],h[0],h[1])}else{t.moveTo(r[0][0],r[0][1]);for(var s=1,c=r.length;s<c;s++)t.lineTo(r[s][0],r[s][1])}n&&t.closePath()}}Ou.prototype.type="ring";var Eu,Bu=function(){this.points=null,this.smooth=0,this.smoothConstraint=null},zu=(a(Fu,Eu=cs),Fu.prototype.getDefaultShape=function(){return new Bu},Fu.prototype.buildPath=function(t,e){Nu(t,e,!0)},Fu);function Fu(t){return Eu.call(this,t)||this}zu.prototype.type="polygon";var Vu,Hu=function(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null},Wu=(a(Gu,Vu=cs),Gu.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},Gu.prototype.getDefaultShape=function(){return new Hu},Gu.prototype.buildPath=function(t,e){Nu(t,e,!1)},Gu);function Gu(t){return Vu.call(this,t)||this}Wu.prototype.type="polyline";var Uu,qu={},Xu=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1},Yu=(a(ju,Uu=cs),ju.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},ju.prototype.getDefaultShape=function(){return new Xu},ju.prototype.buildPath=function(t,e){o=(this.subPixelOptimize?(n=(o=bs(qu,e,this.style)).x1,i=o.y1,r=o.x2,o):(n=e.x1,i=e.y1,r=e.x2,e)).y2;var n,i,r,o,e=e.percent;0!==e&&(t.moveTo(n,i),e<1&&(r=n*(1-e)+r*e,o=i*(1-e)+o*e),t.lineTo(r,o))},ju.prototype.pointAt=function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]},ju);function ju(t){return Uu.call(this,t)||this}Yu.prototype.type="line";var Zu=[],Ku=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1};function $u(t,e,n){var i=t.cpx2,r=t.cpy2;return null!=i||null!=r?[(n?Fn:zn)(t.x1,t.cpx1,t.cpx2,t.x2,e),(n?Fn:zn)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(n?qn:Un)(t.x1,t.cpx1,t.x2,e),(n?qn:Un)(t.y1,t.cpy1,t.y2,e)]}a(th,Qu=cs),th.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},th.prototype.getDefaultShape=function(){return new Ku},th.prototype.buildPath=function(t,e){var n=e.x1,i=e.y1,r=e.x2,o=e.y2,a=e.cpx1,s=e.cpy1,l=e.cpx2,u=e.cpy2,e=e.percent;0!==e&&(t.moveTo(n,i),null==l||null==u?(e<1&&(Yn(n,a,r,e,Zu),a=Zu[1],r=Zu[2],Yn(i,s,o,e,Zu),s=Zu[1],o=Zu[2]),t.quadraticCurveTo(a,s,r,o)):(e<1&&(Wn(n,a,l,r,e,Zu),a=Zu[1],l=Zu[2],r=Zu[3],Wn(i,s,u,o,e,Zu),s=Zu[1],u=Zu[2],o=Zu[3]),t.bezierCurveTo(a,s,l,u,r,o)))},th.prototype.pointAt=function(t){return $u(this.shape,t,!1)},th.prototype.tangentAt=function(t){t=$u(this.shape,t,!0);return $t(t,t)};var Qu,Ju=th;function th(t){return Qu.call(this,t)||this}Ju.prototype.type="bezier-curve";var eh,nh=function(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0},ih=(a(rh,eh=cs),rh.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},rh.prototype.getDefaultShape=function(){return new nh},rh.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r,0),o=e.startAngle,a=e.endAngle,e=e.clockwise,s=Math.cos(o),l=Math.sin(o);t.moveTo(s*r+n,l*r+i),t.arc(n,i,r,o,a,!e)},rh);function rh(t){return eh.call(this,t)||this}ih.prototype.type="arc";a(sh,oh=cs),sh.prototype._updatePathDirty=function(){for(var t=this.shape.paths,e=this.shapeChanged(),n=0;n<t.length;n++)e=e||t[n].shapeChanged();e&&this.dirtyShape()},sh.prototype.beforeBrush=function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),n=0;n<t.length;n++)t[n].path||t[n].createPathProxy(),t[n].path.setScale(e[0],e[1],t[n].segmentIgnoreThreshold)},sh.prototype.buildPath=function(t,e){for(var n=e.paths||[],i=0;i<n.length;i++)n[i].buildPath(t,n[i].shape,!0)},sh.prototype.afterBrush=function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].pathUpdated()},sh.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),cs.prototype.getBoundingRect.call(this)};var oh,ah=sh;function sh(){var t=null!==oh&&oh.apply(this,arguments)||this;return t.type="compound",t}uh.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})};var lh=uh;function uh(t){this.colorStops=t||[]}a(ph,hh=lh);var hh,ch=ph;function ph(t,e,n,i,r,o){r=hh.call(this,r)||this;return r.x=null==t?0:t,r.y=null==e?0:e,r.x2=null==n?1:n,r.y2=null==i?0:i,r.type="linear",r.global=o||!1,r}a(fh,dh=lh);var dh,lh=fh;function fh(t,e,n,i,r){i=dh.call(this,i)||this;return i.x=null==t?.5:t,i.y=null==e?.5:e,i.r=null==n?.5:n,i.type="radial",i.global=r||!1,i}var gh=[0,0],yh=[0,0],mh=new M,vh=new M,_h=(xh.prototype.fromBoundingRect=function(t,e){var n=this._corners,i=this._axes,r=t.x,o=t.y,a=r+t.width,t=o+t.height;if(n[0].set(r,o),n[1].set(a,o),n[2].set(a,t),n[3].set(r,t),e)for(var s=0;s<4;s++)n[s].transform(e);M.sub(i[0],n[1],n[0]),M.sub(i[1],n[3],n[0]),i[0].normalize(),i[1].normalize();for(s=0;s<2;s++)this._origin[s]=i[s].dot(n[0])},xh.prototype.intersect=function(t,e){var n=!0,i=!e;return mh.set(1/0,1/0),vh.set(0,0),!this._intersectCheckOneSide(this,t,mh,vh,i,1)&&(n=!1,i)||!this._intersectCheckOneSide(t,this,mh,vh,i,-1)&&(n=!1,i)||i||M.copy(e,n?mh:vh),n},xh.prototype._intersectCheckOneSide=function(t,e,n,i,r,o){for(var a=!0,s=0;s<2;s++){var l=this._axes[s];if(this._getProjMinMaxOnAxis(s,t._corners,gh),this._getProjMinMaxOnAxis(s,e._corners,yh),gh[1]<yh[0]||yh[1]<gh[0]){if(a=!1,r)return a;var u=Math.abs(yh[0]-gh[1]),h=Math.abs(gh[0]-yh[1]);Math.min(u,h)>i.len()&&(u<h?M.scale(i,l,-u*o):M.scale(i,l,h*o))}else n&&(u=Math.abs(yh[0]-gh[1]),h=Math.abs(gh[0]-yh[1]),Math.min(u,h)<n.len())&&(u<h?M.scale(n,l,u*o):M.scale(n,l,-h*o))}return a},xh.prototype._getProjMinMaxOnAxis=function(t,e,n){for(var i=this._axes[t],r=this._origin,o=e[0].dot(i)+r[t],a=o,s=o,l=1;l<e.length;l++)var u=e[l].dot(i)+r[t],a=Math.min(u,a),s=Math.max(u,s);n[0]=a,n[1]=s},xh);function xh(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var n=0;n<4;n++)this._corners[n]=new M;for(n=0;n<2;n++)this._axes[n]=new M;t&&this.fromBoundingRect(t,e)}var wh,bh=[],n=(a(Sh,wh=n),Sh.prototype.traverse=function(t,e){t.call(e,this)},Sh.prototype.useStyle=function(){this.style={}},Sh.prototype.getCursor=function(){return this._cursor},Sh.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},Sh.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},Sh.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},Sh.prototype.addDisplayable=function(t,e){(e?this._temporaryDisplayables:this._displayables).push(t),this.markRedraw()},Sh.prototype.addDisplayables=function(t,e){e=e||!1;for(var n=0;n<t.length;n++)this.addDisplayable(t[n],e)},Sh.prototype.getDisplayables=function(){return this._displayables},Sh.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},Sh.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},Sh.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++)(e=this._displayables[t]).parent=this,e.update(),e.parent=null;for(var e,t=0;t<this._temporaryDisplayables.length;t++)(e=this._temporaryDisplayables[t]).parent=this,e.update(),e.parent=null},Sh.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new q(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var n=this._displayables[e],i=n.getBoundingRect().clone();n.needLocalTransform()&&i.applyTransform(n.getLocalTransform(bh)),t.union(i)}this._rect=t}return this._rect},Sh.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e);if(this.getBoundingRect().contain(n[0],n[1]))for(var i=0;i<this._displayables.length;i++)if(this._displayables[i].contain(t,e))return!0;return!1},Sh);function Sh(){var t=null!==wh&&wh.apply(this,arguments)||this;return t.notClear=!0,t.incremental=!0,t._displayables=[],t._temporaryDisplayables=[],t._cursor=0,t}var Mh=Lo();function Th(t,e,n,i,r,o,a){var s,l,u,h,c,p,d=!1,f=(I(r)?(a=o,o=r,r=null):R(r)&&(o=r.cb,a=r.during,d=r.isFrom,l=r.removeOpt,r=r.dataIndex),"leave"===t),g=(f||e.stopAnimation("leave"),p=t,s=r,l=f?l||{}:null,i=(g=i)&&i.getAnimationDelayParams?i.getAnimationDelayParams(e,r):null,g&&g.ecModel&&(u=(u=g.ecModel.getUpdatePayload())&&u.animation),p="update"===p,g&&g.isAnimationEnabled()?(c=h=r=void 0,c=l?(r=N(l.duration,200),h=N(l.easing,"cubicOut"),0):(r=g.getShallow(p?"animationDurationUpdate":"animationDuration"),h=g.getShallow(p?"animationEasingUpdate":"animationEasing"),g.getShallow(p?"animationDelayUpdate":"animationDelay")),I(c=u&&(null!=u.duration&&(r=u.duration),null!=u.easing&&(h=u.easing),null!=u.delay)?u.delay:c)&&(c=c(s,i)),{duration:(r=I(r)?r(s):r)||0,delay:c,easing:h}):null);g&&0<g.duration?(p={duration:g.duration,delay:g.delay||0,easing:g.easing,done:o,force:!!o||!!a,setToFinal:!f,scope:t,during:a},d?e.animateFrom(n,p):e.animateTo(n,p)):(e.stopAnimation(),d||e.attr(n),a&&a(1),o&&o())}function Ch(t,e,n,i,r,o){Th("update",t,e,n,i,r,o)}function kh(t,e,n,i,r,o){Th("enter",t,e,n,i,r,o)}function Ih(t){if(!t.__zr)return!0;for(var e=0;e<t.animators.length;e++)if("leave"===t.animators[e].scope)return!0;return!1}function Dh(t,e,n,i,r,o){Ih(t)||Th("leave",t,e,n,i,r,o)}function Lh(t,e,n,i){t.removeTextContent(),t.removeTextGuideLine(),Dh(t,{style:{opacity:0}},e,n,i)}function Ah(t){Mh(t).oldStyle=t.style}var Ph=Math.max,Oh=Math.min,Rh={};function Nh(t){return cs.extend(t)}var Eh=function(t,e){var n,i=ou(t,e);function r(t){t=n.call(this,t)||this;return t.applyTransform=i.applyTransform,t.buildPath=i.buildPath,t}return a(r,n=nu),r};function Bh(t,e){return Eh(t,e)}function zh(t,e){Rh[t]=e}function Fh(t){if(Rh.hasOwnProperty(t))return Rh[t]}function Vh(t,e,n,i){t=new nu(ou(t,e));return n&&("center"===i&&(n=Wh(n,t.getBoundingRect())),Uh(t,n)),t}function Hh(t,e,n){var i=new _s({style:{image:t,x:e.x,y:e.y,width:e.width,height:e.height},onload:function(t){"center"===n&&(t={width:t.width,height:t.height},i.setStyle(Wh(e,t)))}});return i}function Wh(t,e){var e=e.width/e.height,n=t.height*e,e=n<=t.width?t.height:(n=t.width)/e;return{x:t.x+t.width/2-n/2,y:t.y+t.height/2-e/2,width:n,height:e}}var Gh=function(t,e){for(var n=[],i=t.length,r=0;r<i;r++){var o=t[r];n.push(o.getUpdatedPathProxy(!0))}return(e=new cs(e)).createPathProxy(),e.buildPath=function(t){var e;ru(t)&&(t.appendPath(n),e=t.getContext())&&t.rebuildPath(e,1)},e};function Uh(t,e){t.applyTransform&&(e=t.getBoundingRect().calculateTransform(e),t.applyTransform(e))}function qh(t,e){return bs(t,t,{lineWidth:e}),t}var Xh=Ms;function Yh(t,e){for(var n=Re([]);t&&t!==e;)Ee(n,t.getLocalTransform(),n),t=t.parent;return n}function jh(t,e,n){return e&&!st(e)&&(e=vr.getLocalTransform(e)),ie([],t,e=n?Ve([],e):e)}function Zh(t){return!t.isGroup}function Kh(t,n){return z(t,function(t){var e=t[0],e=Ph(e,n.x),t=(e=Oh(e,n.x+n.width),t[1]),t=Ph(t,n.y);return[e,Oh(t,n.y+n.height)]})}function $h(t,e){var n=Ph(t.x,e.x),i=Oh(t.x+t.width,e.x+e.width),r=Ph(t.y,e.y),t=Oh(t.y+t.height,e.y+e.height);if(n<=i&&r<=t)return{x:n,y:r,width:i-n,height:t-r}}function Qh(t,e,n){var e=P({rectHover:!0},e),i=e.style={strokeNoScale:!0};if(n=n||{x:-1,y:-1,width:2,height:2},t)return 0===t.indexOf("image://")?(i.image=t.slice(8),B(i,n),new _s(e)):Vh(t.replace("path://",""),e,n,"center")}function Jh(t,e,n,i,r,o,a,s){var l,n=n-t,i=i-e,a=a-r,s=s-o,u=a*i-n*s;return!((l=u)<=1e-6&&-1e-6<=l||(r=((l=t-r)*i-n*(t=e-o))/u)<0||1<r||(i=(l*s-a*t)/u)<0||1<i)}function tc(t){var e=t.itemTooltipOption,n=t.componentModel,i=t.itemName,e=V(e)?{formatter:e}:e,r=n.mainType,n=n.componentIndex,o={componentType:r,name:i,$vars:["name"]},a=(o[r+"Index"]=n,t.formatterParamsExtra),t=(a&&O(ct(a),function(t){Vt(o,t)||(o[t]=a[t],o.$vars.push(t))}),qs(t.el));t.componentMainType=r,t.componentIndex=n,t.tooltipConfig={name:i,option:B({content:i,encodeHTMLContent:!0,formatterParams:o},e)}}function ec(t,e){var n;(n=t.isGroup?e(t):n)||t.traverse(e)}function nc(t,e){if(t)if(F(t))for(var n=0;n<t.length;n++)ec(t[n],e);else ec(t,e)}zh("circle",lu),zh("ellipse",pu),zh("sector",Du),zh("ring",Ou),zh("polygon",zu),zh("polyline",Wu),zh("rect",Is),zh("line",Yu),zh("bezierCurve",Ju),zh("arc",ih);var ic=Object.freeze({__proto__:null,Arc:ih,BezierCurve:Ju,BoundingRect:q,Circle:lu,CompoundPath:ah,Ellipse:pu,Group:Wr,Image:_s,IncrementalDisplayable:n,Line:Yu,LinearGradient:ch,OrientedBoundingRect:_h,Path:cs,Point:M,Polygon:zu,Polyline:Wu,RadialGradient:lh,Rect:Is,Ring:Ou,Sector:Du,Text:Os,applyTransform:jh,clipPointsByRect:Kh,clipRectByRect:$h,createIcon:Qh,extendPath:Bh,extendShape:Nh,getShapeClass:Fh,getTransform:Yh,groupTransition:function(t,e,i){var r,n;function o(t){var e={x:t.x,y:t.y,rotation:t.rotation};return null!=t.shape&&(e.shape=P({},t.shape)),e}t&&e&&(n={},t.traverse(function(t){Zh(t)&&t.anid&&(n[t.anid]=t)}),r=n,e.traverse(function(t){var e,n;Zh(t)&&t.anid&&(e=r[t.anid])&&(n=o(t),t.attr(o(e)),Ch(t,n,i,qs(t).dataIndex))}))},initProps:kh,isElementRemoved:Ih,lineLineIntersect:Jh,linePolygonIntersect:function(t,e,n,i,r){for(var o=0,a=r[r.length-1];o<r.length;o++){var s=r[o];if(Jh(t,e,n,i,s[0],s[1],a[0],a[1]))return!0;a=s}},makeImage:Hh,makePath:Vh,mergePath:Gh,registerShape:zh,removeElement:Dh,removeElementWithFadeOut:function(t,e,n){function i(){t.parent&&t.parent.remove(t)}t.isGroup?t.traverse(function(t){t.isGroup||Lh(t,e,n,i)}):Lh(t,e,n,i)},resizePath:Uh,setTooltipConfig:tc,subPixelOptimize:Xh,subPixelOptimizeLine:qh,subPixelOptimizeRect:function(t){return Ss(t.shape,t.shape,t.style),t},transformDirection:function(t,e,n){var i=0===e[4]||0===e[5]||0===e[0]?1:Math.abs(2*e[4]/e[0]),r=0===e[4]||0===e[5]||0===e[2]?1:Math.abs(2*e[4]/e[2]),i=jh(["left"===t?-i:"right"===t?i:0,"top"===t?-r:"bottom"===t?r:0],e,n);return Math.abs(i[0])>Math.abs(i[1])?0<i[0]?"right":"left":0<i[1]?"bottom":"top"},traverseElements:nc,updateProps:Ch}),rc={};function oc(t,e){for(var n=0;n<Js.length;n++){var i=Js[n],r=e[i],i=t.ensureState(i);i.style=i.style||{},i.style.text=r}var o=t.currentStates.slice();t.clearStates(!0),t.setStyle({text:e.normal}),t.useStates(o,!0)}function ac(t,e,n){for(var i,r=t.labelFetcher,o=t.labelDataIndex,a=t.labelDimIndex,s=e.normal,l={normal:i=null==(i=r?r.getFormattedLabel(o,"normal",null,a,s&&s.get("formatter"),null!=n?{interpolatedValue:n}:null):i)?I(t.defaultText)?t.defaultText(o,t,n):t.defaultText:i},u=0;u<Js.length;u++){var h=Js[u],c=e[h];l[h]=N(r?r.getFormattedLabel(o,h,null,a,c&&c.get("formatter")):null,i)}return l}function sc(t,e,n,i,r){var o,a={},s=a,l=t,u=n,h=i,c=r;u=u||rc;var p,t=l.ecModel,d=t&&t.option.textStyle,f=function(t){var e;for(;t&&t!==t.ecModel;){var n=(t.option||rc).rich;if(n){e=e||{};for(var i=ct(n),r=0;r<i.length;r++){var o=i[r];e[o]=1}}t=t.parentModel}return e}(l);if(f)for(var g in p={},f)f.hasOwnProperty(g)&&(o=l.getModel(["rich",g]),pc(p[g]={},o,d,u,h,c,!1,!0));return p&&(s.rich=p),(t=l.get("overflow"))&&(s.overflow=t),null!=(t=l.get("minMargin"))&&(s.margin=t),pc(s,l,d,u,h,c,!0,!1),e&&P(a,e),a}function lc(t,e,n){e=e||{};var i={},r=t.getShallow("rotate"),o=N(t.getShallow("distance"),n?null:5),a=t.getShallow("offset"),n=t.getShallow("position")||(n?null:"inside");return null!=(n="outside"===n?e.defaultOutsidePosition||"top":n)&&(i.position=n),null!=a&&(i.offset=a),null!=r&&(r*=Math.PI/180,i.rotation=r),null!=o&&(i.distance=o),i.outsideFill="inherit"===t.get("color")?e.inheritColor||null:"auto",i}var uc=["fontStyle","fontWeight","fontSize","fontFamily","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY"],hc=["align","lineHeight","width","height","tag","verticalAlign","ellipsis"],cc=["padding","borderWidth","borderRadius","borderDashOffset","backgroundColor","borderColor","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];function pc(t,e,n,i,r,o,a,s){n=!r&&n||rc;var l=i&&i.inheritColor,u=e.getShallow("color"),h=e.getShallow("textBorderColor"),c=N(e.getShallow("opacity"),n.opacity),u=("inherit"!==u&&"auto"!==u||(u=l||null),"inherit"!==h&&"auto"!==h||(h=l||null),o||(u=u||n.color,h=h||n.textBorderColor),null!=u&&(t.fill=u),null!=h&&(t.stroke=h),N(e.getShallow("textBorderWidth"),n.textBorderWidth)),h=(null!=u&&(t.lineWidth=u),N(e.getShallow("textBorderType"),n.textBorderType)),u=(null!=h&&(t.lineDash=h),N(e.getShallow("textBorderDashOffset"),n.textBorderDashOffset));null!=u&&(t.lineDashOffset=u),null!=(c=r||null!=c||s?c:i&&i.defaultOpacity)&&(t.opacity=c),r||o||null==t.fill&&i.inheritColor&&(t.fill=i.inheritColor);for(var p=0;p<uc.length;p++){var d=uc[p];null!=(f=N(e.getShallow(d),n[d]))&&(t[d]=f)}for(var p=0;p<hc.length;p++){d=hc[p];null!=(f=e.getShallow(d))&&(t[d]=f)}if(null==t.verticalAlign&&null!=(h=e.getShallow("baseline"))&&(t.verticalAlign=h),!a||!i.disableBox){for(p=0;p<cc.length;p++){var f,d=cc[p];null!=(f=e.getShallow(d))&&(t[d]=f)}u=e.getShallow("borderType");null!=u&&(t.borderDash=u),"auto"!==t.backgroundColor&&"inherit"!==t.backgroundColor||!l||(t.backgroundColor=l),"auto"!==t.borderColor&&"inherit"!==t.borderColor||!l||(t.borderColor=l)}}var dc=Lo();function fc(n,i,r,t,o){var a,s,l,u=dc(n);u.valueAnimation&&u.prevValue!==u.value&&(a=u.defaultInterpolatedText,s=N(u.interpolatedValue,u.prevValue),l=u.value,n.percent=0,(null==u.prevValue?kh:Ch)(n,{percent:1},t,i,null,function(t){var e=function(t,e,n,i,r){var o=null==e||"auto"===e;if(null==i)return i;if(gt(i))return eo(p=yo(n||0,i,r),o?Math.max(no(n||0),no(i)):e);if(V(i))return r<1?n:i;for(var a=[],s=n,l=i,u=Math.max(s?s.length:0,l.length),h=0;h<u;++h){var c,p,d=t.getDimensionInfo(h);d&&"ordinal"===d.type?a[h]=(r<1&&s?s:l)[h]:(p=yo(d=s&&s[h]?s[h]:0,c=l[h],r),a[h]=eo(p,o?Math.max(no(d),no(c)):e))}return a}(r,u.precision,s,l,t),t=(u.interpolatedValue=1===t?null:e,ac({labelDataIndex:i,labelFetcher:o,defaultText:a?a(e):e+""},u.statesModels,e));oc(n,t)}))}var gc=["textStyle","color"],yc=["fontStyle","fontWeight","fontSize","fontFamily","padding","lineHeight","rich","width","height","overflow"],mc=new Os,Xh=(vc.prototype.getTextColor=function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(gc):null)},vc.prototype.getFont=function(){return t={fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},e=(e=this.ecModel)&&e.getModel("textStyle"),It([t.fontStyle||e&&e.getShallow("fontStyle")||"",t.fontWeight||e&&e.getShallow("fontWeight")||"",(t.fontSize||e&&e.getShallow("fontSize")||12)+"px",t.fontFamily||e&&e.getShallow("fontFamily")||"sans-serif"].join(" "));var t,e},vc.prototype.getTextRect=function(t){for(var e={text:t,verticalAlign:this.getShallow("verticalAlign")||this.getShallow("baseline")},n=0;n<yc.length;n++)e[yc[n]]=this.getShallow(yc[n]);return mc.useStyle(e),mc.update(),mc.getBoundingRect()},vc);function vc(){}var _c=[["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","type"],["lineDashOffset","dashOffset"],["lineCap","cap"],["lineJoin","join"],["miterLimit"]],xc=Yo(_c),h=(wc.prototype.getLineStyle=function(t){return xc(this,t)},wc);function wc(){}var bc=[["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","borderType"],["lineDashOffset","borderDashOffset"],["lineCap","borderCap"],["lineJoin","borderJoin"],["miterLimit","borderMiterLimit"]],Sc=Yo(bc),Mc=(Tc.prototype.getItemStyle=function(t,e){return Sc(this,t,e)},Tc);function Tc(){}Ic.prototype.init=function(t,e,n){},Ic.prototype.mergeOption=function(t,e){d(this.option,t,!0)},Ic.prototype.get=function(t,e){return null==t?this.option:this._doGet(this.parsePath(t),!e&&this.parentModel)},Ic.prototype.getShallow=function(t,e){var n=this.option,n=null==n?n:n[t];return null!=n||e||(e=this.parentModel)&&(n=e.getShallow(t)),n},Ic.prototype.getModel=function(t,e){var n=null!=t,t=n?this.parsePath(t):null;return new Ic(n?this._doGet(t):this.option,e=e||this.parentModel&&this.parentModel.getModel(this.resolveParentPath(t)),this.ecModel)},Ic.prototype.isEmpty=function(){return null==this.option},Ic.prototype.restoreData=function(){},Ic.prototype.clone=function(){return new this.constructor(b(this.option))},Ic.prototype.parsePath=function(t){return"string"==typeof t?t.split("."):t},Ic.prototype.resolveParentPath=function(t){return t},Ic.prototype.isAnimationEnabled=function(){if(!p.node&&this.option)return null!=this.option.animation?!!this.option.animation:this.parentModel?this.parentModel.isAnimationEnabled():void 0},Ic.prototype._doGet=function(t,e){var n=this.option;if(t){for(var i=0;i<t.length&&(!t[i]||null!=(n=n&&"object"==typeof n?n[t[i]]:null));i++);null==n&&e&&(n=e._doGet(this.resolveParentPath(t),e.parentModel))}return n};var Cc,kc=Ic;function Ic(t,e,n){this.parentModel=e,this.ecModel=n,this.option=t}Ho(kc),Gc=kc,Cc=["__\0is_clz",Go++].join("_"),Gc.prototype[Cc]=!0,Gc.isInstance=function(t){return!(!t||!t[Cc])},at(kc,h),at(kc,Mc),at(kc,Zo),at(kc,Xh);var Dc=Math.round(10*Math.random());function Lc(t){return[t||"",Dc++].join("_")}var Ac="ZH",Pc="EN",Oc=Pc,Rc={},Nc={},Ec=p.domSupported&&-1<(document.documentElement.lang||navigator.language||navigator.browserLanguage||Oc).toUpperCase().indexOf(Ac)?Ac:Oc;function Bc(t,e){t=t.toUpperCase(),Nc[t]=new kc(e),Rc[t]=e}Bc(Pc,{time:{month:["January","February","March","April","May","June","July","August","September","October","November","December"],monthAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayOfWeek:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayOfWeekAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},legend:{selector:{all:"All",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Box Select",polygon:"Lasso Select",lineX:"Horizontally Select",lineY:"Vertically Select",keep:"Keep Selections",clear:"Clear Selections"}},dataView:{title:"Data View",lang:["Data View","Close","Refresh"]},dataZoom:{title:{zoom:"Zoom",back:"Zoom Reset"}},magicType:{title:{line:"Switch to Line Chart",bar:"Switch to Bar Chart",stack:"Stack",tiled:"Tile"}},restore:{title:"Restore"},saveAsImage:{title:"Save as Image",lang:["Right Click to Save Image"]}},series:{typeNames:{pie:"Pie chart",bar:"Bar chart",line:"Line chart",scatter:"Scatter plot",effectScatter:"Ripple scatter plot",radar:"Radar chart",tree:"Tree",treemap:"Treemap",boxplot:"Boxplot",candlestick:"Candlestick",k:"K line chart",heatmap:"Heat map",map:"Map",parallel:"Parallel coordinate map",lines:"Line graph",graph:"Relationship graph",sankey:"Sankey diagram",funnel:"Funnel chart",gauge:"Gauge",pictorialBar:"Pictorial bar",themeRiver:"Theme River Map",sunburst:"Sunburst",custom:"Custom chart",chart:"Chart"}},aria:{general:{withTitle:'This is a chart about "{title}"',withoutTitle:"This is a chart"},series:{single:{prefix:"",withName:" with type {seriesType} named {seriesName}.",withoutName:" with type {seriesType}."},multiple:{prefix:". It consists of {seriesCount} series count.",withName:" The {seriesId} series is a {seriesType} representing {seriesName}.",withoutName:" The {seriesId} series is a {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"The data is as follows: ",partialData:"The first {displayCnt} items are: ",withName:"the data for {name} is {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}}),Bc(Ac,{time:{month:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],monthAbbr:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],dayOfWeek:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],dayOfWeekAbbr:["日","一","二","三","四","五","六"]},legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图",custom:"自定义图表",chart:"图表"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}});var zc=1e3,Fc=60*zc,Vc=60*Fc,Hc=24*Vc,Go=365*Hc,Wc={year:"{yyyy}",month:"{MMM}",day:"{d}",hour:"{HH}:{mm}",minute:"{HH}:{mm}",second:"{HH}:{mm}:{ss}",millisecond:"{HH}:{mm}:{ss} {SSS}",none:"{yyyy}-{MM}-{dd} {HH}:{mm}:{ss} {SSS}"},Gc="{yyyy}-{MM}-{dd}",Uc={year:"{yyyy}",month:"{yyyy}-{MM}",day:Gc,hour:Gc+" "+Wc.hour,minute:Gc+" "+Wc.minute,second:Gc+" "+Wc.second,millisecond:Wc.none},qc=["year","month","day","hour","minute","second","millisecond"],Xc=["year","half-year","quarter","month","week","half-week","day","half-day","quarter-day","hour","minute","second","millisecond"];function Yc(t,e){return"0000".substr(0,e-(t+="").length)+t}function jc(t){switch(t){case"half-year":case"quarter":return"month";case"week":case"half-week":return"day";case"half-day":case"quarter-day":return"hour";default:return t}}function Zc(t,e,n,i){var t=lo(t),r=t[Qc(n)](),o=t[Jc(n)]()+1,a=Math.floor((o-1)/3)+1,s=t[tp(n)](),l=t["get"+(n?"UTC":"")+"Day"](),u=t[ep(n)](),h=(u-1)%12+1,c=t[np(n)](),p=t[ip(n)](),t=t[rp(n)](),n=12<=u?"pm":"am",d=n.toUpperCase(),i=(i instanceof kc?i:Nc[i||Ec]||Nc[Oc]).getModel("time"),f=i.get("month"),g=i.get("monthAbbr"),y=i.get("dayOfWeek"),i=i.get("dayOfWeekAbbr");return(e||"").replace(/{a}/g,n+"").replace(/{A}/g,d+"").replace(/{yyyy}/g,r+"").replace(/{yy}/g,Yc(r%100+"",2)).replace(/{Q}/g,a+"").replace(/{MMMM}/g,f[o-1]).replace(/{MMM}/g,g[o-1]).replace(/{MM}/g,Yc(o,2)).replace(/{M}/g,o+"").replace(/{dd}/g,Yc(s,2)).replace(/{d}/g,s+"").replace(/{eeee}/g,y[l]).replace(/{ee}/g,i[l]).replace(/{e}/g,l+"").replace(/{HH}/g,Yc(u,2)).replace(/{H}/g,u+"").replace(/{hh}/g,Yc(h+"",2)).replace(/{h}/g,h+"").replace(/{mm}/g,Yc(c,2)).replace(/{m}/g,c+"").replace(/{ss}/g,Yc(p,2)).replace(/{s}/g,p+"").replace(/{SSS}/g,Yc(t,3)).replace(/{S}/g,t+"")}function Kc(t,e){var t=lo(t),n=t[Jc(e)]()+1,i=t[tp(e)](),r=t[ep(e)](),o=t[np(e)](),a=t[ip(e)](),t=0===t[rp(e)](),e=t&&0===a,a=e&&0===o,o=a&&0===r,r=o&&1===i;return r&&1===n?"year":r?"month":o?"day":a?"hour":e?"minute":t?"second":"millisecond"}function $c(t,e,n){var i=gt(t)?lo(t):t;switch(e=e||Kc(t,n)){case"year":return i[Qc(n)]();case"half-year":return 6<=i[Jc(n)]()?1:0;case"quarter":return Math.floor((i[Jc(n)]()+1)/4);case"month":return i[Jc(n)]();case"day":return i[tp(n)]();case"half-day":return i[ep(n)]()/24;case"hour":return i[ep(n)]();case"minute":return i[np(n)]();case"second":return i[ip(n)]();case"millisecond":return i[rp(n)]()}}function Qc(t){return t?"getUTCFullYear":"getFullYear"}function Jc(t){return t?"getUTCMonth":"getMonth"}function tp(t){return t?"getUTCDate":"getDate"}function ep(t){return t?"getUTCHours":"getHours"}function np(t){return t?"getUTCMinutes":"getMinutes"}function ip(t){return t?"getUTCSeconds":"getSeconds"}function rp(t){return t?"getUTCMilliseconds":"getMilliseconds"}function op(t){return t?"setUTCMonth":"setMonth"}function ap(t){return t?"setUTCDate":"setDate"}function sp(t){return t?"setUTCHours":"setHours"}function lp(t){return t?"setUTCMinutes":"setMinutes"}function up(t){return t?"setUTCSeconds":"setSeconds"}function hp(t){return t?"setUTCMilliseconds":"setMilliseconds"}function cp(t){var e;return fo(t)?(e=(t+"").split("."))[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(1<e.length?"."+e[1]:""):V(t)?t:"-"}function pp(t,e){return"{"+t+(null==e?"":e)+"}"}var dp=Ct,fp=["a","b","c","d","e","f","g"];function gp(t,e,n){var i=(e=F(e)?e:[e]).length;if(!i)return"";for(var r=e[0].$vars||[],o=0;o<r.length;o++){var a=fp[o];t=t.replace(pp(a),pp(a,0))}for(var s=0;s<i;s++)for(var l=0;l<r.length;l++){var u=e[s][r[l]];t=t.replace(pp(fp[l],s),n?xe(u):u)}return t}function yp(t,e){var n;"_blank"===e||"blank"===e?((n=window.open()).opener=null,n.location.href=t):window.open(t,e)}var mp=O,vp=["left","right","top","bottom","width","height"],_p=[["width","left","right"],["height","top","bottom"]];function xp(a,s,l,u,h){var c=0,p=0,d=(null==u&&(u=1/0),null==h&&(h=1/0),0);s.eachChild(function(t,e){var n,i,r,o=t.getBoundingRect(),e=s.childAt(e+1),e=e&&e.getBoundingRect();d="horizontal"===a?(i=o.width+(e?-e.x+o.x:0),u<(n=c+i)||t.newline?(c=0,n=i,p+=d+l,o.height):Math.max(d,o.height)):(i=o.height+(e?-e.y+o.y:0),h<(r=p+i)||t.newline?(c+=d+l,p=0,r=i,o.width):Math.max(d,o.width)),t.newline||(t.x=c,t.y=p,t.markRedraw(),"horizontal"===a?c=n+l:p=r+l)})}function wp(t,e,n){n=dp(n||0);var i=e.width,r=e.height,o=to(t.left,i),a=to(t.top,r),e=to(t.right,i),s=to(t.bottom,r),l=to(t.width,i),u=to(t.height,r),h=n[2]+n[0],c=n[1]+n[3],p=t.aspect;switch(isNaN(l)&&(l=i-e-c-o),isNaN(u)&&(u=r-s-h-a),null!=p&&(isNaN(l)&&isNaN(u)&&(i/r<p?l=.8*i:u=.8*r),isNaN(l)&&(l=p*u),isNaN(u))&&(u=l/p),isNaN(o)&&(o=i-e-l-c),isNaN(a)&&(a=r-s-u-h),t.left||t.right){case"center":o=i/2-l/2-n[3];break;case"right":o=i-l-c}switch(t.top||t.bottom){case"middle":case"center":a=r/2-u/2-n[0];break;case"bottom":a=r-u-h}o=o||0,a=a||0,isNaN(l)&&(l=i-c-o-(e||0)),isNaN(u)&&(u=r-h-a-(s||0));p=new q(o+n[3],a+n[0],l,u);return p.margin=n,p}function bp(t){t=t.layoutMode||t.constructor.layoutMode;return R(t)?t:t?{type:t}:null}function Sp(l,u,t){var h=t&&t.ignoreSize,t=(F(h)||(h=[h,h]),n(_p[0],0)),e=n(_p[1],1);function n(t,e){var n={},i=0,r={},o=0;if(mp(t,function(t){r[t]=l[t]}),mp(t,function(t){c(u,t)&&(n[t]=r[t]=u[t]),p(n,t)&&i++,p(r,t)&&o++}),h[e])p(u,t[1])?r[t[2]]=null:p(u,t[2])&&(r[t[1]]=null);else if(2!==o&&i){if(!(2<=i))for(var a=0;a<t.length;a++){var s=t[a];if(!c(n,s)&&c(l,s)){n[s]=l[s];break}}return n}return r}function c(t,e){return t.hasOwnProperty(e)}function p(t,e){return null!=t[e]&&"auto"!==t[e]}function i(t,e,n){mp(t,function(t){e[t]=n[t]})}i(_p[0],l,t),i(_p[1],l,e)}function Mp(t){return e={},(n=t)&&e&&mp(vp,function(t){n.hasOwnProperty(t)&&(e[t]=n[t])}),e;var e,n}dt(xp,"vertical"),dt(xp,"horizontal");var Tp,Cp,kp,Ip,Dp=Lo(),g=(a(Lp,Tp=kc),Lp.prototype.init=function(t,e,n){this.mergeDefaultAndTheme(t,n)},Lp.prototype.mergeDefaultAndTheme=function(t,e){var n=bp(this),i=n?Mp(t):{};d(t,e.getTheme().get(this.mainType)),d(t,this.getDefaultOption()),n&&Sp(t,i,n)},Lp.prototype.mergeOption=function(t,e){d(this.option,t,!0);var n=bp(this);n&&Sp(this.option,t,n)},Lp.prototype.optionUpdated=function(t,e){},Lp.prototype.getDefaultOption=function(){var t=this.constructor;if(!(e=t)||!e[Fo])return t.defaultOption;var e=Dp(this);if(!e.defaultOption){for(var n=[],i=t;i;){var r=i.prototype.defaultOption;r&&n.push(r),i=i.superClass}for(var o={},a=n.length-1;0<=a;a--)o=d(o,n[a],!0);e.defaultOption=o}return e.defaultOption},Lp.prototype.getReferringComponents=function(t,e){var n=t+"Id";return No(this.ecModel,t,{index:this.get(t+"Index",!0),id:this.get(n,!0)},e)},Lp.prototype.getBoxLayoutParams=function(){return{left:this.get("left"),top:this.get("top"),right:this.get("right"),bottom:this.get("bottom"),width:this.get("width"),height:this.get("height")}},Lp.prototype.getZLevelKey=function(){return""},Lp.prototype.setZLevel=function(t){this.option.zlevel=t},Lp.protoInitialize=((h=Lp.prototype).type="component",h.id="",h.name="",h.mainType="",h.subType="",void(h.componentIndex=0)),Lp);function Lp(t,e,n){t=Tp.call(this,t,e,n)||this;return t.uid=Lc("ec_cpt_model"),t}function Ap(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}Wo(g,kc),Xo(g),kp={},(Cp=g).registerSubTypeDefaulter=function(t,e){t=Vo(t);kp[t.main]=e},Cp.determineSubType=function(t,e){var n,i=e.type;return i||(n=Vo(t).main,Cp.hasSubTypes(t)&&kp[n]&&(i=kp[n](e))),i},Ip=function(t){var e=[];O(g.getClassesByMainType(t),function(t){e=e.concat(t.dependencies||t.prototype.dependencies||[])}),e=z(e,function(t){return Vo(t).main}),"dataset"!==t&&k(e,"dataset")<=0&&e.unshift("dataset");return e},g.topologicalTravel=function(t,e,n,i){if(t.length){a={},s=[],O(o=e,function(n){var e,i,r=Ap(a,n),t=r.originalDeps=Ip(n),t=(e=o,i=[],O(t,function(t){0<=k(e,t)&&i.push(t)}),i);r.entryCount=t.length,0===r.entryCount&&s.push(n),O(t,function(t){k(r.predecessor,t)<0&&r.predecessor.push(t);var e=Ap(a,t);k(e.successor,t)<0&&e.successor.push(n)})});var o,a,s,e={graph:a,noEntryList:s},r=e.graph,l=e.noEntryList,u={};for(O(t,function(t){u[t]=!0});l.length;){var h=l.pop(),c=r[h],p=!!u[h];p&&(n.call(i,h,c.originalDeps.slice()),delete u[h]),O(c.successor,p?f:d)}O(u,function(){throw new Error("")})}function d(t){r[t].entryCount--,0===r[t].entryCount&&l.push(t)}function f(t){u[t]=!0,d(t)}};var Mc="",Zo=("undefined"!=typeof navigator&&(Mc=navigator.platform||""),"rgba(0, 0, 0, 0.2)"),Pp={darkMode:"auto",colorBy:"series",color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452","#9a60b4","#ea7ccc"],gradientColor:["#f6efa6","#d88273","#bf444c"],aria:{decal:{decals:[{color:Zo,dashArrayX:[1,0],dashArrayY:[2,5],symbolSize:1,rotation:Math.PI/6},{color:Zo,symbol:"circle",dashArrayX:[[8,8],[0,8,8,0]],dashArrayY:[6,0],symbolSize:.8},{color:Zo,dashArrayX:[1,0],dashArrayY:[4,3],rotation:-Math.PI/4},{color:Zo,dashArrayX:[[6,6],[0,6,6,0]],dashArrayY:[6,0]},{color:Zo,dashArrayX:[[1,0],[1,6]],dashArrayY:[1,0,6,0],rotation:Math.PI/4},{color:Zo,symbol:"triangle",dashArrayX:[[9,9],[0,9,9,0]],dashArrayY:[7,2],symbolSize:.75}]}},textStyle:{fontFamily:Mc.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,stateAnimation:{duration:300,easing:"cubicOut"},animation:"auto",animationDuration:1e3,animationDurationUpdate:500,animationEasing:"cubicInOut",animationEasingUpdate:"cubicInOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1},Op=E(["tooltip","label","itemName","itemId","itemGroupId","itemChildGroupId","seriesName"]),Rp="original",Np="arrayRows",Ep="objectRows",Bp="keyedColumns",zp="typedArray",Fp="unknown",Vp="column",Hp="row",Wp={Must:1,Might:2,Not:3},Gp=Lo();function Up(n,t,e){var r,o,a,i,s,l={},u=qp(t);return u&&n&&(r=[],o=[],t=t.ecModel,t=Gp(t).datasetMap,u=u.uid+"_"+e.seriesLayoutBy,O(n=n.slice(),function(t,e){t=R(t)?t:n[e]={name:t};"ordinal"===t.type&&null==a&&(a=e,i=c(t)),l[t.name]=[]}),s=t.get(u)||t.set(u,{categoryWayDim:i,valueWayDim:0}),O(n,function(t,e){var n,i=t.name,t=c(t);null==a?(n=s.valueWayDim,h(l[i],n,t),h(o,n,t),s.valueWayDim+=t):a===e?(h(l[i],0,t),h(r,0,t)):(n=s.categoryWayDim,h(l[i],n,t),h(o,n,t),s.categoryWayDim+=t)}),r.length&&(l.itemName=r),o.length)&&(l.seriesName=o),l;function h(t,e,n){for(var i=0;i<n;i++)t.push(e+i)}function c(t){t=t.dimsDef;return t?t.length:1}}function qp(t){if(!t.get("data",!0))return No(t.ecModel,"dataset",{index:t.get("datasetIndex",!0),id:t.get("datasetId",!0)},Ro).models[0]}function Xp(t,e){var n,i,r,o=t.data,a=t.sourceFormat,s=t.seriesLayoutBy,l=t.dimensionsDefine,u=t.startIndex,h=e;if(!mt(o)){if(l&&(R(l=l[h])?(i=l.name,r=l.type):V(l)&&(i=l)),null!=r)return"ordinal"===r?Wp.Must:Wp.Not;if(a===Np){var c=o;if(s===Hp){for(var p=c[h],d=0;d<(p||[]).length&&d<5;d++)if(null!=(n=_(p[u+d])))return n}else for(d=0;d<c.length&&d<5;d++){var f=c[u+d];if(f&&null!=(n=_(f[h])))return n}}else if(a===Ep){var g=o;if(!i)return Wp.Not;for(d=0;d<g.length&&d<5;d++)if((m=g[d])&&null!=(n=_(m[i])))return n}else if(a===Bp){l=o;if(!i)return Wp.Not;if(!(p=l[i])||mt(p))return Wp.Not;for(d=0;d<p.length&&d<5;d++)if(null!=(n=_(p[d])))return n}else if(a===Rp)for(var y=o,d=0;d<y.length&&d<5;d++){var m,v=wo(m=y[d]);if(!F(v))return Wp.Not;if(null!=(n=_(v[h])))return n}}return Wp.Not;function _(t){var e=V(t);return null!=t&&Number.isFinite(Number(t))&&""!==t?e?Wp.Might:Wp.Not:e&&"-"!==t?Wp.Must:void 0}}var Yp=E();var jp,Zp,Kp,$p=Lo(),Qp=(Lo(),Jp.prototype.getColorFromPalette=function(t,e,n){var i=vo(this.get("color",!0)),r=this.get("colorLayer",!0),o=this,a=$p;return a=a(e=e||o),o=a.paletteIdx||0,(e=a.paletteNameMap=a.paletteNameMap||{}).hasOwnProperty(t)?e[t]:(r=(r=n==null||!r?i:td(r,n))||i)&&r.length?(n=r[o],t&&(e[t]=n),a.paletteIdx=(o+1)%r.length,n):void 0},Jp.prototype.clearColorPalette=function(){var t,e;(e=$p)(t=this).paletteIdx=0,e(t).paletteNameMap={}},Jp);function Jp(){}function td(t,e){for(var n=t.length,i=0;i<n;i++)if(t[i].length>e)return t[i];return t[n-1]}var ed,nd="\0_ec_inner",id=(a(c,ed=kc),c.prototype.init=function(t,e,n,i,r,o){i=i||{},this.option=null,this._theme=new kc(i),this._locale=new kc(r),this._optionManager=o},c.prototype.setOption=function(t,e,n){e=ad(e);this._optionManager.setOption(t,n,e),this._resetOption(null,e)},c.prototype.resetOption=function(t,e){return this._resetOption(t,ad(e))},c.prototype._resetOption=function(t,e){var n,i=!1,r=this._optionManager;return t&&"recreate"!==t||(n=r.mountOption("recreate"===t),this.option&&"recreate"!==t?(this.restoreData(),this._mergeOption(n,e)):Kp(this,n),i=!0),"timeline"!==t&&"media"!==t||this.restoreData(),t&&"recreate"!==t&&"timeline"!==t||(n=r.getTimelineOption(this))&&(i=!0,this._mergeOption(n,e)),t&&"recreate"!==t&&"media"!==t||(n=r.getMediaOption(this)).length&&O(n,function(t){i=!0,this._mergeOption(t,e)},this),i},c.prototype.mergeOption=function(t){this._mergeOption(t,null)},c.prototype._mergeOption=function(i,t){var r=this.option,h=this._componentsMap,c=this._componentsCount,n=[],o=E(),p=t&&t.replaceMergeMainTypeMap;Gp(this).datasetMap=E(),O(i,function(t,e){null!=t&&(g.hasClass(e)?e&&(n.push(e),o.set(e,!0)):r[e]=null==r[e]?b(t):d(r[e],t,!0))}),p&&p.each(function(t,e){g.hasClass(e)&&!o.get(e)&&(n.push(e),o.set(e,!0))}),g.topologicalTravel(n,g.getAllClassMainTypes(),function(o){var a,t=function(t,e,n){return(e=(e=Yp.get(e))&&e(t))?n.concat(e):n}(this,o,vo(i[o])),e=h.get(o),n=e?p&&p.get(o)?"replaceMerge":"normalMerge":"replaceAll",e=bo(e,t,n),s=(Io(e,o,g),r[o]=null,h.set(o,null),c.set(o,0),[]),l=[],u=0;O(e,function(t,e){var n=t.existing,i=t.newOption;if(i){var r=g.getClass(o,t.keyInfo.subType,!("series"===o));if(!r)return;if("tooltip"===o){if(a)return;a=!0}n&&n.constructor===r?(n.name=t.keyInfo.name,n.mergeOption(i,this),n.optionUpdated(i,!1)):(e=P({componentIndex:e},t.keyInfo),P(n=new r(i,this,this,e),e),t.brandNew&&(n.__requireNewView=!0),n.init(i,this,this),n.optionUpdated(null,!0))}else n&&(n.mergeOption({},this),n.optionUpdated({},!1));n?(s.push(n.option),l.push(n),u++):(s.push(void 0),l.push(void 0))},this),r[o]=s,h.set(o,l),c.set(o,u),"series"===o&&jp(this)},this),this._seriesIndices||jp(this)},c.prototype.getOption=function(){var a=b(this.option);return O(a,function(t,e){if(g.hasClass(e)){for(var n=vo(t),i=n.length,r=!1,o=i-1;0<=o;o--)n[o]&&!ko(n[o])?r=!0:(n[o]=null,r||i--);n.length=i,a[e]=n}}),delete a[nd],a},c.prototype.getTheme=function(){return this._theme},c.prototype.getLocaleModel=function(){return this._locale},c.prototype.setUpdatePayload=function(t){this._payload=t},c.prototype.getUpdatePayload=function(){return this._payload},c.prototype.getComponent=function(t,e){var n=this._componentsMap.get(t);if(n){t=n[e||0];if(t)return t;if(null==e)for(var i=0;i<n.length;i++)if(n[i])return n[i]}},c.prototype.queryComponents=function(t){var e,n,i,r,o,a=t.mainType;return a&&(e=t.index,n=t.id,i=t.name,r=this._componentsMap.get(a))&&r.length?(null!=e?(o=[],O(vo(e),function(t){r[t]&&o.push(r[t])})):o=null!=n?rd("id",n,r):null!=i?rd("name",i,r):ut(r,function(t){return!!t}),od(o,t)):[]},c.prototype.findComponents=function(t){var e,n=t.query,i=t.mainType,r=(r=i+"Index",o=i+"Id",e=i+"Name",!(n=n)||null==n[r]&&null==n[o]&&null==n[e]?null:{mainType:i,index:n[r],id:n[o],name:n[e]}),o=r?this.queryComponents(r):ut(this._componentsMap.get(i),function(t){return!!t});return n=od(o,t),t.filter?ut(n,t.filter):n},c.prototype.eachComponent=function(t,e,n){var i=this._componentsMap;if(I(t)){var r=e,o=t;i.each(function(t,e){for(var n=0;t&&n<t.length;n++){var i=t[n];i&&o.call(r,e,i,i.componentIndex)}})}else for(var a=V(t)?i.get(t):R(t)?this.findComponents(t):null,s=0;a&&s<a.length;s++){var l=a[s];l&&e.call(n,l,l.componentIndex)}},c.prototype.getSeriesByName=function(t){var e=To(t,null);return ut(this._componentsMap.get("series"),function(t){return!!t&&null!=e&&t.name===e})},c.prototype.getSeriesByIndex=function(t){return this._componentsMap.get("series")[t]},c.prototype.getSeriesByType=function(e){return ut(this._componentsMap.get("series"),function(t){return!!t&&t.subType===e})},c.prototype.getSeries=function(){return ut(this._componentsMap.get("series"),function(t){return!!t})},c.prototype.getSeriesCount=function(){return this._componentsCount.get("series")},c.prototype.eachSeries=function(n,i){Zp(this),O(this._seriesIndices,function(t){var e=this._componentsMap.get("series")[t];n.call(i,e,t)},this)},c.prototype.eachRawSeries=function(e,n){O(this._componentsMap.get("series"),function(t){t&&e.call(n,t,t.componentIndex)})},c.prototype.eachSeriesByType=function(n,i,r){Zp(this),O(this._seriesIndices,function(t){var e=this._componentsMap.get("series")[t];e.subType===n&&i.call(r,e,t)},this)},c.prototype.eachRawSeriesByType=function(t,e,n){return O(this.getSeriesByType(t),e,n)},c.prototype.isSeriesFiltered=function(t){return Zp(this),null==this._seriesIndicesMap.get(t.componentIndex)},c.prototype.getCurrentSeriesIndices=function(){return(this._seriesIndices||[]).slice()},c.prototype.filterSeries=function(n,i){Zp(this);var r=[];O(this._seriesIndices,function(t){var e=this._componentsMap.get("series")[t];n.call(i,e,t)&&r.push(t)},this),this._seriesIndices=r,this._seriesIndicesMap=E(r)},c.prototype.restoreData=function(n){jp(this);var t=this._componentsMap,i=[];t.each(function(t,e){g.hasClass(e)&&i.push(e)}),g.topologicalTravel(i,g.getAllClassMainTypes(),function(e){O(t.get(e),function(t){!t||"series"===e&&function(t,e){{var n,i;if(e)return n=e.seriesIndex,i=e.seriesId,e=e.seriesName,null!=n&&t.componentIndex!==n||null!=i&&t.id!==i||null!=e&&t.name!==e}}(t,n)||t.restoreData()})})},c.internalField=(jp=function(t){var e=t._seriesIndices=[];O(t._componentsMap.get("series"),function(t){t&&e.push(t.componentIndex)}),t._seriesIndicesMap=E(e)},Zp=function(t){},void(Kp=function(t,e){t.option={},t.option[nd]=1,t._componentsMap=E({series:[]}),t._componentsCount=E();var n,i,r=e.aria;R(r)&&null==r.enabled&&(r.enabled=!0),n=e,r=t._theme.option,i=n.color&&!n.colorLayer,O(r,function(t,e){"colorLayer"===e&&i||g.hasClass(e)||("object"==typeof t?n[e]=n[e]?d(n[e],t,!1):b(t):null==n[e]&&(n[e]=t))}),d(e,Pp,!1),t._mergeOption(e,null)})),c);function c(){return null!==ed&&ed.apply(this,arguments)||this}function rd(e,t,n){var i,r;return F(t)?(i=E(),O(t,function(t){null!=t&&null!=To(t,null)&&i.set(t,!0)}),ut(n,function(t){return t&&i.get(t[e])})):(r=To(t,null),ut(n,function(t){return t&&null!=r&&t[e]===r}))}function od(t,e){return e.hasOwnProperty("subType")?ut(t,function(t){return t&&t.subType===e.subType}):t}function ad(t){var e=E();return t&&O(vo(t.replaceMerge),function(t){e.set(t,!0)}),{replaceMergeMainTypeMap:e}}at(id,Qp);function sd(e){O(ld,function(t){this[t]=pt(e[t],e)},this)}var ld=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isSSR","isDisposed","on","off","getDataURL","getConnectedDataURL","getOption","getId","updateLabelLayout"],ud={},hd=(cd.prototype.create=function(n,i){var r=[];O(ud,function(t,e){t=t.create(n,i);r=r.concat(t||[])}),this._coordinateSystems=r},cd.prototype.update=function(e,n){O(this._coordinateSystems,function(t){t.update&&t.update(e,n)})},cd.prototype.getCoordinateSystems=function(){return this._coordinateSystems.slice()},cd.register=function(t,e){ud[t]=e},cd.get=function(t){return ud[t]},cd);function cd(){this._coordinateSystems=[]}var pd=/^(min|max)?(.+)$/,dd=(fd.prototype.setOption=function(t,e,n){t&&(O(vo(t.series),function(t){t&&t.data&&mt(t.data)&&Lt(t.data)}),O(vo(t.dataset),function(t){t&&t.source&&mt(t.source)&&Lt(t.source)})),t=b(t);var i=this._optionBackup,t=function(t,n,i){var e,r,o=[],a=t.baseOption,s=t.timeline,l=t.options,u=t.media,h=!!t.media,c=!!(l||s||a&&a.timeline);a?(r=a).timeline||(r.timeline=s):((c||h)&&(t.options=t.media=null),r=t);h&&F(u)&&O(u,function(t){t&&t.option&&(t.query?o.push(t):e=e||t)});function p(e){O(n,function(t){t(e,i)})}return p(r),O(l,p),O(o,function(t){return p(t.option)}),{baseOption:r,timelineOptions:l||[],mediaDefault:e,mediaList:o}}(t,e,!i);this._newBaseOption=t.baseOption,i?(t.timelineOptions.length&&(i.timelineOptions=t.timelineOptions),t.mediaList.length&&(i.mediaList=t.mediaList),t.mediaDefault&&(i.mediaDefault=t.mediaDefault)):this._optionBackup=t},fd.prototype.mountOption=function(t){var e=this._optionBackup;return this._timelineOptions=e.timelineOptions,this._mediaList=e.mediaList,this._mediaDefault=e.mediaDefault,this._currentMediaIndices=[],b(t?e.baseOption:this._newBaseOption)},fd.prototype.getTimelineOption=function(t){var e,n=this._timelineOptions;return e=n.length&&(t=t.getComponent("timeline"))?b(n[t.getCurrentIndex()]):e},fd.prototype.getMediaOption=function(t){var e=this._api.getWidth(),n=this._api.getHeight(),i=this._mediaList,r=this._mediaDefault,o=[],a=[];if(i.length||r){for(var s,l,u=0,h=i.length;u<h;u++)!function(t,e,n){var i={width:e,height:n,aspectratio:e/n},r=!0;return O(t,function(t,e){var n,e=e.match(pd);e&&e[1]&&e[2]&&(n=e[1],e=e[2].toLowerCase(),e=i[e],t=t,("min"===(n=n)?t<=e:"max"===n?e<=t:e===t)||(r=!1))}),r}(i[u].query,e,n)||o.push(u);(o=!o.length&&r?[-1]:o).length&&(s=o,l=this._currentMediaIndices,s.join(",")!==l.join(","))&&(a=z(o,function(t){return b((-1===t?r:i[t]).option)})),this._currentMediaIndices=o}return a},fd);function fd(t){this._timelineOptions=[],this._mediaList=[],this._currentMediaIndices=[],this._api=t}var gd=O,yd=R,md=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function vd(t){var e=t&&t.itemStyle;if(e)for(var n=0,i=md.length;n<i;n++){var r=md[n],o=e.normal,a=e.emphasis;o&&o[r]&&(t[r]=t[r]||{},t[r].normal?d(t[r].normal,o[r]):t[r].normal=o[r],o[r]=null),a&&a[r]&&(t[r]=t[r]||{},t[r].emphasis?d(t[r].emphasis,a[r]):t[r].emphasis=a[r],a[r]=null)}}function _d(t,e,n){var i,r;t&&t[e]&&(t[e].normal||t[e].emphasis)&&(i=t[e].normal,r=t[e].emphasis,i&&(n?(t[e].normal=t[e].emphasis=null,B(t[e],i)):t[e]=i),r)&&(t.emphasis=t.emphasis||{},(t.emphasis[e]=r).focus&&(t.emphasis.focus=r.focus),r.blurScope)&&(t.emphasis.blurScope=r.blurScope)}function xd(t){_d(t,"itemStyle"),_d(t,"lineStyle"),_d(t,"areaStyle"),_d(t,"label"),_d(t,"labelLine"),_d(t,"upperLabel"),_d(t,"edgeLabel")}function wd(t,e){var n=yd(t)&&t[e],i=yd(n)&&n.textStyle;if(i)for(var r=0,o=xo.length;r<o;r++){var a=xo[r];i.hasOwnProperty(a)&&(n[a]=i[a])}}function bd(t){t&&(xd(t),wd(t,"label"),t.emphasis)&&wd(t.emphasis,"label")}function Sd(t){return F(t)?t:t?[t]:[]}function Md(t){return(F(t)?t[0]:t)||{}}function Td(e,t){gd(Sd(e.series),function(t){if(yd(t))if(yd(t)){vd(t),xd(t),wd(t,"label"),wd(t,"upperLabel"),wd(t,"edgeLabel"),t.emphasis&&(wd(t.emphasis,"label"),wd(t.emphasis,"upperLabel"),wd(t.emphasis,"edgeLabel"));var e=t.markPoint,n=(e&&(vd(e),bd(e)),t.markLine),i=(n&&(vd(n),bd(n)),t.markArea),r=(i&&bd(i),t.data);if("graph"===t.type){var r=r||t.nodes,o=t.links||t.edges;if(o&&!mt(o))for(var a=0;a<o.length;a++)bd(o[a]);O(t.categories,function(t){xd(t)})}if(r&&!mt(r))for(a=0;a<r.length;a++)bd(r[a]);if((e=t.markPoint)&&e.data)for(var s=e.data,a=0;a<s.length;a++)bd(s[a]);if((n=t.markLine)&&n.data)for(var l=n.data,a=0;a<l.length;a++)F(l[a])?(bd(l[a][0]),bd(l[a][1])):bd(l[a]);"gauge"===t.type?(wd(t,"axisLabel"),wd(t,"title"),wd(t,"detail")):"treemap"===t.type?(_d(t.breadcrumb,"itemStyle"),O(t.levels,function(t){xd(t)})):"tree"===t.type&&xd(t.leaves)}});var n=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];t&&n.push("valueAxis","categoryAxis","logAxis","timeAxis"),gd(n,function(t){gd(Sd(e[t]),function(t){t&&(wd(t,"axisLabel"),wd(t.axisPointer,"label"))})}),gd(Sd(e.parallel),function(t){t=t&&t.parallelAxisDefault;wd(t,"axisLabel"),wd(t&&t.axisPointer,"label")}),gd(Sd(e.calendar),function(t){_d(t,"itemStyle"),wd(t,"dayLabel"),wd(t,"monthLabel"),wd(t,"yearLabel")}),gd(Sd(e.radar),function(t){wd(t,"name"),t.name&&null==t.axisName&&(t.axisName=t.name,delete t.name),null!=t.nameGap&&null==t.axisNameGap&&(t.axisNameGap=t.nameGap,delete t.nameGap)}),gd(Sd(e.geo),function(t){yd(t)&&(bd(t),gd(Sd(t.regions),function(t){bd(t)}))}),gd(Sd(e.timeline),function(t){bd(t),_d(t,"label"),_d(t,"itemStyle"),_d(t,"controlStyle",!0);t=t.data;F(t)&&O(t,function(t){R(t)&&(_d(t,"label"),_d(t,"itemStyle"))})}),gd(Sd(e.toolbox),function(t){_d(t,"iconStyle"),gd(t.feature,function(t){_d(t,"iconStyle")})}),wd(Md(e.axisPointer),"label"),wd(Md(e.tooltip).axisPointer,"label")}function Cd(e){e&&O(kd,function(t){t[0]in e&&!(t[1]in e)&&(e[t[1]]=e[t[0]])})}var kd=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],Id=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],Dd=[["borderRadius","barBorderRadius"],["borderColor","barBorderColor"],["borderWidth","barBorderWidth"]];function Ld(t){var e=t&&t.itemStyle;if(e)for(var n=0;n<Dd.length;n++){var i=Dd[n][1],r=Dd[n][0];null!=e[i]&&(e[r]=e[i])}}function Ad(t){t&&"edge"===t.alignTo&&null!=t.margin&&null==t.edgeDistance&&(t.edgeDistance=t.margin)}function Pd(t){t&&t.downplay&&!t.blur&&(t.blur=t.downplay)}function Od(e,t){Td(e,t),e.series=vo(e.series),O(e.series,function(t){if(R(t)){var e,n=t.type;if("line"===n)null!=t.clipOverflow&&(t.clip=t.clipOverflow);else if("pie"===n||"gauge"===n){if(null!=t.clockWise&&(t.clockwise=t.clockWise),Ad(t.label),(e=t.data)&&!mt(e))for(var i=0;i<e.length;i++)Ad(e[i]);null!=t.hoverOffset&&(t.emphasis=t.emphasis||{},t.emphasis.scaleSize=null)&&(t.emphasis.scaleSize=t.hoverOffset)}else if("gauge"===n){var r=function(t,e){for(var n=e.split(","),i=t,r=0;r<n.length&&null!=(i=i&&i[n[r]]);r++);return i}(t,"pointer.color");if(null!=r){var o=t;var a="itemStyle.color";var s=void 0;for(var l,u=a.split(","),h=o,c=0;c<u.length-1;c++)null==h[l=u[c]]&&(h[l]={}),h=h[l];!s&&null!=h[u[c]]||(h[u[c]]=r)}}else if("bar"===n){if(Ld(t),Ld(t.backgroundStyle),Ld(t.emphasis),(e=t.data)&&!mt(e))for(i=0;i<e.length;i++)"object"==typeof e[i]&&(Ld(e[i]),Ld(e[i]&&e[i].emphasis))}else"sunburst"===n?((a=t.highlightPolicy)&&(t.emphasis=t.emphasis||{},t.emphasis.focus||(t.emphasis.focus=a)),Pd(t),function t(e,n){if(e)for(var i=0;i<e.length;i++)n(e[i]),e[i]&&t(e[i].children,n)}(t.data,Pd)):"graph"===n||"sankey"===n?(o=t)&&null!=o.focusNodeAdjacency&&(o.emphasis=o.emphasis||{},null==o.emphasis.focus)&&(o.emphasis.focus="adjacency"):"map"===n&&(t.mapType&&!t.map&&(t.map=t.mapType),t.mapLocation)&&B(t,t.mapLocation);null!=t.hoverAnimation&&(t.emphasis=t.emphasis||{},t.emphasis)&&null==t.emphasis.scale&&(t.emphasis.scale=t.hoverAnimation),Cd(t)}}),e.dataRange&&(e.visualMap=e.dataRange),O(Id,function(t){t=e[t];t&&O(t=F(t)?t:[t],function(t){Cd(t)})})}function Rd(_){O(_,function(p,d){var f=[],g=[NaN,NaN],t=[p.stackResultDimension,p.stackedOverDimension],y=p.data,m=p.isStackedByIndex,v=p.seriesModel.get("stackStrategy")||"samesign";y.modify(t,function(t,e,n){var i,r,o=y.get(p.stackedDimension,n);if(isNaN(o))return g;m?r=y.getRawIndex(n):i=y.get(p.stackedByDimension,n);for(var a,s,l,u=NaN,h=d-1;0<=h;h--){var c=_[h];if(0<=(r=m?r:c.data.rawIndexOf(c.stackedByDimension,i))){c=c.data.getByRawIndex(c.stackResultDimension,r);if("all"===v||"positive"===v&&0<c||"negative"===v&&c<0||"samesign"===v&&0<=o&&0<c||"samesign"===v&&o<=0&&c<0){a=o,s=c,l=void 0,l=Math.max(no(a),no(s)),a+=s,o=Qr<l?a:eo(a,l),u=c;break}}}return f[0]=o,f[1]=u,f})})}var Nd,Ed,Bd=function(t){this.data=t.data||(t.sourceFormat===Bp?{}:[]),this.sourceFormat=t.sourceFormat||Fp,this.seriesLayoutBy=t.seriesLayoutBy||Vp,this.startIndex=t.startIndex||0,this.dimensionsDetectedCount=t.dimensionsDetectedCount,this.metaRawOption=t.metaRawOption;var e=this.dimensionsDefine=t.dimensionsDefine;if(e)for(var n=0;n<e.length;n++){var i=e[n];null==i.type&&Xp(this,n)===Wp.Must&&(i.type="ordinal")}};function zd(t){return t instanceof Bd}function Fd(t,e,n){n=n||Hd(t);var i=e.seriesLayoutBy,r=function(t,e,n,i,r){var o,a;if(!t)return{dimensionsDefine:Wd(r),startIndex:a,dimensionsDetectedCount:o};{var s;e===Np?(s=t,"auto"===i||null==i?Gd(function(t){null!=t&&"-"!==t&&(V(t)?null==a&&(a=1):a=0)},n,s,10):a=gt(i)?i:i?1:0,r||1!==a||(r=[],Gd(function(t,e){r[e]=null!=t?t+"":""},n,s,1/0)),o=r?r.length:n===Hp?s.length:s[0]?s[0].length:null):e===Ep?r=r||function(t){var e,n=0;for(;n<t.length&&!(e=t[n++]););if(e)return ct(e)}(t):e===Bp?r||(r=[],O(t,function(t,e){r.push(e)})):e===Rp&&(i=wo(t[0]),o=F(i)&&i.length||1)}return{startIndex:a,dimensionsDefine:Wd(r),dimensionsDetectedCount:o}}(t,n,i,e.sourceHeader,e.dimensions);return new Bd({data:t,sourceFormat:n,seriesLayoutBy:i,dimensionsDefine:r.dimensionsDefine,startIndex:r.startIndex,dimensionsDetectedCount:r.dimensionsDetectedCount,metaRawOption:b(e)})}function Vd(t){return new Bd({data:t,sourceFormat:mt(t)?zp:Rp})}function Hd(t){var e=Fp;if(mt(t))e=zp;else if(F(t)){0===t.length&&(e=Np);for(var n=0,i=t.length;n<i;n++){var r=t[n];if(null!=r){if(F(r)||mt(r)){e=Np;break}if(R(r)){e=Ep;break}}}}else if(R(t))for(var o in t)if(Vt(t,o)&&st(t[o])){e=Bp;break}return e}function Wd(t){var i;if(t)return i=E(),z(t,function(t,e){var n,t={name:(t=R(t)?t:{name:t}).name,displayName:t.displayName,type:t.type};return null!=t.name&&(t.name+="",null==t.displayName&&(t.displayName=t.name),(n=i.get(t.name))?t.name+="-"+n.count++:i.set(t.name,{count:1})),t})}function Gd(t,e,n,i){if(e===Hp)for(var r=0;r<n.length&&r<i;r++)t(n[r]?n[r][0]:null,r);else for(var o=n[0]||[],r=0;r<o.length&&r<i;r++)t(o[r],r)}function Ud(t){t=t.sourceFormat;return t===Ep||t===Bp}Zd.prototype.getSource=function(){return this._source},Zd.prototype.count=function(){return 0},Zd.prototype.getItem=function(t,e){},Zd.prototype.appendData=function(t){},Zd.prototype.clean=function(){},Zd.protoInitialize=((Xh=Zd.prototype).pure=!1,void(Xh.persistent=!0)),Zd.internalField=(Ed=function(t,e,n){var i,r=n.sourceFormat,o=n.seriesLayoutBy,a=n.startIndex,n=n.dimensionsDefine;P(t,Nd[sf(r,o)]),r===zp?(t.getItem=qd,t.count=Yd,t.fillStorage=Xd):(i=Jd(r,o),t.getItem=pt(i,null,e,a,n),i=nf(r,o),t.count=pt(i,null,e,a,n))},qd=function(t,e){t-=this._offset,e=e||[];for(var n=this._data,i=this._dimSize,r=i*t,o=0;o<i;o++)e[o]=n[r+o];return e},Xd=function(t,e,n,i){for(var r=this._data,o=this._dimSize,a=0;a<o;a++){for(var s=i[a],l=null==s[0]?1/0:s[0],u=null==s[1]?-1/0:s[1],h=e-t,c=n[a],p=0;p<h;p++){var d=r[p*o+a];(c[t+p]=d)<l&&(l=d),u<d&&(u=d)}s[0]=l,s[1]=u}},Yd=function(){return this._data?this._data.length/this._dimSize:0},(Xh={})[Np+"_"+Vp]={pure:!0,appendData:Kd},Xh[Np+"_"+Hp]={pure:!0,appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},Xh[Ep]={pure:!0,appendData:Kd},Xh[Bp]={pure:!0,appendData:function(t){var r=this._data;O(t,function(t,e){for(var n=r[e]||(r[e]=[]),i=0;i<(t||[]).length;i++)n.push(t[i])})}},Xh[Rp]={appendData:Kd},Xh[zp]={persistent:!1,pure:!0,appendData:function(t){this._data=t},clean:function(){this._offset+=this.count(),this._data=null}},void(Nd=Xh));var qd,Xd,Yd,jd=Zd;function Zd(t,e){var t=zd(t)?t:Vd(t),n=(this._source=t,this._data=t.data);t.sourceFormat===zp&&(this._offset=0,this._dimSize=e,this._data=n),Ed(this,n,t)}function Kd(t){for(var e=0;e<t.length;e++)this._data.push(t[e])}function $d(t,e,n,i){return t[i]}(Gc={})[Np+"_"+Vp]=function(t,e,n,i){return t[i+e]},Gc[Np+"_"+Hp]=function(t,e,n,i,r){i+=e;for(var o=r||[],a=t,s=0;s<a.length;s++){var l=a[s];o[s]=l?l[i]:null}return o},Gc[Ep]=$d,Gc[Bp]=function(t,e,n,i,r){for(var o=r||[],a=0;a<n.length;a++){var s=t[n[a].name];o[a]=s?s[i]:null}return o},Gc[Rp]=$d;var Qd=Gc;function Jd(t,e){return Qd[sf(t,e)]}function tf(t,e,n){return t.length}(h={})[Np+"_"+Vp]=function(t,e,n){return Math.max(0,t.length-e)},h[Np+"_"+Hp]=function(t,e,n){t=t[0];return t?Math.max(0,t.length-e):0},h[Ep]=tf,h[Bp]=function(t,e,n){t=t[n[0].name];return t?t.length:0},h[Rp]=tf;var ef=h;function nf(t,e){return ef[sf(t,e)]}function rf(t,e,n){return t[e]}(Zo={})[Np]=rf,Zo[Ep]=function(t,e,n){return t[n]},Zo[Bp]=rf,Zo[Rp]=function(t,e,n){t=wo(t);return t instanceof Array?t[e]:t},Zo[zp]=rf;var of=Zo;function af(t){return of[t]}function sf(t,e){return t===Np?t+"_"+e:t}function lf(t,e,n){if(t){var i,r,e=t.getRawDataItem(e);if(null!=e)return i=(r=t.getStore()).getSource().sourceFormat,null!=n?(t=t.getDimensionIndex(n),n=r.getDimensionProperty(t),af(i)(e,t,n)):(r=e,i===Rp?wo(e):r)}}var uf=/\{@(.+?)\}/g,Mc=(hf.prototype.getDataParams=function(t,e){var n=this.getData(e),i=this.getRawValue(t,e),r=n.getRawIndex(t),o=n.getName(t),a=n.getRawDataItem(t),s=n.getItemVisual(t,"style"),t=s&&s[n.getItemVisual(t,"drawType")||"fill"],s=s&&s.stroke,l=this.mainType,u="series"===l,n=n.userOutput&&n.userOutput.get();return{componentType:l,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:u?this.subType:null,seriesIndex:this.seriesIndex,seriesId:u?this.id:null,seriesName:u?this.name:null,name:o,dataIndex:r,data:a,dataType:e,value:i,color:t,borderColor:s,dimensionNames:n?n.fullDimensions:null,encode:n?n.encode:null,$vars:["seriesName","name","value"]}},hf.prototype.getFormattedLabel=function(i,t,e,n,r,o){t=t||"normal";var a=this.getData(e),e=this.getDataParams(i,e);return o&&(e.value=o.interpolatedValue),null!=n&&F(e.value)&&(e.value=e.value[n]),I(r=r||a.getItemModel(i).get("normal"===t?["label","formatter"]:[t,"label","formatter"]))?(e.status=t,e.dimensionIndex=n,r(e)):V(r)?gp(r,e).replace(uf,function(t,e){var n=e.length,n=("["===e.charAt(0)&&"]"===e.charAt(n-1)&&(e=+e.slice(1,n-1)),lf(a,i,e));return null!=(n=o&&F(o.interpolatedValue)&&0<=(e=a.getDimensionIndex(e))?o.interpolatedValue[e]:n)?n+"":""}):void 0},hf.prototype.getRawValue=function(t,e){return lf(this.getData(e),t)},hf.prototype.formatTooltip=function(t,e,n){},hf);function hf(){}function cf(t){return new pf(t)}df.prototype.perform=function(t){var e,n,i=this._upstream,r=t&&t.skip,o=(this._dirty&&i&&((o=this.context).data=o.outputData=i.context.outputData),this.__pipeline&&(this.__pipeline.currentTask=this),this._plan&&!r&&(e=this._plan(this.context)),u(this._modBy)),a=this._modDataCount||0,s=u(t&&t.modBy),l=t&&t.modDataCount||0;function u(t){return t=1<=t?t:1}o===s&&a===l||(e="reset"),!this._dirty&&"reset"!==e||(this._dirty=!1,n=this._doReset(r)),this._modBy=s,this._modDataCount=l;o=t&&t.step;if(this._dueEnd=i?i._outputDueEnd:this._count?this._count(this.context):1/0,this._progress){var h=this._dueIndex,c=Math.min(null!=o?this._dueIndex+o:1/0,this._dueEnd);if(!r&&(n||h<c)){var p=this._progress;if(F(p))for(var d=0;d<p.length;d++)this._doProgress(p[d],h,c,s,l);else this._doProgress(p,h,c,s,l)}this._dueIndex=c;a=null!=this._settedOutputEnd?this._settedOutputEnd:c;this._outputDueEnd=a}else this._dueIndex=this._outputDueEnd=null!=this._settedOutputEnd?this._settedOutputEnd:this._dueEnd;return this.unfinished()},df.prototype.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},df.prototype._doProgress=function(t,e,n,i,r){xf.reset(e,n,i,r),this._callingProgress=t,this._callingProgress({start:e,end:n,count:n-e,next:xf.next},this.context)},df.prototype._doReset=function(t){this._dueIndex=this._outputDueEnd=this._dueEnd=0,this._settedOutputEnd=null,!t&&this._reset&&((e=this._reset(this.context))&&e.progress&&(n=e.forceFirstProgress,e=e.progress),F(e))&&!e.length&&(e=null),this._progress=e,this._modBy=this._modDataCount=null;var e,n,t=this._downstream;return t&&t.dirty(),n},df.prototype.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},df.prototype.pipe=function(t){this._downstream===t&&!this._dirty||((this._downstream=t)._upstream=this,t.dirty())},df.prototype.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},df.prototype.getUpstream=function(){return this._upstream},df.prototype.getDownstream=function(){return this._downstream},df.prototype.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t};var pf=df;function df(t){this._reset=(t=t||{}).reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0}var ff,gf,yf,mf,vf,_f,xf=_f={reset:function(t,e,n,i){gf=t,ff=e,yf=n,mf=i,vf=Math.ceil(mf/yf),_f.next=1<yf&&0<mf?bf:wf}};function wf(){return gf<ff?gf++:null}function bf(){var t=gf%vf*yf+Math.ceil(gf/vf),t=ff<=gf?null:t<mf?t:gf;return gf++,t}function Sf(t,e){e=e&&e.type;return"ordinal"===e?t:null==(t="time"!==e||gt(t)||null==t||"-"===t?t:+lo(t))||""===t?NaN:Number(t)}var Mf=E({number:function(t){return parseFloat(t)},time:function(t){return+lo(t)},trim:function(t){return V(t)?It(t):t}});function Tf(t){return Mf.get(t)}var Cf={lt:function(t,e){return t<e},lte:function(t,e){return t<=e},gt:function(t,e){return e<t},gte:function(t,e){return e<=t}},kf=(If.prototype.evaluate=function(t){return gt(t)?this._opFn(t,this._rvalFloat):this._opFn(po(t),this._rvalFloat)},If);function If(t,e){gt(e)||f(""),this._opFn=Cf[t],this._rvalFloat=po(e)}Lf.prototype.evaluate=function(t,e){var n=gt(t)?t:po(t),i=gt(e)?e:po(e),r=isNaN(n),o=isNaN(i);return r&&(n=this._incomparable),o&&(i=this._incomparable),r&&o&&(r=V(t),o=V(e),r&&(n=o?t:0),o)&&(i=r?e:0),n<i?this._resultLT:i<n?-this._resultLT:0};var Df=Lf;function Lf(t,e){t="desc"===t;this._resultLT=t?1:-1,this._incomparable="min"===(e=null==e?t?"min":"max":e)?-1/0:1/0}Pf.prototype.evaluate=function(t){var e,n=t===this._rval;return n||(e=typeof t)===this._rvalTypeof||"number"!=e&&"number"!==this._rvalTypeof||(n=po(t)===this._rvalFloat),this._isEQ?n:!n};var Af=Pf;function Pf(t,e){this._rval=e,this._isEQ=t,this._rvalTypeof=typeof e,this._rvalFloat=po(e)}Rf.prototype.getRawData=function(){throw new Error("not supported")},Rf.prototype.getRawDataItem=function(t){throw new Error("not supported")},Rf.prototype.cloneRawData=function(){},Rf.prototype.getDimensionInfo=function(t){},Rf.prototype.cloneAllDimensionInfo=function(){},Rf.prototype.count=function(){},Rf.prototype.retrieveValue=function(t,e){},Rf.prototype.retrieveValueFromItem=function(t,e){},Rf.prototype.convertValue=Sf;var Of=Rf;function Rf(){}function Nf(t){return Hf(t.sourceFormat)||f(""),t.data}function Ef(t){var e=t.sourceFormat,n=t.data;if(Hf(e)||f(""),e===Np){for(var i=[],r=0,o=n.length;r<o;r++)i.push(n[r].slice());return i}if(e===Ep){for(i=[],r=0,o=n.length;r<o;r++)i.push(P({},n[r]));return i}}function Bf(t,e,n){if(null!=n)return gt(n)||!isNaN(n)&&!Vt(e,n)?t[n]:Vt(e,n)?e[n]:void 0}function zf(t){return b(t)}var Ff=E();function Vf(t,e){var n=vo(t),t=n.length;t||f("");for(var i=0,r=t;i<r;i++)e=function(t,i){i.length||f("");R(t)||f("");var e=t.type,d=Ff.get(e);d||f("");e=z(i,function(t){var e=t,t=d,n=new Of,i=e.data,r=n.sourceFormat=e.sourceFormat,o=e.startIndex,a=(e.seriesLayoutBy!==Vp&&f(""),[]),s={};if(h=e.dimensionsDefine)O(h,function(t,e){var n=t.name,e={index:e,name:n,displayName:t.displayName};a.push(e),null!=n&&(Vt(s,n)&&f(""),s[n]=e)});else for(var l=0;l<e.dimensionsDetectedCount;l++)a.push({index:l});var u=Jd(r,Vp),h=(t.__isBuiltIn&&(n.getRawDataItem=function(t){return u(i,o,a,t)},n.getRawData=pt(Nf,null,e)),n.cloneRawData=pt(Ef,null,e),nf(r,Vp)),c=(n.count=pt(h,null,i,o,a),af(r)),p=(n.retrieveValue=function(t,e){t=u(i,o,a,t);return p(t,e)},n.retrieveValueFromItem=function(t,e){var n;return null!=t&&(n=a[e])?c(t,e,n.name):void 0});return n.getDimensionInfo=pt(Bf,null,a,s),n.cloneAllDimensionInfo=pt(zf,null,a),n});return z(vo(d.transform({upstream:e[0],upstreamList:e,config:b(t.config)})),function(t,e){R(t)||f(""),t.data||f("");Hf(Hd(t.data))||f("");var n=i[0],e=n&&0===e&&!t.dimensions?((e=n.startIndex)&&(t.data=n.data.slice(0,e).concat(t.data)),{seriesLayoutBy:Vp,sourceHeader:e,dimensions:n.metaRawOption.dimensions}):{seriesLayoutBy:Vp,sourceHeader:0,dimensions:t.dimensions};return Fd(t.data,e,null)})}(n[i],e),i!==r-1&&(e.length=Math.max(e.length,1));return e}function Hf(t){return t===Np||t===Ep}var Wf,Xh="undefined",Gf=typeof Uint32Array==Xh?Array:Uint32Array,Uf=typeof Uint16Array==Xh?Array:Uint16Array,qf=typeof Int32Array==Xh?Array:Int32Array,Gc=typeof Float64Array==Xh?Array:Float64Array,Xf={float:Gc,int:qf,ordinal:Array,number:Array,time:Gc};function Yf(t){return 65535<t?Gf:Uf}function jf(){return[1/0,-1/0]}function Zf(t,e,n,i,r){n=Xf[n||"float"];if(r){var o=t[e],a=o&&o.length;if(a!==i){for(var s=new n(i),l=0;l<a;l++)s[l]=o[l];t[e]=s}}else t[e]=new n(i)}y.prototype.initData=function(t,e,n){this._provider=t,this._chunks=[],this._indices=null,this.getRawIndex=this._getRawIdxIdentity;var i=t.getSource(),r=this.defaultDimValueGetter=Wf[i.sourceFormat];this._dimValueGetter=n||r,this._rawExtent=[],Ud(i),this._dimensions=z(e,function(t){return{type:t.type,property:t.property}}),this._initDataFromProvider(0,t.count())},y.prototype.getProvider=function(){return this._provider},y.prototype.getSource=function(){return this._provider.getSource()},y.prototype.ensureCalculationDimension=function(t,e){var n=this._calcDimNameToIdx,i=this._dimensions,r=n.get(t);if(null!=r){if(i[r].type===e)return r}else r=i.length;return i[r]={type:e},n.set(t,r),this._chunks[r]=new Xf[e||"float"](this._rawCount),this._rawExtent[r]=jf(),r},y.prototype.collectOrdinalMeta=function(t,e){for(var n=this._chunks[t],i=this._dimensions[t],r=this._rawExtent,o=i.ordinalOffset||0,a=n.length,s=(0===o&&(r[t]=jf()),r[t]),l=o;l<a;l++){var u=n[l]=e.parseAndCollect(n[l]);isNaN(u)||(s[0]=Math.min(u,s[0]),s[1]=Math.max(u,s[1]))}i.ordinalMeta=e,i.ordinalOffset=a,i.type="ordinal"},y.prototype.getOrdinalMeta=function(t){return this._dimensions[t].ordinalMeta},y.prototype.getDimensionProperty=function(t){t=this._dimensions[t];return t&&t.property},y.prototype.appendData=function(t){var e=this._provider,n=this.count(),t=(e.appendData(t),e.count());return e.persistent||(t+=n),n<t&&this._initDataFromProvider(n,t,!0),[n,t]},y.prototype.appendValues=function(t,e){for(var n=this._chunks,i=this._dimensions,r=i.length,o=this._rawExtent,a=this.count(),s=a+Math.max(t.length,e||0),l=0;l<r;l++)Zf(n,l,(d=i[l]).type,s,!0);for(var u=[],h=a;h<s;h++)for(var c=h-a,p=0;p<r;p++){var d=i[p],f=Wf.arrayRows.call(this,t[c]||u,d.property,c,p),g=(n[p][h]=f,o[p]);f<g[0]&&(g[0]=f),f>g[1]&&(g[1]=f)}return{start:a,end:this._rawCount=this._count=s}},y.prototype._initDataFromProvider=function(t,e,n){for(var i=this._provider,r=this._chunks,o=this._dimensions,a=o.length,s=this._rawExtent,l=z(o,function(t){return t.property}),u=0;u<a;u++){var h=o[u];s[u]||(s[u]=jf()),Zf(r,u,h.type,e,n)}if(i.fillStorage)i.fillStorage(t,e,r,s);else for(var c=[],p=t;p<e;p++)for(var c=i.getItem(p,c),d=0;d<a;d++){var f=r[d],g=this._dimValueGetter(c,l[d],p,d),f=(f[p]=g,s[d]);g<f[0]&&(f[0]=g),g>f[1]&&(f[1]=g)}!i.persistent&&i.clean&&i.clean(),this._rawCount=this._count=e,this._extent=[]},y.prototype.count=function(){return this._count},y.prototype.get=function(t,e){return 0<=e&&e<this._count&&(t=this._chunks[t])?t[this.getRawIndex(e)]:NaN},y.prototype.getValues=function(t,e){var n=[],i=[];if(null==e){e=t,t=[];for(var r=0;r<this._dimensions.length;r++)i.push(r)}else i=t;for(var r=0,o=i.length;r<o;r++)n.push(this.get(i[r],e));return n},y.prototype.getByRawIndex=function(t,e){return 0<=e&&e<this._rawCount&&(t=this._chunks[t])?t[e]:NaN},y.prototype.getSum=function(t){var e=0;if(this._chunks[t])for(var n=0,i=this.count();n<i;n++){var r=this.get(t,n);isNaN(r)||(e+=r)}return e},y.prototype.getMedian=function(t){var e=[],t=(this.each([t],function(t){isNaN(t)||e.push(t)}),e.sort(function(t,e){return t-e})),n=this.count();return 0===n?0:n%2==1?t[(n-1)/2]:(t[n/2]+t[n/2-1])/2},y.prototype.indexOfRawIndex=function(t){if(!(t>=this._rawCount||t<0)){if(!this._indices)return t;var e=this._indices,n=e[t];if(null!=n&&n<this._count&&n===t)return t;for(var i=0,r=this._count-1;i<=r;){var o=(i+r)/2|0;if(e[o]<t)i=1+o;else{if(!(e[o]>t))return o;r=o-1}}}return-1},y.prototype.indicesOfNearest=function(t,e,n){var i=this._chunks[t],r=[];if(i){null==n&&(n=1/0);for(var o=1/0,a=-1,s=0,l=0,u=this.count();l<u;l++){var h=e-i[this.getRawIndex(l)],c=Math.abs(h);c<=n&&((c<o||c===o&&0<=h&&a<0)&&(o=c,a=h,s=0),h===a)&&(r[s++]=l)}r.length=s}return r},y.prototype.getIndices=function(){var t=this._indices;if(t){var e=t.constructor,n=this._count;if(e===Array)for(var i=new e(n),r=0;r<n;r++)i[r]=t[r];else i=new e(t.buffer,0,n)}else{i=new(e=Yf(this._rawCount))(this.count());for(r=0;r<i.length;r++)i[r]=r}return i},y.prototype.filter=function(t,e){if(!this._count)return this;for(var n=this.clone(),i=n.count(),r=new(Yf(n._rawCount))(i),o=[],a=t.length,s=0,l=t[0],u=n._chunks,h=0;h<i;h++){var c=void 0,p=n.getRawIndex(h);if(0===a)c=e(h);else if(1===a)c=e(u[l][p],h);else{for(var d=0;d<a;d++)o[d]=u[t[d]][p];o[d]=h,c=e.apply(null,o)}c&&(r[s++]=p)}return s<i&&(n._indices=r),n._count=s,n._extent=[],n._updateGetRawIdx(),n},y.prototype.selectRange=function(t){var e=this.clone(),n=e._count;if(!n)return this;var i=ct(t),r=i.length;if(!r)return this;var o=e.count(),a=new(Yf(e._rawCount))(o),s=0,l=i[0],u=t[l][0],h=t[l][1],c=e._chunks,l=!1;if(!e._indices){var p=0;if(1===r){for(var d=c[i[0]],f=0;f<n;f++)(u<=(v=d[f])&&v<=h||isNaN(v))&&(a[s++]=p),p++;l=!0}else if(2===r){for(var d=c[i[0]],g=c[i[1]],y=t[i[1]][0],m=t[i[1]][1],f=0;f<n;f++){var v=d[f],_=g[f];(u<=v&&v<=h||isNaN(v))&&(y<=_&&_<=m||isNaN(_))&&(a[s++]=p),p++}l=!0}}if(!l)if(1===r)for(f=0;f<o;f++){var x=e.getRawIndex(f);(u<=(v=c[i[0]][x])&&v<=h||isNaN(v))&&(a[s++]=x)}else for(f=0;f<o;f++){for(var w=!0,x=e.getRawIndex(f),b=0;b<r;b++){var S=i[b];((v=c[S][x])<t[S][0]||v>t[S][1])&&(w=!1)}w&&(a[s++]=e.getRawIndex(f))}return s<o&&(e._indices=a),e._count=s,e._extent=[],e._updateGetRawIdx(),e},y.prototype.map=function(t,e){var n=this.clone(t);return this._updateDims(n,t,e),n},y.prototype.modify=function(t,e){this._updateDims(this,t,e)},y.prototype._updateDims=function(t,e,n){for(var i=t._chunks,r=[],o=e.length,a=t.count(),s=[],l=t._rawExtent,u=0;u<e.length;u++)l[e[u]]=jf();for(var h=0;h<a;h++){for(var c=t.getRawIndex(h),p=0;p<o;p++)s[p]=i[e[p]][c];s[o]=h;var d=n&&n.apply(null,s);if(null!=d){"object"!=typeof d&&(r[0]=d,d=r);for(u=0;u<d.length;u++){var f=e[u],g=d[u],y=l[f],f=i[f];f&&(f[c]=g),g<y[0]&&(y[0]=g),g>y[1]&&(y[1]=g)}}}},y.prototype.lttbDownSample=function(t,e){var n,i=this.clone([t],!0),r=i._chunks[t],o=this.count(),a=0,s=Math.floor(1/e),l=this.getRawIndex(0),u=new(Yf(this._rawCount))(Math.min(2*(Math.ceil(o/s)+2),o));u[a++]=l;for(var h=1;h<o-1;h+=s){for(var c=Math.min(h+s,o-1),p=Math.min(h+2*s,o),d=(p+c)/2,f=0,g=c;g<p;g++){var y=r[M=this.getRawIndex(g)];isNaN(y)||(f+=y)}f/=p-c;for(var c=h,m=Math.min(h+s,o),v=h-1,_=r[l],x=-1,w=c,b=-1,S=0,g=c;g<m;g++){var M,y=r[M=this.getRawIndex(g)];isNaN(y)?(S++,b<0&&(b=M)):x<(n=Math.abs((v-d)*(y-_)-(v-g)*(f-_)))&&(x=n,w=M)}0<S&&S<m-c&&(u[a++]=Math.min(b,w),w=Math.max(b,w)),l=u[a++]=w}return u[a++]=this.getRawIndex(o-1),i._count=a,i._indices=u,i.getRawIndex=this._getRawIdx,i},y.prototype.downSample=function(t,e,n,i){for(var r=this.clone([t],!0),o=r._chunks,a=[],s=Math.floor(1/e),l=o[t],u=this.count(),h=r._rawExtent[t]=jf(),c=new(Yf(this._rawCount))(Math.ceil(u/s)),p=0,d=0;d<u;d+=s){u-d<s&&(a.length=s=u-d);for(var f=0;f<s;f++){var g=this.getRawIndex(d+f);a[f]=l[g]}var y=n(a),m=this.getRawIndex(Math.min(d+i(a,y)||0,u-1));(l[m]=y)<h[0]&&(h[0]=y),y>h[1]&&(h[1]=y),c[p++]=m}return r._count=p,r._indices=c,r._updateGetRawIdx(),r},y.prototype.each=function(t,e){if(this._count)for(var n=t.length,i=this._chunks,r=0,o=this.count();r<o;r++){var a=this.getRawIndex(r);switch(n){case 0:e(r);break;case 1:e(i[t[0]][a],r);break;case 2:e(i[t[0]][a],i[t[1]][a],r);break;default:for(var s=0,l=[];s<n;s++)l[s]=i[t[s]][a];l[s]=r,e.apply(null,l)}}},y.prototype.getDataExtent=function(t){var e=this._chunks[t],n=jf();if(!e)return n;var i=this.count();if(!this._indices)return this._rawExtent[t].slice();if(r=this._extent[t])return r.slice();for(var r,o=(r=n)[0],a=r[1],s=0;s<i;s++){var l=e[this.getRawIndex(s)];l<o&&(o=l),a<l&&(a=l)}return this._extent[t]=r=[o,a]},y.prototype.getRawDataItem=function(t){var e=this.getRawIndex(t);if(this._provider.persistent)return this._provider.getItem(e);for(var n=[],i=this._chunks,r=0;r<i.length;r++)n.push(i[r][e]);return n},y.prototype.clone=function(t,e){var n,i,r=new y,o=this._chunks,a=t&&lt(t,function(t,e){return t[e]=!0,t},{});if(a)for(var s=0;s<o.length;s++)r._chunks[s]=a[s]?(n=o[s],i=void 0,(i=n.constructor)===Array?n.slice():new i(n)):o[s];else r._chunks=o;return this._copyCommonProps(r),e||(r._indices=this._cloneIndices()),r._updateGetRawIdx(),r},y.prototype._copyCommonProps=function(t){t._count=this._count,t._rawCount=this._rawCount,t._provider=this._provider,t._dimensions=this._dimensions,t._extent=b(this._extent),t._rawExtent=b(this._rawExtent)},y.prototype._cloneIndices=function(){if(this._indices){var t=this._indices.constructor,e=void 0;if(t===Array)for(var n=this._indices.length,e=new t(n),i=0;i<n;i++)e[i]=this._indices[i];else e=new t(this._indices);return e}return null},y.prototype._getRawIdxIdentity=function(t){return t},y.prototype._getRawIdx=function(t){return t<this._count&&0<=t?this._indices[t]:-1},y.prototype._updateGetRawIdx=function(){this.getRawIndex=this._indices?this._getRawIdx:this._getRawIdxIdentity},y.internalField=void(Wf={arrayRows:$f,objectRows:function(t,e,n,i){return Sf(t[e],this._dimensions[i])},keyedColumns:$f,original:function(t,e,n,i){t=t&&(null==t.value?t:t.value);return Sf(t instanceof Array?t[i]:t,this._dimensions[i])},typedArray:function(t,e,n,i){return t[i]}});var Kf=y;function y(){this._chunks=[],this._rawExtent=[],this._extent=[],this._count=0,this._rawCount=0,this._calcDimNameToIdx=E()}function $f(t,e,n,i){return Sf(t[i],this._dimensions[i])}Jf.prototype.dirty=function(){this._setLocalSource([],[]),this._storeList=[],this._dirty=!0},Jf.prototype._setLocalSource=function(t,e){this._sourceList=t,this._upstreamSignList=e,this._versionSignBase++,9e10<this._versionSignBase&&(this._versionSignBase=0)},Jf.prototype._getVersionSign=function(){return this._sourceHost.uid+"_"+this._versionSignBase},Jf.prototype.prepareSource=function(){this._isDirty()&&(this._createSource(),this._dirty=!1)},Jf.prototype._createSource=function(){this._setLocalSource([],[]);var t,e,n,i,r,o,a,s=this._sourceHost,l=this._getUpstreamSourceManagers(),u=!!l.length;eg(s)?(i=s,r=t=o=void 0,e=u?((e=l[0]).prepareSource(),o=(r=e.getSource()).data,t=r.sourceFormat,[e._getVersionSign()]):(t=mt(o=i.get("data",!0))?zp:Rp,[]),i=this._getSourceMetaRawOption()||{},r=r&&r.metaRawOption||{},a=N(i.seriesLayoutBy,r.seriesLayoutBy)||null,n=N(i.sourceHeader,r.sourceHeader),i=N(i.dimensions,r.dimensions),r=a!==r.seriesLayoutBy||!!n!=!!r.sourceHeader||i?[Fd(o,{seriesLayoutBy:a,sourceHeader:n,dimensions:i},t)]:[]):(o=s,e=u?(r=(a=this._applyTransform(l)).sourceList,a.upstreamSignList):(r=[Fd(o.get("source",!0),this._getSourceMetaRawOption(),null)],[])),this._setLocalSource(r,e)},Jf.prototype._applyTransform=function(t){var e,n=this._sourceHost,i=n.get("transform",!0),r=n.get("fromTransformResult",!0),o=(null!=r&&1!==t.length&&ng(""),[]),a=[];return O(t,function(t){t.prepareSource();var e=t.getSource(r||0);null==r||e||ng(""),o.push(e),a.push(t._getVersionSign())}),i?e=Vf(i,o,n.componentIndex):null!=r&&(e=[new Bd({data:(t=o[0]).data,sourceFormat:t.sourceFormat,seriesLayoutBy:t.seriesLayoutBy,dimensionsDefine:b(t.dimensionsDefine),startIndex:t.startIndex,dimensionsDetectedCount:t.dimensionsDetectedCount})]),{sourceList:e,upstreamSignList:a}},Jf.prototype._isDirty=function(){if(this._dirty)return!0;for(var t=this._getUpstreamSourceManagers(),e=0;e<t.length;e++){var n=t[e];if(n._isDirty()||this._upstreamSignList[e]!==n._getVersionSign())return!0}},Jf.prototype.getSource=function(t){var e=this._sourceList[t=t||0];return e||(e=this._getUpstreamSourceManagers())[0]&&e[0].getSource(t)},Jf.prototype.getSharedDataStore=function(t){var e=t.makeStoreSchema();return this._innerGetDataStore(e.dimensions,t.source,e.hash)},Jf.prototype._innerGetDataStore=function(t,e,n){var i,r=this._storeList,o=r[0],r=(o=o||(r[0]={}))[n];return r||(i=this._getUpstreamSourceManagers()[0],eg(this._sourceHost)&&i?r=i._innerGetDataStore(t,e,n):(r=new Kf).initData(new jd(e,t.length),t),o[n]=r),r},Jf.prototype._getUpstreamSourceManagers=function(){var t,e=this._sourceHost;return eg(e)?(t=qp(e))?[t.getSourceManager()]:[]:z((t=e).get("transform",!0)||t.get("fromTransformResult",!0)?No(t.ecModel,"dataset",{index:t.get("fromDatasetIndex",!0),id:t.get("fromDatasetId",!0)},Ro).models:[],function(t){return t.getSourceManager()})},Jf.prototype._getSourceMetaRawOption=function(){var t,e,n,i=this._sourceHost;return eg(i)?(t=i.get("seriesLayoutBy",!0),e=i.get("sourceHeader",!0),n=i.get("dimensions",!0)):this._getUpstreamSourceManagers().length||(t=(i=i).get("seriesLayoutBy",!0),e=i.get("sourceHeader",!0),n=i.get("dimensions",!0)),{seriesLayoutBy:t,sourceHeader:e,dimensions:n}};var Qf=Jf;function Jf(t){this._sourceList=[],this._storeList=[],this._upstreamSignList=[],this._versionSignBase=0,this._dirty=!0,this._sourceHost=t}function tg(t){t.option.transform&&Lt(t.option.transform)}function eg(t){return"series"===t.mainType}function ng(t){throw new Error(t)}function ig(t,e){return e.type=t,e}function rg(t,e){var n,e=t.getData().getItemVisual(e,"style")[t.visualDrawType];return n=n||"transparent",V(t=e)?t:R(t)&&t.colorStops&&(t.colorStops[0]||{}).color||n}function og(t){var e,n,i,r,o,a,s,l,u,h,c,p=t.series,d=t.dataIndex,t=t.multipleSeries,f=p.getData(),g=f.mapDimensionsAll("defaultedTooltip"),y=g.length,m=p.getRawValue(d),v=F(m),_=rg(p,d);function x(t,e){e=s.getDimensionInfo(e);e&&!1!==e.otherDims.tooltip&&(l?c.push(ig("nameValue",{markerType:"subItem",markerColor:a,name:e.displayName,value:t,valueType:e.type})):(u.push(t),h.push(e.type)))}1<y||v&&!y?(i=m,r=d,o=g,a=_,s=p.getData(),l=lt(i,function(t,e,n){n=s.getDimensionInfo(n);return t||n&&!1!==n.tooltip&&null!=n.displayName},!1),u=[],h=[],c=[],o.length?O(o,function(t){x(lf(s,r,t),t)}):O(i,x),i=(o={inlineValues:u,inlineValueTypes:h,blocks:c}).inlineValueTypes,e=o.blocks,n=(o=o.inlineValues)[0]):y?(y=f.getDimensionInfo(g[0]),n=o=lf(f,d,g[0]),i=y.type):n=o=v?m[0]:m;g=Co(p),y=g&&p.name||"",v=f.getName(d),m=t?y:v;return ig("section",{header:y,noHeader:t||!g,sortParam:n,blocks:[ig("nameValue",{markerType:"item",markerColor:_,name:m,noName:!It(m),value:o,valueType:i,dataIndex:d})].concat(e||[])})}var ag=Lo();function sg(t,e){return t.getName(e)||t.getId(e)}a(m,lg=g),m.prototype.init=function(t,e,n){this.seriesIndex=this.componentIndex,this.dataTask=cf({count:cg,reset:pg}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(t,n);(ag(this).sourceManager=new Qf(this)).prepareSource();t=this.getInitialData(t,n);fg(t,this),this.dataTask.context.data=t,ag(this).dataBeforeProcessed=t,hg(this),this._initSelectedMapFromData(t)},m.prototype.mergeDefaultAndTheme=function(t,e){var n=bp(this),i=n?Mp(t):{},r=this.subType;g.hasClass(r),d(t,e.getTheme().get(this.subType)),d(t,this.getDefaultOption()),_o(t,"label",["show"]),this.fillDataTextStyle(t.data),n&&Sp(t,i,n)},m.prototype.mergeOption=function(t,e){t=d(this.option,t,!0),this.fillDataTextStyle(t.data);var n=bp(this),n=(n&&Sp(this.option,t,n),ag(this).sourceManager),n=(n.dirty(),n.prepareSource(),this.getInitialData(t,e));fg(n,this),this.dataTask.dirty(),this.dataTask.context.data=n,ag(this).dataBeforeProcessed=n,hg(this),this._initSelectedMapFromData(n)},m.prototype.fillDataTextStyle=function(t){if(t&&!mt(t))for(var e=["show"],n=0;n<t.length;n++)t[n]&&t[n].label&&_o(t[n],"label",e)},m.prototype.getInitialData=function(t,e){},m.prototype.appendData=function(t){this.getRawData().appendData(t.data)},m.prototype.getData=function(t){var e=yg(this);return e?(e=e.context.data,null!=t&&e.getLinkedData?e.getLinkedData(t):e):ag(this).data},m.prototype.getAllData=function(){var t=this.getData();return t&&t.getLinkedDataAll?t.getLinkedDataAll():[{data:t}]},m.prototype.setData=function(t){var e,n=yg(this);n&&((e=n.context).outputData=t,n!==this.dataTask)&&(e.data=t),ag(this).data=t},m.prototype.getEncode=function(){var t=this.get("encode",!0);if(t)return E(t)},m.prototype.getSourceManager=function(){return ag(this).sourceManager},m.prototype.getSource=function(){return this.getSourceManager().getSource()},m.prototype.getRawData=function(){return ag(this).dataBeforeProcessed},m.prototype.getColorBy=function(){return this.get("colorBy")||"series"},m.prototype.isColorBySeries=function(){return"series"===this.getColorBy()},m.prototype.getBaseAxis=function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},m.prototype.formatTooltip=function(t,e,n){return og({series:this,dataIndex:t,multipleSeries:e})},m.prototype.isAnimationEnabled=function(){var t=this.ecModel;return!!(!p.node||t&&t.ssr)&&!!(t=(t=this.getShallow("animation"))&&this.getData().count()>this.getShallow("animationThreshold")?!1:t)},m.prototype.restoreData=function(){this.dataTask.dirty()},m.prototype.getColorFromPalette=function(t,e,n){var i=this.ecModel;return Qp.prototype.getColorFromPalette.call(this,t,e,n)||i.getColorFromPalette(t,e,n)},m.prototype.coordDimToDataDim=function(t){return this.getRawData().mapDimensionsAll(t)},m.prototype.getProgressive=function(){return this.get("progressive")},m.prototype.getProgressiveThreshold=function(){return this.get("progressiveThreshold")},m.prototype.select=function(t,e){this._innerSelect(this.getData(e),t)},m.prototype.unselect=function(t,e){var n=this.option.selectedMap;if(n){var i=this.option.selectedMode,r=this.getData(e);if("series"===i||"all"===n)this.option.selectedMap={},this._selectedDataIndicesMap={};else for(var o=0;o<t.length;o++){var a=sg(r,t[o]);n[a]=!1,this._selectedDataIndicesMap[a]=-1}}},m.prototype.toggleSelect=function(t,e){for(var n=[],i=0;i<t.length;i++)n[0]=t[i],this.isSelected(t[i],e)?this.unselect(n,e):this.select(n,e)},m.prototype.getSelectedDataIndices=function(){if("all"===this.option.selectedMap)return[].slice.call(this.getData().getIndices());for(var t=this._selectedDataIndicesMap,e=ct(t),n=[],i=0;i<e.length;i++){var r=t[e[i]];0<=r&&n.push(r)}return n},m.prototype.isSelected=function(t,e){var n=this.option.selectedMap;return!!n&&(e=this.getData(e),"all"===n||n[sg(e,t)])&&!e.getItemModel(t).get(["select","disabled"])},m.prototype.isUniversalTransitionEnabled=function(){var t;return!!this.__universalTransitionEnabled||!!(t=this.option.universalTransition)&&(!0===t||t&&t.enabled)},m.prototype._innerSelect=function(t,e){var n=this.option,i=n.selectedMode,r=e.length;if(i&&r)if("series"===i)n.selectedMap="all";else if("multiple"===i){R(n.selectedMap)||(n.selectedMap={});for(var o=n.selectedMap,a=0;a<r;a++){var s,l=e[a];o[s=sg(t,l)]=!0,this._selectedDataIndicesMap[s]=t.getRawIndex(l)}}else"single"!==i&&!0!==i||(s=sg(t,i=e[r-1]),n.selectedMap=((n={})[s]=!0,n),this._selectedDataIndicesMap=((n={})[s]=t.getRawIndex(i),n))},m.prototype._initSelectedMapFromData=function(n){var i;this.option.selectedMap||(i=[],n.hasItemOption&&n.each(function(t){var e=n.getRawDataItem(t);e&&e.selected&&i.push(t)}),0<i.length&&this._innerSelect(n,i))},m.registerClass=function(t){return g.registerClass(t)},m.protoInitialize=((h=m.prototype).type="series.__base__",h.seriesIndex=0,h.ignoreStyleOnData=!1,h.hasSymbolVisual=!1,h.defaultSymbol="circle",h.visualStyleAccessPath="itemStyle",void(h.visualDrawType="fill"));var lg,ug=m;function m(){var t=null!==lg&&lg.apply(this,arguments)||this;return t._selectedDataIndicesMap={},t}function hg(t){var e,n,i=t.name;Co(t)||(t.name=(t=(e=(t=t).getRawData()).mapDimensionsAll("seriesName"),n=[],O(t,function(t){t=e.getDimensionInfo(t);t.displayName&&n.push(t.displayName)}),n.join(" ")||i))}function cg(t){return t.model.getRawData().count()}function pg(t){t=t.model;return t.setData(t.getRawData().cloneShallow()),dg}function dg(t,e){e.outputData&&t.end>e.outputData.count()&&e.model.getRawData().cloneShallow(e.outputData)}function fg(e,n){O(Bt(e.CHANGABLE_METHODS,e.DOWNSAMPLE_METHODS),function(t){e.wrapMethod(t,dt(gg,n))})}function gg(t,e){t=yg(t);return t&&t.setOutputEnd((e||this).count()),e}function yg(t){var e,n=(t.ecModel||{}).scheduler,n=n&&n.getPipeline(t.uid);if(n)return(n=n.currentTask)&&(e=n.agentStubMap)?e.get(t.uid):n}at(ug,Mc),at(ug,Qp),Wo(ug,g);vg.prototype.init=function(t,e){},vg.prototype.render=function(t,e,n,i){},vg.prototype.dispose=function(t,e){},vg.prototype.updateView=function(t,e,n,i){},vg.prototype.updateLayout=function(t,e,n,i){},vg.prototype.updateVisual=function(t,e,n,i){},vg.prototype.toggleBlurSeries=function(t,e,n){},vg.prototype.eachRendered=function(t){var e=this.group;e&&e.traverse(t)};var mg=vg;function vg(){this.group=new Wr,this.uid=Lc("viewComponent")}Ho(mg),Xo(mg);var _g,xg=Lo(),wg=(_g=Lo(),function(t){var e=_g(t),t=t.pipelineContext,n=!!e.large,i=!!e.progressiveRender,r=e.large=!(!t||!t.large),e=e.progressiveRender=!(!t||!t.progressiveRender);return!(n==r&&i==e)&&"reset"}),bg=(Sg.prototype.init=function(t,e){},Sg.prototype.render=function(t,e,n,i){},Sg.prototype.highlight=function(t,e,n,i){t=t.getData(i&&i.dataType);t&&Tg(t,i,"emphasis")},Sg.prototype.downplay=function(t,e,n,i){t=t.getData(i&&i.dataType);t&&Tg(t,i,"normal")},Sg.prototype.remove=function(t,e){this.group.removeAll()},Sg.prototype.dispose=function(t,e){},Sg.prototype.updateView=function(t,e,n,i){this.render(t,e,n,i)},Sg.prototype.updateLayout=function(t,e,n,i){this.render(t,e,n,i)},Sg.prototype.updateVisual=function(t,e,n,i){this.render(t,e,n,i)},Sg.prototype.eachRendered=function(t){nc(this.group,t)},Sg.markUpdateMethod=function(t,e){xg(t).updateMethod=e},Sg.protoInitialize=void(Sg.prototype.type="chart"),Sg);function Sg(){this.group=new Wr,this.uid=Lc("viewChart"),this.renderTask=cf({plan:Cg,reset:kg}),this.renderTask.context={view:this}}function Mg(t,e,n){t&&zl(t)&&("emphasis"===e?wl:bl)(t,n)}function Tg(e,t,n){var i,r=Do(e,t),o=t&&null!=t.highlightKey?(t=t.highlightKey,i=null==(i=Ys[t])&&Xs<=32?Ys[t]=Xs++:i):null;null!=r?O(vo(r),function(t){Mg(e.getItemGraphicEl(t),n,o)}):e.eachItemGraphicEl(function(t){Mg(t,n,o)})}function Cg(t){return wg(t.model)}function kg(t){var e=t.model,n=t.ecModel,i=t.api,r=t.payload,o=e.pipelineContext.progressiveRender,t=t.view,a=r&&xg(r).updateMethod,o=o?"incrementalPrepareRender":a&&t[a]?a:"render";return"render"!==o&&t[o](e,n,i,r),Ig[o]}Ho(bg),Xo(bg);var Ig={incrementalPrepareRender:{progress:function(t,e){e.view.incrementalRender(t,e.model,e.ecModel,e.api,e.payload)}},render:{forceFirstProgress:!0,progress:function(t,e){e.view.render(e.model,e.ecModel,e.api,e.payload)}}};function Dg(t,r,o){var a,s,l,u,h,c=0,p=0,d=null;function f(){p=(new Date).getTime(),d=null,t.apply(l,u||[])}r=r||0;function e(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];a=(new Date).getTime(),l=this,u=t;var n=h||r,i=h||o;h=null,s=a-(i?c:p)-n,clearTimeout(d),i?d=setTimeout(f,n):0<=s?f():d=setTimeout(f,-s),c=a}return e.clear=function(){d&&(clearTimeout(d),d=null)},e.debounceNextCall=function(t){h=t},e}var Lg=Lo(),Ag={itemStyle:Yo(bc,!0),lineStyle:Yo(_c,!0)},Pg={lineStyle:"stroke",itemStyle:"fill"};function Og(t,e){t=t.visualStyleMapper||Ag[e];return t||(console.warn("Unknown style type '"+e+"'."),Ag.itemStyle)}function Rg(t,e){t=t.visualDrawType||Pg[e];return t||(console.warn("Unknown style type '"+e+"'."),"fill")}var Zo={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){var e=r.getData(),n=r.visualStyleAccessPath||"itemStyle",i=r.getModel(n),o=Og(r,n)(i),i=i.getShallow("decal"),a=(i&&(e.setVisual("decal",i),i.dirty=!0),Rg(r,n)),i=o[a],s=I(i)?i:null,n="auto"===o.fill||"auto"===o.stroke;if(o[a]&&!s&&!n||(i=r.getColorFromPalette(r.name,null,t.getSeriesCount()),o[a]||(o[a]=i,e.setVisual("colorFromPalette",!0)),o.fill="auto"===o.fill||I(o.fill)?i:o.fill,o.stroke="auto"===o.stroke||I(o.stroke)?i:o.stroke),e.setVisual("style",o),e.setVisual("drawType",a),!t.isSeriesFiltered(r)&&s)return e.setVisual("colorFromPalette",!1),{dataEach:function(t,e){var n=r.getDataParams(e),i=P({},o);i[a]=s(n),t.setItemVisual(e,"style",i)}}}},Ng=new kc,Xh={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var i,r,o;if(!t.ignoreStyleOnData&&!e.isSeriesFiltered(t))return e=t.getData(),i=t.visualStyleAccessPath||"itemStyle",r=Og(t,i),o=e.getVisual("drawType"),{dataEach:e.hasItemOption?function(t,e){var n=t.getRawDataItem(e);n&&n[i]&&(Ng.option=n[i],n=r(Ng),P(t.ensureUniqueItemVisual(e,"style"),n),Ng.option.decal&&(t.setItemVisual(e,"decal",Ng.option.decal),Ng.option.decal.dirty=!0),o in n)&&t.setItemVisual(e,"colorFromPalette",!1)}:null}}},Gc={performRawSeries:!0,overallReset:function(e){var i=E();e.eachSeries(function(t){var e,n=t.getColorBy();t.isColorBySeries()||(n=t.type+"-"+n,(e=i.get(n))||i.set(n,e={}),Lg(t).scope=e)}),e.eachSeries(function(i){var r,o,a,s,t,l;i.isColorBySeries()||e.isSeriesFiltered(i)||(r=i.getRawData(),o={},a=i.getData(),s=Lg(i).scope,t=i.visualStyleAccessPath||"itemStyle",l=Rg(i,t),a.each(function(t){var e=a.getRawIndex(t);o[e]=t}),r.each(function(t){var e,n=o[t];a.getItemVisual(n,"colorFromPalette")&&(n=a.ensureUniqueItemVisual(n,"style"),t=r.getName(t)||t+"",e=r.count(),n[l]=i.getColorFromPalette(t,s,e))}))})}},Eg=Math.PI;zg.prototype.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each(function(t){t=t.overallTask;t&&t.dirty()})},zg.prototype.getPerformArgs=function(t,e){var n,i;if(t.__pipeline)return i=(n=this._pipelineMap.get(t.__pipeline.id)).context,{step:e=!e&&n.progressiveEnabled&&(!i||i.progressiveRender)&&t.__idxInPipeline>n.blockIndex?n.step:null,modBy:null!=(t=i&&i.modDataCount)?Math.ceil(t/e):null,modDataCount:t}},zg.prototype.getPipeline=function(t){return this._pipelineMap.get(t)},zg.prototype.updateStreamModes=function(t,e){var n=this._pipelineMap.get(t.uid),i=t.getData().count(),e=n.progressiveEnabled&&e.incrementalPrepareRender&&i>=n.threshold,r=t.get("large")&&i>=t.get("largeThreshold"),i="mod"===t.get("progressiveChunkMode")?i:null;t.pipelineContext=n.context={progressiveRender:e,modDataCount:i,large:r}},zg.prototype.restorePipelines=function(t){var i=this,r=i._pipelineMap=E();t.eachSeries(function(t){var e=t.getProgressive(),n=t.uid;r.set(n,{id:n,head:null,tail:null,threshold:t.getProgressiveThreshold(),progressiveEnabled:e&&!(t.preventIncremental&&t.preventIncremental()),blockIndex:-1,step:Math.round(e||700),count:0}),i._pipe(t,t.dataTask)})},zg.prototype.prepareStageTasks=function(){var n=this._stageTaskMap,i=this.api.getModel(),r=this.api;O(this._allHandlers,function(t){var e=n.get(t.uid)||n.set(t.uid,{});kt(!(t.reset&&t.overallReset),""),t.reset&&this._createSeriesStageTask(t,e,i,r),t.overallReset&&this._createOverallStageTask(t,e,i,r)},this)},zg.prototype.prepareView=function(t,e,n,i){var r=t.renderTask,o=r.context;o.model=e,o.ecModel=n,o.api=i,r.__block=!t.incrementalPrepareRender,this._pipe(e,r)},zg.prototype.performDataProcessorTasks=function(t,e){this._performStageTasks(this._dataProcessorHandlers,t,e,{block:!0})},zg.prototype.performVisualTasks=function(t,e,n){this._performStageTasks(this._visualHandlers,t,e,n)},zg.prototype._performStageTasks=function(t,s,l,u){u=u||{};var h=!1,c=this;function p(t,e){return t.setDirty&&(!t.dirtyMap||t.dirtyMap.get(e.__pipeline.id))}O(t,function(i,t){var e,n,r,o,a;u.visualType&&u.visualType!==i.visualType||(e=(n=c._stageTaskMap.get(i.uid)).seriesTaskMap,(n=n.overallTask)?((o=n.agentStubMap).each(function(t){p(u,t)&&(t.dirty(),r=!0)}),r&&n.dirty(),c.updatePayload(n,l),a=c.getPerformArgs(n,u.block),o.each(function(t){t.perform(a)}),n.perform(a)&&(h=!0)):e&&e.each(function(t,e){p(u,t)&&t.dirty();var n=c.getPerformArgs(t,u.block);n.skip=!i.performRawSeries&&s.isSeriesFiltered(t.context.model),c.updatePayload(t,l),t.perform(n)&&(h=!0)}))}),this.unfinished=h||this.unfinished},zg.prototype.performSeriesTasks=function(t){var e;t.eachSeries(function(t){e=t.dataTask.perform()||e}),this.unfinished=e||this.unfinished},zg.prototype.plan=function(){this._pipelineMap.each(function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}}while(e=e.getUpstream())})},zg.prototype.updatePayload=function(t,e){"remain"!==e&&(t.context.payload=e)},zg.prototype._createSeriesStageTask=function(n,t,i,r){var o=this,a=t.seriesTaskMap,s=t.seriesTaskMap=E(),t=n.seriesType,e=n.getTargetSeries;function l(t){var e=t.uid,e=s.set(e,a&&a.get(e)||cf({plan:Gg,reset:Ug,count:Yg}));e.context={model:t,ecModel:i,api:r,useClearVisual:n.isVisual&&!n.isLayout,plan:n.plan,reset:n.reset,scheduler:o},o._pipe(t,e)}n.createOnAllSeries?i.eachRawSeries(l):t?i.eachRawSeriesByType(t,l):e&&e(i,r).each(l)},zg.prototype._createOverallStageTask=function(t,e,n,i){var r=this,o=e.overallTask=e.overallTask||cf({reset:Fg}),a=(o.context={ecModel:n,api:i,overallReset:t.overallReset,scheduler:r},o.agentStubMap),s=o.agentStubMap=E(),e=t.seriesType,l=t.getTargetSeries,u=!0,h=!1;function c(t){var e=t.uid,e=s.set(e,a&&a.get(e)||(h=!0,cf({reset:Vg,onDirty:Wg})));e.context={model:t,overallProgress:u},e.agent=o,e.__block=u,r._pipe(t,e)}kt(!t.createOnAllSeries,""),e?n.eachRawSeriesByType(e,c):l?l(n,i).each(c):(u=!1,O(n.getSeries(),c)),h&&o.dirty()},zg.prototype._pipe=function(t,e){t=t.uid,t=this._pipelineMap.get(t);t.head||(t.head=e),t.tail&&t.tail.pipe(e),(t.tail=e).__idxInPipeline=t.count++,e.__pipeline=t},zg.wrapStageHandler=function(t,e){return(t=I(t)?{overallReset:t,seriesType:function(t){jg=null;try{t(Zg,Kg)}catch(t){}return jg}(t)}:t).uid=Lc("stageHandler"),e&&(t.visualType=e),t};var Bg=zg;function zg(t,e,n,i){this._stageTaskMap=E(),this.ecInstance=t,this.api=e,n=this._dataProcessorHandlers=n.slice(),i=this._visualHandlers=i.slice(),this._allHandlers=n.concat(i)}function Fg(t){t.overallReset(t.ecModel,t.api,t.payload)}function Vg(t){return t.overallProgress&&Hg}function Hg(){this.agent.dirty(),this.getDownstream().dirty()}function Wg(){this.agent&&this.agent.dirty()}function Gg(t){return t.plan?t.plan(t.model,t.ecModel,t.api,t.payload):null}function Ug(t){t.useClearVisual&&t.data.clearAllVisual();t=t.resetDefines=vo(t.reset(t.model,t.ecModel,t.api,t.payload));return 1<t.length?z(t,function(t,e){return Xg(e)}):qg}var qg=Xg(0);function Xg(o){return function(t,e){var n=e.data,i=e.resetDefines[o];if(i&&i.dataEach)for(var r=t.start;r<t.end;r++)i.dataEach(n,r);else i&&i.progress&&i.progress(t,n)}}function Yg(t){return t.data.count()}var jg,Zg={},Kg={};function $g(t,e){for(var n in e.prototype)t[n]=Ht}$g(Zg,id),$g(Kg,sd),Zg.eachSeriesByType=Zg.eachRawSeriesByType=function(t){jg=t},Zg.eachComponent=function(t){"series"===t.mainType&&t.subType&&(jg=t.subType)};function Qg(){return{axisLine:{lineStyle:{color:Jg}},splitLine:{lineStyle:{color:"#484753"}},splitArea:{areaStyle:{color:["rgba(255,255,255,0.02)","rgba(255,255,255,0.05)"]}},minorSplitLine:{lineStyle:{color:"#20203B"}}}}var h=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"],Mc={color:h,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],h]},Jg="#B9B8CE",bc="#100C2A",_c=["#4992ff","#7cffb2","#fddd60","#ff6e76","#58d9f9","#05c091","#ff8a45","#8d48e3","#dd79ff"],h={darkMode:!0,color:_c,backgroundColor:bc,axisPointer:{lineStyle:{color:"#817f91"},crossStyle:{color:"#817f91"},label:{color:"#fff"}},legend:{textStyle:{color:Jg}},textStyle:{color:Jg},title:{textStyle:{color:"#EEF1FA"},subtextStyle:{color:"#B9B8CE"}},toolbox:{iconStyle:{borderColor:Jg}},dataZoom:{borderColor:"#71708A",textStyle:{color:Jg},brushStyle:{color:"rgba(135,163,206,0.3)"},handleStyle:{color:"#353450",borderColor:"#C5CBE3"},moveHandleStyle:{color:"#B0B6C3",opacity:.3},fillerColor:"rgba(135,163,206,0.2)",emphasis:{handleStyle:{borderColor:"#91B7F2",color:"#4D587D"},moveHandleStyle:{color:"#636D9A",opacity:.7}},dataBackground:{lineStyle:{color:"#71708A",width:1},areaStyle:{color:"#71708A"}},selectedDataBackground:{lineStyle:{color:"#87A3CE"},areaStyle:{color:"#87A3CE"}}},visualMap:{textStyle:{color:Jg}},timeline:{lineStyle:{color:Jg},label:{color:Jg},controlStyle:{color:Jg,borderColor:Jg}},calendar:{itemStyle:{color:bc},dayLabel:{color:Jg},monthLabel:{color:Jg},yearLabel:{color:Jg}},timeAxis:Qg(),logAxis:Qg(),valueAxis:Qg(),categoryAxis:Qg(),line:{symbol:"circle"},graph:{color:_c},gauge:{title:{color:Jg},axisLine:{lineStyle:{color:[[1,"rgba(207,212,219,0.2)"]]}},axisLabel:{color:Jg},detail:{color:"#EEF1FA"}},candlestick:{itemStyle:{color:"#f64e56",color0:"#54ea92",borderColor:"#f64e56",borderColor0:"#54ea92"}}},ty=(h.categoryAxis.splitLine.show=!1,ey.prototype.normalizeQuery=function(t){var e,a,s,l={},u={},h={};return V(t)?(e=Vo(t),l.mainType=e.main||null,l.subType=e.sub||null):(a=["Index","Name","Id"],s={name:1,dataIndex:1,dataType:1},O(t,function(t,e){for(var n=!1,i=0;i<a.length;i++){var r=a[i],o=e.lastIndexOf(r);0<o&&o===e.length-r.length&&"data"!==(o=e.slice(0,o))&&(l.mainType=o,l[r.toLowerCase()]=t,n=!0)}s.hasOwnProperty(e)&&(u[e]=t,n=!0),n||(h[e]=t)})),{cptQuery:l,dataQuery:u,otherQuery:h}},ey.prototype.filter=function(t,e){var n,i,r,o,a,s=this.eventInfo;return!s||(n=s.targetEl,i=s.packedEvent,r=s.model,s=s.view,!r)||!s||(o=e.cptQuery,a=e.dataQuery,l(o,r,"mainType")&&l(o,r,"subType")&&l(o,r,"index","componentIndex")&&l(o,r,"name")&&l(o,r,"id")&&l(a,i,"name")&&l(a,i,"dataIndex")&&l(a,i,"dataType")&&(!s.filterForExposedEvent||s.filterForExposedEvent(t,e.otherQuery,n,i)));function l(t,e,n,i){return null==t[n]||e[i||n]===t[n]}},ey.prototype.afterTrigger=function(){this.eventInfo=null},ey);function ey(){}var ny=["symbol","symbolSize","symbolRotate","symbolOffset"],iy=ny.concat(["symbolKeepAspect"]),bc={createOnAllSeries:!0,performRawSeries:!0,reset:function(a,t){var e=a.getData();if(a.legendIcon&&e.setVisual("legendIcon",a.legendIcon),a.hasSymbolVisual){for(var s,n={},l={},i=!1,r=0;r<ny.length;r++){var o=ny[r],u=a.get(o);I(u)?(i=!0,l[o]=u):n[o]=u}if(n.symbol=n.symbol||a.defaultSymbol,e.setVisual(P({legendIcon:a.legendIcon||n.symbol,symbolKeepAspect:a.get("symbolKeepAspect")},n)),!t.isSeriesFiltered(a))return s=ct(l),{dataEach:i?function(t,e){for(var n=a.getRawValue(e),i=a.getDataParams(e),r=0;r<s.length;r++){var o=s[r];t.setItemVisual(e,o,l[o](n,i))}}:null}}}},_c={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){if(t.hasSymbolVisual&&!e.isSeriesFiltered(t))return{dataEach:t.getData().hasItemOption?function(t,e){for(var n=t.getItemModel(e),i=0;i<iy.length;i++){var r=iy[i],o=n.getShallow(r,!0);null!=o&&t.setItemVisual(e,r,o)}}:null}}};function ry(t,e,s,n,l){var u=t+e;s.isSilent(u)||n.eachComponent({mainType:"series",subType:"pie"},function(t){for(var e,n,i=t.seriesIndex,r=t.option.selectedMap,o=l.selected,a=0;a<o.length;a++)o[a].seriesIndex===i&&(n=Do(e=t.getData(),l.fromActionPayload),s.trigger(u,{type:u,seriesId:t.id,name:F(n)?e.getName(n[0]):e.getName(n),selected:V(r)?r:P({},r)}))})}function oy(t,e,n){for(var i;t&&(!e(t)||(i=t,!n));)t=t.__hostTarget||t.parent;return i}var ay=Math.round(9*Math.random()),sy="function"==typeof Object.defineProperty,ly=(uy.prototype.get=function(t){return this._guard(t)[this._id]},uy.prototype.set=function(t,e){t=this._guard(t);return sy?Object.defineProperty(t,this._id,{value:e,enumerable:!1,configurable:!0}):t[this._id]=e,this},uy.prototype.delete=function(t){return!!this.has(t)&&(delete this._guard(t)[this._id],!0)},uy.prototype.has=function(t){return!!this._guard(t)[this._id]},uy.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},uy);function uy(){this._id="__ec_inner_"+ay++}var hy=cs.extend({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,e=e.height/2;t.moveTo(n,i-e),t.lineTo(n+r,i+e),t.lineTo(n-r,i+e),t.closePath()}}),cy=cs.extend({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,e=e.height/2;t.moveTo(n,i-e),t.lineTo(n+r,i),t.lineTo(n,i+e),t.lineTo(n-r,i),t.closePath()}}),py=cs.extend({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.x,i=e.y,r=e.width/5*3,e=Math.max(r,e.height),r=r/2,o=r*r/(e-r),e=i-e+r+o,a=Math.asin(o/r),s=Math.cos(a)*r,l=Math.sin(a),u=Math.cos(a),h=.6*r,c=.7*r;t.moveTo(n-s,e+o),t.arc(n,e,r,Math.PI-a,2*Math.PI+a),t.bezierCurveTo(n+s-l*h,e+o+u*h,n,i-c,n,i),t.bezierCurveTo(n,i-c,n-s+l*h,e+o+u*h,n-s,e+o),t.closePath()}}),dy=cs.extend({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.height,i=e.width,r=e.x,e=e.y,i=i/3*2;t.moveTo(r,e),t.lineTo(r+i,e+n),t.lineTo(r,e+n/4*3),t.lineTo(r-i,e+n),t.lineTo(r,e),t.closePath()}}),fy={line:function(t,e,n,i,r){r.x1=t,r.y1=e+i/2,r.x2=t+n,r.y2=e+i/2},rect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i},roundRect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i,r.r=Math.min(n,i)/4},square:function(t,e,n,i,r){n=Math.min(n,i);r.x=t,r.y=e,r.width=n,r.height=n},circle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.r=Math.min(n,i)/2},diamond:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i},pin:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},arrow:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},triangle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i}},gy={},yy=(O({line:Yu,rect:Is,roundRect:Is,square:Is,circle:lu,diamond:cy,pin:py,arrow:dy,triangle:hy},function(t,e){gy[e]=new t}),cs.extend({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(t,e,n){var t=Dr(t,e,n),i=this.shape;return i&&"pin"===i.symbolType&&"inside"===e.position&&(t.y=n.y+.4*n.height),t},buildPath:function(t,e,n){var i,r=e.symbolType;"none"!==r&&(i=(i=gy[r])||gy[r="rect"],fy[r](e.x,e.y,e.width,e.height,i.shape),i.buildPath(t,i.shape,n))}}));function my(t,e){var n;"image"!==this.type&&(n=this.style,this.__isEmptyBrush?(n.stroke=t,n.fill=e||"#fff",n.lineWidth=2):"line"===this.shape.symbolType?n.stroke=t:n.fill=t,this.markRedraw())}function vy(t,e,n,i,r,o,a){var s=0===t.indexOf("empty");return(a=0===(t=s?t.substr(5,1).toLowerCase()+t.substr(6):t).indexOf("image://")?Hh(t.slice(8),new q(e,n,i,r),a?"center":"cover"):0===t.indexOf("path://")?Vh(t.slice(7),{},new q(e,n,i,r),a?"center":"cover"):new yy({shape:{symbolType:t,x:e,y:n,width:i,height:r}})).__isEmptyBrush=s,a.setColor=my,o&&a.setColor(o),a}function _y(t){return isFinite(t)}function xy(t,e,n){for(var i,r,o,a,s,l,u,h,c,p="radial"===e.type?(i=t,r=e,a=(o=n).width,s=o.height,l=Math.min(a,s),u=null==r.x?.5:r.x,h=null==r.y?.5:r.y,c=null==r.r?.5:r.r,r.global||(u=u*a+o.x,h=h*s+o.y,c*=l),u=_y(u)?u:.5,h=_y(h)?h:.5,c=0<=c&&_y(c)?c:.5,i.createRadialGradient(u,h,0,u,h,c)):(r=t,a=n,o=null==(s=e).x?0:s.x,l=null==s.x2?1:s.x2,i=null==s.y?0:s.y,u=null==s.y2?0:s.y2,s.global||(o=o*a.width+a.x,l=l*a.width+a.x,i=i*a.height+a.y,u=u*a.height+a.y),o=_y(o)?o:0,l=_y(l)?l:1,i=_y(i)?i:0,u=_y(u)?u:0,r.createLinearGradient(o,i,l,u)),d=e.colorStops,f=0;f<d.length;f++)p.addColorStop(d[f].offset,d[f].color);return p}function wy(t){return parseInt(t,10)}function by(t,e,n){var i=["width","height"][e],r=["clientWidth","clientHeight"][e],o=["paddingLeft","paddingTop"][e],e=["paddingRight","paddingBottom"][e];return null!=n[i]&&"auto"!==n[i]?parseFloat(n[i]):(n=document.defaultView.getComputedStyle(t),(t[r]||wy(n[i])||wy(t.style[i]))-(wy(n[o])||0)-(wy(n[e])||0)|0)}function Sy(t){var e,n=t.style,i=n.lineDash&&0<n.lineWidth&&(r=n.lineDash,i=n.lineWidth,r&&"solid"!==r&&0<i?"dashed"===r?[4*i,2*i]:"dotted"===r?[i]:gt(r)?[r]:F(r)?r:null:null),r=n.lineDashOffset;return i&&(e=n.strokeNoScale&&t.getLineScale?t.getLineScale():1)&&1!==e&&(i=z(i,function(t){return t/e}),r/=e),[i,r]}var My=new Ya(!0);function Ty(t){var e=t.stroke;return!(null==e||"none"===e||!(0<t.lineWidth))}function Cy(t){return"string"==typeof t&&"none"!==t}function ky(t){t=t.fill;return null!=t&&"none"!==t}function Iy(t,e){var n;null!=e.fillOpacity&&1!==e.fillOpacity?(n=t.globalAlpha,t.globalAlpha=e.fillOpacity*e.opacity,t.fill(),t.globalAlpha=n):t.fill()}function Dy(t,e){var n;null!=e.strokeOpacity&&1!==e.strokeOpacity?(n=t.globalAlpha,t.globalAlpha=e.strokeOpacity*e.opacity,t.stroke(),t.globalAlpha=n):t.stroke()}function Ly(t,e,n){var n=Qo(e.image,e.__image,n);if(ta(n))return t=t.createPattern(n,e.repeat||"repeat"),"function"==typeof DOMMatrix&&t&&t.setTransform&&((n=new DOMMatrix).translateSelf(e.x||0,e.y||0),n.rotateSelf(0,0,(e.rotation||0)*Wt),n.scaleSelf(e.scaleX||1,e.scaleY||1),t.setTransform(n)),t}var Ay=["shadowBlur","shadowOffsetX","shadowOffsetY"],Py=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function Oy(t,e,n,i,r){var o,a=!1;if(!i&&e===(n=n||{}))return!1;!i&&e.opacity===n.opacity||(Vy(t,r),a=!0,o=Math.max(Math.min(e.opacity,1),0),t.globalAlpha=isNaN(o)?fa.opacity:o),!i&&e.blend===n.blend||(a||(Vy(t,r),a=!0),t.globalCompositeOperation=e.blend||fa.blend);for(var s=0;s<Ay.length;s++){var l=Ay[s];!i&&e[l]===n[l]||(a||(Vy(t,r),a=!0),t[l]=t.dpr*(e[l]||0))}return!i&&e.shadowColor===n.shadowColor||(a||(Vy(t,r),a=!0),t.shadowColor=e.shadowColor||fa.shadowColor),a}function Ry(t,e,n,i,r){var o=Hy(e,r.inHover),a=i?null:n&&Hy(n,r.inHover)||{};if(o!==a){var s=Oy(t,o,a,i,r);(i||o.fill!==a.fill)&&(s||(Vy(t,r),s=!0),Cy(o.fill))&&(t.fillStyle=o.fill),(i||o.stroke!==a.stroke)&&(s||(Vy(t,r),s=!0),Cy(o.stroke))&&(t.strokeStyle=o.stroke),!i&&o.opacity===a.opacity||(s||(Vy(t,r),s=!0),t.globalAlpha=null==o.opacity?1:o.opacity),e.hasStroke()&&(n=o.lineWidth/(o.strokeNoScale&&e.getLineScale?e.getLineScale():1),t.lineWidth!==n)&&(s||(Vy(t,r),s=!0),t.lineWidth=n);for(var l=0;l<Py.length;l++){var u=Py[l],h=u[0];!i&&o[h]===a[h]||(s||(Vy(t,r),s=!0),t[h]=o[h]||u[1])}}}function Ny(t,e){var e=e.transform,n=t.dpr||1;e?t.setTransform(n*e[0],n*e[1],n*e[2],n*e[3],n*e[4],n*e[5]):t.setTransform(n,0,0,n,0,0)}var Ey=1,By=2,zy=3,Fy=4;function Vy(t,e){e.batchFill&&t.fill(),e.batchStroke&&t.stroke(),e.batchFill="",e.batchStroke=""}function Hy(t,e){return e&&t.__hoverStyle||t.style}function Wy(t,e){Gy(t,e,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function Gy(t,e,n,E){var i=e.transform;if(e.shouldBePainted(n.viewWidth,n.viewHeight,!1,!1)){var r=e.__clipPaths,o=n.prevElClipPaths,a=!1,s=!1;if(!o||function(t,e){if(t!==e&&(t||e)){if(!t||!e||t.length!==e.length)return 1;for(var n=0;n<t.length;n++)if(t[n]!==e[n])return 1}}(r,o)){if(o&&o.length&&(Vy(t,n),t.restore(),s=a=!0,n.prevElClipPaths=null,n.allClipped=!1,n.prevEl=null),r&&r.length){Vy(t,n),t.save();for(var B=r,l=t,o=n,z=!1,F=0;F<B.length;F++){var V=B[F],z=z||V.isZeroArea();Ny(l,V),l.beginPath(),V.buildPath(l,V.shape),l.clip()}o.allClipped=z,a=!0}n.prevElClipPaths=r}if(n.allClipped)e.__isRendered=!1;else{e.beforeBrush&&e.beforeBrush(),e.innerBeforeBrush();var u,h,c,p,d,f,g,y,m,v,_,x,w,H,b,S,M,T,C,k,I,D,L,o=n.prevEl,A=(o||(s=a=!0),e instanceof cs&&e.autoBatch&&(r=e.style,A=ky(r),u=Ty(r),!(r.lineDash||!(+A^+u)||A&&"string"!=typeof r.fill||u&&"string"!=typeof r.stroke||r.strokePercent<1||r.strokeOpacity<1||r.fillOpacity<1))),a=(a||(u=i,r=o.transform,u&&r?u[0]!==r[0]||u[1]!==r[1]||u[2]!==r[2]||u[3]!==r[3]||u[4]!==r[4]||u[5]!==r[5]:u||r)?(Vy(t,n),Ny(t,e)):A||Vy(t,n),Hy(e,n.inHover));if(e instanceof cs)n.lastDrawType!==Ey&&(s=!0,n.lastDrawType=Ey),Ry(t,e,o,s,n),A&&(n.batchFill||n.batchStroke)||t.beginPath(),i=t,r=e,R=A,b=Ty(p=a),S=ky(p),M=p.strokePercent,T=M<1,C=!r.path,r.silent&&!T||!C||r.createPathProxy(),k=r.path||My,I=r.__dirty,R||(d=p.fill,L=p.stroke,f=S&&!!d.colorStops,g=b&&!!L.colorStops,y=S&&!!d.image,m=b&&!!L.image,D=w=x=_=v=void 0,(f||g)&&(D=r.getBoundingRect()),f&&(v=I?xy(i,d,D):r.__canvasFillGradient,r.__canvasFillGradient=v),g&&(_=I?xy(i,L,D):r.__canvasStrokeGradient,r.__canvasStrokeGradient=_),y&&(x=I||!r.__canvasFillPattern?Ly(i,d,r):r.__canvasFillPattern,r.__canvasFillPattern=x),m&&(w=I||!r.__canvasStrokePattern?Ly(i,L,r):r.__canvasStrokePattern,r.__canvasStrokePattern=x),f?i.fillStyle=v:y&&(x?i.fillStyle=x:S=!1),g?i.strokeStyle=_:m&&(w?i.strokeStyle=w:b=!1)),D=r.getGlobalScale(),k.setScale(D[0],D[1],r.segmentIgnoreThreshold),i.setLineDash&&p.lineDash&&(H=(d=Sy(r))[0],O=d[1]),L=!0,(C||I&xn)&&(k.setDPR(i.dpr),T?k.setContext(null):(k.setContext(i),L=!1),k.reset(),r.buildPath(k,r.shape,R),k.toStatic(),r.pathUpdated()),L&&k.rebuildPath(i,T?M:1),H&&(i.setLineDash(H),i.lineDashOffset=O),R||(p.strokeFirst?(b&&Dy(i,p),S&&Iy(i,p)):(S&&Iy(i,p),b&&Dy(i,p))),H&&i.setLineDash([]),A&&(n.batchFill=a.fill||"",n.batchStroke=a.stroke||"");else if(e instanceof fs)n.lastDrawType!==zy&&(s=!0,n.lastDrawType=zy),Ry(t,e,o,s,n),f=t,v=e,null!=(x=(y=a).text)&&(x+=""),x&&(f.font=y.font||Z,f.textAlign=y.textAlign,f.textBaseline=y.textBaseline,_=g=void 0,f.setLineDash&&y.lineDash&&(g=(v=Sy(v))[0],_=v[1]),g&&(f.setLineDash(g),f.lineDashOffset=_),y.strokeFirst?(Ty(y)&&f.strokeText(x,y.x,y.y),ky(y)&&f.fillText(x,y.x,y.y)):(ky(y)&&f.fillText(x,y.x,y.y),Ty(y)&&f.strokeText(x,y.x,y.y)),g)&&f.setLineDash([]);else if(e instanceof _s)n.lastDrawType!==By&&(s=!0,n.lastDrawType=By),m=o,w=s,Oy(t,Hy(e,(D=n).inHover),m&&Hy(m,D.inHover),w,D),d=t,C=a,(r=(I=e).__image=Qo(C.image,I.__image,I,I.onload))&&ta(r)&&(L=C.x||0,k=C.y||0,T=I.getWidth(),I=I.getHeight(),M=r.width/r.height,null==T&&null!=I?T=I*M:null==I&&null!=T?I=T/M:null==T&&null==I&&(T=r.width,I=r.height),C.sWidth&&C.sHeight?(h=C.sx||0,c=C.sy||0,d.drawImage(r,h,c,C.sWidth,C.sHeight,L,k,T,I)):C.sx&&C.sy?(h=C.sx,c=C.sy,d.drawImage(r,h,c,T-h,I-c,L,k,T,I)):d.drawImage(r,L,k,T,I));else if(e.getTemporalDisplayables){n.lastDrawType!==Fy&&(s=!0,n.lastDrawType=Fy);var P,W,G=t,O=e,R=n,U=O.getDisplayables(),q=O.getTemporalDisplayables(),X=(G.save(),{prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:R.viewWidth,viewHeight:R.viewHeight,inHover:R.inHover});for(P=O.getCursor(),W=U.length;P<W;P++)(N=U[P]).beforeBrush&&N.beforeBrush(),N.innerBeforeBrush(),Gy(G,N,X,P===W-1),N.innerAfterBrush(),N.afterBrush&&N.afterBrush(),X.prevEl=N;for(var N,Y=0,j=q.length;Y<j;Y++)(N=q[Y]).beforeBrush&&N.beforeBrush(),N.innerBeforeBrush(),Gy(G,N,X,Y===j-1),N.innerAfterBrush(),N.afterBrush&&N.afterBrush(),X.prevEl=N;O.clearTemporalDisplayables(),O.notClear=!0,G.restore()}A&&E&&Vy(t,n),e.innerAfterBrush(),e.afterBrush&&e.afterBrush(),(n.prevEl=e).__dirty=0,e.__isRendered=!0}}else e.__dirty&=~_n,e.__isRendered=!1}var Uy=new ly,qy=new ni(100),Xy=["symbol","symbolSize","symbolKeepAspect","color","backgroundColor","dashArrayX","dashArrayY","maxTileWidth","maxTileHeight"];function Yy(t,e){if("none"===t)return null;var a=e.getDevicePixelRatio(),s=e.getZr(),l="svg"===s.painter.type,e=(t.dirty&&Uy.delete(t),Uy.get(t));if(e)return e;for(var n,u=B(t,{symbol:"rect",symbolSize:1,symbolKeepAspect:!0,color:"rgba(0, 0, 0, 0.2)",backgroundColor:null,dashArrayX:5,dashArrayY:5,rotation:0,maxTileWidth:512,maxTileHeight:512}),e=("none"===u.backgroundColor&&(u.backgroundColor=null),{repeat:"repeat"}),i=e,r=[a],o=!0,h=0;h<Xy.length;++h){var c=u[Xy[h]];if(null!=c&&!F(c)&&!V(c)&&!gt(c)&&"boolean"!=typeof c){o=!1;break}r.push(c)}o&&(n=r.join(",")+(l?"-svg":""),v=qy.get(n))&&(l?i.svgElement=v:i.image=v);var p,d=function t(e){if(!e||0===e.length)return[[0,0]];if(gt(e))return[[o=Math.ceil(e),o]];var n=!0;for(var i=0;i<e.length;++i)if(!gt(e[i])){n=!1;break}if(n)return t([e]);var r=[];for(i=0;i<e.length;++i){var o;gt(e[i])?(o=Math.ceil(e[i]),r.push([o,o])):(o=z(e[i],function(t){return Math.ceil(t)})).length%2==1?r.push(o.concat(o)):r.push(o)}return r}(u.dashArrayX),f=function(t){if(!t||"object"==typeof t&&0===t.length)return[0,0];if(gt(t))return[e=Math.ceil(t),e];var e=z(t,function(t){return Math.ceil(t)});return t.length%2?e.concat(e):e}(u.dashArrayY),g=function t(e){if(!e||0===e.length)return[["rect"]];if(V(e))return[[e]];var n=!0;for(var i=0;i<e.length;++i)if(!V(e[i])){n=!1;break}if(n)return t([e]);var r=[];for(i=0;i<e.length;++i)V(e[i])?r.push([e[i]]):r.push(e[i]);return r}(u.symbol),y=function(t){return z(t,jy)}(d),m=jy(f),v=!l&&H.createCanvas(),_=l&&{tag:"g",attrs:{},key:"dcl",children:[]},x=function(){for(var t=1,e=0,n=y.length;e<n;++e)t=go(t,y[e]);for(var i=1,e=0,n=g.length;e<n;++e)i=go(i,g[e].length);t*=i;var r=m*y.length*g.length;return{width:Math.max(1,Math.min(t,u.maxTileWidth)),height:Math.max(1,Math.min(r,u.maxTileHeight))}}();v&&(v.width=x.width*a,v.height=x.height*a,p=v.getContext("2d")),p&&(p.clearRect(0,0,v.width,v.height),u.backgroundColor)&&(p.fillStyle=u.backgroundColor,p.fillRect(0,0,v.width,v.height));for(var w=0,b=0;b<f.length;++b)w+=f[b];if(!(w<=0))for(var S=-m,M=0,T=0,C=0;S<x.height;){if(M%2==0){for(var k=T/2%g.length,I=0,D=0,L=0;I<2*x.width;){for(var A,P,O,R,N,E=0,b=0;b<d[C].length;++b)E+=d[C][b];if(E<=0)break;D%2==0&&(P=.5*(1-u.symbolSize),A=I+d[C][D]*P,P=S+f[M]*P,O=d[C][D]*u.symbolSize,R=f[M]*u.symbolSize,N=L/2%g[k].length,function(t,e,n,i,r){var o=l?1:a,r=vy(r,t*o,e*o,n*o,i*o,u.color,u.symbolKeepAspect);l?(t=s.painter.renderOneToVNode(r))&&_.children.push(t):Wy(p,r)}(A,P,O,R,g[k][N])),I+=d[C][D],++L,++D===d[C].length&&(D=0)}++C===d.length&&(C=0)}S+=f[M],++T,++M===f.length&&(M=0)}return o&&qy.put(n,v||_),i.image=v,i.svgElement=_,i.svgWidth=x.width,i.svgHeight=x.height,e.rotation=u.rotation,e.scaleX=e.scaleY=l?1:1/a,Uy.set(t,e),t.dirty=!1,e}function jy(t){for(var e=0,n=0;n<t.length;++n)e+=t[n];return t.length%2==1?2*e:e}var Zy=new he,Ky={};var cy={PROCESSOR:{FILTER:1e3,SERIES_FILTER:800,STATISTIC:5e3},VISUAL:{LAYOUT:1e3,PROGRESSIVE_LAYOUT:1100,GLOBAL:2e3,CHART:3e3,POST_CHART_LAYOUT:4600,COMPONENT:4e3,BRUSH:5e3,CHART_ITEM:4500,ARIA:6e3,DECAL:7e3}},$y="__flagInMainProcess",Qy="__pendingUpdate",Jy="__needsUpdateStatus",t0=/^[a-zA-Z0-9_]+$/,e0="__connectUpdateStatus";function n0(n){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(!this.isDisposed())return r0(this,n,t);this.id}}function i0(n){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return r0(this,n,t)}}function r0(t,e,n){return n[0]=n[0]&&n[0].toLowerCase(),he.prototype[e].apply(t,n)}a(s0,o0=he);var o0,a0=s0;function s0(){return null!==o0&&o0.apply(this,arguments)||this}var l0,u0,h0,c0,p0,d0,f0,g0,y0,m0,v0,_0,x0,w0,b0,S0,M0,T0,C0,py=a0.prototype,k0=(py.on=i0("on"),py.off=i0("off"),a(v,C0=he),v.prototype._onframe=function(){if(!this._disposed){T0(this);var t=this._scheduler;if(this[Qy]){var e=this[Qy].silent;this[$y]=!0;try{l0(this),c0.update.call(this,null,this[Qy].updateParams)}catch(t){throw this[$y]=!1,this[Qy]=null,t}this._zr.flush(),this[$y]=!1,this[Qy]=null,g0.call(this,e),y0.call(this,e)}else if(t.unfinished){var n=1,i=this._model,r=this._api;t.unfinished=!1;do{var o=+new Date}while(t.performSeriesTasks(i),t.performDataProcessorTasks(i),d0(this,i),t.performVisualTasks(i),w0(this,this._model,r,"remain",{}),0<(n-=+new Date-o)&&t.unfinished);t.unfinished||this._zr.flush()}}},v.prototype.getDom=function(){return this._dom},v.prototype.getId=function(){return this.id},v.prototype.getZr=function(){return this._zr},v.prototype.isSSR=function(){return this._ssr},v.prototype.setOption=function(t,e,n){if(!this[$y])if(this._disposed)this.id;else{R(e)&&(n=e.lazyUpdate,i=e.silent,r=e.replaceMerge,o=e.transition,e=e.notMerge),this[$y]=!0,this._model&&!e||(e=new dd(this._api),a=this._theme,(s=this._model=new id).scheduler=this._scheduler,s.ssr=this._ssr,s.init(null,null,null,a,this._locale,e)),this._model.setOption(t,{replaceMerge:r},B0);var i,r,o,a,s={seriesTransition:o,optionChanged:!0};if(n)this[Qy]={silent:i,updateParams:s},this[$y]=!1,this.getZr().wakeUp();else{try{l0(this),c0.update.call(this,null,s)}catch(t){throw this[Qy]=null,this[$y]=!1,t}this._ssr||this._zr.flush(),this[Qy]=null,this[$y]=!1,g0.call(this,i),y0.call(this,i)}}},v.prototype.setTheme=function(){},v.prototype.getModel=function(){return this._model},v.prototype.getOption=function(){return this._model&&this._model.getOption()},v.prototype.getWidth=function(){return this._zr.getWidth()},v.prototype.getHeight=function(){return this._zr.getHeight()},v.prototype.getDevicePixelRatio=function(){return this._zr.painter.dpr||p.hasGlobalWindow&&window.devicePixelRatio||1},v.prototype.getRenderedCanvas=function(t){return this.renderToCanvas(t)},v.prototype.renderToCanvas=function(t){return this._zr.painter.getRenderedCanvas({backgroundColor:(t=t||{}).backgroundColor||this._model.get("backgroundColor"),pixelRatio:t.pixelRatio||this.getDevicePixelRatio()})},v.prototype.renderToSVGString=function(t){return this._zr.painter.renderToString({useViewBox:(t=t||{}).useViewBox})},v.prototype.getSvgDataURL=function(){var t;if(p.svgSupported)return O((t=this._zr).storage.getDisplayList(),function(t){t.stopAnimation(null,!0)}),t.painter.toDataURL()},v.prototype.getDataURL=function(t){var e,n,i,r;if(!this._disposed)return r=(t=t||{}).excludeComponents,e=this._model,n=[],i=this,O(r,function(t){e.eachComponent({mainType:t},function(t){t=i._componentsMap[t.__viewId];t.group.ignore||(n.push(t),t.group.ignore=!0)})}),r="svg"===this._zr.painter.getType()?this.getSvgDataURL():this.renderToCanvas(t).toDataURL("image/"+(t&&t.type||"png")),O(n,function(t){t.group.ignore=!1}),r;this.id},v.prototype.getConnectedDataURL=function(i){var r,o,a,s,l,u,h,c,p,e,t,n,d,f,g;if(!this._disposed)return r="svg"===i.type,o=this.group,a=Math.min,s=Math.max,W0[o]?(u=l=1/0,c=h=-1/0,p=[],e=i&&i.pixelRatio||this.getDevicePixelRatio(),O(H0,function(t,e){var n;t.group===o&&(n=r?t.getZr().painter.getSvgDom().innerHTML:t.renderToCanvas(b(i)),t=t.getDom().getBoundingClientRect(),l=a(t.left,l),u=a(t.top,u),h=s(t.right,h),c=s(t.bottom,c),p.push({dom:n,left:t.left,top:t.top}))}),t=(h*=e)-(l*=e),n=(c*=e)-(u*=e),d=H.createCanvas(),(f=jr(d,{renderer:r?"svg":"canvas"})).resize({width:t,height:n}),r?(g="",O(p,function(t){var e=t.left-l,n=t.top-u;g+='<g transform="translate('+e+","+n+')">'+t.dom+"</g>"}),f.painter.getSvgRoot().innerHTML=g,i.connectedBackgroundColor&&f.painter.setBackgroundColor(i.connectedBackgroundColor),f.refreshImmediately(),f.painter.toDataURL()):(i.connectedBackgroundColor&&f.add(new Is({shape:{x:0,y:0,width:t,height:n},style:{fill:i.connectedBackgroundColor}})),O(p,function(t){t=new _s({style:{x:t.left*e-l,y:t.top*e-u,image:t.dom}});f.add(t)}),f.refreshImmediately(),d.toDataURL("image/"+(i&&i.type||"png")))):this.getDataURL(i);this.id},v.prototype.convertToPixel=function(t,e){return p0(this,"convertToPixel",t,e)},v.prototype.convertFromPixel=function(t,e){return p0(this,"convertFromPixel",t,e)},v.prototype.containPixel=function(t,i){var r;if(!this._disposed)return O(Po(this._model,t),function(t,n){0<=n.indexOf("Models")&&O(t,function(t){var e=t.coordinateSystem;e&&e.containPoint?r=r||!!e.containPoint(i):"seriesModels"===n&&(e=this._chartsMap[t.__viewId])&&e.containPoint&&(r=r||e.containPoint(i,t))},this)},this),!!r;this.id},v.prototype.getVisual=function(t,e){var t=Po(this._model,t,{defaultMainType:"series"}),n=t.seriesModel.getData(),t=t.hasOwnProperty("dataIndexInside")?t.dataIndexInside:t.hasOwnProperty("dataIndex")?n.indexOfRawIndex(t.dataIndex):null;if(null!=t){var i=n,r=t,o=e;switch(o){case"color":return i.getItemVisual(r,"style")[i.getVisual("drawType")];case"opacity":return i.getItemVisual(r,"style").opacity;case"symbol":case"symbolSize":case"liftZ":return i.getItemVisual(r,o)}}else{var a=n,s=e;switch(s){case"color":return a.getVisual("style")[a.getVisual("drawType")];case"opacity":return a.getVisual("style").opacity;case"symbol":case"symbolSize":case"liftZ":return a.getVisual(s)}}},v.prototype.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},v.prototype.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]},v.prototype._initEvents=function(){var t,n,i,s=this;O(O0,function(a){function t(t){var n,e,i,r=s.getModel(),o=t.target;"globalout"===a?n={}:o&&oy(o,function(t){var e,t=qs(t);return t&&null!=t.dataIndex?(e=t.dataModel||r.getSeriesByIndex(t.seriesIndex),n=e&&e.getDataParams(t.dataIndex,t.dataType,o)||{},1):t.eventData&&(n=P({},t.eventData),1)},!0),n&&(e=n.componentType,i=n.componentIndex,"markLine"!==e&&"markPoint"!==e&&"markArea"!==e||(e="series",i=n.seriesIndex),i=(e=e&&null!=i&&r.getComponent(e,i))&&s["series"===e.mainType?"_chartsMap":"_componentsMap"][e.__viewId],n.event=t,n.type=a,s._$eventProcessor.eventInfo={targetEl:o,packedEvent:n,model:e,view:i},s.trigger(a,n))}t.zrEventfulCallAtLast=!0,s._zr.on(a,t,s)}),O(N0,function(t,e){s._messageCenter.on(e,function(t){this.trigger(e,t)},s)}),O(["selectchanged"],function(e){s._messageCenter.on(e,function(t){this.trigger(e,t)},s)}),t=this._messageCenter,i=(n=this)._api,t.on("selectchanged",function(t){var e=i.getModel();t.isFromClick?(ry("map","selectchanged",n,e,t),ry("pie","selectchanged",n,e,t)):"select"===t.fromAction?(ry("map","selected",n,e,t),ry("pie","selected",n,e,t)):"unselect"===t.fromAction&&(ry("map","unselected",n,e,t),ry("pie","unselected",n,e,t))})},v.prototype.isDisposed=function(){return this._disposed},v.prototype.clear=function(){this._disposed?this.id:this.setOption({series:[]},!0)},v.prototype.dispose=function(){var t,e,n;this._disposed?this.id:(this._disposed=!0,this.getDom()&&Eo(this.getDom(),q0,""),e=(t=this)._api,n=t._model,O(t._componentsViews,function(t){t.dispose(n,e)}),O(t._chartsViews,function(t){t.dispose(n,e)}),t._zr.dispose(),t._dom=t._model=t._chartsMap=t._componentsMap=t._chartsViews=t._componentsViews=t._scheduler=t._api=t._zr=t._throttledZrFlush=t._theme=t._coordSysMgr=t._messageCenter=null,delete H0[t.id])},v.prototype.resize=function(t){if(!this[$y])if(this._disposed)this.id;else{this._zr.resize(t);var e=this._model;if(this._loadingFX&&this._loadingFX.resize(),e){var e=e.resetOption("media"),n=t&&t.silent;this[Qy]&&(null==n&&(n=this[Qy].silent),e=!0,this[Qy]=null),this[$y]=!0;try{e&&l0(this),c0.update.call(this,{type:"resize",animation:P({duration:0},t&&t.animation)})}catch(t){throw this[$y]=!1,t}this[$y]=!1,g0.call(this,n),y0.call(this,n)}}},v.prototype.showLoading=function(t,e){this._disposed?this.id:(R(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),V0[t]&&(t=V0[t](this._api,e),e=this._zr,this._loadingFX=t,e.add(t)))},v.prototype.hideLoading=function(){this._disposed?this.id:(this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null)},v.prototype.makeActionFromEvent=function(t){var e=P({},t);return e.type=N0[t.type],e},v.prototype.dispatchAction=function(t,e){var n;this._disposed?this.id:(R(e)||(e={silent:!!e}),R0[t.type]&&this._model&&(this[$y]?this._pendingActions.push(t):(n=e.silent,f0.call(this,t,n),(t=e.flush)?this._zr.flush():!1!==t&&p.browser.weChat&&this._throttledZrFlush(),g0.call(this,n),y0.call(this,n))))},v.prototype.updateLabelLayout=function(){Zy.trigger("series:layoutlabels",this._model,this._api,{updatedSeries:[]})},v.prototype.appendData=function(t){var e;this._disposed?this.id:(e=t.seriesIndex,this.getModel().getSeriesByIndex(e).appendData(t),this._scheduler.unfinished=!0,this.getZr().wakeUp())},v.internalField=(l0=function(t){var e=t._scheduler;e.restorePipelines(t._model),e.prepareStageTasks(),u0(t,!0),u0(t,!1),e.plan()},u0=function(t,r){for(var o=t._model,a=t._scheduler,s=r?t._componentsViews:t._chartsViews,l=r?t._componentsMap:t._chartsMap,u=t._zr,h=t._api,e=0;e<s.length;e++)s[e].__alive=!1;function n(t){var e,n=t.__requireNewView,i=(t.__requireNewView=!1,"_ec_"+t.id+"_"+t.type),n=!n&&l[i];n||(e=Vo(t.type),(n=new(r?mg.getClass(e.main,e.sub):bg.getClass(e.sub))).init(o,h),l[i]=n,s.push(n),u.add(n.group)),t.__viewId=n.__id=i,n.__alive=!0,n.__model=t,n.group.__ecComponentInfo={mainType:t.mainType,index:t.componentIndex},r||a.prepareView(n,t,o,h)}r?o.eachComponent(function(t,e){"series"!==t&&n(e)}):o.eachSeries(n);for(e=0;e<s.length;){var i=s[e];i.__alive?e++:(r||i.renderTask.dispose(),u.remove(i.group),i.dispose(o,h),s.splice(e,1),l[i.__id]===i&&delete l[i.__id],i.__id=i.group.__ecComponentInfo=null)}},h0=function(c,e,p,n,t){var i,d,r=c._model;function o(t){t&&t.__alive&&t[e]&&t[e](t.__model,r,c._api,p)}r.setUpdatePayload(p),n?((i={})[n+"Id"]=p[n+"Id"],i[n+"Index"]=p[n+"Index"],i[n+"Name"]=p[n+"Name"],i={mainType:n,query:i},t&&(i.subType=t),null!=(t=p.excludeSeriesId)&&(d=E(),O(vo(t),function(t){t=To(t,null);null!=t&&d.set(t,!0)})),r&&r.eachComponent(i,function(t){var e,n,i=d&&null!=d.get(t.id);if(!i)if(Vl(p))if(t instanceof ug){if(p.type===el&&!p.notBlur&&!t.get(["emphasis","disabled"])){var i=t,r=p,o=c._api,a=i.seriesIndex,s=i.getData(r.dataType);if(s){var r=(F(r=Do(s,r))?r[0]:r)||0,l=s.getItemGraphicEl(r);if(!l)for(var u=s.count(),h=0;!l&&h<u;)l=s.getItemGraphicEl(h++);l?Il(a,(r=qs(l)).focus,r.blurScope,o):(r=i.get(["emphasis","focus"]),i=i.get(["emphasis","blurScope"]),null!=r&&Il(a,r,i,o))}}}else{a=Ll(t.mainType,t.componentIndex,p.name,c._api),r=a.focusSelf,i=a.dispatchers;p.type===el&&r&&!p.notBlur&&Dl(t.mainType,t.componentIndex,c._api),i&&O(i,function(t){(p.type===el?wl:bl)(t)})}else Fl(p)&&t instanceof ug&&(o=t,i=p,c._api,Fl(i)&&(e=i.dataType,F(n=Do(o.getData(e),i))||(n=[n]),o[i.type===ol?"toggleSelect":i.type===il?"select":"unselect"](n,e)),Al(t),M0(c))},c),r&&r.eachComponent(i,function(t){d&&null!=d.get(t.id)||o(c["series"===n?"_chartsMap":"_componentsMap"][t.__viewId])},c)):O([].concat(c._componentsViews).concat(c._chartsViews),o)},c0={prepareAndUpdate:function(t){l0(this),c0.update.call(this,t,{optionChanged:null!=t.newOption})},update:function(t,e){var n=this._model,i=this._api,r=this._zr,o=this._coordSysMgr,a=this._scheduler;n&&(n.setUpdatePayload(t),a.restoreData(n,t),a.performSeriesTasks(n),o.create(n,i),a.performDataProcessorTasks(n,t),d0(this,n),o.update(n,i),I0(n),a.performVisualTasks(n,t),_0(this,n,i,t,e),o=n.get("backgroundColor")||"transparent",a=n.get("darkMode"),r.setBackgroundColor(o),null!=a&&"auto"!==a&&r.setDarkMode(a),Zy.trigger("afterupdate",n,i))},updateTransform:function(n){var i,r,o=this,a=this._model,s=this._api;a&&(a.setUpdatePayload(n),i=[],a.eachComponent(function(t,e){"series"!==t&&(t=o.getViewOfComponentModel(e))&&t.__alive&&(!t.updateTransform||(e=t.updateTransform(e,a,s,n))&&e.update)&&i.push(t)}),r=E(),a.eachSeries(function(t){var e=o._chartsMap[t.__viewId];(!e.updateTransform||(e=e.updateTransform(t,a,s,n))&&e.update)&&r.set(t.uid,1)}),I0(a),this._scheduler.performVisualTasks(a,n,{setDirty:!0,dirtyMap:r}),w0(this,a,s,n,{},r),Zy.trigger("afterupdate",a,s))},updateView:function(t){var e=this._model;e&&(e.setUpdatePayload(t),bg.markUpdateMethod(t,"updateView"),I0(e),this._scheduler.performVisualTasks(e,t,{setDirty:!0}),_0(this,e,this._api,t,{}),Zy.trigger("afterupdate",e,this._api))},updateVisual:function(n){var i=this,r=this._model;r&&(r.setUpdatePayload(n),r.eachSeries(function(t){t.getData().clearAllVisual()}),bg.markUpdateMethod(n,"updateVisual"),I0(r),this._scheduler.performVisualTasks(r,n,{visualType:"visual",setDirty:!0}),r.eachComponent(function(t,e){"series"!==t&&(t=i.getViewOfComponentModel(e))&&t.__alive&&t.updateVisual(e,r,i._api,n)}),r.eachSeries(function(t){i._chartsMap[t.__viewId].updateVisual(t,r,i._api,n)}),Zy.trigger("afterupdate",r,this._api))},updateLayout:function(t){c0.update.call(this,t)}},p0=function(t,e,n,i){if(t._disposed)t.id;else for(var r=t._model,o=t._coordSysMgr.getCoordinateSystems(),a=Po(r,n),s=0;s<o.length;s++){var l=o[s];if(l[e]&&null!=(l=l[e](r,a,i)))return l}},d0=function(t,e){var n=t._chartsMap,i=t._scheduler;e.eachSeries(function(t){i.updateStreamModes(t,n[t.__viewId])})},f0=function(i,t){var r,o,a=this,e=this.getModel(),n=i.type,s=i.escapeConnect,l=R0[n],u=l.actionInfo,h=(u.update||"update").split(":"),c=h.pop(),p=null!=h[0]&&Vo(h[0]),h=(this[$y]=!0,[i]),d=!1,f=(i.batch&&(d=!0,h=z(i.batch,function(t){return(t=B(P({},t),i)).batch=null,t})),[]),g=Fl(i),y=Vl(i);if(y&&kl(this._api),O(h,function(t){var e,n;(r=(r=l.action(t,a._model,a._api))||P({},t)).type=u.event||r.type,f.push(r),y?(e=(n=Oo(i)).queryOptionMap,n=n.mainTypeSpecified?e.keys()[0]:"series",h0(a,c,t,n),M0(a)):g?(h0(a,c,t,"series"),M0(a)):p&&h0(a,c,t,p.main,p.sub)}),"none"!==c&&!y&&!g&&!p)try{this[Qy]?(l0(this),c0.update.call(this,i),this[Qy]=null):c0[c].call(this,i)}catch(t){throw this[$y]=!1,t}r=d?{type:u.event||n,escapeConnect:s,batch:f}:f[0],this[$y]=!1,t||((h=this._messageCenter).trigger(r.type,r),g&&(d={type:"selectchanged",escapeConnect:s,selected:(o=[],e.eachSeries(function(n){O(n.getAllData(),function(t){t.data;var t=t.type,e=n.getSelectedDataIndices();0<e.length&&(e={dataIndex:e,seriesIndex:n.seriesIndex},null!=t&&(e.dataType=t),o.push(e))})}),o),isFromClick:i.isFromClick||!1,fromAction:i.type,fromActionPayload:i},h.trigger(d.type,d)))},g0=function(t){for(var e=this._pendingActions;e.length;){var n=e.shift();f0.call(this,n,t)}},y0=function(t){t||this.trigger("updated")},m0=function(e,n){e.on("rendered",function(t){n.trigger("rendered",t),!e.animation.isFinished()||n[Qy]||n._scheduler.unfinished||n._pendingActions.length||n.trigger("finished")})},v0=function(t,a){t.on("mouseover",function(t){var e,n,i,r,o=oy(t.target,zl);o&&(o=o,e=t,t=a._api,n=qs(o),i=(r=Ll(n.componentMainType,n.componentIndex,n.componentHighDownName,t)).dispatchers,r=r.focusSelf,i?(r&&Dl(n.componentMainType,n.componentIndex,t),O(i,function(t){return _l(t,e)})):(Il(n.seriesIndex,n.focus,n.blurScope,t),"self"===n.focus&&Dl(n.componentMainType,n.componentIndex,t),_l(o,e)),M0(a))}).on("mouseout",function(t){var e,n,i=oy(t.target,zl);i&&(i=i,e=t,kl(t=a._api),(n=Ll((n=qs(i)).componentMainType,n.componentIndex,n.componentHighDownName,t).dispatchers)?O(n,function(t){return xl(t,e)}):xl(i,e),M0(a))}).on("click",function(t){var e,t=oy(t.target,function(t){return null!=qs(t).dataIndex},!0);t&&(e=t.selected?"unselect":"select",t=qs(t),a._api.dispatchAction({type:e,dataType:t.dataType,dataIndexInside:t.dataIndex,seriesIndex:t.seriesIndex,isFromClick:!0}))})},_0=function(t,e,n,i,r){var o,a,s,l,u,h,c;u=[],c=!(h=[]),(o=e).eachComponent(function(t,e){var n=e.get("zlevel")||0,i=e.get("z")||0,r=e.getZLevelKey();c=c||!!r,("series"===t?h:u).push({zlevel:n,z:i,idx:e.componentIndex,type:t,key:r})}),c&&(vn(a=u.concat(h),function(t,e){return t.zlevel===e.zlevel?t.z-e.z:t.zlevel-e.zlevel}),O(a,function(t){var e=o.getComponent(t.type,t.idx),n=t.zlevel,t=t.key;null!=s&&(n=Math.max(s,n)),t?(n===s&&t!==l&&n++,l=t):l&&(n===s&&n++,l=""),s=n,e.setZLevel(n)})),x0(t,e,n,i,r),O(t._chartsViews,function(t){t.__alive=!1}),w0(t,e,n,i,r),O(t._chartsViews,function(t){t.__alive||t.remove(e,n)})},x0=function(t,n,i,r,e,o){O(o||t._componentsViews,function(t){var e=t.__model;A0(0,t),t.render(e,n,i,r),L0(e,t),P0(e,t)})},w0=function(r,t,e,o,n,a){var i,s,l,u,h=r._scheduler,c=(n=P(n||{},{updatedSeries:t.getSeries()}),Zy.trigger("series:beforeupdate",t,e,n),!1);t.eachSeries(function(t){var e,n=r._chartsMap[t.__viewId],i=(n.__alive=!0,n.renderTask);h.updatePayload(i,o),A0(0,n),a&&a.get(t.uid)&&i.dirty(),i.perform(h.getPerformArgs(i))&&(c=!0),n.group.silent=!!t.get("silent"),i=n,e=t.get("blendMode")||null,i.eachRendered(function(t){t.isGroup||(t.style.blend=e)}),Al(t)}),h.unfinished=c||h.unfinished,Zy.trigger("series:layoutlabels",t,e,n),Zy.trigger("series:transition",t,e,n),t.eachSeries(function(t){var e=r._chartsMap[t.__viewId];L0(t,e),P0(t,e)}),s=t,l=(i=r)._zr.storage,u=0,l.traverse(function(t){t.isGroup||u++}),u>s.get("hoverLayerThreshold")&&!p.node&&!p.worker&&s.eachSeries(function(t){t.preventUsingHoverLayer||(t=i._chartsMap[t.__viewId]).__alive&&t.eachRendered(function(t){t.states.emphasis&&(t.states.emphasis.hoverLayer=!0)})}),Zy.trigger("series:afterupdate",t,e,n)},M0=function(t){t[Jy]=!0,t.getZr().wakeUp()},T0=function(t){t[Jy]&&(t.getZr().storage.traverse(function(t){Ih(t)||D0(t)}),t[Jy]=!1)},b0=function(n){return a(t,e=sd),t.prototype.getCoordinateSystems=function(){return n._coordSysMgr.getCoordinateSystems()},t.prototype.getComponentByElement=function(t){for(;t;){var e=t.__ecComponentInfo;if(null!=e)return n._model.getComponent(e.mainType,e.index);t=t.parent}},t.prototype.enterEmphasis=function(t,e){wl(t,e),M0(n)},t.prototype.leaveEmphasis=function(t,e){bl(t,e),M0(n)},t.prototype.enterBlur=function(t){gl(t,hl),M0(n)},t.prototype.leaveBlur=function(t){Sl(t),M0(n)},t.prototype.enterSelect=function(t){Ml(t),M0(n)},t.prototype.leaveSelect=function(t){Tl(t),M0(n)},t.prototype.getModel=function(){return n.getModel()},t.prototype.getViewOfComponentModel=function(t){return n.getViewOfComponentModel(t)},t.prototype.getViewOfSeriesModel=function(t){return n.getViewOfSeriesModel(t)},new t(n);function t(){return null!==e&&e.apply(this,arguments)||this}var e},void(S0=function(i){function r(t,e){for(var n=0;n<t.length;n++)t[n][e0]=e}O(N0,function(t,e){i._messageCenter.on(e,function(t){var e,n;!W0[i.group]||0===i[e0]||t&&t.escapeConnect||(e=i.makeActionFromEvent(t),n=[],O(H0,function(t){t!==i&&t.group===i.group&&n.push(t)}),r(n,0),O(n,function(t){1!==t[e0]&&t.dispatchAction(e)}),r(n,2))})})})),v);function v(t,e,n){var i=C0.call(this,new ty)||this,t=(i._chartsViews=[],i._chartsMap={},i._componentsViews=[],i._componentsMap={},i._pendingActions=[],n=n||{},V(e)&&(e=F0[e]),i._dom=t,n.ssr&&Kr(function(t){var e,t=qs(t),n=t.dataIndex;if(null!=n)return(e=E()).set("series_index",t.seriesIndex),e.set("data_index",n),t.ssrType&&e.set("ssr_type",t.ssrType),e}),i._zr=jr(t,{renderer:n.renderer||"canvas",devicePixelRatio:n.devicePixelRatio,width:n.width,height:n.height,ssr:n.ssr,useDirtyRect:N(n.useDirtyRect,!1),useCoarsePointer:N(n.useCoarsePointer,"auto"),pointerSize:n.pointerSize})),n=(i._ssr=n.ssr,i._throttledZrFlush=Dg(pt(t.flush,t),17),(e=b(e))&&Od(e,!0),i._theme=e,i._locale=V(e=n.locale||Ec)?(n=Rc[e.toUpperCase()]||{},e===Ac||e===Pc?b(n):d(b(n),b(Rc[Oc]),!1)):d(b(e),b(Rc[Oc]),!1),i._coordSysMgr=new hd,i._api=b0(i));function r(t,e){return t.__prio-e.__prio}return vn(z0,r),vn(E0,r),i._scheduler=new Bg(i,n,E0,z0),i._messageCenter=new a0,i._initEvents(),i.resize=pt(i.resize,i),t.animation.on("frame",i._onframe,i),m0(t,i),v0(t,i),Lt(i),i}function I0(t){t.clearColorPalette(),t.eachSeries(function(t){t.clearColorPalette()})}function D0(t){for(var e=[],n=t.currentStates,i=0;i<n.length;i++){var r=n[i];"emphasis"!==r&&"blur"!==r&&"select"!==r&&e.push(r)}t.selected&&t.states.select&&e.push("select"),t.hoverState===Qs&&t.states.emphasis?e.push("emphasis"):t.hoverState===$s&&t.states.blur&&e.push("blur"),t.useStates(e)}function L0(t,e){var n,i;t.preventAutoZ||(n=t.get("z")||0,i=t.get("zlevel")||0,e.eachRendered(function(t){return function t(e,n,i,r){var o=e.getTextContent();var a=e.getTextGuideLine();var s=e.isGroup;if(s)for(var l=e.childrenRef(),u=0;u<l.length;u++)r=Math.max(t(l[u],n,i,r),r);else e.z=n,e.zlevel=i,r=Math.max(e.z2,r);o&&(o.z=n,o.zlevel=i,isFinite(r))&&(o.z2=r+2);a&&(s=e.textGuideLineConfig,a.z=n,a.zlevel=i,isFinite(r))&&(a.z2=r+(s&&s.showAbove?1:-1));return r}(t,n,i,-1/0),!0}))}function A0(t,e){e.eachRendered(function(t){var e,n;Ih(t)||(e=t.getTextContent(),n=t.getTextGuideLine(),t.stateTransition&&(t.stateTransition=null),e&&e.stateTransition&&(e.stateTransition=null),n&&n.stateTransition&&(n.stateTransition=null),t.hasState()?(t.prevStates=t.currentStates,t.clearStates()):t.prevStates&&(t.prevStates=null))})}function P0(t,e){var n=t.getModel("stateAnimation"),r=t.isAnimationEnabled(),t=n.get("duration"),o=0<t?{duration:t,delay:n.get("delay"),easing:n.get("easing")}:null;e.eachRendered(function(t){var e,n,i;t.states&&t.states.emphasis&&(Ih(t)||(t instanceof cs&&((i=js(n=t)).normalFill=n.style.fill,i.normalStroke=n.style.stroke,n=n.states.select||{},i.selectFill=n.style&&n.style.fill||null,i.selectStroke=n.style&&n.style.stroke||null),t.__dirty&&(i=t.prevStates)&&t.useStates(i),r&&(t.stateTransition=o,n=t.getTextContent(),e=t.getTextGuideLine(),n&&(n.stateTransition=o),e)&&(e.stateTransition=o),t.__dirty&&D0(t)))})}var dy=k0.prototype,O0=(dy.on=n0("on"),dy.off=n0("off"),dy.one=function(i,r,t){var o=this;this.on.call(this,i,function t(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];r&&r.apply&&r.apply(this,e),o.off(i,t)},t)},["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"]);var R0={},N0={},E0=[],B0=[],z0=[],F0={},V0={},H0={},W0={},G0=+new Date,U0=+new Date,q0="_echarts_instance_";function X0(t){W0[t]=!1}hy=X0;function Y0(t){return H0[e=q0,(t=t).getAttribute?t.getAttribute(e):t[e]];var e}function j0(t,e){F0[t]=e}function Z0(t){k(B0,t)<0&&B0.push(t)}function K0(t,e){om(E0,t,e,2e3)}function $0(t){J0("afterinit",t)}function Q0(t){J0("afterupdate",t)}function J0(t,e){Zy.on(t,e)}function tm(t,e,n){I(e)&&(n=e,e="");var i=R(t)?t.type:[t,t={event:e}][0];t.event=(t.event||i).toLowerCase(),e=t.event,N0[e]||(kt(t0.test(i)&&t0.test(e)),R0[i]||(R0[i]={action:n,actionInfo:t}),N0[e]=i)}function em(t,e){hd.register(t,e)}function nm(t,e){om(z0,t,e,1e3,"layout")}function im(t,e){om(z0,t,e,3e3,"visual")}var rm=[];function om(t,e,n,i,r){(I(e)||R(e))&&(n=e,e=i),0<=k(rm,n)||(rm.push(n),(i=Bg.wrapStageHandler(n,r)).__prio=e,i.__raw=n,t.push(i))}function am(t,e){V0[t]=e}function sm(t,e,n){var i=Ky.registerMap;i&&i(t,e,n)}function lm(t){var e=(t=b(t)).type,n=(e||f(""),e.split(":")),i=(2!==n.length&&f(""),!1);"echarts"===n[0]&&(e=n[1],i=!0),t.__isBuiltIn=i,Ff.set(e,t)}im(2e3,Zo),im(4500,Xh),im(4500,Gc),im(2e3,bc),im(4500,_c),im(7e3,function(e,i){e.eachRawSeries(function(t){var n;!e.isSeriesFiltered(t)&&((n=t.getData()).hasItemVisual()&&n.each(function(t){var e=n.getItemVisual(t,"decal");e&&(n.ensureUniqueItemVisual(t,"style").decal=Yy(e,i))}),t=n.getVisual("decal"))&&(n.getVisual("style").decal=Yy(t,i))})}),Z0(Od),K0(900,function(t){var i=E();t.eachSeries(function(t){var e,n=t.get("stack");n&&(n=i.get(n)||i.set(n,[]),(t={stackResultDimension:(e=t.getData()).getCalculationInfo("stackResultDimension"),stackedOverDimension:e.getCalculationInfo("stackedOverDimension"),stackedDimension:e.getCalculationInfo("stackedDimension"),stackedByDimension:e.getCalculationInfo("stackedByDimension"),isStackedByIndex:e.getCalculationInfo("isStackedByIndex"),data:e,seriesModel:t}).stackedDimension)&&(t.isStackedByIndex||t.stackedByDimension)&&(n.length&&e.setCalculationInfo("stackedOnSeries",n[n.length-1].seriesModel),n.push(t))}),i.each(Rd)}),am("default",function(i,r){B(r=r||{},{text:"loading",textColor:"#000",fontSize:12,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#5470c6",spinnerRadius:10,lineWidth:5,zlevel:0});var o,t=new Wr,a=new Is({style:{fill:r.maskColor},zlevel:r.zlevel,z:1e4}),s=(t.add(a),new Os({style:{text:r.text,fill:r.textColor,fontSize:r.fontSize,fontWeight:r.fontWeight,fontStyle:r.fontStyle,fontFamily:r.fontFamily},zlevel:r.zlevel,z:10001})),l=new Is({style:{fill:"none"},textContent:s,textConfig:{position:"right",distance:10},zlevel:r.zlevel,z:10001});return t.add(l),r.showSpinner&&((o=new ih({shape:{startAngle:-Eg/2,endAngle:-Eg/2+.1,r:r.spinnerRadius},style:{stroke:r.color,lineCap:"round",lineWidth:r.lineWidth},zlevel:r.zlevel,z:10001})).animateShape(!0).when(1e3,{endAngle:3*Eg/2}).start("circularInOut"),o.animateShape(!0).when(1e3,{startAngle:3*Eg/2}).delay(300).start("circularInOut"),t.add(o)),t.resize=function(){var t=s.getBoundingRect().width,e=r.showSpinner?r.spinnerRadius:0,t=(i.getWidth()-2*e-(r.showSpinner&&t?10:0)-t)/2-(r.showSpinner&&t?0:5+t/2)+(r.showSpinner?0:t/2)+(t?0:e),n=i.getHeight()/2;r.showSpinner&&o.setShape({cx:t,cy:n}),l.setShape({x:t-e,y:n-e,width:2*e,height:2*e}),a.setShape({x:0,y:0,width:i.getWidth(),height:i.getHeight()})},t.resize(),t}),tm({type:el,event:el,update:el},Ht),tm({type:nl,event:nl,update:nl},Ht),tm({type:il,event:il,update:il},Ht),tm({type:rl,event:rl,update:rl},Ht),tm({type:ol,event:ol,update:ol},Ht),j0("light",Mc),j0("dark",h);function um(t){return null==t?0:t.length||1}function hm(t){return t}pm.prototype.add=function(t){return this._add=t,this},pm.prototype.update=function(t){return this._update=t,this},pm.prototype.updateManyToOne=function(t){return this._updateManyToOne=t,this},pm.prototype.updateOneToMany=function(t){return this._updateOneToMany=t,this},pm.prototype.updateManyToMany=function(t){return this._updateManyToMany=t,this},pm.prototype.remove=function(t){return this._remove=t,this},pm.prototype.execute=function(){this[this._diffModeMultiple?"_executeMultiple":"_executeOneToOne"]()},pm.prototype._executeOneToOne=function(){var t=this._old,e=this._new,n={},i=new Array(t.length),r=new Array(e.length);this._initIndexMap(t,null,i,"_oldKeyGetter"),this._initIndexMap(e,n,r,"_newKeyGetter");for(var o=0;o<t.length;o++){var a,s=i[o],l=n[s],u=um(l);1<u?(a=l.shift(),1===l.length&&(n[s]=l[0]),this._update&&this._update(a,o)):1===u?(n[s]=null,this._update&&this._update(l,o)):this._remove&&this._remove(o)}this._performRestAdd(r,n)},pm.prototype._executeMultiple=function(){var t=this._old,e=this._new,n={},i={},r=[],o=[];this._initIndexMap(t,n,r,"_oldKeyGetter"),this._initIndexMap(e,i,o,"_newKeyGetter");for(var a=0;a<r.length;a++){var s=r[a],l=n[s],u=i[s],h=um(l),c=um(u);if(1<h&&1===c)this._updateManyToOne&&this._updateManyToOne(u,l),i[s]=null;else if(1===h&&1<c)this._updateOneToMany&&this._updateOneToMany(u,l),i[s]=null;else if(1===h&&1===c)this._update&&this._update(u,l),i[s]=null;else if(1<h&&1<c)this._updateManyToMany&&this._updateManyToMany(u,l),i[s]=null;else if(1<h)for(var p=0;p<h;p++)this._remove&&this._remove(l[p]);else this._remove&&this._remove(l)}this._performRestAdd(o,i)},pm.prototype._performRestAdd=function(t,e){for(var n=0;n<t.length;n++){var i=t[n],r=e[i],o=um(r);if(1<o)for(var a=0;a<o;a++)this._add&&this._add(r[a]);else 1===o&&this._add&&this._add(r);e[i]=null}},pm.prototype._initIndexMap=function(t,e,n,i){for(var r=this._diffModeMultiple,o=0;o<t.length;o++){var a,s,l="_ec_"+this[i](t[o],o);r||(n[o]=l),e&&(0===(s=um(a=e[l]))?(e[l]=o,r&&n.push(l)):1===s?e[l]=[a,o]:a.push(o))}};var cm=pm;function pm(t,e,n,i,r,o){this._old=t,this._new=e,this._oldKeyGetter=n||hm,this._newKeyGetter=i||hm,this.context=r,this._diffModeMultiple="multiple"===o}fm.prototype.get=function(){return{fullDimensions:this._getFullDimensionNames(),encode:this._encode}},fm.prototype._getFullDimensionNames=function(){return this._cachedDimNames||(this._cachedDimNames=this._schema?this._schema.makeOutputDimensionNames():[]),this._cachedDimNames};var dm=fm;function fm(t,e){this._encode=t,this._schema=e}function gm(o,t){var e={},a=e.encode={},s=E(),l=[],u=[],h={},i=(O(o.dimensions,function(t){var e,n,i=o.getDimensionInfo(t),r=i.coordDim;r&&(e=i.coordDimIndex,ym(a,r)[e]=t,i.isExtraCoord||(s.set(r,1),"ordinal"!==(n=i.type)&&"time"!==n&&(l[0]=t),ym(h,r)[e]=o.getDimensionIndex(i.name)),i.defaultTooltip)&&u.push(t),Op.each(function(t,e){var n=ym(a,e),e=i.otherDims[e];null!=e&&!1!==e&&(n[e]=i.name)})}),[]),r={},n=(s.each(function(t,e){var n=a[e];r[e]=n[0],i=i.concat(n)}),e.dataDimsOnCoord=i,e.dataDimIndicesOnCoord=z(i,function(t){return o.getDimensionInfo(t).storeDimIndex}),e.encodeFirstDimNotExtra=r,a.label),n=(n&&n.length&&(l=n.slice()),a.tooltip);return n&&n.length?u=n.slice():u.length||(u=l.slice()),a.defaultedLabel=l,a.defaultedTooltip=u,e.userOutput=new dm(h,t),e}function ym(t,e){return t.hasOwnProperty(e)||(t[e]=[]),t[e]}var mm=function(t){this.otherDims={},null!=t&&P(this,t)},vm=Lo(),_m={float:"f",int:"i",ordinal:"o",number:"n",time:"t"},xm=(wm.prototype.isDimensionOmitted=function(){return this._dimOmitted},wm.prototype._updateDimOmitted=function(t){(this._dimOmitted=t)&&!this._dimNameMap&&(this._dimNameMap=Mm(this.source))},wm.prototype.getSourceDimensionIndex=function(t){return N(this._dimNameMap.get(t),-1)},wm.prototype.getSourceDimension=function(t){var e=this.source.dimensionsDefine;if(e)return e[t]},wm.prototype.makeStoreSchema=function(){for(var t=this._fullDimCount,e=Ud(this.source),n=!(30<t),i="",r=[],o=0,a=0;o<t;o++){var s,l=void 0,u=void 0,h=void 0,c=this.dimensions[a];c&&c.storeDimIndex===o?(l=e?c.name:null,u=c.type,h=c.ordinalMeta,a++):(s=this.getSourceDimension(o))&&(l=e?s.name:null,u=s.type),r.push({property:l,type:u,ordinalMeta:h}),!e||null==l||c&&c.isCalculationCoord||(i+=n?l.replace(/\`/g,"`1").replace(/\$/g,"`2"):l),i=i+"$"+(_m[u]||"f"),h&&(i+=h.uid),i+="$"}var p=this.source;return{dimensions:r,hash:[p.seriesLayoutBy,p.startIndex,i].join("$$")}},wm.prototype.makeOutputDimensionNames=function(){for(var t=[],e=0,n=0;e<this._fullDimCount;e++){var i=void 0,r=this.dimensions[n];r&&r.storeDimIndex===e?(r.isCalculationCoord||(i=r.name),n++):(r=this.getSourceDimension(e))&&(i=r.name),t.push(i)}return t},wm.prototype.appendCalculationDimension=function(t){this.dimensions.push(t),t.isCalculationCoord=!0,this._fullDimCount++,this._updateDimOmitted(!0)},wm);function wm(t){this.dimensions=t.dimensions,this._dimOmitted=t.dimensionOmitted,this.source=t.source,this._fullDimCount=t.fullDimensionCount,this._updateDimOmitted(t.dimensionOmitted)}function bm(t){return t instanceof xm}function Sm(t){for(var e=E(),n=0;n<(t||[]).length;n++){var i=t[n],i=R(i)?i.name:i;null!=i&&null==e.get(i)&&e.set(i,n)}return e}function Mm(t){var e=vm(t);return e.dimNameMap||(e.dimNameMap=Sm(t.dimensionsDefine))}var Tm,Cm,km,Im,Dm,Lm,Am,Pm=R,Om=z,Rm="undefined"==typeof Int32Array?Array:Int32Array,Nm=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_dimSummary","userOutput","_rawData","_dimValueGetter","_nameDimIdx","_idDimIdx","_nameRepeatCount"],Em=["_approximateExtent"],Bm=(_.prototype.getDimension=function(t){var e;return null==(e=this._recognizeDimIndex(t))?t:(e=t,this._dimOmitted?null!=(t=this._dimIdxToName.get(e))?t:(t=this._schema.getSourceDimension(e))?t.name:void 0:this.dimensions[e])},_.prototype.getDimensionIndex=function(t){var e=this._recognizeDimIndex(t);return null!=e?e:null==t?-1:(e=this._getDimInfo(t))?e.storeDimIndex:this._dimOmitted?this._schema.getSourceDimensionIndex(t):-1},_.prototype._recognizeDimIndex=function(t){if(gt(t)||null!=t&&!isNaN(t)&&!this._getDimInfo(t)&&(!this._dimOmitted||this._schema.getSourceDimensionIndex(t)<0))return+t},_.prototype._getStoreDimIndex=function(t){return this.getDimensionIndex(t)},_.prototype.getDimensionInfo=function(t){return this._getDimInfo(this.getDimension(t))},_.prototype._initGetDimensionInfo=function(t){var e=this._dimInfos;this._getDimInfo=t?function(t){return e.hasOwnProperty(t)?e[t]:void 0}:function(t){return e[t]}},_.prototype.getDimensionsOnCoord=function(){return this._dimSummary.dataDimsOnCoord.slice()},_.prototype.mapDimension=function(t,e){var n=this._dimSummary;return null==e?n.encodeFirstDimNotExtra[t]:(n=n.encode[t])?n[e]:null},_.prototype.mapDimensionsAll=function(t){return(this._dimSummary.encode[t]||[]).slice()},_.prototype.getStore=function(){return this._store},_.prototype.initData=function(t,e,n){var i,r,o=this;(i=t instanceof Kf?t:i)||(r=this.dimensions,t=zd(t)||st(t)?new jd(t,r.length):t,i=new Kf,r=Om(r,function(t){return{type:o._dimInfos[t].type,property:t}}),i.initData(t,r,n)),this._store=i,this._nameList=(e||[]).slice(),this._idList=[],this._nameRepeatCount={},this._doInit(0,i.count()),this._dimSummary=gm(this,this._schema),this.userOutput=this._dimSummary.userOutput},_.prototype.appendData=function(t){t=this._store.appendData(t);this._doInit(t[0],t[1])},_.prototype.appendValues=function(t,e){var t=this._store.appendValues(t,e.length),n=t.start,i=t.end,r=this._shouldMakeIdFromName();if(this._updateOrdinalMeta(),e)for(var o=n;o<i;o++)this._nameList[o]=e[o-n],r&&Am(this,o)},_.prototype._updateOrdinalMeta=function(){for(var t=this._store,e=this.dimensions,n=0;n<e.length;n++){var i=this._dimInfos[e[n]];i.ordinalMeta&&t.collectOrdinalMeta(i.storeDimIndex,i.ordinalMeta)}},_.prototype._shouldMakeIdFromName=function(){var t=this._store.getProvider();return null==this._idDimIdx&&t.getSource().sourceFormat!==zp&&!t.fillStorage},_.prototype._doInit=function(t,e){if(!(e<=t)){var n=this._store.getProvider(),i=(this._updateOrdinalMeta(),this._nameList),r=this._idList;if(n.getSource().sourceFormat===Rp&&!n.pure)for(var o=[],a=t;a<e;a++){var s,l=n.getItem(a,o);this.hasItemOption||!R(s=l)||s instanceof Array||(this.hasItemOption=!0),l&&(s=l.name,null==i[a]&&null!=s&&(i[a]=To(s,null)),l=l.id,null==r[a])&&null!=l&&(r[a]=To(l,null))}if(this._shouldMakeIdFromName())for(a=t;a<e;a++)Am(this,a);Tm(this)}},_.prototype.getApproximateExtent=function(t){return this._approximateExtent[t]||this._store.getDataExtent(this._getStoreDimIndex(t))},_.prototype.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},_.prototype.getCalculationInfo=function(t){return this._calculationInfo[t]},_.prototype.setCalculationInfo=function(t,e){Pm(t)?P(this._calculationInfo,t):this._calculationInfo[t]=e},_.prototype.getName=function(t){var t=this.getRawIndex(t),e=this._nameList[t];return e=null==(e=null==e&&null!=this._nameDimIdx?km(this,this._nameDimIdx,t):e)?"":e},_.prototype._getCategory=function(t,e){e=this._store.get(t,e),t=this._store.getOrdinalMeta(t);return t?t.categories[e]:e},_.prototype.getId=function(t){return Cm(this,this.getRawIndex(t))},_.prototype.count=function(){return this._store.count()},_.prototype.get=function(t,e){var n=this._store,t=this._dimInfos[t];if(t)return n.get(t.storeDimIndex,e)},_.prototype.getByRawIndex=function(t,e){var n=this._store,t=this._dimInfos[t];if(t)return n.getByRawIndex(t.storeDimIndex,e)},_.prototype.getIndices=function(){return this._store.getIndices()},_.prototype.getDataExtent=function(t){return this._store.getDataExtent(this._getStoreDimIndex(t))},_.prototype.getSum=function(t){return this._store.getSum(this._getStoreDimIndex(t))},_.prototype.getMedian=function(t){return this._store.getMedian(this._getStoreDimIndex(t))},_.prototype.getValues=function(t,e){var n=this,i=this._store;return F(t)?i.getValues(Om(t,function(t){return n._getStoreDimIndex(t)}),e):i.getValues(t)},_.prototype.hasValue=function(t){for(var e=this._dimSummary.dataDimIndicesOnCoord,n=0,i=e.length;n<i;n++)if(isNaN(this._store.get(e[n],t)))return!1;return!0},_.prototype.indexOfName=function(t){for(var e=0,n=this._store.count();e<n;e++)if(this.getName(e)===t)return e;return-1},_.prototype.getRawIndex=function(t){return this._store.getRawIndex(t)},_.prototype.indexOfRawIndex=function(t){return this._store.indexOfRawIndex(t)},_.prototype.rawIndexOf=function(t,e){t=(t&&this._invertedIndicesMap[t])[e];return null==t||isNaN(t)?-1:t},_.prototype.indicesOfNearest=function(t,e,n){return this._store.indicesOfNearest(this._getStoreDimIndex(t),e,n)},_.prototype.each=function(t,e,n){I(t)&&(n=e,e=t,t=[]);n=n||this,t=Om(Im(t),this._getStoreDimIndex,this);this._store.each(t,n?pt(e,n):e)},_.prototype.filterSelf=function(t,e,n){I(t)&&(n=e,e=t,t=[]);n=n||this,t=Om(Im(t),this._getStoreDimIndex,this);return this._store=this._store.filter(t,n?pt(e,n):e),this},_.prototype.selectRange=function(n){var i=this,r={};return O(ct(n),function(t){var e=i._getStoreDimIndex(t);r[e]=n[t]}),this._store=this._store.selectRange(r),this},_.prototype.mapArray=function(t,e,n){I(t)&&(n=e,e=t,t=[]);var i=[];return this.each(t,function(){i.push(e&&e.apply(this,arguments))},n=n||this),i},_.prototype.map=function(t,e,n,i){n=n||i||this,i=Om(Im(t),this._getStoreDimIndex,this),t=Lm(this);return t._store=this._store.map(i,n?pt(e,n):e),t},_.prototype.modify=function(t,e,n,i){n=n||i||this,i=Om(Im(t),this._getStoreDimIndex,this);this._store.modify(i,n?pt(e,n):e)},_.prototype.downSample=function(t,e,n,i){var r=Lm(this);return r._store=this._store.downSample(this._getStoreDimIndex(t),e,n,i),r},_.prototype.lttbDownSample=function(t,e){var n=Lm(this);return n._store=this._store.lttbDownSample(this._getStoreDimIndex(t),e),n},_.prototype.getRawDataItem=function(t){return this._store.getRawDataItem(t)},_.prototype.getItemModel=function(t){var e=this.hostModel,t=this.getRawDataItem(t);return new kc(t,e,e&&e.ecModel)},_.prototype.diff=function(e){var n=this;return new cm(e?e.getStore().getIndices():[],this.getStore().getIndices(),function(t){return Cm(e,t)},function(t){return Cm(n,t)})},_.prototype.getVisual=function(t){var e=this._visual;return e&&e[t]},_.prototype.setVisual=function(t,e){this._visual=this._visual||{},Pm(t)?P(this._visual,t):this._visual[t]=e},_.prototype.getItemVisual=function(t,e){t=this._itemVisuals[t],t=t&&t[e];return null==t?this.getVisual(e):t},_.prototype.hasItemVisual=function(){return 0<this._itemVisuals.length},_.prototype.ensureUniqueItemVisual=function(t,e){var n=this._itemVisuals,i=n[t],n=(i=i||(n[t]={}))[e];return null==n&&(F(n=this.getVisual(e))?n=n.slice():Pm(n)&&(n=P({},n)),i[e]=n),n},_.prototype.setItemVisual=function(t,e,n){var i=this._itemVisuals[t]||{};this._itemVisuals[t]=i,Pm(e)?P(i,e):i[e]=n},_.prototype.clearAllVisual=function(){this._visual={},this._itemVisuals=[]},_.prototype.setLayout=function(t,e){Pm(t)?P(this._layout,t):this._layout[t]=e},_.prototype.getLayout=function(t){return this._layout[t]},_.prototype.getItemLayout=function(t){return this._itemLayouts[t]},_.prototype.setItemLayout=function(t,e,n){this._itemLayouts[t]=n?P(this._itemLayouts[t]||{},e):e},_.prototype.clearItemLayouts=function(){this._itemLayouts.length=0},_.prototype.setItemGraphicEl=function(t,e){var n,i,r,o,a=this.hostModel&&this.hostModel.seriesIndex;n=a,i=this.dataType,r=t,(a=e)&&((o=qs(a)).dataIndex=r,o.dataType=i,o.seriesIndex=n,o.ssrType="chart","group"===a.type)&&a.traverse(function(t){t=qs(t);t.seriesIndex=n,t.dataIndex=r,t.dataType=i,t.ssrType="chart"}),this._graphicEls[t]=e},_.prototype.getItemGraphicEl=function(t){return this._graphicEls[t]},_.prototype.eachItemGraphicEl=function(n,i){O(this._graphicEls,function(t,e){t&&n&&n.call(i,t,e)})},_.prototype.cloneShallow=function(t){return t=t||new _(this._schema||Om(this.dimensions,this._getDimInfo,this),this.hostModel),Dm(t,this),t._store=this._store,t},_.prototype.wrapMethod=function(t,e){var n=this[t];I(n)&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=n.apply(this,arguments);return e.apply(this,[t].concat(Tt(arguments)))})},_.internalField=(Tm=function(a){var s=a._invertedIndicesMap;O(s,function(t,e){var n=a._dimInfos[e],i=n.ordinalMeta,r=a._store;if(i){t=s[e]=new Rm(i.categories.length);for(var o=0;o<t.length;o++)t[o]=-1;for(o=0;o<r.count();o++)t[r.get(n.storeDimIndex,o)]=o}})},km=function(t,e,n){return To(t._getCategory(e,n),null)},Cm=function(t,e){var n=t._idList[e];return n=null==(n=null==n&&null!=t._idDimIdx?km(t,t._idDimIdx,e):n)?"e\0\0"+e:n},Im=function(t){return t=F(t)?t:null!=t?[t]:[]},Lm=function(t){var e=new _(t._schema||Om(t.dimensions,t._getDimInfo,t),t.hostModel);return Dm(e,t),e},Dm=function(e,n){O(Nm.concat(n.__wrappedMethods||[]),function(t){n.hasOwnProperty(t)&&(e[t]=n[t])}),e.__wrappedMethods=n.__wrappedMethods,O(Em,function(t){e[t]=b(n[t])}),e._calculationInfo=P({},n._calculationInfo)},void(Am=function(t,e){var n=t._nameList,i=t._idList,r=t._nameDimIdx,o=t._idDimIdx,a=n[e],s=i[e];null==a&&null!=r&&(n[e]=a=km(t,r,e)),null==s&&null!=o&&(i[e]=s=km(t,o,e)),null==s&&null!=a&&(s=a,1<(r=(n=t._nameRepeatCount)[a]=(n[a]||0)+1)&&(s+="__ec__"+r),i[e]=s)})),_);function _(t,e){this.type="list",this._dimOmitted=!1,this._nameList=[],this._idList=[],this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._approximateExtent={},this._calculationInfo={},this.hasItemOption=!1,this.TRANSFERABLE_METHODS=["cloneShallow","downSample","lttbDownSample","map"],this.CHANGABLE_METHODS=["filterSelf","selectRange"];for(var n,i,r=!(this.DOWNSAMPLE_METHODS=["downSample","lttbDownSample"]),o=(bm(t)?(n=t.dimensions,this._dimOmitted=t.isDimensionOmitted(),this._schema=t):(r=!0,n=t),n=n||["x","y"],{}),a=[],s={},l=!1,u={},h=0;h<n.length;h++){var c=n[h],c=V(c)?new mm({name:c}):c instanceof mm?c:new mm(c),p=c.name,d=(c.type=c.type||"float",c.coordDim||(c.coordDim=p,c.coordDimIndex=0),c.otherDims=c.otherDims||{});a.push(p),null!=u[p]&&(l=!0),(o[p]=c).createInvertedIndices&&(s[p]=[]),0===d.itemName&&(this._nameDimIdx=h),0===d.itemId&&(this._idDimIdx=h),r&&(c.storeDimIndex=h)}this.dimensions=a,this._dimInfos=o,this._initGetDimensionInfo(l),this.hostModel=e,this._invertedIndicesMap=s,this._dimOmitted&&(i=this._dimIdxToName=E(),O(a,function(t){i.set(o[t].storeDimIndex,t)}))}function zm(t,e){zd(t)||(t=Vd(t));for(var n,i,r=(e=e||{}).coordDimensions||[],o=e.dimensionsDefine||t.dimensionsDefine||[],a=E(),s=[],l=(u=t,n=r,p=e.dimensionsCount,i=Math.max(u.dimensionsDetectedCount||1,n.length,o.length,p||0),O(n,function(t){R(t)&&(t=t.dimsDef)&&(i=Math.max(i,t.length))}),i),u=e.canOmitUnusedDimensions&&30<l,h=o===t.dimensionsDefine,c=h?Mm(t):Sm(o),p=e.encodeDefine,d=E(p=!p&&e.encodeDefaulter?e.encodeDefaulter(t,l):p),f=new qf(l),g=0;g<f.length;g++)f[g]=-1;function y(t){var e,n,i,r=f[t];return r<0?(e=R(e=o[t])?e:{name:e},n=new mm,null!=(i=e.name)&&null!=c.get(i)&&(n.name=n.displayName=i),null!=e.type&&(n.type=e.type),null!=e.displayName&&(n.displayName=e.displayName),f[t]=s.length,n.storeDimIndex=t,s.push(n),n):s[r]}if(!u)for(g=0;g<l;g++)y(g);d.each(function(t,n){var i,t=vo(t).slice();1===t.length&&!V(t[0])&&t[0]<0?d.set(n,!1):(i=d.set(n,[]),O(t,function(t,e){t=V(t)?c.get(t):t;null!=t&&t<l&&v(y(i[e]=t),n,e)}))});var m=0;function v(t,e,n){null!=Op.get(e)?t.otherDims[e]=n:(t.coordDim=e,t.coordDimIndex=n,a.set(e,!0))}O(r,function(t){V(t)?(o=t,r={}):(o=(r=t).name,t=r.ordinalMeta,r.ordinalMeta=null,(r=P({},r)).ordinalMeta=t,n=r.dimsDef,i=r.otherDims,r.name=r.coordDim=r.coordDimIndex=r.dimsDef=r.otherDims=null);var n,i,r,o,e=d.get(o);if(!1!==e){if(!(e=vo(e)).length)for(var a=0;a<(n&&n.length||1);a++){for(;m<l&&null!=y(m).coordDim;)m++;m<l&&e.push(m++)}O(e,function(t,e){t=y(t);h&&null!=r.type&&(t.type=r.type),v(B(t,r),o,e),null==t.name&&n&&(R(e=n[e])||(e={name:e}),t.name=t.displayName=e.name,t.defaultTooltip=e.defaultTooltip),i&&B(t.otherDims,i)})}});var _=e.generateCoord,x=null!=(w=e.generateCoordCount),w=_?w||1:0,b=_||"value";function S(t){null==t.name&&(t.name=t.coordDim)}if(u)O(s,function(t){S(t)}),s.sort(function(t,e){return t.storeDimIndex-e.storeDimIndex});else for(var M=0;M<l;M++){var T=y(M);null==T.coordDim&&(T.coordDim=function(t,e,n){if(n||e.hasKey(t)){for(var i=0;e.hasKey(t+i);)i++;t+=i}return e.set(t,!0),t}(b,a,x),T.coordDimIndex=0,(!_||w<=0)&&(T.isExtraCoord=!0),w--),S(T),null!=T.type||Xp(t,M)!==Wp.Must&&(!T.isExtraCoord||null==T.otherDims.itemName&&null==T.otherDims.seriesName)||(T.type="ordinal")}for(var C=s,k=E(),I=0;I<C.length;I++){var D=C[I],L=D.name,A=k.get(L)||0;0<A&&(D.name=L+(A-1)),A++,k.set(L,A)}return new xm({source:t,dimensions:s,fullDimensionCount:l,dimensionOmitted:u})}var Fm=function(t){this.coordSysDims=[],this.axisMap=E(),this.categoryAxisMap=E(),this.coordSysName=t};var Vm={cartesian2d:function(t,e,n,i){var r=t.getReferringComponents("xAxis",Ro).models[0],t=t.getReferringComponents("yAxis",Ro).models[0];e.coordSysDims=["x","y"],n.set("x",r),n.set("y",t),Hm(r)&&(i.set("x",r),e.firstCategoryDimIndex=0),Hm(t)&&(i.set("y",t),null==e.firstCategoryDimIndex)&&(e.firstCategoryDimIndex=1)},singleAxis:function(t,e,n,i){t=t.getReferringComponents("singleAxis",Ro).models[0];e.coordSysDims=["single"],n.set("single",t),Hm(t)&&(i.set("single",t),e.firstCategoryDimIndex=0)},polar:function(t,e,n,i){var t=t.getReferringComponents("polar",Ro).models[0],r=t.findAxisModel("radiusAxis"),t=t.findAxisModel("angleAxis");e.coordSysDims=["radius","angle"],n.set("radius",r),n.set("angle",t),Hm(r)&&(i.set("radius",r),e.firstCategoryDimIndex=0),Hm(t)&&(i.set("angle",t),null==e.firstCategoryDimIndex)&&(e.firstCategoryDimIndex=1)},geo:function(t,e,n,i){e.coordSysDims=["lng","lat"]},parallel:function(t,i,r,o){var a=t.ecModel,t=a.getComponent("parallel",t.get("parallelIndex")),s=i.coordSysDims=t.dimensions.slice();O(t.parallelAxisIndex,function(t,e){var t=a.getComponent("parallelAxis",t),n=s[e];r.set(n,t),Hm(t)&&(o.set(n,t),null==i.firstCategoryDimIndex)&&(i.firstCategoryDimIndex=e)})}};function Hm(t){return"category"===t.get("type")}function Wm(t,e,n){var i,r,o,a,s,l,u,h,c,p=(n=n||{}).byIndex,d=n.stackedCoordDimension,f=(bm(e.schema)?(r=e.schema,i=r.dimensions,o=e.store):i=e,!(!t||!t.get("stack")));return O(i,function(t,e){V(t)&&(i[e]=t={name:t}),f&&!t.isExtraCoord&&(p||a||!t.ordinalMeta||(a=t),s||"ordinal"===t.type||"time"===t.type||d&&d!==t.coordDim||(s=t))}),!s||p||a||(p=!0),s&&(l="__\0ecstackresult_"+t.id,u="__\0ecstackedover_"+t.id,a&&(a.createInvertedIndices=!0),h=s.coordDim,n=s.type,c=0,O(i,function(t){t.coordDim===h&&c++}),e={name:l,coordDim:h,coordDimIndex:c,type:n,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length},t={name:u,coordDim:u,coordDimIndex:c+1,type:n,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length+1},r?(o&&(e.storeDimIndex=o.ensureCalculationDimension(u,n),t.storeDimIndex=o.ensureCalculationDimension(l,n)),r.appendCalculationDimension(e),r.appendCalculationDimension(t)):(i.push(e),i.push(t))),{stackedDimension:s&&s.name,stackedByDimension:a&&a.name,isStackedByIndex:p,stackedOverDimension:u,stackResultDimension:l}}function Gm(t,e){return!!e&&e===t.getCalculationInfo("stackedDimension")}function Um(t,e,n){n=n||{};var i,r,o,a,s,l,u=e.getSourceManager(),h=!1,t=(t?(h=!0,i=Vd(t)):h=(i=u.getSource()).sourceFormat===Rp,function(t){var e=t.get("coordinateSystem"),n=new Fm(e);if(e=Vm[e])return e(t,n,n.axisMap,n.categoryAxisMap),n}(e)),c=(r=t,c=(c=e).get("coordinateSystem"),c=hd.get(c),p=(p=r&&r.coordSysDims?z(r.coordSysDims,function(t){var e={name:t},t=r.axisMap.get(t);return t&&(t=t.get("type"),e.type="category"===(t=t)?"ordinal":"time"===t?"time":"float"),e}):p)||c&&(c.getDimensionsInfo?c.getDimensionsInfo():c.dimensions.slice())||["x","y"]),p=n.useEncodeDefaulter,p=I(p)?p:p?dt(Up,c,e):null,c={coordDimensions:c,generateCoord:n.generateCoord,encodeDefine:e.getEncode(),encodeDefaulter:p,canOmitUnusedDimensions:!h},p=zm(i,c),d=(c=p.dimensions,o=n.createInvertedIndices,(a=t)&&O(c,function(t,e){var n=t.coordDim,n=a.categoryAxisMap.get(n);n&&(null==s&&(s=e),t.ordinalMeta=n.getOrdinalMeta(),o)&&(t.createInvertedIndices=!0),null!=t.otherDims.itemName&&(l=!0)}),l||null==s||(c[s].otherDims.itemName=0),s),n=h?null:u.getSharedDataStore(p),t=Wm(e,{schema:p,store:n}),c=new Bm(p,e),p=(c.setCalculationInfo(t),null==d||(u=i).sourceFormat!==Rp||F(wo(function(t){var e=0;for(;e<t.length&&null==t[e];)e++;return t[e]}(u.data||[])))?null:function(t,e,n,i){return i===d?n:this.defaultDimValueGetter(t,e,n,i)});return c.hasItemOption=!1,c.initData(h?i:n,null,p),c}Xm.prototype.getSetting=function(t){return this._setting[t]},Xm.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},Xm.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},Xm.prototype.getExtent=function(){return this._extent.slice()},Xm.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=t),isNaN(e)||(n[1]=e)},Xm.prototype.isInExtentRange=function(t){return this._extent[0]<=t&&this._extent[1]>=t},Xm.prototype.isBlank=function(){return this._isBlank},Xm.prototype.setBlank=function(t){this._isBlank=t};var qm=Xm;function Xm(t){this._setting=t||{},this._extent=[1/0,-1/0]}Xo(qm);var Ym=0,jm=(Zm.createByAxisModel=function(t){var t=t.option,e=t.data,e=e&&z(e,Km);return new Zm({categories:e,needCollect:!e,deduplication:!1!==t.dedplication})},Zm.prototype.getOrdinal=function(t){return this._getOrCreateMap().get(t)},Zm.prototype.parseAndCollect=function(t){var e,n,i=this._needCollect;return V(t)||i?(i&&!this._deduplication?(n=this.categories.length,this.categories[n]=t):null==(n=(e=this._getOrCreateMap()).get(t))&&(i?(n=this.categories.length,this.categories[n]=t,e.set(t,n)):n=NaN),n):t},Zm.prototype._getOrCreateMap=function(){return this._map||(this._map=E(this.categories))},Zm);function Zm(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this.uid=++Ym}function Km(t){return R(t)&&null!=t.value?t.value:t+""}function $m(t,e,n,i){var r={},o=t[1]-t[0],o=r.interval=co(o/e,!0),e=(null!=n&&o<n&&(o=r.interval=n),null!=i&&i<o&&(o=r.interval=i),r.intervalPrecision=Jm(o)),n=r.niceTickExtent=[eo(Math.ceil(t[0]/o)*o,e),eo(Math.floor(t[1]/o)*o,e)];return i=n,o=t,isFinite(i[0])||(i[0]=o[0]),isFinite(i[1])||(i[1]=o[1]),tv(i,0,o),tv(i,1,o),i[0]>i[1]&&(i[0]=i[1]),r}function Qm(t){var e=Math.pow(10,ho(t)),t=t/e;return t?2===t?t=3:3===t?t=5:t*=2:t=1,eo(t*e)}function Jm(t){return no(t)+2}function tv(t,e,n){t[e]=Math.max(Math.min(t[e],n[1]),n[0])}function ev(t,e){return t>=e[0]&&t<=e[1]}function nv(t,e){return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])}function iv(t,e){return t*(e[1]-e[0])+e[0]}a(av,rv=qm),av.prototype.parse=function(t){return null==t?NaN:V(t)?this._ordinalMeta.getOrdinal(t):Math.round(t)},av.prototype.contain=function(t){return ev(t=this.parse(t),this._extent)&&null!=this._ordinalMeta.categories[t]},av.prototype.normalize=function(t){return nv(t=this._getTickNumber(this.parse(t)),this._extent)},av.prototype.scale=function(t){return t=Math.round(iv(t,this._extent)),this.getRawOrdinalNumber(t)},av.prototype.getTicks=function(){for(var t=[],e=this._extent,n=e[0];n<=e[1];)t.push({value:n}),n++;return t},av.prototype.getMinorTicks=function(t){},av.prototype.setSortInfo=function(t){if(null==t)this._ordinalNumbersByTick=this._ticksByOrdinalNumber=null;else{for(var e=t.ordinalNumbers,n=this._ordinalNumbersByTick=[],i=this._ticksByOrdinalNumber=[],r=0,o=this._ordinalMeta.categories.length,a=Math.min(o,e.length);r<a;++r){var s=e[r];i[n[r]=s]=r}for(var l=0;r<o;++r){for(;null!=i[l];)l++;n.push(l),i[l]=r}}},av.prototype._getTickNumber=function(t){var e=this._ticksByOrdinalNumber;return e&&0<=t&&t<e.length?e[t]:t},av.prototype.getRawOrdinalNumber=function(t){var e=this._ordinalNumbersByTick;return e&&0<=t&&t<e.length?e[t]:t},av.prototype.getLabel=function(t){if(!this.isBlank())return t=this.getRawOrdinalNumber(t.value),null==(t=this._ordinalMeta.categories[t])?"":t+""},av.prototype.count=function(){return this._extent[1]-this._extent[0]+1},av.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},av.prototype.isInExtentRange=function(t){return t=this._getTickNumber(t),this._extent[0]<=t&&this._extent[1]>=t},av.prototype.getOrdinalMeta=function(){return this._ordinalMeta},av.prototype.calcNiceTicks=function(){},av.prototype.calcNiceExtent=function(){},av.type="ordinal";var rv,ov=av;function av(t){var t=rv.call(this,t)||this,e=(t.type="ordinal",t.getSetting("ordinalMeta"));return F(e=e||new jm({}))&&(e=new jm({categories:z(e,function(t){return R(t)?t.value:t})})),t._ordinalMeta=e,t._extent=t.getSetting("extent")||[0,e.categories.length-1],t}qm.registerClass(ov);var sv,lv=eo,uv=(a(hv,sv=qm),hv.prototype.parse=function(t){return t},hv.prototype.contain=function(t){return ev(t,this._extent)},hv.prototype.normalize=function(t){return nv(t,this._extent)},hv.prototype.scale=function(t){return iv(t,this._extent)},hv.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=parseFloat(t)),isNaN(e)||(n[1]=parseFloat(e))},hv.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),this.setExtent(e[0],e[1])},hv.prototype.getInterval=function(){return this._interval},hv.prototype.setInterval=function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=Jm(t)},hv.prototype.getTicks=function(t){var e=this._interval,n=this._extent,i=this._niceExtent,r=this._intervalPrecision,o=[];if(e){n[0]<i[0]&&o.push(t?{value:lv(i[0]-e,r)}:{value:n[0]});for(var a=i[0];a<=i[1]&&(o.push({value:a}),(a=lv(a+e,r))!==o[o.length-1].value);)if(1e4<o.length)return[];var s=o.length?o[o.length-1].value:i[1];n[1]>s&&o.push(t?{value:lv(s+e,r)}:{value:n[1]})}return o},hv.prototype.getMinorTicks=function(t){for(var e=this.getTicks(!0),n=[],i=this.getExtent(),r=1;r<e.length;r++){for(var o=e[r],a=e[r-1],s=0,l=[],u=(o.value-a.value)/t;s<t-1;){var h=lv(a.value+(s+1)*u);h>i[0]&&h<i[1]&&l.push(h),s++}n.push(l)}return n},hv.prototype.getLabel=function(t,e){return null==t?"":(null==(e=e&&e.precision)?e=no(t.value)||0:"auto"===e&&(e=this._intervalPrecision),cp(lv(t.value,e,!0)))},hv.prototype.calcNiceTicks=function(t,e,n){t=t||5;var i=this._extent,r=i[1]-i[0];isFinite(r)&&(r<0&&i.reverse(),r=$m(i,t,e,n),this._intervalPrecision=r.intervalPrecision,this._interval=r.interval,this._niceExtent=r.niceTickExtent)},hv.prototype.calcNiceExtent=function(t){var e=this._extent,n=(e[0]===e[1]&&(0!==e[0]?(n=Math.abs(e[0]),t.fixMax||(e[1]+=n/2),e[0]-=n/2):e[1]=1),e[1]-e[0]),n=(isFinite(n)||(e[0]=0,e[1]=1),this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval),this._interval);t.fixMin||(e[0]=lv(Math.floor(e[0]/n)*n)),t.fixMax||(e[1]=lv(Math.ceil(e[1]/n)*n))},hv.prototype.setNiceExtent=function(t,e){this._niceExtent=[t,e]},hv.type="interval",hv);function hv(){var t=null!==sv&&sv.apply(this,arguments)||this;return t.type="interval",t._interval=0,t._intervalPrecision=2,t}qm.registerClass(uv);function cv(t){return t.get("stack")||"__ec_stack_"+t.seriesIndex}function pv(t){return t.dim+t.index}function dv(t,e){var n=[];return e.eachSeriesByType(t,function(t){var e;(e=t).coordinateSystem&&"cartesian2d"===e.coordinateSystem.type&&n.push(t)}),n}function fv(t){var a,d,u=function(t){var e,l={},n=(O(t,function(t){var e=t.coordinateSystem.getBaseAxis();if("time"===e.type||"value"===e.type)for(var t=t.getData(),n=e.dim+"_"+e.index,i=t.getDimensionIndex(t.mapDimension(e.dim)),r=t.getStore(),o=0,a=r.count();o<a;++o){var s=r.get(i,o);l[n]?l[n].push(s):l[n]=[s]}}),{});for(e in l)if(l.hasOwnProperty(e)){var i=l[e];if(i){i.sort(function(t,e){return t-e});for(var r=null,o=1;o<i.length;++o){var a=i[o]-i[o-1];0<a&&(r=null===r?a:Math.min(r,a))}n[e]=r}}return n}(t),h=[];return O(t,function(t){var e,n,i=t.coordinateSystem.getBaseAxis(),r=i.getExtent(),o=(e="category"===i.type?i.getBandWidth():"value"===i.type||"time"===i.type?(e=i.dim+"_"+i.index,e=u[e],o=Math.abs(r[1]-r[0]),n=i.scale.getExtent(),n=Math.abs(n[1]-n[0]),e?o/n*e:o):(n=t.getData(),Math.abs(r[1]-r[0])/n.count()),to(t.get("barWidth"),e)),r=to(t.get("barMaxWidth"),e),a=to(t.get("barMinWidth")||((n=t).pipelineContext&&n.pipelineContext.large?.5:1),e),s=t.get("barGap"),l=t.get("barCategoryGap");h.push({bandWidth:e,barWidth:o,barMaxWidth:r,barMinWidth:a,barGap:s,barCategoryGap:l,axisKey:pv(i),stackId:cv(t)})}),a={},O(h,function(t,e){var n=t.axisKey,i=t.bandWidth,i=a[n]||{bandWidth:i,remainedWidth:i,autoWidthCount:0,categoryGap:null,gap:"20%",stacks:{}},r=i.stacks,n=(a[n]=i,t.stackId),o=(r[n]||i.autoWidthCount++,r[n]=r[n]||{width:0,maxWidth:0},t.barWidth),o=(o&&!r[n].width&&(r[n].width=o,o=Math.min(i.remainedWidth,o),i.remainedWidth-=o),t.barMaxWidth),o=(o&&(r[n].maxWidth=o),t.barMinWidth),r=(o&&(r[n].minWidth=o),t.barGap),n=(null!=r&&(i.gap=r),t.barCategoryGap);null!=n&&(i.categoryGap=n)}),d={},O(a,function(t,n){d[n]={};var i,e=t.stacks,r=t.bandWidth,o=t.categoryGap,a=(null==o&&(a=ct(e).length,o=Math.max(35-4*a,15)+"%"),to(o,r)),s=to(t.gap,1),l=t.remainedWidth,u=t.autoWidthCount,h=(l-a)/(u+(u-1)*s),h=Math.max(h,0),c=(O(e,function(t){var e,n=t.maxWidth,i=t.minWidth;t.width?(e=t.width,n&&(e=Math.min(e,n)),i&&(e=Math.max(e,i)),t.width=e,l-=e+s*e,u--):(e=h,n&&n<e&&(e=Math.min(n,l)),(e=i&&e<i?i:e)!==h&&(t.width=e,l-=e+s*e,u--))}),h=(l-a)/(u+(u-1)*s),h=Math.max(h,0),0),p=(O(e,function(t,e){t.width||(t.width=h),c+=(i=t).width*(1+s)}),i&&(c-=i.width*s),-c/2);O(e,function(t,e){d[n][e]=d[n][e]||{bandWidth:r,offset:p,width:t.width},p+=t.width*(1+s)})}),d}a(mv,gv=uv),mv.prototype.getLabel=function(t){var e=this.getSetting("useUTC");return Zc(t.value,Uc[function(t){switch(t){case"year":case"month":return"day";case"millisecond":return"millisecond";default:return"second"}}(jc(this._minLevelUnit))]||Uc.second,e,this.getSetting("locale"))},mv.prototype.getFormattedLabel=function(t,e,n){var i=this.getSetting("useUTC"),r=this.getSetting("locale"),o=null;if(V(n))o=n;else if(I(n))o=n(t.value,e,{level:t.level});else{var a=P({},Wc);if(0<t.level)for(var s=0;s<qc.length;++s)a[qc[s]]="{primary|"+a[qc[s]]+"}";var l=n?!1===n.inherit?n:B(n,a):a,u=Kc(t.value,i);if(l[u])o=l[u];else if(l.inherit){for(s=Xc.indexOf(u)-1;0<=s;--s)if(l[u]){o=l[u];break}o=o||a.none}F(o)&&(e=null==t.level?0:0<=t.level?t.level:o.length+t.level,o=o[e=Math.min(e,o.length-1)])}return Zc(new Date(t.value),o,i,r)},mv.prototype.getTicks=function(){var t=this._interval,e=this._extent,n=[];return t&&(n.push({value:e[0],level:0}),t=this.getSetting("useUTC"),t=function(t,b,S,M){var e=Xc,n=0;function i(t,e,n){var i=[],r=!e.length;if(!function(t,e,n,i){function r(t){return $c(c,t,i)===$c(p,t,i)}function o(){return r("year")}function a(){return o()&&r("month")}function s(){return a()&&r("day")}function l(){return s()&&r("hour")}function u(){return l()&&r("minute")}function h(){return u()&&r("second")}var c=lo(e),p=lo(n);switch(t){case"year":return o();case"month":return a();case"day":return s();case"hour":return l();case"minute":return u();case"second":return h();case"millisecond":return h()&&r("millisecond")}}(jc(t),M[0],M[1],S)){r&&(e=[{value:function(t,e,n){var i=new Date(t);switch(jc(e)){case"year":case"month":i[op(n)](0);case"day":i[ap(n)](1);case"hour":i[sp(n)](0);case"minute":i[lp(n)](0);case"second":i[up(n)](0),i[hp(n)](0)}return i.getTime()}(new Date(M[0]),t,S)},{value:M[1]}]);for(var o,a,s=0;s<e.length-1;s++){var l=e[s].value,u=e[s+1].value;if(l!==u){var h=void 0,c=void 0,p=void 0;switch(t){case"year":h=Math.max(1,Math.round(b/Hc/365)),c=Qc(S),p=S?"setUTCFullYear":"setFullYear";break;case"half-year":case"quarter":case"month":a=b,h=6<(a/=30*Hc)?6:3<a?3:2<a?2:1,c=Jc(S),p=op(S);break;case"week":case"half-week":case"day":a=b,h=16<(a/=Hc)?16:7.5<a?7:3.5<a?4:1.5<a?2:1,c=tp(S),p=ap(S),0;break;case"half-day":case"quarter-day":case"hour":o=b,h=12<(o/=Vc)?12:6<o?6:3.5<o?4:2<o?2:1,c=ep(S),p=sp(S);break;case"minute":h=_v(b,!0),c=np(S),p=lp(S);break;case"second":h=_v(b,!1),c=ip(S),p=up(S);break;case"millisecond":h=co(b,!0),c=rp(S),p=hp(S)}w=x=_=v=m=y=g=f=d=void 0;for(var d=h,f=l,g=u,y=c,m=p,v=i,_=new Date(f),x=f,w=_[y]();x<g&&x<=M[1];)v.push({value:x}),_[m](w+=d),x=_.getTime();v.push({value:x,notAdd:!0}),"year"===t&&1<n.length&&0===s&&n.unshift({value:n[0].value-h})}}for(s=0;s<i.length;s++)n.push(i[s])}}for(var r=[],o=[],a=0,s=0,l=0;l<e.length&&n++<1e4;++l){var u=jc(e[l]);if(function(t){return t===jc(t)}(e[l])){i(e[l],r[r.length-1]||[],o);var h=e[l+1]?jc(e[l+1]):null;if(u!==h){if(o.length){s=a,o.sort(function(t,e){return t.value-e.value});for(var c=[],p=0;p<o.length;++p){var d=o[p].value;0!==p&&o[p-1].value===d||(c.push(o[p]),d>=M[0]&&d<=M[1]&&a++)}u=(M[1]-M[0])/b;if(1.5*u<a&&u/1.5<s)break;if(r.push(c),u<a||t===e[l])break}o=[]}}}for(var f=ut(z(r,function(t){return ut(t,function(t){return t.value>=M[0]&&t.value<=M[1]&&!t.notAdd})}),function(t){return 0<t.length}),g=[],y=f.length-1,l=0;l<f.length;++l)for(var m=f[l],v=0;v<m.length;++v)g.push({value:m[v].value,level:y-l});g.sort(function(t,e){return t.value-e.value});for(var _=[],l=0;l<g.length;++l)0!==l&&g[l].value===g[l-1].value||_.push(g[l]);return _}(this._minLevelUnit,this._approxInterval,t,e),(n=n.concat(t)).push({value:e[1],level:0})),n},mv.prototype.calcNiceExtent=function(t){var e,n=this._extent;n[0]===n[1]&&(n[0]-=Hc,n[1]+=Hc),n[1]===-1/0&&n[0]===1/0&&(e=new Date,n[1]=+new Date(e.getFullYear(),e.getMonth(),e.getDate()),n[0]=n[1]-Hc),this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval)},mv.prototype.calcNiceTicks=function(t,e,n){var i=this._extent,i=i[1]-i[0],i=(this._approxInterval=i/(t=t||10),null!=e&&this._approxInterval<e&&(this._approxInterval=e),null!=n&&this._approxInterval>n&&(this._approxInterval=n),vv.length),t=Math.min(function(t,e,n,i){for(;n<i;){var r=n+i>>>1;t[r][1]<e?n=1+r:i=r}return n}(vv,this._approxInterval,0,i),i-1);this._interval=vv[t][1],this._minLevelUnit=vv[Math.max(t-1,0)][0]},mv.prototype.parse=function(t){return gt(t)?t:+lo(t)},mv.prototype.contain=function(t){return ev(this.parse(t),this._extent)},mv.prototype.normalize=function(t){return nv(this.parse(t),this._extent)},mv.prototype.scale=function(t){return iv(t,this._extent)},mv.type="time";var gv,yv=mv;function mv(t){t=gv.call(this,t)||this;return t.type="time",t}var vv=[["second",zc],["minute",Fc],["hour",Vc],["quarter-day",6*Vc],["half-day",12*Vc],["day",1.2*Hc],["half-week",3.5*Hc],["week",7*Hc],["month",31*Hc],["quarter",95*Hc],["half-year",Go/2],["year",Go]];function _v(t,e){return 30<(t/=e?Fc:zc)?30:20<t?20:15<t?15:10<t?10:5<t?5:2<t?2:1}qm.registerClass(yv);var xv,wv=qm.prototype,bv=uv.prototype,Sv=eo,Mv=Math.floor,Tv=Math.ceil,Cv=Math.pow,kv=Math.log,Iv=(a(Dv,xv=qm),Dv.prototype.getTicks=function(t){var e=this._originalScale,n=this._extent,i=e.getExtent();return z(bv.getTicks.call(this,t),function(t){var t=t.value,e=eo(Cv(this.base,t)),e=t===n[0]&&this._fixMin?Lv(e,i[0]):e;return{value:t===n[1]&&this._fixMax?Lv(e,i[1]):e}},this)},Dv.prototype.setExtent=function(t,e){var n=kv(this.base);t=kv(Math.max(0,t))/n,e=kv(Math.max(0,e))/n,bv.setExtent.call(this,t,e)},Dv.prototype.getExtent=function(){var t=this.base,e=wv.getExtent.call(this);e[0]=Cv(t,e[0]),e[1]=Cv(t,e[1]);t=this._originalScale.getExtent();return this._fixMin&&(e[0]=Lv(e[0],t[0])),this._fixMax&&(e[1]=Lv(e[1],t[1])),e},Dv.prototype.unionExtent=function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=kv(t[0])/kv(e),t[1]=kv(t[1])/kv(e),wv.unionExtent.call(this,t)},Dv.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},Dv.prototype.calcNiceTicks=function(t){t=t||10;var e=this._extent,n=e[1]-e[0];if(!(n==1/0||n<=0)){var i=uo(n);for(t/n*i<=.5&&(i*=10);!isNaN(i)&&Math.abs(i)<1&&0<Math.abs(i);)i*=10;t=[eo(Tv(e[0]/i)*i),eo(Mv(e[1]/i)*i)];this._interval=i,this._niceExtent=t}},Dv.prototype.calcNiceExtent=function(t){bv.calcNiceExtent.call(this,t),this._fixMin=t.fixMin,this._fixMax=t.fixMax},Dv.prototype.parse=function(t){return t},Dv.prototype.contain=function(t){return ev(t=kv(t)/kv(this.base),this._extent)},Dv.prototype.normalize=function(t){return nv(t=kv(t)/kv(this.base),this._extent)},Dv.prototype.scale=function(t){return t=iv(t,this._extent),Cv(this.base,t)},Dv.type="log",Dv);function Dv(){var t=null!==xv&&xv.apply(this,arguments)||this;return t.type="log",t.base=10,t._originalScale=new uv,t._interval=0,t}ly=Iv.prototype;function Lv(t,e){return Sv(t,no(e))}ly.getMinorTicks=bv.getMinorTicks,ly.getLabel=bv.getLabel,qm.registerClass(Iv);Pv.prototype._prepareParams=function(t,e,n){n[1]<n[0]&&(n=[NaN,NaN]),this._dataMin=n[0],this._dataMax=n[1];var i=this._isOrdinal="ordinal"===t.type,r=(this._needCrossZero="interval"===t.type&&e.getNeedCrossZero&&e.getNeedCrossZero(),e.get("min",!0)),r=(null==r&&(r=e.get("startValue",!0)),this._modelMinRaw=r),r=(I(r)?this._modelMinNum=Nv(t,r({min:n[0],max:n[1]})):"dataMin"!==r&&(this._modelMinNum=Nv(t,r)),this._modelMaxRaw=e.get("max",!0));I(r)?this._modelMaxNum=Nv(t,r({min:n[0],max:n[1]})):"dataMax"!==r&&(this._modelMaxNum=Nv(t,r)),i?this._axisDataLen=e.getCategories().length:"boolean"==typeof(t=F(n=e.get("boundaryGap"))?n:[n||0,n||0])[0]||"boolean"==typeof t[1]?this._boundaryGapInner=[0,0]:this._boundaryGapInner=[Ir(t[0],1),Ir(t[1],1)]},Pv.prototype.calculate=function(){var t=this._isOrdinal,e=this._dataMin,n=this._dataMax,i=this._axisDataLen,r=this._boundaryGapInner,o=t?null:n-e||Math.abs(e),a="dataMin"===this._modelMinRaw?e:this._modelMinNum,s="dataMax"===this._modelMaxRaw?n:this._modelMaxNum,l=null!=a,u=null!=s,e=(null==a&&(a=t?i?0:NaN:e-r[0]*o),null==s&&(s=t?i?i-1:NaN:n+r[1]*o),null!=a&&isFinite(a)||(a=NaN),null!=s&&isFinite(s)||(s=NaN),bt(a)||bt(s)||t&&!i),n=(this._needCrossZero&&(a=0<a&&0<s&&!l?0:a)<0&&s<0&&!u&&(s=0),this._determinedMin),r=this._determinedMax;return null!=n&&(a=n,l=!0),null!=r&&(s=r,u=!0),{min:a,max:s,minFixed:l,maxFixed:u,isBlank:e}},Pv.prototype.modifyDataMinMax=function(t,e){this[Rv[t]]=e},Pv.prototype.setDeterminedMinMax=function(t,e){this[Ov[t]]=e},Pv.prototype.freeze=function(){this.frozen=!0};var Av=Pv;function Pv(t,e,n){this._prepareParams(t,e,n)}var Ov={min:"_determinedMin",max:"_determinedMax"},Rv={min:"_dataMin",max:"_dataMax"};function Nv(t,e){return null==e?null:bt(e)?NaN:t.parse(e)}function Ev(t,e){var n,i,r,o,a,s=t.type,l=(l=e,u=(h=t).getExtent(),(c=h.rawExtentInfo)||(c=new Av(h,l,u),h.rawExtentInfo=c),c.calculate()),u=(t.setBlank(l.isBlank),l.min),h=l.max,c=e.ecModel;return c&&"time"===s&&(t=dv("bar",c),n=!1,O(t,function(t){n=n||t.getBaseAxis()===e.axis}),n)&&(s=fv(t),c=u,t=h,s=s,a=(a=(i=e).axis.getExtent())[1]-a[0],void 0!==(s=function(t,e,n){if(t&&e)return null!=(t=t[pv(e)])&&null!=n?t[cv(n)]:t}(s,i.axis))&&(r=1/0,O(s,function(t){r=Math.min(t.offset,r)}),o=-1/0,O(s,function(t){o=Math.max(t.offset+t.width,o)}),r=Math.abs(r),o=Math.abs(o),t+=o/(i=r+o)*(a=(s=t-c)/(1-(r+o)/a)-s),c-=r/i*a),u=(s={min:c,max:t}).min,h=s.max),{extent:[u,h],fixMin:l.minFixed,fixMax:l.maxFixed}}function Bv(r){var o,e,n,t=r.getLabelModel().get("formatter"),a="category"===r.type?r.scale.getExtent()[0]:null;return"time"===r.scale.type?(n=t,function(t,e){return r.scale.getFormattedLabel(t,e,n)}):V(t)?(e=t,function(t){t=r.scale.getLabel(t);return e.replace("{value}",null!=t?t:"")}):I(t)?(o=t,function(t,e){return null!=a&&(e=t.value-a),o((i=t,"category"===(n=r).type?n.scale.getLabel(i):i.value),e,null!=t.level?{level:t.level}:null);var n,i}):function(t){return r.scale.getLabel(t)}}function zv(t){t=t.get("interval");return null==t?"auto":t}function Fv(t){return"category"===t.type&&0===zv(t.getLabelModel())}Hv.prototype.getNeedCrossZero=function(){return!this.option.scale},Hv.prototype.getCoordSysModel=function(){};var Vv=Hv;function Hv(){}var py=Object.freeze({__proto__:null,createDimensions:function(t,e){return zm(t,e).dimensions},createList:function(t){return Um(null,t)},createScale:function(t,e){var n,i,r,o,a,s=e;return(e=function(t,e){if(e=e||t.get("type"))switch(e){case"category":return new ov({ordinalMeta:t.getOrdinalMeta?t.getOrdinalMeta():t.getCategories(),extent:[1/0,-1/0]});case"time":return new yv({locale:t.ecModel.getLocaleModel(),useUTC:t.ecModel.get("useUTC")});default:return new(qm.getClass(e)||uv)}}(s=e instanceof kc?s:new kc(e))).setExtent(t[0],t[1]),n=Ev(t=e,s=s),i=n.extent,r=s.get("splitNumber"),t instanceof Iv&&(t.base=s.get("logBase")),o=t.type,a=s.get("interval"),o="interval"===o||"time"===o,t.setExtent(i[0],i[1]),t.calcNiceExtent({splitNumber:r,fixMin:n.fixMin,fixMax:n.fixMax,minInterval:o?s.get("minInterval"):null,maxInterval:o?s.get("maxInterval"):null}),null!=a&&t.setInterval&&t.setInterval(a),e},createSymbol:vy,createTextStyle:function(t,e){return sc(t,null,null,"normal"!==(e=e||{}).state)},dataStack:{isDimensionStacked:Gm,enableDataStack:Wm,getStackedDimension:function(t,e){return Gm(t,e)?t.getCalculationInfo("stackResultDimension"):e}},enableHoverEmphasis:Pl,getECData:qs,getLayoutRect:wp,mixinAxisModelCommonMethods:function(t){at(t,Vv)}}),Wv=[],Gv={registerPreprocessor:Z0,registerProcessor:K0,registerPostInit:$0,registerPostUpdate:Q0,registerUpdateLifecycle:J0,registerAction:tm,registerCoordinateSystem:em,registerLayout:nm,registerVisual:im,registerTransform:lm,registerLoading:am,registerMap:sm,registerImpl:function(t,e){Ky[t]=e},PRIORITY:cy,ComponentModel:g,ComponentView:mg,SeriesModel:ug,ChartView:bg,registerComponentModel:function(t){g.registerClass(t)},registerComponentView:function(t){mg.registerClass(t)},registerSeriesModel:function(t){ug.registerClass(t)},registerChartView:function(t){bg.registerClass(t)},registerSubTypeDefaulter:function(t,e){g.registerSubTypeDefaulter(t,e)},registerPainter:function(t,e){Zr(t,e)}};function Uv(t){F(t)?O(t,function(t){Uv(t)}):0<=k(Wv,t)||(Wv.push(t),(t=I(t)?{install:t}:t).install(Gv))}var qv=1e-8;function Xv(t,e){return Math.abs(t-e)<qv}function Yv(t,e,n){var i=0,r=t[0];if(r){for(var o=1;o<t.length;o++){var a=t[o];i+=Qa(r[0],r[1],a[0],a[1],e,n),r=a}var s=t[0];return Xv(r[0],s[0])&&Xv(r[1],s[1])||(i+=Qa(r[0],r[1],s[0],s[1],e,n)),0!==i}}var jv=[];function Zv(t,e){for(var n=0;n<t.length;n++)ie(t[n],t[n],e)}function Kv(t,e,n,i){for(var r=0;r<t.length;r++){var o=t[r];(o=i?i.project(o):o)&&isFinite(o[0])&&isFinite(o[1])&&(re(e,e,o),oe(n,n,o))}}$v.prototype.setCenter=function(t){this._center=t},$v.prototype.getCenter=function(){return this._center||(this._center=this.calcCenter())};dy=$v;function $v(t){this.name=t}var Qv,Jv,t_=function(t,e){this.type="polygon",this.exterior=t,this.interiors=e},e_=function(t){this.type="linestring",this.points=t},n_=(a(i_,Qv=dy),i_.prototype.calcCenter=function(){for(var t,e=this.geometries,n=0,i=0;i<e.length;i++){var r=e[i],o=r.exterior,o=o&&o.length;n<o&&(t=r,n=o)}if(t){for(var a=t.exterior,s=0,l=0,u=0,h=a.length,c=a[h-1][0],p=a[h-1][1],d=0;d<h;d++){var f=a[d][0],g=a[d][1],y=c*g-f*p;s+=y,l+=(c+f)*y,u+=(p+g)*y,c=f,p=g}return s?[l/s/3,u/s/3,s]:[a[0][0]||0,a[0][1]||0]}var m=this.getBoundingRect();return[m.x+m.width/2,m.y+m.height/2]},i_.prototype.getBoundingRect=function(e){var n,i,t=this._rect;return t&&!e||(n=[1/0,1/0],i=[-1/0,-1/0],O(this.geometries,function(t){"polygon"===t.type?Kv(t.exterior,n,i,e):O(t.points,function(t){Kv(t,n,i,e)})}),isFinite(n[0])&&isFinite(n[1])&&isFinite(i[0])&&isFinite(i[1])||(n[0]=n[1]=i[0]=i[1]=0),t=new q(n[0],n[1],i[0]-n[0],i[1]-n[1]),e)||(this._rect=t),t},i_.prototype.contain=function(t){var e=this.getBoundingRect(),n=this.geometries;if(e.contain(t[0],t[1]))t:for(var i=0,r=n.length;i<r;i++){var o=n[i];if("polygon"===o.type){var a=o.exterior,s=o.interiors;if(Yv(a,t[0],t[1])){for(var l=0;l<(s?s.length:0);l++)if(Yv(s[l],t[0],t[1]))continue t;return!0}}}return!1},i_.prototype.transformTo=function(t,e,n,i){for(var r=this.getBoundingRect(),o=r.width/r.height,o=(n?i=i||n/o:n=o*i,new q(t,e,n,i)),a=r.calculateTransform(o),s=this.geometries,l=0;l<s.length;l++){var u=s[l];"polygon"===u.type?(Zv(u.exterior,a),O(u.interiors,function(t){Zv(t,a)})):O(u.points,function(t){Zv(t,a)})}(r=this._rect).copy(o),this._center=[r.x+r.width/2,r.y+r.height/2]},i_.prototype.cloneShallow=function(t){t=new i_(t=null==t?this.name:t,this.geometries,this._center);return t._rect=this._rect,t.transformTo=null,t},i_);function i_(t,e,n){t=Qv.call(this,t)||this;return t.type="geoJSON",t.geometries=e,t._center=n&&[n[0],n[1]],t}function r_(t,e){t=Jv.call(this,t)||this;return t.type="geoSVG",t._elOnlyForCalculate=e,t}function o_(t,e,n){for(var i=0;i<t.length;i++)t[i]=a_(t[i],e[i],n)}function a_(t,e,n){for(var i=[],r=e[0],o=e[1],a=0;a<t.length;a+=2){var s=(s=t.charCodeAt(a)-64)>>1^-(1&s),l=(l=t.charCodeAt(a+1)-64)>>1^-(1&l);i.push([(r=s+=r)/n,(o=l+=o)/n])}return i}function s_(t,o){var e,n,r;return z(ut((t=(e=t).UTF8Encoding?(null==(r=(n=e).UTF8Scale)&&(r=1024),O(n.features,function(t){var e=t.geometry,n=e.encodeOffsets,i=e.coordinates;if(n)switch(e.type){case"LineString":e.coordinates=a_(i,n,r);break;case"Polygon":case"MultiLineString":o_(i,n,r);break;case"MultiPolygon":O(i,function(t,e){return o_(t,n[e],r)})}}),n.UTF8Encoding=!1,n):e).features,function(t){return t.geometry&&t.properties&&0<t.geometry.coordinates.length}),function(t){var e=t.properties,n=t.geometry,i=[];switch(n.type){case"Polygon":var r=n.coordinates;i.push(new t_(r[0],r.slice(1)));break;case"MultiPolygon":O(n.coordinates,function(t){t[0]&&i.push(new t_(t[0],t.slice(1)))});break;case"LineString":i.push(new e_([n.coordinates]));break;case"MultiLineString":i.push(new e_(n.coordinates))}t=new n_(e[o||"name"],i,e.cp);return t.properties=e,t})}a(r_,Jv=dy),r_.prototype.calcCenter=function(){for(var t=this._elOnlyForCalculate,e=t.getBoundingRect(),e=[e.x+e.width/2,e.y+e.height/2],n=Re(jv),i=t;i&&!i.isGeoSVGGraphicRoot;)Ee(n,i.getLocalTransform(),n),i=i.parent;return Ve(n,n),ie(e,e,n),e};var Zo=Object.freeze({__proto__:null,MAX_SAFE_INTEGER:9007199254740991,asc:function(t){return t.sort(function(t,e){return t-e}),t},getPercentWithPrecision:function(t,e,n){return t[e]&&function(t,e){var n=lt(t,function(t,e){return t+(isNaN(e)?0:e)},0);if(0===n)return[];var i=Math.pow(10,e),e=z(t,function(t){return(isNaN(t)?0:t)/n*i*100}),r=100*i,o=z(e,function(t){return Math.floor(t)}),a=lt(o,function(t,e){return t+e},0),s=z(e,function(t,e){return t-o[e]});for(;a<r;){for(var l=Number.NEGATIVE_INFINITY,u=null,h=0,c=s.length;h<c;++h)s[h]>l&&(l=s[h],u=h);++o[u],s[u]=0,++a}return z(o,function(t){return t/i})}(t,n)[e]||0},getPixelPrecision:ro,getPrecision:no,getPrecisionSafe:io,isNumeric:fo,isRadianAroundZero:ao,linearMap:Jr,nice:co,numericToNumber:po,parseDate:lo,quantile:function(t,e){var e=(t.length-1)*e+1,n=Math.floor(e),i=+t[n-1];return(e=e-n)?i+e*(t[n]-i):i},quantity:uo,quantityExponent:ho,reformIntervals:function(t){t.sort(function(t,e){return function t(e,n,i){return e.interval[i]<n.interval[i]||e.interval[i]===n.interval[i]&&(e.close[i]-n.close[i]==(i?-1:1)||!i&&t(e,n,1))}(t,e,0)?-1:1});for(var e=-1/0,n=1,i=0;i<t.length;){for(var r=t[i].interval,o=t[i].close,a=0;a<2;a++)r[a]<=e&&(r[a]=e,o[a]=a?1:1-n),e=r[a],n=o[a];r[0]===r[1]&&o[0]*o[1]!=1?t.splice(i,1):i++}return t},remRadian:oo,round:eo}),Xh=Object.freeze({__proto__:null,format:Zc,parse:lo}),Gc=Object.freeze({__proto__:null,Arc:ih,BezierCurve:Ju,BoundingRect:q,Circle:lu,CompoundPath:ah,Ellipse:pu,Group:Wr,Image:_s,IncrementalDisplayable:n,Line:Yu,LinearGradient:ch,Polygon:zu,Polyline:Wu,RadialGradient:lh,Rect:Is,Ring:Ou,Sector:Du,Text:Os,clipPointsByRect:Kh,clipRectByRect:$h,createIcon:Qh,extendPath:Bh,extendShape:Nh,getShapeClass:Fh,getTransform:Yh,initProps:kh,makeImage:Hh,makePath:Vh,mergePath:Gh,registerShape:zh,resizePath:Uh,updateProps:Ch}),bc=Object.freeze({__proto__:null,addCommas:cp,capitalFirst:function(t){return t&&t.charAt(0).toUpperCase()+t.substr(1)},encodeHTML:xe,formatTime:function(t,e,n){"week"!==t&&"month"!==t&&"quarter"!==t&&"half-year"!==t&&"year"!==t||(t="MM-dd\nyyyy");var i=(e=lo(e))[(n=n?"getUTC":"get")+"FullYear"](),r=e[n+"Month"]()+1,o=e[n+"Date"](),a=e[n+"Hours"](),s=e[n+"Minutes"](),l=e[n+"Seconds"](),e=e[n+"Milliseconds"]();return t=t.replace("MM",Yc(r,2)).replace("M",r).replace("yyyy",i).replace("yy",Yc(i%100+"",2)).replace("dd",Yc(o,2)).replace("d",o).replace("hh",Yc(a,2)).replace("h",a).replace("mm",Yc(s,2)).replace("m",s).replace("ss",Yc(l,2)).replace("s",l).replace("SSS",Yc(e,3))},formatTpl:gp,getTextRect:function(t,e,n,i,r,o,a,s){return new Os({style:{text:t,font:e,align:n,verticalAlign:i,padding:r,rich:o,overflow:a?"truncate":null,lineHeight:s}}).getBoundingRect()},getTooltipMarker:function(t,e){var n=(t=V(t)?{color:t,extraCssText:e}:t||{}).color,i=t.type,r=(e=t.extraCssText,t.renderMode||"html");return n?"html"===r?"subItem"===i?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+xe(n)+";"+(e||"")+'"></span>':'<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:'+xe(n)+";"+(e||"")+'"></span>':{renderMode:r,content:"{"+(t.markerId||"markerX")+"|}  ",style:"subItem"===i?{width:4,height:4,borderRadius:2,backgroundColor:n}:{width:10,height:10,borderRadius:5,backgroundColor:n}}:""},normalizeCssArray:dp,toCamelCase:function(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,function(t,e){return e.toUpperCase()}),t=e?t&&t.charAt(0).toUpperCase()+t.slice(1):t},truncateText:na}),_c=Object.freeze({__proto__:null,bind:pt,clone:b,curry:dt,defaults:B,each:O,extend:P,filter:ut,indexOf:k,inherits:ot,isArray:F,isFunction:I,isObject:R,isString:V,map:z,merge:d,reduce:lt}),l_=Lo();function u_(e,t){t=z(t,function(t){return e.scale.parse(t)});return"time"===e.type&&0<t.length&&(t.sort(),t.unshift(t[0]),t.push(t[t.length-1])),t}function h_(n){var i,r,o,t,e,a=n.getLabelModel().get("customValues");return a?(i=Bv(n),{labels:u_(n,a).map(function(t){var e={value:t};return{formattedLabel:i(e),rawLabel:n.scale.getLabel(e),tickValue:t}})}):"category"===n.type?(t=(a=n).getLabelModel(),e=p_(a,t),!t.get("show")||a.scale.isBlank()?{labels:[],labelCategoryInterval:e.labelCategoryInterval}:e):(t=(r=n).scale.getTicks(),o=Bv(r),{labels:z(t,function(t,e){return{level:t.level,formattedLabel:o(t,e),rawLabel:r.scale.getLabel(t),tickValue:t.value}})})}function c_(t,e){var n,i,r,o,a,s=t.getTickModel().get("customValues");return s?{ticks:u_(t,s)}:"category"===t.type?(s=e,r=d_(e=t,"ticks"),o=zv(s),(a=f_(r,o))||(s.get("show")&&!e.scale.isBlank()||(n=[]),n=I(o)?m_(e,o,!0):"auto"===o?(a=p_(e,e.getLabelModel()),i=a.labelCategoryInterval,z(a.labels,function(t){return t.tickValue})):y_(e,i=o,!0),g_(r,o,{ticks:n,tickCategoryInterval:i}))):{ticks:z(t.scale.getTicks(),function(t){return t.value})}}function p_(t,e){var n,i=d_(t,"labels"),e=zv(e),r=f_(i,e);return r||g_(i,e,{labels:I(e)?m_(t,e):y_(t,n="auto"===e?null!=(i=l_(r=t).autoInterval)?i:l_(r).autoInterval=r.calculateCategoryInterval():e),labelCategoryInterval:n})}function d_(t,e){return l_(t)[e]||(l_(t)[e]=[])}function f_(t,e){for(var n=0;n<t.length;n++)if(t[n].key===e)return t[n].value}function g_(t,e,n){return t.push({key:e,value:n}),n}function y_(t,e,n){for(var i=Bv(t),r=t.scale,o=r.getExtent(),a=t.getLabelModel(),s=[],l=Math.max((e||0)+1,1),e=o[0],u=r.count(),u=(0!==e&&1<l&&2<u/l&&(e=Math.round(Math.ceil(e/l)*l)),Fv(t)),t=a.get("showMinLabel")||u,a=a.get("showMaxLabel")||u,h=(t&&e!==o[0]&&c(o[0]),e);h<=o[1];h+=l)c(h);function c(t){var e={value:t};s.push(n?t:{formattedLabel:i(e),rawLabel:r.getLabel(e),tickValue:t})}return a&&h-l!==o[1]&&c(o[1]),s}function m_(t,i,r){var o=t.scale,a=Bv(t),s=[];return O(o.getTicks(),function(t){var e=o.getLabel(t),n=t.value;i(t.value,e)&&s.push(r?n:{formattedLabel:a(t),rawLabel:e,tickValue:n})}),s}var v_=[0,1],Mc=(__.prototype.contain=function(t){var e=this._extent,n=Math.min(e[0],e[1]),e=Math.max(e[0],e[1]);return n<=t&&t<=e},__.prototype.containData=function(t){return this.scale.contain(t)},__.prototype.getExtent=function(){return this._extent.slice()},__.prototype.getPixelPrecision=function(t){return ro(t||this.scale.getExtent(),this._extent)},__.prototype.setExtent=function(t,e){var n=this._extent;n[0]=t,n[1]=e},__.prototype.dataToCoord=function(t,e){var n=this._extent,i=this.scale;return t=i.normalize(t),this.onBand&&"ordinal"===i.type&&x_(n=n.slice(),i.count()),Jr(t,v_,n,e)},__.prototype.coordToData=function(t,e){var n=this._extent,i=this.scale,i=(this.onBand&&"ordinal"===i.type&&x_(n=n.slice(),i.count()),Jr(t,n,v_,e));return this.scale.scale(i)},__.prototype.pointToData=function(t,e){},__.prototype.getTicksCoords=function(t){var e,n,i,r,o,a,s,l=(t=t||{}).tickModel||this.getTickModel(),u=z(c_(this,l).ticks,function(t){return{coord:this.dataToCoord("ordinal"===this.scale.type?this.scale.getRawOrdinalNumber(t):t),tickValue:t}},this),l=l.get("alignWithLabel");function h(t,e){return t=eo(t),e=eo(e),a?e<t:t<e}return e=this,n=u,l=l,t=t.clamp,s=n.length,e.onBand&&!l&&s&&(l=e.getExtent(),1===s?(n[0].coord=l[0],i=n[1]={coord:l[1]}):(o=n[s-1].tickValue-n[0].tickValue,r=(n[s-1].coord-n[0].coord)/o,O(n,function(t){t.coord-=r/2}),e=1+(o=e.scale.getExtent())[1]-n[s-1].tickValue,i={coord:n[s-1].coord+r*e},n.push(i)),a=l[0]>l[1],h(n[0].coord,l[0])&&(t?n[0].coord=l[0]:n.shift()),t&&h(l[0],n[0].coord)&&n.unshift({coord:l[0]}),h(l[1],i.coord)&&(t?i.coord=l[1]:n.pop()),t)&&h(i.coord,l[1])&&n.push({coord:l[1]}),u},__.prototype.getMinorTicksCoords=function(){var t;return"ordinal"===this.scale.type?[]:(t=this.model.getModel("minorTick").get("splitNumber"),z(this.scale.getMinorTicks(t=0<t&&t<100?t:5),function(t){return z(t,function(t){return{coord:this.dataToCoord(t),tickValue:t}},this)},this))},__.prototype.getViewLabels=function(){return h_(this).labels},__.prototype.getLabelModel=function(){return this.model.getModel("axisLabel")},__.prototype.getTickModel=function(){return this.model.getModel("axisTick")},__.prototype.getBandWidth=function(){var t=this._extent,e=this.scale.getExtent(),e=e[1]-e[0]+(this.onBand?1:0),t=(0===e&&(e=1),Math.abs(t[1]-t[0]));return Math.abs(t)/e},__.prototype.calculateCategoryInterval=function(){r=(n=d=this).getLabelModel();var t={axisRotate:n.getRotate?n.getRotate():n.isHorizontal&&!n.isHorizontal()?90:0,labelRotate:r.get("rotate")||0,font:r.getFont()},e=Bv(d),n=(t.axisRotate-t.labelRotate)/180*Math.PI,i=(r=d.scale).getExtent(),r=r.count();if(i[1]-i[0]<1)return 0;for(var o=1,a=(40<r&&(o=Math.max(1,Math.floor(r/40))),i[0]),s=d.dataToCoord(a+1)-d.dataToCoord(a),l=Math.abs(s*Math.cos(n)),s=Math.abs(s*Math.sin(n)),u=0,h=0;a<=i[1];a+=o)var c=1.3*(p=Mr(e({value:a}),t.font,"center","top")).width,p=1.3*p.height,u=Math.max(u,c,7),h=Math.max(h,p,7);var n=u/l,l=h/s,s=(isNaN(n)&&(n=1/0),isNaN(l)&&(l=1/0),Math.max(0,Math.floor(Math.min(n,l)))),n=l_(d.model),l=d.getExtent(),d=n.lastAutoInterval,f=n.lastTickCount;return null!=d&&null!=f&&Math.abs(d-s)<=1&&Math.abs(f-r)<=1&&s<d&&n.axisExtent0===l[0]&&n.axisExtent1===l[1]?s=d:(n.lastTickCount=r,n.lastAutoInterval=s,n.axisExtent0=l[0],n.axisExtent1=l[1]),s},__);function __(t,e,n){this.onBand=!1,this.inverse=!1,this.dim=t,this.scale=e,this._extent=n||[0,0]}function x_(t,e){e=(t[1]-t[0])/e/2;t[0]+=e,t[1]-=e}var w_=2*Math.PI,b_=Ya.CMD,S_=["top","right","bottom","left"];function M_(t,e,n,i,r,o,a,s){var l=r-t,u=o-e,n=n-t,i=i-e,h=Math.sqrt(n*n+i*i),l=(l*(n/=h)+u*(i/=h))/h,u=(s&&(l=Math.min(Math.max(l,0),1)),a[0]=t+(l*=h)*n),s=a[1]=e+l*i;return Math.sqrt((u-r)*(u-r)+(s-o)*(s-o))}function T_(t,e,n,i,r,o,a){n<0&&(t+=n,n=-n),i<0&&(e+=i,i=-i);n=t+n,i=e+i,t=a[0]=Math.min(Math.max(r,t),n),n=a[1]=Math.min(Math.max(o,e),i);return Math.sqrt((t-r)*(t-r)+(n-o)*(n-o))}var C_=[];function k_(t,e,n){for(var i,r,o,a,s,l,u,h,c,p=0,d=0,f=0,g=0,y=1/0,m=e.data,v=t.x,_=t.y,x=0;x<m.length;){var w=m[x++],b=(1===x&&(f=p=m[x],g=d=m[x+1]),y);switch(w){case b_.M:p=f=m[x++],d=g=m[x++];break;case b_.L:b=M_(p,d,m[x],m[x+1],v,_,C_,!0),p=m[x++],d=m[x++];break;case b_.C:b=Gn(p,d,m[x++],m[x++],m[x++],m[x++],m[x],m[x+1],v,_,C_),p=m[x++],d=m[x++];break;case b_.Q:b=jn(p,d,m[x++],m[x++],m[x],m[x+1],v,_,C_),p=m[x++],d=m[x++];break;case b_.A:var S=m[x++],M=m[x++],T=m[x++],C=m[x++],k=m[x++],I=m[x++],D=(x+=1,!!(1-m[x++])),L=Math.cos(k)*T+S,A=Math.sin(k)*C+M;x<=1&&(f=L,g=A),A=(L=k)+I,D=D,a=(v-S)*(o=C)/T+S,s=_,l=C_,c=h=u=void 0,a-=i=S,s-=r=M,u=Math.sqrt(a*a+s*s),h=(a/=u)*o+i,c=(s/=u)*o+r,b=Math.abs(L-A)%w_<1e-4||((A=D?(D=L,L=Ka(A),Ka(D)):(L=Ka(L),Ka(A)))<L&&(A+=w_),(D=Math.atan2(s,a))<0&&(D+=w_),L<=D&&D<=A)||L<=D+w_&&D+w_<=A?(l[0]=h,l[1]=c,u-o):(c=((D=o*Math.cos(L)+i)-a)*(D-a)+((h=o*Math.sin(L)+r)-s)*(h-s))<(i=((u=o*Math.cos(A)+i)-a)*(u-a)+((L=o*Math.sin(A)+r)-s)*(L-s))?(l[0]=D,l[1]=h,Math.sqrt(c)):(l[0]=u,l[1]=L,Math.sqrt(i)),p=Math.cos(k+I)*T+S,d=Math.sin(k+I)*C+M;break;case b_.R:b=T_(f=p=m[x++],g=d=m[x++],m[x++],m[x++],v,_,C_);break;case b_.Z:b=M_(p,d,f,g,v,_,C_,!0),p=f,d=g}b<y&&(y=b,n.set(C_[0],C_[1]))}return y}var I_=new M,D_=new M,L_=new M,A_=new M,P_=new M;function O_(t,e){if(t){var n=t.getTextGuideLine(),i=t.getTextContent();if(i&&n){var r=t.textGuideLineConfig||{},o=[[0,0],[0,0],[0,0]],a=r.candidates||S_,s=i.getBoundingRect().clone(),l=(s.applyTransform(i.getComputedTransform()),1/0),u=r.anchor,h=t.getComputedTransform(),c=h&&Ve([],h),p=e.get("length2")||0;u&&L_.copy(u);for(var d,f,g=0;g<a.length;g++){var y=a[g],m=(S=b=w=x=_=v=m=void 0,y),v=0,_=s,x=I_,w=A_,b=_.width,S=_.height;switch(m){case"top":x.set(_.x+b/2,_.y-v),w.set(0,-1);break;case"bottom":x.set(_.x+b/2,_.y+S+v),w.set(0,1);break;case"left":x.set(_.x-v,_.y+S/2),w.set(-1,0);break;case"right":x.set(_.x+b+v,_.y+S/2),w.set(1,0)}M.scaleAndAdd(D_,I_,A_,p),D_.transform(c);y=t.getBoundingRect(),y=u?u.distance(D_):t instanceof cs?k_(D_,t.path,L_):(m=L_,d=T_((d=y).x,y.y,y.width,y.height,D_.x,D_.y,C_),m.set(C_[0],C_[1]),d);y<l&&(l=y,D_.transform(h),L_.transform(h),L_.toArray(o[0]),D_.toArray(o[1]),I_.toArray(o[2]))}i=o,(r=e.get("minTurnAngle"))<=180&&0<r&&(r=r/180*Math.PI,I_.fromArray(i[0]),D_.fromArray(i[1]),L_.fromArray(i[2]),M.sub(A_,I_,D_),M.sub(P_,L_,D_),e=A_.len(),f=P_.len(),e<.001||f<.001||(A_.scale(1/e),P_.scale(1/f),e=A_.dot(P_),Math.cos(r)<e&&(f=M_(D_.x,D_.y,L_.x,L_.y,I_.x,I_.y,R_,!1),N_.fromArray(R_),N_.scaleAndAdd(P_,f/Math.tan(Math.PI-r)),e=L_.x!==D_.x?(N_.x-D_.x)/(L_.x-D_.x):(N_.y-D_.y)/(L_.y-D_.y),isNaN(e)||(e<0?M.copy(N_,D_):1<e&&M.copy(N_,L_),N_.toArray(i[1]))))),n.setShape({points:o})}}}var R_=[],N_=new M;function E_(t,e,n,i){var r="normal"===n,n=r?t:t.ensureState(n),e=(n.ignore=e,i.get("smooth")),e=(e&&!0===e&&(e=.3),n.shape=n.shape||{},0<e&&(n.shape.smooth=e),i.getModel("lineStyle").getLineStyle());r?t.useStyle(e):n.style=e}function B_(t,e){var n=e.smooth,i=e.points;if(i)if(t.moveTo(i[0][0],i[0][1]),0<n&&3<=i.length){var e=Jt(i[0],i[1]),r=Jt(i[1],i[2]);e&&r?(n=Math.min(e,r)*n,e=ne([],i[1],i[0],n/e),n=ne([],i[1],i[2],n/r),r=ne([],e,n,.5),t.bezierCurveTo(e[0],e[1],e[0],e[1],r[0],r[1]),t.bezierCurveTo(n[0],n[1],n[0],n[1],i[2][0],i[2][1])):(t.lineTo(i[1][0],i[1][1]),t.lineTo(i[2][0],i[2][1]))}else for(var o=1;o<i.length;o++)t.lineTo(i[o][0],i[o][1])}function z_(t){for(var e=[],n=0;n<t.length;n++){var i,r,o,a,s,l,u=t[n];u.defaultAttr.ignore||(r=(i=u.label).getComputedTransform(),o=i.getBoundingRect(),a=!r||r[1]<1e-5&&r[2]<1e-5,l=i.style.margin||0,(s=o.clone()).applyTransform(r),s.x-=l/2,s.y-=l/2,s.width+=l,s.height+=l,l=a?new _h(o,r):null,e.push({label:i,labelLine:u.labelLine,rect:s,localRect:o,obb:l,priority:u.priority,defaultAttr:u.defaultAttr,layoutOption:u.computedLayoutOption,axisAligned:a,transform:r}))}return e}function F_(s,l,u,t,e,n){var h=s.length;if(!(h<2)){s.sort(function(t,e){return t.rect[l]-e.rect[l]});for(var i=0,o=!1,r=0,a=0;a<h;a++){var c,p=s[a],d=p.rect;(c=d[l]-i)<0&&(d[l]-=c,p.label[l]-=c,o=!0),r+=Math.max(-c,0),i=d[l]+d[u]}0<r&&n&&x(-r/h,0,h);var f,g,y=s[0],m=s[h-1];return v(),f<0&&w(-f,.8),g<0&&w(g,.8),v(),_(f,g,1),_(g,f,-1),v(),f<0&&b(-f),g<0&&b(g),o}function v(){f=y.rect[l]-t,g=e-m.rect[l]-m.rect[u]}function _(t,e,n){t<0&&(0<(e=Math.min(e,-t))?(x(e*n,0,h),(e=e+t)<0&&w(-e*n,1)):w(-t*n,1))}function x(t,e,n){0!==t&&(o=!0);for(var i=e;i<n;i++){var r=s[i];r.rect[l]+=t,r.label[l]+=t}}function w(t,e){for(var n=[],i=0,r=1;r<h;r++){var o=s[r-1].rect,o=Math.max(s[r].rect[l]-o[l]-o[u],0);n.push(o),i+=o}if(i){var a=Math.min(Math.abs(t)/i,e);if(0<t)for(r=0;r<h-1;r++)x(n[r]*a,0,r+1);else for(r=h-1;0<r;r--)x(-(n[r-1]*a),r,h)}}function b(t){for(var e=t<0?-1:1,n=(t=Math.abs(t),Math.ceil(t/(h-1))),i=0;i<h-1;i++)if(0<e?x(n,0,i+1):x(-n,h-i-1,h),(t-=n)<=0)return}}function V_(t){var e=[],n=(t.sort(function(t,e){return e.priority-t.priority}),new q(0,0,0,0));function i(t){var e;t.ignore||null==(e=t.ensureState("emphasis")).ignore&&(e.ignore=!1),t.ignore=!0}for(var r=0;r<t.length;r++){for(var o=t[r],a=o.axisAligned,s=o.localRect,l=o.transform,u=o.label,h=o.labelLine,c=(n.copy(o.rect),n.width-=.1,n.height-=.1,n.x+=.05,n.y+=.05,o.obb),p=!1,d=0;d<e.length;d++){var f=e[d];if(n.intersect(f.rect)){if(a&&f.axisAligned){p=!0;break}if(f.obb||(f.obb=new _h(f.localRect,f.transform)),(c=c||new _h(s,l)).intersect(f.obb)){p=!0;break}}}p?(i(u),h&&i(h)):(u.attr("ignore",o.defaultAttr.ignore),h&&h.attr("ignore",o.defaultAttr.labelGuideIgnore),e.push(o))}}function H_(t,e){var n=t.label,e=e&&e.getTextGuideLine();return{dataIndex:t.dataIndex,dataType:t.dataType,seriesIndex:t.seriesModel.seriesIndex,text:t.label.style.text,rect:t.hostRect,labelRect:t.rect,align:n.style.align,verticalAlign:n.style.verticalAlign,labelLinePoints:function(t){if(t){for(var e=[],n=0;n<t.length;n++)e.push(t[n].slice());return e}}(e&&e.shape.points)}}var W_=["align","verticalAlign","width","height","fontSize"],G_=new vr,U_=Lo(),q_=Lo();function X_(t,e,n){for(var i=0;i<n.length;i++){var r=n[i];null!=e[r]&&(t[r]=e[r])}}var Y_=["x","y","rotation"],j_=(Z_.prototype.clearLabels=function(){this._labelList=[],this._chartViewList=[]},Z_.prototype._addLabel=function(t,e,n,i,r){var o,a=i.style,s=i.__hostTarget.textConfig||{},l=i.getComputedTransform(),u=i.getBoundingRect().plain(),l=(q.applyTransform(u,u,l),l?G_.setLocalTransform(l):(G_.x=G_.y=G_.rotation=G_.originX=G_.originY=0,G_.scaleX=G_.scaleY=1),G_.rotation=Ka(G_.rotation),i.__hostTarget),h=(l&&(o=l.getBoundingRect().plain(),h=l.getComputedTransform(),q.applyTransform(o,o,h)),o&&l.getTextGuideLine());this._labelList.push({label:i,labelLine:h,seriesModel:n,dataIndex:t,dataType:e,layoutOption:r,computedLayoutOption:null,rect:u,hostRect:o,priority:o?o.width*o.height:0,defaultAttr:{ignore:i.ignore,labelGuideIgnore:h&&h.ignore,x:G_.x,y:G_.y,scaleX:G_.scaleX,scaleY:G_.scaleY,rotation:G_.rotation,style:{x:a.x,y:a.y,align:a.align,verticalAlign:a.verticalAlign,width:a.width,height:a.height,fontSize:a.fontSize},cursor:i.cursor,attachedPos:s.position,attachedRot:s.rotation}})},Z_.prototype.addLabelsOfSeries=function(t){var n=this,i=(this._chartViewList.push(t),t.__model),r=i.get("labelLayout");(I(r)||ct(r).length)&&t.group.traverse(function(t){if(t.ignore)return!0;var e=t.getTextContent(),t=qs(t);e&&!e.disableLabelLayout&&n._addLabel(t.dataIndex,t.dataType,i,e,r)})},Z_.prototype.updateLayoutConfig=function(t){var e=t.getWidth(),n=t.getHeight();for(var i=0;i<this._labelList.length;i++){var r=this._labelList[i],o=r.label,a=o.__hostTarget,s=r.defaultAttr,l=void 0,l=I(r.layoutOption)?r.layoutOption(H_(r,a)):r.layoutOption,u=(r.computedLayoutOption=l=l||{},Math.PI/180),h=(a&&a.setTextConfig({local:!1,position:null!=l.x||null!=l.y?null:s.attachedPos,rotation:null!=l.rotate?l.rotate*u:s.attachedRot,offset:[l.dx||0,l.dy||0]}),!1);null!=l.x?(o.x=to(l.x,e),o.setStyle("x",0),h=!0):(o.x=s.x,o.setStyle("x",s.style.x)),null!=l.y?(o.y=to(l.y,n),o.setStyle("y",0),h=!0):(o.y=s.y,o.setStyle("y",s.style.y)),l.labelLinePoints&&(c=a.getTextGuideLine())&&(c.setShape({points:l.labelLinePoints}),h=!1),U_(o).needsUpdateLabelLine=h,o.rotation=null!=l.rotate?l.rotate*u:s.rotation,o.scaleX=s.scaleX,o.scaleY=s.scaleY;for(var c,p=0;p<W_.length;p++){var d=W_[p];o.setStyle(d,(null!=l[d]?l:s.style)[d])}l.draggable?(o.draggable=!0,o.cursor="move",a&&(c=r.seriesModel,null!=r.dataIndex&&(c=r.seriesModel.getData(r.dataType).getItemModel(r.dataIndex)),o.on("drag",function(t,e){return function(){O_(t,e)}}(a,c.getModel("labelLine"))))):(o.off("drag"),o.cursor=s.cursor)}},Z_.prototype.layout=function(t){var e,n,i=t.getWidth(),t=t.getHeight(),r=z_(this._labelList),o=ut(r,function(t){return"shiftX"===t.layoutOption.moveOverlap}),a=ut(r,function(t){return"shiftY"===t.layoutOption.moveOverlap});F_(o,"x","width",0,i,e),F_(a,"y","height",0,t,n),V_(ut(r,function(t){return t.layoutOption.hideOverlap}))},Z_.prototype.processLabelsOverall=function(){var a=this;O(this._chartViewList,function(t){var i=t.__model,r=t.ignoreLabelLineUpdate,o=i.isAnimationEnabled();t.group.traverse(function(t){if(t.ignore&&!t.forceLabelAnimation)return!0;var e=!r,n=t.getTextContent();(e=!e&&n?U_(n).needsUpdateLabelLine:e)&&a._updateLabelLine(t,i),o&&a._animateLabels(t,i)})})},Z_.prototype._updateLabelLine=function(t,e){var n=t.getTextContent(),i=qs(t),r=i.dataIndex;if(n&&null!=r){var n=e.getData(i.dataType),e=n.getItemModel(r),i={},r=n.getItemVisual(r,"style"),r=(r&&(n=n.getVisual("drawType"),i.stroke=r[n]),e.getModel("labelLine")),o=t,a=function(t,e){for(var n={normal:t.getModel(e=e||"labelLine")},i=0;i<Js.length;i++){var r=Js[i];n[r]=t.getModel([r,e])}return n}(e),n=i,s=o.getTextGuideLine(),l=o.getTextContent();if(l){for(var e=a.normal,u=e.get("show"),h=l.ignore,c=0;c<tl.length;c++){var p,d=tl[c],f=a[d],g="normal"===d;f&&(p=f.get("show"),(g?h:N(l.states[d]&&l.states[d].ignore,h))||!N(p,u)?((p=g?s:s&&s.states[d])&&(p.ignore=!0),s&&E_(s,!0,d,f)):(s||(s=new Wu,o.setTextGuideLine(s),g||!h&&u||E_(s,!0,"normal",a.normal),o.stateProxy&&(s.stateProxy=o.stateProxy)),E_(s,!1,d,f)))}s&&(B(s.style,n),s.style.fill=null,n=e.get("showAbove"),(o.textGuideLineConfig=o.textGuideLineConfig||{}).showAbove=n||!1,s.buildPath=B_)}else s&&o.removeTextGuideLine();O_(t,r)}},Z_.prototype._animateLabels=function(t,e){var n,i,r,o,a,s=t.getTextContent(),l=t.getTextGuideLine();!s||!t.forceLabelAnimation&&(s.ignore||s.invisible||t.disableLabelAnimation||Ih(t))||(o=(r=U_(s)).oldLayout,n=(i=qs(t)).dataIndex,a={x:s.x,y:s.y,rotation:s.rotation},i=e.getData(i.dataType),o?(s.attr(o),(t=t.prevStates)&&(0<=k(t,"select")&&s.attr(r.oldLayoutSelect),0<=k(t,"emphasis"))&&s.attr(r.oldLayoutEmphasis),Ch(s,a,e,n)):(s.attr(a),dc(s).valueAnimation||(t=N(s.style.opacity,1),s.style.opacity=0,kh(s,{style:{opacity:t}},e,n))),r.oldLayout=a,s.states.select&&(X_(t=r.oldLayoutSelect={},a,Y_),X_(t,s.states.select,Y_)),s.states.emphasis&&(X_(t=r.oldLayoutEmphasis={},a,Y_),X_(t,s.states.emphasis,Y_)),fc(s,n,i,e,e)),!l||l.ignore||l.invisible||(o=(r=q_(l)).oldLayout,a={points:l.shape.points},o?(l.attr({shape:o}),Ch(l,{shape:a},e)):(l.setShape(a),l.style.strokePercent=0,kh(l,{style:{strokePercent:1}},e)),r.oldLayout=a)},Z_);function Z_(){this._labelList=[],this._chartViewList=[]}var K_=Lo();function $_(t){t.registerUpdateLifecycle("series:beforeupdate",function(t,e,n){(K_(e).labelManager||(K_(e).labelManager=new j_)).clearLabels()}),t.registerUpdateLifecycle("series:layoutlabels",function(t,e,n){var i=K_(e).labelManager;n.updatedSeries.forEach(function(t){i.addLabelsOfSeries(e.getViewOfSeriesModel(t))}),i.updateLayoutConfig(e),i.layout(e),i.processLabelsOverall()})}function Q_(t,e,n){var i=H.createCanvas(),r=e.getWidth(),e=e.getHeight(),o=i.style;return o&&(o.position="absolute",o.left="0",o.top="0",o.width=r+"px",o.height=e+"px",i.setAttribute("data-zr-dom-id",t)),i.width=r*n,i.height=e*n,i}Uv($_);a(e1,J_=he),e1.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},e1.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},e1.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},e1.prototype.setUnpainted=function(){this.__firstTimePaint=!0},e1.prototype.createBackBuffer=function(){var t=this.dpr;this.domBack=Q_("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},e1.prototype.createRepaintRects=function(t,e,n,i){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var l=[],u=this.maxRepaintRectCount,h=!1,c=new q(0,0,0,0);function r(t){if(t.isFinite()&&!t.isZero())if(0===l.length)(e=new q(0,0,0,0)).copy(t),l.push(e);else{for(var e,n=!1,i=1/0,r=0,o=0;o<l.length;++o){var a=l[o];if(a.intersect(t)){var s=new q(0,0,0,0);s.copy(a),s.union(t),l[o]=s,n=!0;break}h&&(c.copy(t),c.union(a),s=t.width*t.height,a=a.width*a.height,(a=c.width*c.height-s-a)<i)&&(i=a,r=o)}h&&(l[r].union(t),n=!0),n||((e=new q(0,0,0,0)).copy(t),l.push(e)),h=h||l.length>=u}}for(var o,a=this.__startIndex;a<this.__endIndex;++a)(s=t[a])&&(d=s.shouldBePainted(n,i,!0,!0),(p=s.__isRendered&&(s.__dirty&_n||!d)?s.getPrevPaintRect():null)&&r(p),o=d&&(s.__dirty&_n||!s.__isRendered)?s.getPaintRect():null)&&r(o);for(a=this.__prevStartIndex;a<this.__prevEndIndex;++a){var s,p,d=(s=e[a])&&s.shouldBePainted(n,i,!0,!0);!s||d&&s.__zr||!s.__isRendered||(p=s.getPrevPaintRect())&&r(p)}do{for(var f=!1,a=0;a<l.length;)if(l[a].isZero())l.splice(a,1);else{for(var g=a+1;g<l.length;)l[a].intersect(l[g])?(f=!0,l[a].union(l[g]),l.splice(g,1)):g++;a++}}while(f);return this._paintRects=l},e1.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},e1.prototype.resize=function(t,e){var n=this.dpr,i=this.dom,r=i.style,o=this.domBack;r&&(r.width=t+"px",r.height=e+"px"),i.width=t*n,i.height=e*n,o&&(o.width=t*n,o.height=e*n,1!==n)&&this.ctxBack.scale(n,n)},e1.prototype.clear=function(t,o,e){var n=this.dom,a=this.ctx,i=n.width,r=n.height,s=(o=o||this.clearColor,this.motionBlur&&!t),l=this.lastFrameAlpha,u=this.dpr,h=this,c=(s&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(n,0,0,i/u,r/u)),this.domBack);function p(t,e,n,i){var r;a.clearRect(t,e,n,i),o&&"transparent"!==o&&(r=void 0,_t(o)?(r=(o.global||o.__width===n&&o.__height===i)&&o.__canvasGradient||xy(a,o,{x:0,y:0,width:n,height:i}),o.__canvasGradient=r,o.__width=n,o.__height=i):xt(o)&&(o.scaleX=o.scaleX||u,o.scaleY=o.scaleY||u,r=Ly(a,o,{dirty:function(){h.setUnpainted(),h.painter.refresh()}})),a.save(),a.fillStyle=r||o,a.fillRect(t,e,n,i),a.restore()),s&&(a.save(),a.globalAlpha=l,a.drawImage(c,t,e,n,i),a.restore())}!e||s?p(0,0,i,r):e.length&&O(e,function(t){p(t.x*u,t.y*u,t.width*u,t.height*u)})};var J_,t1=e1;function e1(t,e,n){var i,r=J_.call(this)||this,t=(r.motionBlur=!1,r.lastFrameAlpha=.7,r.dpr=1,r.virtual=!1,r.config={},r.incremental=!1,r.zlevel=0,r.maxRepaintRectCount=5,r.__dirty=!0,r.__firstTimePaint=!0,r.__used=!1,r.__drawIndex=0,r.__startIndex=0,r.__endIndex=0,r.__prevStartIndex=null,r.__prevEndIndex=null,n=n||ur,"string"==typeof t?i=Q_(t,e,n):R(t)&&(t=(i=t).id),r.id=t,(r.dom=i).style);return t&&(Ft(i),i.onselectstart=function(){return!1},t.padding="0",t.margin="0",t.borderWidth="0"),r.painter=e,r.dpr=n,r}var n1=314159;x.prototype.getType=function(){return"canvas"},x.prototype.isSingleCanvas=function(){return this._singleCanvas},x.prototype.getViewportRoot=function(){return this._domRoot},x.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},x.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),n=this._prevDisplayList,i=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,n,t,this._redrawId);for(var r=0;r<i.length;r++){var o,a=i[r],a=this._layers[a];!a.__builtin__&&a.refresh&&(o=0===r?this._backgroundColor:null,a.refresh(o))}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},x.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},x.prototype._paintHoverList=function(t){var e=t.length,n=this._hoverlayer;if(n&&n.clear(),e){for(var i,r={inHover:!0,viewWidth:this._width,viewHeight:this._height},o=0;o<e;o++){var a=t[o];a.__inHover&&(n=n||(this._hoverlayer=this.getLayer(1e5)),i||(i=n.ctx).save(),Gy(i,a,r,o===e-1))}i&&i.restore()}},x.prototype.getHoverLayer=function(){return this.getLayer(1e5)},x.prototype.paintOne=function(t,e){Wy(t,e)},x.prototype._paintList=function(t,e,n,i){var r,o,a;this._redrawId===i&&(n=n||!1,this._updateLayerStatus(t),r=(o=this._doPaintList(t,e,n)).finished,o=o.needsRefreshHover,this._needsManuallyCompositing&&this._compositeManually(),o&&this._paintHoverList(t),r?this.eachLayer(function(t){t.afterBrush&&t.afterBrush()}):(a=this,Cn(function(){a._paintList(t,e,n,i)})))},x.prototype._compositeManually=function(){var e=this.getLayer(n1).ctx,n=this._domRoot.width,i=this._domRoot.height;e.clearRect(0,0,n,i),this.eachBuiltinLayer(function(t){t.virtual&&e.drawImage(t.dom,0,0,n,i)})},x.prototype._doPaintList=function(d,f,g){for(var y=this,m=[],v=this._opts.useDirtyRect,t=0;t<this._zlevelList.length;t++){var e=this._zlevelList[t],e=this._layers[e];e.__builtin__&&e!==this._hoverlayer&&(e.__dirty||g)&&m.push(e)}for(var _=!0,x=!1,n=function(t){function e(t){var e={inHover:!1,allClipped:!1,prevEl:null,viewWidth:y._width,viewHeight:y._height};for(i=s;i<r.__endIndex;i++){var n=d[i];if(n.__inHover&&(x=!0),y._doPaintEl(n,r,v,t,e,i===r.__endIndex-1),l)if(15<Date.now()-u)break}e.prevElClipPaths&&o.restore()}var n,i,r=m[t],o=r.ctx,a=v&&r.createRepaintRects(d,f,w._width,w._height),s=g?r.__startIndex:r.__drawIndex,l=!g&&r.incremental&&Date.now,u=l&&Date.now(),t=r.zlevel===w._zlevelList[0]?w._backgroundColor:null;r.__startIndex!==r.__endIndex&&(s!==r.__startIndex||(n=d[s]).incremental&&n.notClear&&!g)||r.clear(!1,t,a),-1===s&&(console.error("For some unknown reason. drawIndex is -1"),s=r.__startIndex);if(a)if(0===a.length)i=r.__endIndex;else for(var h=w.dpr,c=0;c<a.length;++c){var p=a[c];o.save(),o.beginPath(),o.rect(p.x*h,p.y*h,p.width*h,p.height*h),o.clip(),e(p),o.restore()}else o.save(),e(),o.restore();r.__drawIndex=i,r.__drawIndex<r.__endIndex&&(_=!1)},w=this,i=0;i<m.length;i++)n(i);return p.wxa&&O(this._layers,function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()}),{finished:_,needsRefreshHover:x}},x.prototype._doPaintEl=function(t,e,n,i,r,o){e=e.ctx;n?(n=t.getPaintRect(),(!i||n&&n.intersect(i))&&(Gy(e,t,r,o),t.setPrevPaintRect(n))):Gy(e,t,r,o)},x.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=n1);var n=this._layers[t];return n||((n=new t1("zr_"+t,this,this.dpr)).zlevel=t,n.__builtin__=!0,this._layerConfig[t]?d(n,this._layerConfig[t],!0):this._layerConfig[t-.01]&&d(n,this._layerConfig[t-.01],!0),e&&(n.virtual=e),this.insertLayer(t,n),n.initContext()),n},x.prototype.insertLayer=function(t,e){var n,i=this._layers,r=this._zlevelList,o=r.length,a=this._domRoot,s=null,l=-1;if(!i[t]&&(n=e)&&(n.__builtin__||"function"==typeof n.resize&&"function"==typeof n.refresh)){if(0<o&&t>r[0]){for(l=0;l<o-1&&!(r[l]<t&&r[l+1]>t);l++);s=i[r[l]]}r.splice(l+1,0,t),(i[t]=e).virtual||(s?(n=s.dom).nextSibling?a.insertBefore(e.dom,n.nextSibling):a.appendChild(e.dom):a.firstChild?a.insertBefore(e.dom,a.firstChild):a.appendChild(e.dom)),e.painter||(e.painter=this)}},x.prototype.eachLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i];t.call(e,this._layers[r],r)}},x.prototype.eachBuiltinLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i],o=this._layers[r];o.__builtin__&&t.call(e,o,r)}},x.prototype.eachOtherLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i],o=this._layers[r];o.__builtin__||t.call(e,o,r)}},x.prototype.getLayers=function(){return this._layers},x.prototype._updateLayerStatus=function(t){function e(t){r&&(r.__endIndex!==t&&(r.__dirty=!0),r.__endIndex=t)}if(this.eachBuiltinLayer(function(t,e){t.__dirty=t.__used=!1}),this._singleCanvas)for(var n=1;n<t.length;n++)if((s=t[n]).zlevel!==t[n-1].zlevel||s.incremental){this._needsManuallyCompositing=!0;break}for(var i,r=null,o=0,a=0;a<t.length;a++){var s,l=(s=t[a]).zlevel,u=void 0;i!==l&&(i=l,o=0),s.incremental?((u=this.getLayer(l+.001,this._needsManuallyCompositing)).incremental=!0,o=1):u=this.getLayer(l+(0<o?.01:0),this._needsManuallyCompositing),u.__builtin__||it("ZLevel "+l+" has been used by unkown layer "+u.id),u!==r&&(u.__used=!0,u.__startIndex!==a&&(u.__dirty=!0),u.__startIndex=a,u.incremental?u.__drawIndex=-1:u.__drawIndex=a,e(a),r=u),s.__dirty&_n&&!s.__inHover&&(u.__dirty=!0,u.incremental)&&u.__drawIndex<0&&(u.__drawIndex=a)}e(a),this.eachBuiltinLayer(function(t,e){!t.__used&&0<t.getElementCount()&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)})},x.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},x.prototype._clearLayer=function(t){t.clear()},x.prototype.setBackgroundColor=function(t){this._backgroundColor=t,O(this._layers,function(t){t.setUnpainted()})},x.prototype.configLayer=function(t,e){if(e){var n=this._layerConfig;n[t]?d(n[t],e,!0):n[t]=e;for(var i=0;i<this._zlevelList.length;i++){var r=this._zlevelList[i];r!==t&&r!==t+.01||d(this._layers[r],n[t],!0)}}},x.prototype.delLayer=function(t){var e=this._layers,n=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],n.splice(k(n,t),1))},x.prototype.resize=function(t,e){if(this._domRoot.style){var n=this._domRoot,i=(n.style.display="none",this._opts),r=this.root;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=by(r,0,i),e=by(r,1,i),n.style.display="",this._width!==t||e!==this._height){for(var o in n.style.width=t+"px",n.style.height=e+"px",this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(n1).resize(t,e)}return this},x.prototype.clearLayer=function(t){t=this._layers[t];t&&t.clear()},x.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},x.prototype.getRenderedCanvas=function(t){if(this._singleCanvas&&!this._compositeManually)return this._layers[n1].dom;var e=new t1("image",this,(t=t||{}).pixelRatio||this.dpr),n=(e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor),e.ctx);if(t.pixelRatio<=this.dpr){this.refresh();var i=e.dom.width,r=e.dom.height;this.eachLayer(function(t){t.__builtin__?n.drawImage(t.dom,0,0,i,r):t.renderToCanvas&&(n.save(),t.renderToCanvas(n),n.restore())})}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},a=this.storage.getDisplayList(!0),s=0,l=a.length;s<l;s++){var u=a[s];Gy(n,u,o,s===l-1)}return e.dom},x.prototype.getWidth=function(){return this._width},x.prototype.getHeight=function(){return this._height};var i1=x;function x(t,e,n,i){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var r=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=n=P({},n||{}),this.dpr=n.devicePixelRatio||ur,this._singleCanvas=r;(this.root=t).style&&(Ft(t),t.innerHTML=""),this.storage=e;var o,a,e=this._zlevelList,s=(this._prevDisplayList=[],this._layers);r?(o=(r=t).width,a=r.height,null!=n.width&&(o=n.width),null!=n.height&&(a=n.height),this.dpr=n.devicePixelRatio||1,r.width=o*this.dpr,r.height=a*this.dpr,this._width=o,this._height=a,(o=new t1(r,this,this.dpr)).__builtin__=!0,o.initContext(),(s[n1]=o).zlevel=n1,e.push(n1),this._domRoot=t):(this._width=by(t,0,n),this._height=by(t,1,n),o=this._domRoot=(a=this._width,r=this._height,(s=document.createElement("div")).style.cssText=["position:relative","width:"+a+"px","height:"+r+"px","padding:0","margin:0","border-width:0"].join(";")+";",s),t.appendChild(o))}a(a1,r1=g),a1.prototype.init=function(t,e,n){r1.prototype.init.call(this,t,e,n),this._sourceManager=new Qf(this),tg(this)},a1.prototype.mergeOption=function(t,e){r1.prototype.mergeOption.call(this,t,e),tg(this)},a1.prototype.optionUpdated=function(){this._sourceManager.dirty()},a1.prototype.getSourceManager=function(){return this._sourceManager},a1.type="dataset",a1.defaultOption={seriesLayoutBy:Vp};var r1,o1=a1;function a1(){var t=null!==r1&&r1.apply(this,arguments)||this;return t.type="dataset",t}a(u1,s1=mg),u1.type="dataset";var s1,l1=u1;function u1(){var t=null!==s1&&s1.apply(this,arguments)||this;return t.type="dataset",t}function h1(t){t.registerComponentModel(o1),t.registerComponentView(l1)}function c1(t){t.eachSeriesByType("radar",function(t){var e,i=t.getData(),r=[],o=t.coordinateSystem;o&&(O(e=o.getIndicatorAxes(),function(t,n){i.each(i.mapDimension(e[n].dim),function(t,e){r[e]=r[e]||[];t=o.dataToPoint(t,n);r[e][n]=p1(t)?t:d1(o)})}),i.each(function(t){var e=ht(r[t],p1)||d1(o);r[t].push(e.slice()),i.setItemLayout(t,r[t])}))})}function p1(t){return!isNaN(t[0])&&!isNaN(t[1])}function d1(t){return[t.cx,t.cy]}function f1(n){var i,t=n.polar;t&&(F(t)||(t=[t]),i=[],O(t,function(t,e){(t.indicator?(t.type&&!t.shape&&(t.shape=t.type),n.radar=n.radar||[],F(n.radar)||(n.radar=[n.radar]),n.radar):i).push(t)}),n.polar=i),O(n.series,function(t){t&&"radar"===t.type&&t.polarIndex&&(t.radarIndex=t.polarIndex)})}Uv([function(t){t.registerPainter("canvas",i1)},h1]),Uv($_);a(m1,g1=bg),m1.prototype.render=function(l,t,e){var n=l.coordinateSystem,o=this.group,w=l.getData(),a=this._data;function u(t,e){var n,i=t.getItemVisual(e,"symbol")||"circle";if("none"!==i)return n=[(n=F(n=t.getItemVisual(e,"symbolSize"))?n:[+n,+n])[0]||0,n[1]||0],i=vy(i,-1,-1,2,2),t=t.getItemVisual(e,"symbolRotate")||0,i.attr({style:{strokeNoScale:!0},z2:100,scaleX:n[0]/2,scaleY:n[1]/2,rotation:t*Math.PI/180||0}),i}function s(t,e,n,i,r,o){n.removeAll();for(var a=0;a<e.length-1;a++){var s=u(i,r);s&&(t[s.__dimIdx=a]?(s.setPosition(t[a]),ic[o?"initProps":"updateProps"](s,{x:e[a][0],y:e[a][1]},l,r)):s.setPosition(e[a]),n.add(s))}}function h(t){return z(t,function(t){return[n.cx,n.cy]})}w.diff(a).add(function(t){var e,n,i,r,o=w.getItemLayout(t);o&&(e=new zu,n=new Wu,i={shape:{points:o}},e.shape.points=h(o),n.shape.points=h(o),kh(e,i,l,t),kh(n,i,l,t),i=new Wr,r=new Wr,i.add(n),i.add(e),i.add(r),s(n.shape.points,o,r,w,t,!0),w.setItemGraphicEl(t,i))}).update(function(t,e){var e=a.getItemGraphicEl(e),n=e.childAt(0),i=e.childAt(1),r=e.childAt(2),o={shape:{points:w.getItemLayout(t)}};o.shape.points&&(s(n.shape.points,o.shape.points,r,w,t,!1),Ah(i),Ah(n),Ch(n,o,l),Ch(i,o,l),w.setItemGraphicEl(t,e))}).remove(function(t){o.remove(a.getItemGraphicEl(t))}).execute(),w.eachItemGraphicEl(function(t,y){var m=w.getItemModel(y),e=t.childAt(0),n=t.childAt(1),i=t.childAt(2),v=w.getItemVisual(y,"style"),_=v.fill,e=(o.add(t),e.useStyle(B(m.getModel("lineStyle").getLineStyle(),{fill:"none",stroke:_})),El(e,m,"lineStyle"),El(n,m,"areaStyle"),m.getModel("areaStyle")),r=e.isEmpty()&&e.parentModel.isEmpty(),e=(n.ignore=r,O(["emphasis","select","blur"],function(t){var e=m.getModel([t,"areaStyle"]),e=e.isEmpty()&&e.parentModel.isEmpty();n.ensureState(t).ignore=e&&r}),n.useStyle(B(e.getAreaStyle(),{fill:_,opacity:.7,decal:v.decal})),m.getModel("emphasis")),x=e.getModel("itemStyle").getItemStyle();i.eachChild(function(t){t instanceof _s?(e=t.style,t.useStyle(P({image:e.image,x:e.x,y:e.y,width:e.width,height:e.height},v))):(t.useStyle(v),t.setColor(_),t.style.strokeNoScale=!0);t.ensureState("emphasis").style=b(x);var e=w.getStore().get(w.getDimensionIndex(t.__dimIdx),y),n=(null!=e&&!isNaN(e)||(e=""),t),i=function(t,e){for(var n={normal:t.getModel(e=e||"label")},i=0;i<Js.length;i++){var r=Js[i];n[r]=t.getModel([r,e])}return n}(m),r={labelFetcher:w.hostModel,labelDataIndex:y,labelDimIndex:t.__dimIdx,defaultText:e,inheritColor:_,defaultOpacity:v.opacity},o=void 0;r=r||rc;for(var a=n instanceof Os,s=!1,l=0;l<tl.length;l++)if((p=i[tl[l]])&&p.getShallow("show")){s=!0;break}var u=a?n:n.getTextContent();if(s){a||(u||(u=new Os,n.setTextContent(u)),n.stateProxy&&(u.stateProxy=n.stateProxy));var h=ac(r,i),t=i.normal,c=!!t.getShallow("show"),e=sc(t,o&&o.normal,r,!1,!a);e.text=h.normal,a||n.setTextConfig(lc(t,r,!1));for(l=0;l<Js.length;l++){var p,d,f,g=Js[l];(p=i[g])&&(d=u.ensureState(g),(f=!!N(p.getShallow("show"),c))!=c&&(d.ignore=!f),d.style=sc(p,o&&o[g],r,!0,!a),d.style.text=h[g],a||(n.ensureState(g).textConfig=lc(p,r,!0)))}u.silent=!!t.getShallow("silent"),null!=u.style.x&&(e.x=u.style.x),null!=u.style.y&&(e.y=u.style.y),u.ignore=!c,u.useStyle(e),u.dirty(),r.enableTextSetter&&(dc(u).setLabelText=function(t){t=ac(r,i,t);oc(u,t)})}else u&&(u.ignore=!0);n.dirty()}),Ol(t,e.get("focus"),e.get("blurScope"),e.get("disabled"))}),this._data=w},m1.prototype.remove=function(){this.group.removeAll(),this._data=null},m1.type="radar";var g1,y1=m1;function m1(){var t=null!==g1&&g1.apply(this,arguments)||this;return t.type=m1.type,t}_1.prototype.getAllNames=function(){var t=this._getRawData();return t.mapArray(t.getName)},_1.prototype.containName=function(t){return 0<=this._getRawData().indexOfName(t)},_1.prototype.indexOfName=function(t){return this._getDataWithEncodedVisual().indexOfName(t)},_1.prototype.getItemVisual=function(t,e){return this._getDataWithEncodedVisual().getItemVisual(t,e)};var v1=_1;function _1(t,e){this._getDataWithEncodedVisual=t,this._getRawData=e}a(b1,x1=ug),b1.prototype.init=function(t){x1.prototype.init.apply(this,arguments),this.legendVisualProvider=new v1(pt(this.getData,this),pt(this.getRawData,this))},b1.prototype.getInitialData=function(t,e){return n=this,i=F(i={generateCoord:"indicator_",generateCoordCount:1/0})?{coordDimensions:i}:P({encodeDefine:n.getEncode()},i),o=n.getSource(),i=zm(o,i).dimensions,(i=new Bm(i,n)).initData(o,r),i;var n,i,r,o},b1.prototype.formatTooltip=function(n,t,e){var i=this.getData(),r=this.coordinateSystem.getIndicatorAxes(),o=this.getData().getName(n),o=""===o?this.name:o,a=rg(this,n);return ig("section",{header:o,sortBlocks:!0,blocks:z(r,function(t){var e=i.get(i.mapDimension(t.dim),n);return ig("nameValue",{markerType:"subItem",markerColor:a,name:t.name,value:e,sortParam:e})})})},b1.prototype.getTooltipPosition=function(t){if(null!=t)for(var e,n=this.getData(),i=this.coordinateSystem,r=n.getValues(z(i.dimensions,function(t){return n.mapDimension(t)}),t),o=0,a=r.length;o<a;o++)if(!isNaN(r[o]))return e=i.getIndicatorAxes(),i.coordToPoint(e[o].dataToCoord(r[o]),o)},b1.type="series.radar",b1.dependencies=["radar"],b1.defaultOption={z:2,colorBy:"data",coordinateSystem:"radar",legendHoverLink:!0,radarIndex:0,lineStyle:{width:2,type:"solid",join:"round"},label:{position:"top"},symbolSize:8};var x1,w1=b1;function b1(){var t=null!==x1&&x1.apply(this,arguments)||this;return t.type=b1.type,t.hasSymbolVisual=!0,t}h={show:!0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#6E7079",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,lineStyle:{color:["#E0E6F1"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.2)","rgba(210,219,238,0.2)"]}}},Go=d({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},h),ly=d({boundaryGap:[0,0],axisLine:{show:"auto"},axisTick:{show:"auto"},splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#F4F7FD",width:1}}},h),dy={category:Go,value:ly,time:d({splitNumber:6,axisLabel:{showMinLabel:!1,showMaxLabel:!1,rich:{primary:{fontWeight:"bold"}}},splitLine:{show:!1}},ly),log:B({logBase:10},ly)}.value;function S1(t,e){return B({show:e},t)}a(C1,M1=g),C1.prototype.optionUpdated=function(){var n=this.get("boundaryGap"),i=this.get("splitNumber"),r=this.get("scale"),o=this.get("axisLine"),a=this.get("axisTick"),s=this.get("axisLabel"),l=this.get("axisName"),u=this.get(["axisName","show"]),h=this.get(["axisName","formatter"]),c=this.get("axisNameGap"),p=this.get("triggerEvent"),t=z(this.get("indicator")||[],function(t){null!=t.max&&0<t.max&&!t.min?t.min=0:null!=t.min&&t.min<0&&!t.max&&(t.max=0);var e=l,t=(null!=t.color&&(e=B({color:t.color},l)),d(b(t),{boundaryGap:n,splitNumber:i,scale:r,axisLine:o,axisTick:a,axisLabel:s,name:t.text,showName:u,nameLocation:"end",nameGap:c,nameTextStyle:e,triggerEvent:p},!1)),e=(V(h)?(e=t.name,t.name=h.replace("{value}",null!=e?e:"")):I(h)&&(t.name=h(t.name,t)),new kc(t,null,this.ecModel));return at(e,Vv.prototype),e.mainType="radar",e.componentIndex=this.componentIndex,e},this);this._indicatorModels=t},C1.prototype.getIndicatorModels=function(){return this._indicatorModels},C1.type="radar",C1.defaultOption={z:0,center:["50%","50%"],radius:"75%",startAngle:90,axisName:{show:!0},boundaryGap:[0,0],splitNumber:5,axisNameGap:15,scale:!1,shape:"polygon",axisLine:d({lineStyle:{color:"#bbb"}},dy.axisLine),axisLabel:S1(dy.axisLabel,!1),axisTick:S1(dy.axisTick,!1),splitLine:S1(dy.splitLine,!0),splitArea:S1(dy.splitArea,!0),indicator:[]};var M1,T1=C1;function C1(){var t=null!==M1&&M1.apply(this,arguments)||this;return t.type=C1.type,t}var k1=Math.PI,I1=(D1.prototype.hasBuilder=function(t){return!!L1[t]},D1.prototype.add=function(t){L1[t](this.opt,this.axisModel,this.group,this._transformGroup)},D1.prototype.getGroup=function(){return this.group},D1.innerTextLayout=function(t,e,n){var i,e=oo(e-t),t=ao(e)?(i=0<n?"top":"bottom","center"):ao(e-k1)?(i=0<n?"bottom":"top","center"):(i="middle",0<e&&e<k1?0<n?"right":"left":0<n?"left":"right");return{rotation:e,textAlign:t,textVerticalAlign:i}},D1.makeAxisEventDataBase=function(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e},D1.isLabelSilent=function(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)},D1);function D1(t,e){this.group=new Wr,this.opt=e,this.axisModel=t,B(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0,handleAutoShown:function(){return!0}});t=new Wr({x:e.position[0],y:e.position[1],rotation:e.rotation});t.updateTransform(),this._transformGroup=t}var L1={axisLine:function(i,t,r,e){var o,a,s,l,u,h,c,n=t.get(["axisLine","show"]);(n="auto"===n&&i.handleAutoShown?i.handleAutoShown("axisLine"):n)&&(n=t.axis.getExtent(),e=e.transform,o=[n[0],0],a=[n[1],0],s=a[0]<o[0],e&&(ie(o,o,e),ie(a,a,e)),l=P({lineCap:"round"},t.getModel(["axisLine","lineStyle"]).getLineStyle()),qh((n=new Yu({shape:{x1:o[0],y1:o[1],x2:a[0],y2:a[1]},style:l,strokeContainThreshold:i.strokeContainThreshold||5,silent:!0,z2:1})).shape,n.style.lineWidth),n.anid="line",r.add(n),null!=(u=t.get(["axisLine","symbol"])))&&(e=t.get(["axisLine","symbolSize"]),V(u)&&(u=[u,u]),(V(e)||gt(e))&&(e=[e,e]),n=function(t,e){if(null!=t)return[to((t=F(t)?t:[t,t])[0],e[0])||0,to(N(t[1],t[0]),e[1])||0]}(t.get(["axisLine","symbolOffset"])||0,e),h=e[0],c=e[1],O([{rotate:i.rotation+Math.PI/2,offset:n[0],r:0},{rotate:i.rotation-Math.PI/2,offset:n[1],r:Math.sqrt((o[0]-a[0])*(o[0]-a[0])+(o[1]-a[1])*(o[1]-a[1]))}],function(t,e){var n;"none"!==u[e]&&null!=u[e]&&(e=vy(u[e],-h/2,-c/2,h,c,l.stroke,!0),n=t.r+t.offset,e.attr({rotation:t.rotate,x:(t=s?a:o)[0]+n*Math.cos(i.rotation),y:t[1]-n*Math.sin(i.rotation),silent:!0,z2:11}),r.add(e))}))},axisTickLabel:function(t,e,n,i){var r,o,a,s,l,u=function(t,e,n,i){var r=n.axis,o=n.getModel("axisTick"),a=o.get("show");"auto"===a&&i.handleAutoShown&&(a=i.handleAutoShown("axisTick"));if(a&&!r.scale.isBlank()){for(var a=o.getModel("lineStyle"),i=i.tickDirection*o.get("length"),s=R1(r.getTicksCoords(),e.transform,i,B(a.getLineStyle(),{stroke:n.get(["axisLine","lineStyle","color"])}),"ticks"),l=0;l<s.length;l++)t.add(s[l]);return s}}(n,i,e,t),h=function(f,g,y,m){var v,_,x,w,b,S,M,T,C=y.axis,t=St(m.axisLabelShow,y.get(["axisLabel","show"]));if(t&&!C.scale.isBlank())return v=y.getModel("axisLabel"),_=v.get("margin"),x=C.getViewLabels(),t=(St(m.labelRotate,v.get("rotate"))||0)*k1/180,w=I1.innerTextLayout(m.rotation,t,m.labelDirection),b=y.getCategories&&y.getCategories(!0),S=[],M=I1.isLabelSilent(y),T=y.get("triggerEvent"),O(x,function(t,e){var n="ordinal"===C.scale.type?C.scale.getRawOrdinalNumber(t.tickValue):t.tickValue,i=t.formattedLabel,r=t.rawLabel,o=v,a=(o=b&&b[n]&&R(a=b[n])&&a.textStyle?new kc(a.textStyle,v,y.ecModel):o).getTextColor()||y.get(["axisLine","lineStyle","color"]),s=C.dataToCoord(n),l=o.getShallow("align",!0)||w.textAlign,u=N(o.getShallow("alignMinLabel",!0),l),h=N(o.getShallow("alignMaxLabel",!0),l),c=o.getShallow("verticalAlign",!0)||o.getShallow("baseline",!0)||w.textVerticalAlign,p=N(o.getShallow("verticalAlignMinLabel",!0),c),d=N(o.getShallow("verticalAlignMaxLabel",!0),c),s=new Os({x:s,y:m.labelOffset+m.labelDirection*_,rotation:w.rotation,silent:M,z2:10+(t.level||0),style:sc(o,{text:i,align:0===e?u:e===x.length-1?h:l,verticalAlign:0===e?p:e===x.length-1?d:c,fill:I(a)?a("category"===C.type?r:"value"===C.type?n+"":n,e):a})});s.anid="label_"+n,T&&((t=I1.makeAxisEventDataBase(y)).targetType="axisLabel",t.value=r,t.tickIndex=e,"category"===C.type&&(t.dataIndex=n),qs(s).eventData=t),g.add(s),s.updateTransform(),S.push(s),f.add(s),s.decomposeTransform()}),S}(n,i,e,t),c=(o=h,u=u,Fv((r=e).axis)||(d=r.get(["axisLabel","showMinLabel"]),r=r.get(["axisLabel","showMaxLabel"]),u=u||[],y=(o=o||[])[0],f=o[1],a=o[o.length-1],o=o[o.length-2],s=u[0],g=u[1],l=u[u.length-1],u=u[u.length-2],!1===d?(A1(y),A1(s)):P1(y,f)&&(d?(A1(f),A1(g)):(A1(y),A1(s))),!1===r?(A1(a),A1(l)):P1(o,a)&&(r?(A1(o),A1(u)):(A1(a),A1(l)))),n),p=i,d=e,f=t.tickDirection,g=d.axis,y=d.getModel("minorTick");if(y.get("show")&&!g.scale.isBlank()){var m=g.getMinorTicksCoords();if(m.length)for(var g=y.getModel("lineStyle"),v=f*y.get("length"),_=B(g.getLineStyle(),B(d.getModel("axisTick").getLineStyle(),{stroke:d.get(["axisLine","lineStyle","color"])})),x=0;x<m.length;x++)for(var w=R1(m[x],p.transform,v,_,"minorticks_"+x),b=0;b<w.length;b++)c.add(w[b])}e.get(["axisLabel","hideOverlap"])&&V_(z_(z(h,function(t){return{label:t,priority:t.z2,defaultAttr:{ignore:t.ignore}}})))},axisName:function(t,e,n,i){var r,o,a,s,l,u,h,c,p=St(t.axisName,e.get("name"));p&&(c=e.get("nameLocation"),l=t.nameDirection,r=e.getModel("nameTextStyle"),u=e.get("nameGap")||0,o=(h=e.axis.getExtent())[0]>h[1]?-1:1,o=["start"===c?h[0]-o*u:"end"===c?h[1]+o*u:(h[0]+h[1])/2,O1(c)?t.labelOffset+l*u:0],null!=(u=e.get("nameRotate"))&&(u=u*k1/180),O1(c)?a=I1.innerTextLayout(t.rotation,null!=u?u:t.rotation,l):(a=function(t,e,n,i){var r,n=oo(n-t),t=i[0]>i[1],i="start"===e&&!t||"start"!==e&&t;e=ao(n-k1/2)?(r=i?"bottom":"top","center"):ao(n-1.5*k1)?(r=i?"top":"bottom","center"):(r="middle",n<1.5*k1&&k1/2<n?i?"left":"right":i?"right":"left");return{rotation:n,textAlign:e,textVerticalAlign:r}}(t.rotation,c,u||0,h),null!=(s=t.axisNameAvailableWidth)&&(s=Math.abs(s/Math.sin(a.rotation)),isFinite(s)||(s=null))),l=r.getFont(),u=(c=e.get("nameTruncate",!0)||{}).ellipsis,h=St(t.nameTruncateMaxWidth,c.maxWidth,s),tc({el:t=new Os({x:o[0],y:o[1],rotation:a.rotation,silent:I1.isLabelSilent(e),style:sc(r,{text:p,font:l,overflow:"truncate",width:h,ellipsis:u,fill:r.getTextColor()||e.get(["axisLine","lineStyle","color"]),align:r.get("align")||a.textAlign,verticalAlign:r.get("verticalAlign")||a.textVerticalAlign}),z2:1}),componentModel:e,itemName:p}),t.__fullText=p,t.anid="name",e.get("triggerEvent")&&((c=I1.makeAxisEventDataBase(e)).targetType="axisName",c.name=p,qs(t).eventData=c),i.add(t),t.updateTransform(),n.add(t),t.decomposeTransform())}};function A1(t){t&&(t.ignore=!0)}function P1(t,e){var n,i=t&&t.getBoundingRect().clone(),r=e&&e.getBoundingRect().clone();if(i&&r)return ze(n=Re([]),n,-t.rotation),i.applyTransform(Ee([],n,t.getLocalTransform())),r.applyTransform(Ee([],n,e.getLocalTransform())),i.intersect(r)}function O1(t){return"middle"===t||"center"===t}function R1(t,e,n,i,r){for(var o=[],a=[],s=[],l=0;l<t.length;l++){var u=t[l].coord,u=(a[0]=u,s[a[1]=0]=u,s[1]=n,e&&(ie(a,a,e),ie(s,s,e)),new Yu({shape:{x1:a[0],y1:a[1],x2:s[0],y2:s[1]},style:i,z2:2,autoBatch:!0,silent:!0}));qh(u.shape,u.style.lineWidth),u.anid=r+"_"+t[l].tickValue,o.push(u)}return o}var N1,E1=["axisLine","axisTickLabel","axisName"],B1=(a(z1,N1=mg),z1.prototype.render=function(t,e,n){this.group.removeAll(),this._buildAxes(t),this._buildSplitLineAndArea(t)},z1.prototype._buildAxes=function(t){var n=t.coordinateSystem;O(z(n.getIndicatorAxes(),function(t){var e=t.model.get("showName")?t.name:"";return new I1(t.model,{axisName:e,position:[n.cx,n.cy],rotation:t.angle,labelDirection:-1,tickDirection:-1,nameDirection:1})}),function(t){O(E1,t.add,t),this.group.add(t.getGroup())},this)},z1.prototype._buildSplitLineAndArea=function(t){var n=t.coordinateSystem,e=n.getIndicatorAxes();if(e.length){var i=t.get("shape"),r=t.getModel("splitLine"),t=t.getModel("splitArea"),o=r.getModel("lineStyle"),a=t.getModel("areaStyle"),s=r.get("show"),l=t.get("show"),r=o.get("color"),t=a.get("color"),u=F(r)?r:[r],h=F(t)?t:[t],c=[],p=[];if("circle"===i)for(var d=e[0].getTicksCoords(),f=n.cx,g=n.cy,y=0;y<d.length;y++)s&&c[M(c,u,y)].push(new lu({shape:{cx:f,cy:g,r:d[y].coord}})),l&&y<d.length-1&&p[M(p,h,y)].push(new Ou({shape:{cx:f,cy:g,r0:d[y].coord,r:d[y+1].coord}}));else for(var m,v=z(e,function(t,e){t=t.getTicksCoords();return m=null==m?t.length-1:Math.min(t.length-1,m),z(t,function(t){return n.coordToPoint(t.coord,e)})}),_=[],y=0;y<=m;y++){for(var x=[],w=0;w<e.length;w++)x.push(v[w][y]);x[0]&&x.push(x[0].slice()),s&&c[M(c,u,y)].push(new Wu({shape:{points:x}})),l&&_&&p[M(p,h,y-1)].push(new zu({shape:{points:x.concat(_)}})),_=x.slice().reverse()}var b=o.getLineStyle(),S=a.getAreaStyle();O(p,function(t,e){this.group.add(Gh(t,{style:B({stroke:"none",fill:h[e%h.length]},S),silent:!0}))},this),O(c,function(t,e){this.group.add(Gh(t,{style:B({fill:"none",stroke:u[e%u.length]},b),silent:!0}))},this)}function M(t,e,n){n%=e.length;return t[n]=t[n]||[],n}},z1.type="radar",z1);function z1(){var t=null!==N1&&N1.apply(this,arguments)||this;return t.type=z1.type,t}a(H1,F1=Mc);var F1,V1=H1;function H1(t,e,n){t=F1.call(this,t,e,n)||this;return t.type="value",t.angle=0,t.name="",t}var W1=Math.log;U1.prototype.getIndicatorAxes=function(){return this._indicatorAxes},U1.prototype.dataToPoint=function(t,e){var n=this._indicatorAxes[e];return this.coordToPoint(n.dataToCoord(t),e)},U1.prototype.coordToPoint=function(t,e){e=this._indicatorAxes[e].angle;return[this.cx+t*Math.cos(e),this.cy-t*Math.sin(e)]},U1.prototype.pointToData=function(t){for(var e,n=t[0]-this.cx,t=t[1]-this.cy,i=Math.sqrt(n*n+t*t),r=(n/=i,t/=i,Math.atan2(-t,n)),o=1/0,a=-1,s=0;s<this._indicatorAxes.length;s++){var l=this._indicatorAxes[s],u=Math.abs(r-l.angle);u<o&&(e=l,a=s,o=u)}return[a,+(e&&e.coordToData(i))]},U1.prototype.resize=function(t,e){var n=t.get("center"),i=e.getWidth(),e=e.getHeight(),r=Math.min(i,e)/2,i=(this.cx=to(n[0],i),this.cy=to(n[1],e),this.startAngle=t.get("startAngle")*Math.PI/180,t.get("radius"));(V(i)||gt(i))&&(i=[0,i]),this.r0=to(i[0],r),this.r=to(i[1],r),O(this._indicatorAxes,function(t,e){t.setExtent(this.r0,this.r);e=this.startAngle+e*Math.PI*2/this._indicatorAxes.length,e=Math.atan2(Math.sin(e),Math.cos(e));t.angle=e},this)},U1.prototype.update=function(i,t){var r=this._indicatorAxes,o=this._model,e=(O(r,function(t){t.scale.setExtent(1/0,-1/0)}),i.eachSeriesByType("radar",function(t,e){var n;"radar"===t.get("coordinateSystem")&&i.getComponent("radar",t.get("radarIndex"))===o&&(n=t.getData(),O(r,function(t){t.scale.unionExtentFromData(n,n.mapDimension(t.dim))}))},this),o.get("splitNumber")),f=new uv;f.setExtent(0,e),f.setInterval(1),O(r,function(t,e){var n=t.scale,t=t.model,i=f,r=uv.prototype,o=r.getTicks.call(i),a=r.getTicks.call(i,!0),s=o.length-1,i=r.getInterval.call(i),l=(t=Ev(n,t)).extent,u=t.fixMin,t=t.fixMax,h=("log"===n.type&&(h=W1(n.base),l=[W1(l[0])/h,W1(l[1])/h]),n.setExtent(l[0],l[1]),n.calcNiceExtent({splitNumber:s,fixMin:u,fixMax:t}),r.getExtent.call(n)),c=(u&&(l[0]=h[0]),t&&(l[1]=h[1]),r.getInterval.call(n)),p=l[0],d=l[1];if(u&&t)c=(d-p)/s;else if(u)for(d=l[0]+c*s;d<l[1]&&isFinite(d)&&isFinite(l[1]);)c=Qm(c),d=l[0]+c*s;else if(t)for(p=l[1]-c*s;p>l[0]&&isFinite(p)&&isFinite(l[0]);)c=Qm(c),p=l[1]-c*s;else{h=(c=s<n.getTicks().length-1?Qm(c):c)*s;(p=eo((d=Math.ceil(l[1]/c)*c)-h))<0&&0<=l[0]?(p=0,d=eo(h)):0<d&&l[1]<=0&&(d=0,p=-eo(h))}u=(o[0].value-a[0].value)/i,t=(o[s].value-a[s].value)/i,r.setExtent.call(n,p+c*u,d+c*t),r.setInterval.call(n,c),(u||t)&&r.setNiceExtent.call(n,p+c,d-c)})},U1.prototype.convertToPixel=function(t,e,n){return console.warn("Not implemented."),null},U1.prototype.convertFromPixel=function(t,e,n){return console.warn("Not implemented."),null},U1.prototype.containPoint=function(t){return console.warn("Not implemented."),!1},U1.create=function(t,n){var i=[];return t.eachComponent("radar",function(t){var e=new U1(t,0,n);i.push(e),t.coordinateSystem=e}),t.eachSeriesByType("radar",function(t){"radar"===t.get("coordinateSystem")&&(t.coordinateSystem=i[t.get("radarIndex")||0])}),i},U1.dimensions=[];var G1=U1;function U1(t,e,n){this.dimensions=[],this._model=t,this._indicatorAxes=z(t.getIndicatorModels(),function(t,e){var e="indicator_"+e,n=new V1(e,new uv);return n.name=t.get("name"),(n.model=t).axis=n,this.dimensions.push(e),n},this),this.resize(t,n)}function q1(t){t.registerCoordinateSystem("radar",G1),t.registerComponentModel(T1),t.registerComponentView(B1),t.registerVisual({seriesType:"radar",reset:function(t){var e=t.getData();e.each(function(t){e.setItemVisual(t,"legendIcon","roundRect")}),e.setVisual("legendIcon","roundRect")}})}Uv(function(t){Uv(q1),t.registerChartView(y1),t.registerSeriesModel(w1),t.registerLayout(c1),t.registerProcessor({seriesType:"radar",reset:function(t,e){var i,r=e.findComponents({mainType:"legend"});r&&r.length&&(i=t.getData()).filterSelf(function(t){for(var e=i.getName(t),n=0;n<r.length;n++)if(!r[n].isSelected(e))return!1;return!0})}}),t.registerPreprocessor(f1)});a(j1,X1=g),j1.type="title",j1.defaultOption={z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bold",color:"#464646"},subtextStyle:{fontSize:12,color:"#6E7079"}};var X1,Y1=j1;function j1(){var t=null!==X1&&X1.apply(this,arguments)||this;return t.type=j1.type,t.layoutMode={type:"box",ignoreSize:!0},t}a($1,Z1=mg),$1.prototype.render=function(t,e,n){var i,r,o,a,s,l,u,h,c;this.group.removeAll(),t.get("show")&&(i=this.group,u=t.getModel("textStyle"),r=t.getModel("subtextStyle"),h=t.get("textAlign"),c=N(t.get("textBaseline"),t.get("textVerticalAlign")),s=(u=new Os({style:sc(u,{text:t.get("text"),fill:u.getTextColor()},{disableBox:!0}),z2:10})).getBoundingRect(),l=t.get("subtext"),r=new Os({style:sc(r,{text:l,fill:r.getTextColor(),y:s.height+t.get("itemGap"),verticalAlign:"top"},{disableBox:!0}),z2:10}),o=t.get("link"),a=t.get("sublink"),s=t.get("triggerEvent",!0),u.silent=!o&&!s,r.silent=!a&&!s,o&&u.on("click",function(){yp(o,"_"+t.get("target"))}),a&&r.on("click",function(){yp(a,"_"+t.get("subtarget"))}),qs(u).eventData=qs(r).eventData=s?{componentType:"title",componentIndex:t.componentIndex}:null,i.add(u),l&&i.add(r),s=i.getBoundingRect(),(l=t.getBoxLayoutParams()).width=s.width,l.height=s.height,l=wp(l,{width:n.getWidth(),height:n.getHeight()},t.get("padding")),h||("right"===(h="middle"===(h=t.get("left")||t.get("right"))?"center":h)?l.x+=l.width:"center"===h&&(l.x+=l.width/2)),c||("bottom"===(c="center"===(c=t.get("top")||t.get("bottom"))?"middle":c)?l.y+=l.height:"middle"===c&&(l.y+=l.height/2),c=c||"top"),i.x=l.x,i.y=l.y,i.markRedraw(),u.setStyle(n={align:h,verticalAlign:c}),r.setStyle(n),s=i.getBoundingRect(),u=l.margin,(h=t.getItemStyle(["color","opacity"])).fill=t.get("backgroundColor"),c=new Is({shape:{x:s.x-u[3],y:s.y-u[0],width:s.width+u[1]+u[3],height:s.height+u[0]+u[2],r:t.get("borderRadius")},style:h,subPixelOptimize:!0,silent:!0}),i.add(c))},$1.type="title";var Z1,K1=$1;function $1(){var t=null!==Z1&&Z1.apply(this,arguments)||this;return t.type=$1.type,t}Uv(function(t){t.registerComponentModel(Y1),t.registerComponentView(K1)}),Uv(h1);var Q1={value:"eq","<":"lt","<=":"lte",">":"gt",">=":"gte","=":"eq","!=":"ne","<>":"ne"},J1=(tx.prototype.evaluate=function(t){var e=typeof t;return V(e)?this._condVal.test(t):!!gt(e)&&this._condVal.test(t+"")},tx);function tx(t){null==(this._condVal=V(t)?new RegExp(t):wt(t)?t:null)&&f("")}nx.prototype.evaluate=function(){return this.value};var ex=nx;function nx(){}rx.prototype.evaluate=function(){for(var t=this.children,e=0;e<t.length;e++)if(!t[e].evaluate())return!1;return!0};var ix=rx;function rx(){}ax.prototype.evaluate=function(){for(var t=this.children,e=0;e<t.length;e++)if(t[e].evaluate())return!0;return!1};var ox=ax;function ax(){}lx.prototype.evaluate=function(){return!this.child.evaluate()};var sx=lx;function lx(){}hx.prototype.evaluate=function(){for(var t=!!this.valueParser,e=(0,this.getValue)(this.valueGetterParam),n=t?this.valueParser(e):null,i=0;i<this.subCondList.length;i++)if(!this.subCondList[i].evaluate(t?n:e))return!1;return!0};var ux=hx;function hx(){}function cx(t,e){if(!0===t||!1===t)return(n=new ex).value=t,n;var n;if(dx(t)||f(""),t.and)return px("and",t,e);if(t.or)return px("or",t,e);if(t.not)return n=e,dx(o=(o=t).not)||f(""),(l=new sx).child=cx(o,n),l.child||f(""),l;for(var i=t,r=e,o=r.prepareGetValue(i),a=[],s=ct(i),l=i.parser,u=l?Tf(l):null,h=0;h<s.length;h++){var c,p=s[h];"parser"===p||r.valueGetterAttrMap.get(p)||(c=Vt(Q1,p)?Q1[p]:p,p=i[p],p=u?u(p):p,(c=function(t,e){return"eq"===t||"ne"===t?new Af("eq"===t,e):Vt(Cf,t)?new kf(t,e):null}(c,p)||"reg"===c&&new J1(p))||f(""),a.push(c))}return a.length||f(""),(l=new ux).valueGetterParam=o,l.valueParser=u,l.getValue=r.getValue,l.subCondList=a,l}function px(t,e,n){e=e[t],F(e)||f(""),e.length||f(""),t=new("and"===t?ix:ox);return t.children=z(e,function(t){return cx(t,n)}),t.children.length||f(""),t}function dx(t){return R(t)&&!st(t)}gx.prototype.evaluate=function(){return this._cond.evaluate()};var fx=gx;function gx(t,e){this._cond=cx(t,e)}var yx={type:"echarts:filter",transform:function(t){for(var e,n,i=t.upstream,r=(t=t.config,n={valueGetterAttrMap:E({dimension:!0}),prepareGetValue:function(t){var e=t.dimension,t=(Vt(t,"dimension")||f(""),i.getDimensionInfo(e));return t||f(""),{dimIdx:t.index}},getValue:function(t){return i.retrieveValueFromItem(e,t.dimIdx)}},new fx(t,n)),o=[],a=0,s=i.count();a<s;a++)e=i.getRawDataItem(a),r.evaluate()&&o.push(e);return{data:o}}},mx={type:"echarts:sort",transform:function(t){for(var a=t.upstream,t=t.config,t=vo(t),s=(t.length||f(""),[]),t=(O(t,function(t){var e=t.dimension,n=t.order,i=t.parser,t=t.incomparable,e=(null==e&&f(""),"asc"!==n&&"desc"!==n&&f(""),t&&"min"!==t&&"max"!==t&&f(""),"asc"!==n&&"desc"!==n&&f(""),a.getDimensionInfo(e)),r=(e||f(""),i?Tf(i):null);i&&!r&&f(""),s.push({dimIdx:e.index,parser:r,comparator:new Df(n,t)})}),a.sourceFormat),e=(t!==Np&&t!==Ep&&f(""),[]),n=0,i=a.count();n<i;n++)e.push(a.getRawDataItem(n));return e.sort(function(t,e){for(var n=0;n<s.length;n++){var i=s[n],r=a.retrieveValueFromItem(t,i.dimIdx),o=a.retrieveValueFromItem(e,i.dimIdx),i=(i.parser&&(r=i.parser(r),o=i.parser(o)),i.comparator.evaluate(r,o));if(0!==i)return i}return 0}),{data:e}}};Uv(function(t){t.registerTransform(yx),t.registerTransform(mx)}),t.Axis=Mc,t.ChartView=bg,t.ComponentModel=g,t.ComponentView=mg,t.List=Bm,t.Model=kc,t.PRIORITY=cy,t.SeriesModel=ug,t.color=xi,t.connect=function(e){var t;return F(e)&&(t=e,e=null,O(t,function(t){null!=t.group&&(e=t.group)}),e=e||"g_"+U0++,O(t,function(t){t.group=e})),W0[e]=!0,e},t.dataTool={},t.dependencies={zrender:"5.6.0"},t.disConnect=hy,t.disconnect=X0,t.dispose=function(t){V(t)?t=H0[t]:t instanceof k0||(t=Y0(t)),t instanceof k0&&!t.isDisposed()&&t.dispose()},t.env=p,t.extendChartView=function(t){return t=bg.extend(t),bg.registerClass(t),t},t.extendComponentModel=function(t){return t=g.extend(t),g.registerClass(t),t},t.extendComponentView=function(t){return t=mg.extend(t),mg.registerClass(t),t},t.extendSeriesModel=function(t){return t=ug.extend(t),ug.registerClass(t),t},t.format=bc,t.getCoordinateSystemDimensions=function(t){if(t=hd.get(t))return t.getDimensionsInfo?t.getDimensionsInfo():t.dimensions.slice()},t.getInstanceByDom=Y0,t.getInstanceById=function(t){return H0[t]},t.getMap=function(t){var e=Ky.getMap;return e&&e(t)},t.graphic=Gc,t.helper=py,t.init=function(t,e,n){var i=!(n&&n.ssr);if(i){var r=Y0(t);if(r)return r}return(r=new k0(t,e,n)).id="ec_"+G0++,H0[r.id]=r,i&&Eo(t,q0,r.id),S0(r),Zy.trigger("afterinit",r),r},t.innerDrawElementOnCanvas=Wy,t.matrix=He,t.number=Zo,t.parseGeoJSON=s_,t.parseGeoJson=s_,t.registerAction=tm,t.registerCoordinateSystem=em,t.registerLayout=nm,t.registerLoading=am,t.registerLocale=Bc,t.registerMap=sm,t.registerPostInit=$0,t.registerPostUpdate=Q0,t.registerPreprocessor=Z0,t.registerProcessor=K0,t.registerTheme=j0,t.registerTransform=lm,t.registerUpdateLifecycle=J0,t.registerVisual=im,t.setCanvasCreator=function(t){L({createCanvas:t})},t.setPlatformAPI=L,t.throttle=Dg,t.time=Xh,t.use=Uv,t.util=_c,t.vector=ae,t.version="5.5.1",t.zrUtil=Gt,t.zrender=$r});
<template>
	<view>
		<view class="adjustment">
			<view class="adjust" v-for="item in noticelist" :key="item.id" @click="reading(item.id)">
				<view class="newsItem paddingBottom20">
					<view class="displayFlex displayFlexCenter ">
						<view class="round" v-if="item.status === 1" style="display: none;"></view>
						<view class="round" v-else></view>
						消息提醒
					</view>
					
					
					<view class="font30 color_grey">
						{{item.createTime}}
					</view>
				</view>
				<view class="paddingTop20" style="line-height: 52upx;">
					{{item.content}}
				</view>

			</view>

			<!-- 加载更多 -->
			<!-- 	<u-loadmore :status="status" />		 -->
		</view>
		
		<!-- 加载更多 -->
		<uni-load-more :status="status" :contentText="contentText" />
	</view>
</template>

<script>
	export default {
		name: "Course",
		data() {
			return {
				pageNum: 1,
				pageSize: 20,
				// total: '',
				// isloading: false ,// 是否正在请求数据
				
				noticelist: [], // 调课通知列表				
				totalItems: '', // 消息数量
				
				ifBottomRefresh:false,
				status: 'more', // 加载更多
				contentText: {
					"contentdown": "加载更多数据",
					"contentrefresh": "加载中...",
					"contentnomore": "暂无更多数据"
				},
			};
		},
		onShow() {
			this.noticeData() // 调课通知
		},

		mounted() {
			// this.noticeData() // 调课通知
		},
		
		// 触底的事件
		onReachBottom() {
			// 让页码值自增 +1
			this.pageNum += 1
			this.noticeData()
			if (this.status == 'no-more') return
		},

		methods: {
			// 加载事件
			loadingmore(e) {
				uni.showToast({
					icon: 'none',
					title: "当前状态：" + e.detail.status
				})
			},

		
			// 点击已读
			reading(id) {
				this.status = 0;
				this.readData(id);
			},

			// 调课通知
			async noticeData() {
				// this.isloading = true
				let res = await uni.$http.get('/deliver/app/teacher/notifyList', {
					pageNum: this.pageNum,
					pageSize: this.pageSize
				})
				// this.noticelist = res.data.data.data;
				// this.status = res.data.data.data.status;
				if (res.data.success) {
					// 为数据赋值：通过展开运算符的形式，进行新旧数据的拼接
					this.noticelist = [...this.noticelist, ...res.data.data.data]
					this.totalItems = res.data.data.totalItems
					this.status = this.noticelist.length < res.data.data.totalItems ? 'more' : 'no-more'
				}	
			},

			// 已读通知
			async readData(id) {
				let res = await uni.$http.post('/deliver/app/teacher/notifyList/' + id)
				if(res.data.success) {
					this.status = 1;
					uni.redirectTo({
						url:'/pkgUser/news/news'
					})
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.nav-title {
		position: fixed;
		height: 180rpx;
		width: 100%;
		background-color: #fff;
	}

	.status_bar {
		text-align: center;
		font-size: 40rpx;
		color: #000;
		line-height: 220rpx;
	}

	.icon_img {
		position: absolute;
		left: 10rpx;
	}

	.adjustment {
		padding-top: 20rpx;
		vertical-align: middle;
	}
	
	/deep/.adjust {
		width: 90%;
		background-color: #fff;
		font-size: 25rpx;
		border-radius: 20rpx;
		margin: auto;
		padding: 20rpx;
		margin-bottom: 20rpx;
	}
	.newsItem{
		display: flex;
		width: 100%;
		justify-content: space-between;
		color: 32upx;
		border-bottom: 1upx dashed #eee;
		align-items: center;
	}

	/deep/.round {
		width: 20rpx;
		height: 20rpx;
		margin-right: 20rpx;
		background-color: #2E896F;
		border-radius: 50%;
	}


</style>

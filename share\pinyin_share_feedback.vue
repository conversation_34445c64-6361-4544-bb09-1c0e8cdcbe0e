<template>
  <view style="height: 1600prx; background-color: #2e896f">
    <view>
      <image :src="path" mode="widthFix" @longpress="longPress()" style="width: 100%"></image>
      <Painter isCanvasToTempFilePath ref="painter" @success="path = $event" custom-style="position: fixed; left: 200%" css="width: 750rpx; padding-bottom: 40rpx">
        <PainterImage
          src="https://document.dxznjy.com/app/images/zhujiaoduan/feedback_bg.png"
          css="position: absolute;top:-50rpx;object-fit: contain;width: 100%;"
        ></PainterImage>
        <PainterView css="position: absolute;top:120rpx;z-index:2;width:100%;text-align:center;color:#FFFFFF;font-size:34rpx;">
          <PainterText text="学习反馈" />
        </PainterView>
        <PainterView css="position: relative;margin-top: 250rpx; padding: 32rpx; box-sizing: border-box; background: #fff;border:30rpx solid #2e896f;">

          <!-- 学习反馈  -->
          <PainterView>
            <PainterText :text="'日期：' + backlist.dateTime " css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <PainterText :text="'姓名：' + backlist.studentName " css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <PainterText :text="'年级：' + backlist.gradeName " css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <PainterText :text="'学员编号：' + backlist.studentCode " css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <PainterText :text="'时间：' + backlist.studyTime " css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <PainterText :text="'实际时间：' + (timelist.actualStart ) + '~' +  (timelist.actualEnd )"
                                    css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <PainterText :text="'学习学时：' + backlist.studyHour  + '小时'" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <PainterText :text="'所学课程类型：' + backlist.curriculumName + backlist.extendProperty.courseTypeName" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <PainterText :text="'所学课程名称：' + backlist.extendProperty.courseNames " css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <PainterText :text="'学习元辅音个数：' + backlist.extendProperty.consonantCounts " css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <PainterText :text="'学习音节个数：' + backlist.extendProperty.syllableCounts " css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <PainterText :text="'学习单词个数：' + backlist.extendProperty.wordCounts " css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />

            <PainterText text="教练评语：" css="font-size: 30rpx;display: block;margin-bottom: 30rpx;line-height: 1.8em" />
            <PainterView css="padding: 30rpx;box-sizing: border-box;background: rgba(153, 153, 153, 0.08);border-radius: 8rpx;width: 630rpx;min-height:481rpx;">
              <PainterText :text="feedback" css="font-size: 30rpx;display: block;line-height: 1.8em" />
            </PainterView>
          </PainterView>
        </PainterView>
      </Painter>
    </view>
  </view>
</template>

<script>
import PainterText from '@/share/components/lime-painter/components/l-painter-text/l-painter-text.vue'
import PainterImage from '@/share/components/lime-painter/components/l-painter-image/l-painter-image.vue'
import PainterView from '@/share/components/lime-painter/components/l-painter-view/l-painter-view.vue'
import Painter from '@/share/components/lime-painter/components/l-painter/l-painter.vue'
export default {
    components: {
    	PainterText,
    	Painter,
        PainterImage,
        PainterView
    },
    data() {
        return {
            path: '', //生成海报
            backlist: '', // 获取反馈详情
            //反馈详情列表
            trialclass: false, // 是否是试课反馈
            isStudy: false, // 学习还是复习

            feedback: '', // 弹出层文本框输入的内容
            Intention: '', // 学习意愿
            timelist: {
                actualStart: '',
                actualEnd: ''
            }
        };
    },
  onLoad(e) {
	  console.log('eee',e)
    let sendData = JSON.parse(decodeURIComponent(e.sendData));
	console.log('eee',e,sendData)
    if (sendData != null && sendData != undefined) {
      this.trialclass = sendData.trialclass;
      this.isStudy = sendData.isStudy;
      this.backlist = sendData.detailsData;
      if (this.backlist != null && this.backlist != undefined) {
        this.Intention = this.backlist.studyIntention;
        this.feedback = this.backlist.feedback;
        this.timelist.actualStart = this.backlist.actualStart;
        this.timelist.actualEnd = this.backlist.actualEnd;
      }
    }
  },
  methods: {
    //长按
    longPress() {
      let that = this;
      uni.showModal({
        content: '保存图片',
        success: (res) => {
			console.log(res,'-------------------------------------------------')
          if (res.confirm) {
            that.$refs.painter.canvasToTempFilePathSync({
              fileType: 'jpg',
              pathType: 'url',
              quality: 1,
              success: (res) => {
                console.log(res,'666666666666666666666666666666666666666666666');
                that.path = res.tempFilePath;
                that.saveLocal();
              },
              fail: (err) => {
                console.log(err,'55555555555555555555555555555555555');
                uni.showModal({
                  title: '提示',
                  content: '生成海报失败,请重试',
                  showCancel: false
                });
              }
            });
          } else {
            uni.showToast({
              title: '已取消！',
              icon: 'none'
            });
          }
        }
      });
    },

    //保存本地
    saveLocal() {
		 // #ifdef APP-PLUS
      uni.saveImageToPhotosAlbum({
            filePath: this.path,
            success: (res) => {
              uni.showToast({
                icon: 'none',
                title: '保存成功'
              });
              console.log(res);
              //分享
              // wx.showShareImageMenu({
              //     path: res.path,
              //     success(msg) {
              //         console.log(msg);
              //     },
              //     fail(err) {
              //         console.log("11111");
              //         console.log(err);
              //     }
              // })
            },
            fail(err) {
              uni.showToast({
                icon: 'none',
                title: '保存失败'
              });
            }
          });
		  // #endif
		  // #ifdef MP-WEIXIN
		  uni.authorize({
			scope: 'scope.writePhotosAlbum',
			success: () => {
			  uni.saveImageToPhotosAlbum({
				filePath: this.path,
				success: (res) => {
				  uni.showToast({
					icon: 'none',
					title: '保存成功'
				  });
				  console.log(res);
				  //分享
				  // wx.showShareImageMenu({
				  //     path: res.path,
				  //     success(msg) {
				  //         console.log(msg);
				  //     },
				  //     fail(err) {
				  //         console.log("11111");
				  //         console.log(err);
				  //     }
				  // })
				},
				fail(err) {
				  uni.showToast({
					icon: 'none',
					title: '保存失败'
				  });
				}
			  });
			},
			fail() {
			  uni.showModal({
				title: '保存失败',
				content: '您没有授权，无法保存到相册',
				showCancel: false
			  });
			}
		  });
		  // #endif
    },

    Back() {
      uni.navigateBack();
    }
  }
};
</script>

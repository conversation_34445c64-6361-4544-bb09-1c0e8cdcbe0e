<template>
  <view>
    <view class="nav-title status_bar">
      <uni-icons @click="Back" type="left" color="#a8a8a8" size="24" class="icon_img"></uni-icons>
      {{ this.trialclass ? (triallist.curriculumName || '') + '体验结果反馈' : '学习反馈' }}
    </view>
    <view :class="trialclass ? 'container' : 'formal-container'">
      <!-- 评估和试课/体验 -->
      <view class="card" v-if="trialclass">
        <view class="review-experience-card">
          <view class="review-experience-card-text-wrapper">
            <text class="review-experience-card-text" style="margin-right: 16rpx">课程类型：</text>
            <text class="review-experience-card-text">{{ triallist.curriculumName }}</text>
          </view>
          <view class="review-experience-card-text-wrapper">
            <text class="review-experience-card-text mr-70">日期：</text>
            <text class="review-experience-card-text">{{ triallist.dateTime }}</text>
          </view>
          <view class="review-experience-card-text-wrapper">
            <text class="review-experience-card-text mr-70">姓名：</text>
            <text class="review-experience-card-text">{{ triallist.studentName }}</text>
          </view>
          <view class="review-experience-card-text-wrapper">
            <text class="review-experience-card-text mr-70">年级：</text>
            <text class="review-experience-card-text">{{ triallist.gradeName }}</text>
          </view>
          <view class="review-experience-card-text-wrapper">
            <text class="review-experience-card-text" style="margin-right: 16rpx">学员编号：</text>
            <text class="review-experience-card-text">{{ triallist.studentCode }}</text>
          </view>
          <image class="review-experience-card-img" src="/static/images/pic_peitu.png"></image>
        </view>

        <!--评估-->
        <view v-if="triallist.extendProperty&&triallist.extendProperty.assessmentFeedbackDto" class="review">
          <view class="review-title">
            <image class="review-title-img-left" src="/static/images/dxn_left_title_decorate.png"></image>
            <text class="review-title-text">评估课报告及反馈</text>
            <image class="review-title-img-right" src="/static/images/dxn_right_title_decorate.png"></image>
          </view>
          <view class="review-score-block">
            <text class="review-score">得分：</text>
            <text class="review-score-value">{{ triallist.extendProperty.assessmentFeedbackDto.sum }}</text>
            <text>分</text>
          </view>
          <view class="review-correct-error-block">
            <text class="review-correct-title">正确题数：</text>
            <text class="review-correct-num">{{ triallist.extendProperty.assessmentFeedbackDto.correct }}</text>
            <text class="review-error-title">错误题数：</text>
            <text class="review-error-num">{{ triallist.extendProperty.assessmentFeedbackDto.error }}</text>
          </view>
          <view class="reviewChartRef" style="height: 600rpx">
            <l-echart custom-style="{width: 100%;height: 100%;}" ref="reviewChartRef" @finished="reviewInit"></l-echart>
          </view>
          <view class="over-reviewChartRef"></view>
          <view class="review-comment">
            <view class="review-comment-title">评估反馈</view>
            <view class="review-comment-content">{{ triallist.extendProperty.assessmentFeedbackDto.comment }}</view>
          </view>
          <view class="review-planning">
            <view class="review-planning-title">未来规划</view>
            <!-- <view class="review-planning-content">{{ triallist.extendProperty.assessmentFeedbackDto.planning }}</view> -->
            <view class="review-planning-content">
              根据科学的评估分析，您家孩子的专注力一般，学习效率有待提升。通过注意力课程的专项训练，会让孩子的学习能力更上一层楼，学习的效率更高，达到输入即输出的目的，课堂听课听到就会，并灵活运用。专注力的提高可以帮助学生更好的专注于学习，更容易被认可，激发学生学习的内驱力。
            </view>
          </view>
        </view>

        <!--试课/体验-->
        <view class="experience"  v-if="triallist.extendProperty&&triallist.extendProperty.assessmentFeedbackDto">
          <view class="experience-title">
            <image class="experience-title-img-left" src="/static/images/dxn_left_title_decorate.png"></image>
            <text class="experience-title-text">试课报告及反馈</text>
            <image class="experience-title-img-right" src="/static/images/dxn_right_title_decorate.png"></image>
          </view>

          <view class="experience-time-block">
            <text class="experience-time-title" style="margin-right: 16rpx">上课时间：</text>
            <text class="experience-time">{{ triallist.extendProperty.experienceFeedbackDto.time }}</text>
          </view>
          <view class="experience-costTime-block">
            <text class="experience-costTime-title" style="margin-right: 16rpx">上课用时：</text>
            <text class="experience-costTime">{{ triallist.extendProperty.experienceFeedbackDto.costTime }}</text>
          </view>
          <view style="margin-bottom: 24rpx; width: 85%">
            <view class="experience-outstanding">突出项：</view>
            <text v-for="(item, index) in triallist.extendProperty.experienceFeedbackDto.highLights" class="experience-highLights">
              {{ item }}
            </text>
          </view>
          <view style="margin-bottom: 24rpx; width: 85%">
            <view class="experience-recommended-promotion">推荐提升项：</view>
            <text v-for="(item, index) in triallist.extendProperty.experienceFeedbackDto.shortComing" class="experience-shortComing">
              {{ item }}
            </text>
          </view>
          <view class="experience-correct-error-block">
            <text class="experience-correct-title">正确题数：</text>
            <text class="experience-correct-num">{{ triallist.extendProperty.experienceFeedbackDto.correctNums }}</text>
            <text class="experience-error-title">错误题数：</text>
            <text class="experience-error-num">{{ triallist.extendProperty.experienceFeedbackDto.errorNums }}</text>
          </view>
          <view class="experienceChartRef" style="height: 600rpx">
            <l-echart custom-style="{width: 100%;height: 100%;}" ref="experienceChartRef" @finished="experienceInit"></l-echart>
          </view>
          <view class="over-experienceChartRef"></view>
          <view :class="Feedback || isEdit ? 'experience-flex-view' : 'experience-flex-view-notclean'">
            <view class="experience-student-status-feedback-title">学员学习状况反馈</view>
            <view v-if="Feedback || isEdit" class="experience-flex-view-textarea" @click="cleanFeedback()">清空内容</view>
          </view>
          <u--textarea
            v-if="Feedback || isEdit"
            class="experience-student-status-feedback-textarea"
            v-model="feedback"
            count="true"
            adjustPosition="true"
            cursorSpacing="165"
            :value="feedback"
            placeholder="请输入学习反馈……"
            placeholderStyle="color:#BFBFBF"
            maxlength="200"
            height="140"
          ></u--textarea>
          <view v-else class="experience-flex-view-text">{{ feedback }}</view>

          <!-- 按钮 -->
          <view class="button-sp-area marginTop40 flexs">
            <button v-if="!Feedback && !isEdit" class="mini-btn refresh" type="default" size="mini" @click="editOption()">修改</button>
            <button v-if="Feedback || isEdit" class="mini-btn background_green_two" type="default" size="mini" @click="addOrLook(id)">确定</button>
            <button v-else class="mini-btn background_green_two" type="default" size="mini" @click="shareJump">分享</button>
          </view>
        </view>
      </view>

      <!-- 正式 -->
      <view class="card" v-else>
        <view class="white-content">
          <view class="review-experience-card-text-wrapper">
            <text class="review-experience-card-text" style="margin-right: 16rpx">课程类型：</text>
            <text class="review-experience-card-text">{{ backlist.curriculumName }}</text>
            <view @click="refresh(id)" class="refresh-view">
              <image class="shuaxin-icon" src="/static/images/shuaxin.png" mode=""></image>
              <view class="shuaxin-text f-24" style="color: #2e896f; margin-left: 4rpx">刷新</view>
            </view>
          </view>
          <view class="review-experience-card-text-wrapper">
            <text class="review-experience-card-text" style="margin-right: 70rpx">日期：</text>
            <text class="review-experience-card-text">{{ backlist.dateTime }}</text>
          </view>
          <view class="review-experience-card-text-wrapper">
            <text class="review-experience-card-text" style="margin-right: 70rpx">姓名：</text>
            <text class="review-experience-card-text">{{ backlist.studentName }}</text>
          </view>
          <view class="review-experience-card-text-wrapper">
            <text class="review-experience-card-text" style="margin-right: 70rpx">年级：</text>
            <text class="review-experience-card-text">{{ backlist.gradeName }}</text>
          </view>
          <view class="review-experience-card-text-wrapper">
            <text class="review-experience-card-text" style="margin-right: 16rpx">学员编号：</text>
            <text class="review-experience-card-text">{{ backlist.studentCode }}</text>
          </view>
          <view class="review-experience-card-text-wrapper">
            <text class="review-experience-card-text" style="margin-right: 70rpx">时间：</text>
            <text class="review-experience-card-text">{{ backlist.extendProperty?backlist.extendProperty.time :''}}</text>
          </view>
          <view class="review-experience-card-text-wrapper">
            <text class="review-experience-card-text" style="margin-right: 16rpx">上课用时：</text>
            <text class="review-experience-card-text">{{ backlist.extendProperty?backlist.extendProperty.costTime:'' }}</text>
          </view>
          <view class="review-experience-card-text-wrapper">
            <text class="review-experience-card-text" style="margin-right: 16rpx">已购鼎学能课时：</text>
            <text class="review-experience-card-text">{{ backlist.extendProperty?backlist.extendProperty.haveCourseTime:'' }}小时</text>
          </view>
          <view class="review-experience-card-text-wrapper">
            <text class="review-experience-card-text" style="margin-right: 16rpx">剩余鼎学能课时：</text>
            <text class="review-experience-card-text">{{ backlist.extendProperty?backlist.extendProperty.surplusCourseTime:'' }}小时</text>
          </view>
          <view style="margin-bottom: 24rpx; width: 85%">
            <view class="outstanding-item h-50">突出项：</view>
            <text v-for="(item, index) in backlist.extendProperty?backlist.extendProperty.highLights:[]" :key="index" class="outstanding-item-value h-50">
              {{ item }}
            </text>
          </view>
          <view style="margin-bottom: 24rpx; width: 85%">
            <view class="recommended-improvement-items h-50">推荐提升项：</view>
            <text v-for="(item, index) in backlist.extendProperty?backlist.extendProperty.shortComing:[]" :key="index" class="recommended-improvement-items-value h-50">
              {{ item }}
            </text>
          </view>
          <view class="formal-correctNums h-50">正确题数：{{ backlist.extendProperty?backlist.extendProperty.correctNums:'' }}</view>
          <view class="formal-errorNums h-50">错误题数：{{ backlist.extendProperty?backlist.extendProperty.errorNums:'' }}</view>

          <!--雷达图-->
          <view class="formalChartRef" style="height: 500rpx; margin-bottom: 20rpx">
            <l-echart custom-style="{width: 100%;height: 100%;}" ref="formalChartRef" @finished="formalInit"></l-echart>
          </view>
          <view class="over-formalChartRef"></view>

          <!--评语-->
          <view :class="isFeedback || isEdit ? 'flex-view-edit' : 'flex-view'">
            <view :class="isFeedback || isEdit ? 'teacher-Comment' : 'teacher-Comment mb-25'">教练评语</view>
            <view v-if="isFeedback || isEdit" class="flex-view-text" @click="cleanFeedback()">清空内容</view>
          </view>
          <!-- 反馈 -->
          <u--textarea
            v-if="isFeedback || isEdit"
            :disabled="!isFeedback && !isEdit"
            class="feedback"
            v-model="feedback"
            :value="feedback"
            placeholder="请输入"
            count="true"
            adjustPosition="true"
            cursorSpacing="165"
            placeholderStyle="color:#b0b0b6"
            height="165"
            maxlength="200"
          ></u--textarea>
          <view v-else class="font24 feedbackBoxNew marginBottom10" style="color: #555555">
            {{ feedback }}
          </view>

          <!--按钮-->
          <view class="button-sp-area marginTop40 flexs mb-30">
            <button v-if="!isFeedback && !isEdit" class="mini-btn refresh" type="default" size="mini" @click="editOption()">修改</button>
            <button v-if="isFeedback || isEdit" class="mini-btn background_green_two" type="default" size="mini" @click="addOrLook(id)">确定</button>
            <button v-else class="mini-btn background_green_two" type="default" size="mini" @click="shareJump">分享</button>
          </view>
        </view>
      </view>

      <uni-popup ref="loadingPopup" type="center" :is-mask-click="false">
        <view class="bg-ff radius-15 t-c">
          <view class="loadingpadding">
            <image src="https://document.dxznjy.com/Assistant/loading.png" class="loadingImg"></image>
            <view class="mt-30">加载中...</view>
            <view>请耐心等候</view>
          </view>
        </view>
      </uni-popup>
    </view>
  </view>
</template>

<style lang="scss" scoped>
  .experience-costTime-block {
    margin-bottom: 24rpx;
  }

  .experience-costTime-title {
    font-size: 28rpx;
    color: #333333;
    line-height: 40rpx;
    text-align: left;
    font-style: normal;
    margin-bottom: 16rpx;
  }

  .experience-costTime {
    font-size: 28rpx;
    color: #333333;
  }

  .experience-time-block {
    margin-bottom: 24rpx;
  }

  .experience-time-title {
    font-size: 28rpx;
    color: #333333;
    line-height: 40rpx;
    text-align: left;
    font-style: normal;
    margin-bottom: 16rpx;
  }

  .experience-time {
    font-size: 28rpx;
    color: #333333;
  }

  .over-formalChartRef {
    background-color: #b1b1b1;
    position: absolute;
    top: 1150rpx;
    left: 0rpx;
    width: 100%;
    height: 500rpx;
    opacity: 0;
  }

  .over-experienceChartRef {
    background-color: #b1b1b1;
    position: absolute;
    top: 2550rpx;
    left: 0rpx;
    width: 100%;
    height: 620rpx;
    opacity: 0;
  }

  .over-reviewChartRef {
    background-color: #b1b1b1;
    position: absolute;
    top: 850rpx;
    left: 0rpx;
    width: 100%;
    height: 600rpx;
    opacity: 0;
  }

  .teacher-Comment {
    font-size: 28rpx;
    color: #555555;
    line-height: 40rpx;
    text-align: left;
    font-style: normal;
  }

  .formal-errorNums {
    margin-bottom: 52rpx;
    font-size: 28rpx;
    color: #555555;
  }

  .formal-correctNums {
    margin-bottom: 24rpx;
    font-size: 28rpx;
    color: #555555;
  }

  .recommended-improvement-items-value {
    // margin-bottom: 24rpx;
    font-size: 28rpx;
    font-weight: 600;
    color: #ea6030;
    margin-right: 40rpx;
  }

  .recommended-improvement-items {
    // margin-bottom: 16rpx;
    font-size: 28rpx;
    color: #555555;
  }

  .outstanding-item-value {
    // margin-bottom: 24rpx;
    font-size: 28rpx;
    font-weight: 600;
    color: #428a6f;
    margin-right: 40rpx;
  }

  .outstanding-item {
    // margin-bottom: 16rpx;
    font-size: 28rpx;
    color: #555555;
  }

  .experience-flex-view-notclean {
    flex-direction: row;
    display: flex;
    justify-content: space-between;
  }

  .experience-flex-view-text {
    margin-bottom: 10rpx;
    color: #333333;
    font-size: 26rpx;
    background-color: rgba(153, 153, 153, 0.08);
    line-height: 40rpx;
    padding: 12rpx 30rpx;
    height: 300rpx;
    overflow-y: scroll;
    border-radius: 12rpx;
    box-sizing: border-box;
  }

  .experience-flex-view-textarea {
    color: #428a6f;
    font-size: 24rpx;
    font-weight: 600;
  }

  .experience-flex-view {
    flex-direction: row;
    display: flex;
    justify-content: space-between;
    padding: 0 20rpx 0 20rpx;
  }

  .experienceChartRef {
    margin-bottom: 32rpx;
    background: linear-gradient(90deg, #ffffff, #eefeee, #eefeee, #ffffff);
  }

  .experience-error-num {
    font-size: 28rpx;
    color: #333333;
    line-height: 40rpx;
    text-align: left;
    font-style: normal;
  }

  .experience-recommended-promotion {
    font-size: 28rpx;
    color: #333333;
    line-height: 40rpx;
    text-align: left;
    font-style: normal;
    margin-bottom: 16rpx;
  }

  .experience-outstanding {
    font-size: 28rpx;
    color: #333333;
    line-height: 40rpx;
    text-align: left;
    font-style: normal;
    margin-bottom: 16rpx;
  }

  .experience-error-title {
    margin-right: 16rpx;
    font-size: 28rpx;
    color: #333333;
    line-height: 40rpx;
    text-align: left;
    font-style: normal;
  }

  .experience-correct-num {
    margin-right: 38rpx;
    font-size: 28rpx;
    color: #333333;
    line-height: 40rpx;
    text-align: left;
    font-style: normal;
  }

  .experience-correct-title {
    margin-right: 16rpx;
    font-size: 28rpx;
    color: #333333;
    line-height: 40rpx;
    text-align: left;
    font-style: normal;
  }

  .experience-correct-error-block {
    margin-bottom: 30rpx;
  }

  .experience-title-text {
    margin-right: 22rpx;
  }

  .experience-title-img-right {
    width: 36rpx;
    height: 24rpx;
  }

  .experience-title-img-left {
    width: 36rpx;
    height: 24rpx;
    margin-right: 24rpx;
  }

  .review-title-text {
    margin-right: 22rpx;
  }

  .review-title-img-right {
    width: 36rpx;
    height: 24rpx;
  }

  .review-title-img-left {
    width: 36rpx;
    height: 24rpx;
    margin-right: 24rpx;
  }

  .review-comment {
    margin-bottom: 32rpx;
  }

  .reviewChartRef {
    margin-bottom: 32rpx;
    background: linear-gradient(90deg, #ffffff, #eefeee, #eefeee, #ffffff);
  }

  .review-error-title {
    margin-right: 16rpx;
  }

  .review-correct-num {
    margin-right: 38rpx;
  }

  .review-correct-title {
    margin-right: 16rpx;
  }

  .review-correct-error-block {
    text-align: center;
    margin-bottom: 32rpx;
  }

  .review-score-block {
    text-align: center;
    margin-bottom: 24rpx;
  }

  .review-score {
    color: #333333;
  }

  .review-score-value {
    color: #36957a;
    font-weight: 600;
    font-size: 20px;
    font-style: normal;
  }

  .review-experience-card-text {
    overflow-wrap: break-word;
    color: rgba(51, 51, 51, 1);
    font-size: 28rpx;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
  }

  .review-experience-card-text-wrapper {
    height: 38rpx;
    width: 100%;
    margin-right: 196rpx;
    flex-direction: row;
    display: flex;
    margin-bottom: 24rpx;
  }

  .review-experience-card-img {
    width: 189rpx;
    height: 216rpx;
    position: absolute;
    right: 0rpx;
    bottom: 0rpx;
    margin-right: 18rpx;
  }

  .review-experience-card {
    background: #ffffff;
    border-radius: 8px;
    margin-bottom: 24rpx;
    margin-top: 48rpx;
    padding: 40rpx 0rpx 28rpx 24rpx;
    position: relative;
  }

  .text-content-small-font {
    font-size: 28rpx;
    color: #000;
    margin-bottom: 30rpx;
    display: flex;
  }

  .experience-student-status-feedback-title {
    margin-bottom: 24rpx;
    font-weight: 600;
    font-size: 28rpx;
    color: #333333;
    line-height: 40rpx;
    text-align: left;
    font-style: normal;
  }
  .experience-text-content {
    font-size: 28rpx;
    color: #000;
    margin-bottom: 16rpx;
    display: flex;
  }
  .review-comment-content {
    font-size: 24rpx;
    color: #333333;
    background-color: #f9fafb;
    line-height: 42rpx;
    padding: 28rpx 40rpx 28rpx 24rpx;
    height: 214rpx;
    overflow-y: scroll;
    box-sizing: border-box;
    margin-bottom: 32rpx;
  }
  .review-planning-content {
    font-size: 24rpx;
    color: #555555;
    background-color: #f9fafb;
    line-height: 42rpx;
    padding: 28rpx 40rpx 128rpx 24rpx;
    height: 240rpx;
    overflow-y: scroll;
    box-sizing: border-box;
  }

  .review-planning-title {
    margin-bottom: 24rpx;
    font-weight: 600;
    font-size: 28rpx;
    color: #333333;
    line-height: 40rpx;
    text-align: left;
    font-style: normal;
  }
  .review-comment-title {
    margin-bottom: 24rpx;
    font-weight: 600;
    font-size: 28rpx;
    color: #333333;
    line-height: 40rpx;
    text-align: left;
    font-style: normal;
  }

  .review-text-content {
    font-size: 28rpx;
    color: #000;
    margin-bottom: 20rpx;
    display: flex;
  }

  .experience {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 40rpx 30rpx 30rpx 24rpx;
    margin-bottom: 70rpx;
  }

  .experience-title {
    font-size: 32rpx;
    color: #2f896f;
    line-height: 44rpx;
    text-align: center;
    font-style: normal;
    font-weight: 600;
    margin-bottom: 24rpx;
  }

  .review {
    background-color: #fff;
    border-radius: 14rpx;
    padding: 40rpx 30rpx 36rpx 24rpx;
    margin-bottom: 40rpx;
  }

  .review-title {
    font-size: 32rpx;
    color: #2f896f;
    line-height: 44rpx;
    text-align: center;
    font-style: normal;
    font-weight: 600;
    margin-bottom: 24rpx;
  }

  .experience-highLights {
    display: inline;
    flex-wrap: wrap;
    font-weight: 600;
    font-size: 28rpx;
    color: #2f896f;
    line-height: 40rpx;
    text-align: left;
    font-style: normal;
    margin-right: 40rpx;
  }

  .experience-shortComing {
    margin-bottom: 24rpx;
    font-weight: 600;
    font-size: 28rpx;
    color: #ec7a52;
    line-height: 40rpx;
    text-align: left;
    font-style: normal;
    margin-right: 40rpx;
  }

  .text-content-highLights {
    color: #00a500;
  }

  .text-content-shortComing {
    color: #c5c500;
  }

  .nav-title {
    background: #f3f8fc;
    position: fixed;
    height: 150rpx;
    width: 100%;
    z-index: 999;
  }

  .status_bar {
    text-align: center;
    font-size: 40rpx;
    color: #000;
    line-height: 220rpx;
  }

  .icon_img {
    position: absolute;
    left: 10rpx;
  }

  .container {
    background: linear-gradient(#339378, #ffffff);
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 170rpx;
    padding-bottom: 70rpx;
  }

  .formal-container {
    background: #f5f8fa;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 170rpx;
    padding-bottom: 22rpx;
  }

  .page {
    width: 100%;
  }

  .details {
    display: flex;
    font-size: 25rpx;
    padding: 0 30rpx 50rpx;
    margin-top: 20rpx;

    /deep/.u-row {
      margin-bottom: 20rpx !important;
    }

    /deep/.u-demo-block__content {
      width: 98% !important;
      margin-left: 5rpx;
    }

    /deep/.u-col {
      padding: 0 !important;
    }
  }

  .left {
    font-size: 26rpx;
    align-items: center;
  }

  .right {
    position: relative;
    padding: 20rpx 20rpx 40rpx;
    border-radius: 30rpx;
    background-color: #fff;
  }

  .pic {
    position: absolute;
    left: 300rpx;
    top: 30rpx;
    height: 50rpx;
    width: 50rpx;
  }

  .rightbtn {
    float: right;
    position: absolute;
    top: 40rpx;
    right: 40rpx;
  }

  .icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 10rpx;
  }

  .right-content {
    margin-top: 10rpx;
  }

  .tickling {
    text-align: center;
    font-weight: 700;
    font-size: 30rpx;
  }

  .text-content {
    font-size: 28rpx;
    color: #000;
    margin-bottom: 30rpx;
    display: flex;
    font-weight: 600;
  }

  .widthAHalf {
    width: 50%;
  }

  .text_content_inner {
    width: 450rpx;
    white-space: pre-line;
    line-height: 45upx;
  }

  /deep/.u-textarea__field {
    background-color: #f7f7f7;
    border: 1rpx solid #d9d9d9;
    padding: 10rpx;
  }

  /deep/.u-textarea--disabled {
    background-color: #fff !important;
  }

  .button-sp-area {
    display: flex;
  }

  .cancel-btn {
    font-size: 24rpx;
    width: 120rpx;
    height: 55rpx;
    margin-bottom: 30rpx;
  }

  .refresh {
    margin-right: 30rpx;
    color: #2e896f !important;
    border: 1px solid #2e896f;
  }

  .shuaxin-icon {
    width: 23rpx;
    height: 20rpx;
    margin-right: 4rpx;
  }

  .shuaxin-text {
    font-size: 24rpx;
  }

  .refresh-view {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: auto;
    border: 1rpx solid #339378;
    border-radius: 8rpx;
    padding: 4rpx 10rpx 4rpx 10rpx;
  }

  .mini-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 45rpx;
    font-size: 30rpx;
    color: #fff;
    width: 690rpx;
    height: 90rpx;
    line-height: 90rpx;
  }

  .border-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 30rpx;
    font-size: 24rpx;
    color: #1a8eff;
    background-color: #fff;
    border: 1.4rpx solid #1a8eff;
    width: 160rpx;
    transform: scale(0.995); /* 解决ios上圆角失效 */
    height: 60rpx;
  }

  // 弹框的样式设置
  /deep/.card {
    width: 680rpx;

    .white-content {
      background-color: #fff;
      border-radius: 8rpx;
      padding: 40rpx 30rpx 10rpx 30rpx;
      margin-bottom: 40rpx;
    }

    .reality {
      display: flex;
      align-items: center;
      margin-top: 10rpx;
      line-height: 40rpx;
      font-size: 26rpx;
    }

    .begin {
      border: 1px solid #999;
      border-radius: 10rpx;
      height: 40rpx;
      line-height: 40rpx;
      width: 220rpx;
      text-align: center;
      margin-right: 6upx;
      font-size: 24rpx;
      overflow: visible;
      transform: scale(0.995); /* 解决ios上圆角失效 */
    }

    .finish {
      border: 1px solid #999;
      margin-left: 6upx;
      border-radius: 10rpx;
      height: 40rpx;
      line-height: 40rpx;
      width: 220rpx;
      text-align: center;
      font-size: 24rpx;
      overflow: visible;
      transform: scale(0.995); /* 解决ios上圆角失效 */
    }
  }
  .chart {
    width: 100%;
    height: 400px;
  }
  .flexs {
    display: flex;
    align-items: center;
  }

  .borderInput {
    width: 100upx;
    border-bottom: 1upx solid #b1b1b1;
    text-align: center;
  }

  .border-bottom {
    border-bottom: 1rpx solid #efefef;
  }

  .loadingImg {
    width: 120rpx;
    height: 120rpx;
  }

  .loadingpadding {
    padding: 85rpx 250rpx;
  }

  .flex-view-edit {
    color: #555555;
    font-size: 28rpx;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    padding: 0 20rpx 0 20rpx;
  }

  .flex-view {
    color: #555555;
    font-size: 28rpx;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
  }
  .flex-view-text {
    font-weight: 600;
    font-size: 24rpx;
    color: #428a6f;
  }
</style>

<script>
  import { DXHost } from '@/utils/config.js';
  import * as echarts from './components/lime-echart/static/echarts.min.js';
  import lEchart from './components/lime-echart/components/l-echart/l-echart.vue';
  import dateTime from '@/components/lanxiujuan-dyDateTime/lanxiujuan-dyDateTime.vue';
  import dayjs from 'dayjs';
  import longDate from '@/components/long-date/long-date.vue';
  export default {
    name: 'StudyDetails',
    components: {
      dateTime,
      longDate,
      lEchart
    },
    data() {
      return {
        backlist: '', // 获取反馈详情列表
        subject: '', // 课程列表详情
        timelist: {
          actualStart: '',
          actualEnd: ''
        },
        triallist: '', // 试课反馈详情
        isFeedback: false, // 是否显示反馈详情确定按钮
        //反馈详情列表
        trialclass: false, // 是否是试课反馈
        Feedback: false, // 是否显示试课反馈详情确定按钮
        feedback: '', // 弹出层文本框输入的内容
        Intention: '', // 学习意愿
        recall: {
          // 记忆情况
          minutes: '',
          words: ''
        },
        intendToStudy: [
          //学习意愿
          {
            value: '0',
            text: '愿意'
          },
          {
            value: '1',
            text: '不愿意'
          }
        ],
        dates: '',
        flag: false, // 防止多次请求
        isEdit: false, //是否修改
        //复习时间
        showReviewTimeDialog: false,
        reviewTimeArr: ['', '', '']
      };
    },

    onLoad(e) {
      this.dates = e.dates;
      let sendData = JSON.parse(decodeURIComponent(e.sendData));
      if (sendData != null && sendData != undefined) {
        this.trialclass = sendData.trialclass;
        this.isFeedback = sendData.isFeedback;
        this.triallist = sendData.triallist;
        this.subject = sendData.subject;
        this.backlist = sendData.backlist;
        if (this.trialclass) {
          if (this.triallist != null && this.triallist != undefined) {
            this.Feedback = this.triallist.feedback === '' || this.triallist.feedback == null || this.triallist.feedback == undefined;
            this.feedback = this.triallist.feedback;
            this.Intention = this.triallist.studyIntention;
            this.timelist.actualStart = this.triallist.actualStart;
            this.timelist.actualEnd = this.triallist.actualEnd;
            this.reviewTimeArr = this.getNormalReviewTime(this.triallist.reviewDateList);
            this.triallist.extendProperty.experienceFeedbackDto.costTime = this.secondsToTimeFormat(this.triallist.extendProperty.experienceFeedbackDto.costTime); //上课用时
          }
        } else {
          if (this.backlist != null && this.backlist != undefined) {
            this.feedback = this.backlist.feedback;
            this.isFeedback = this.backlist.feedback === '' || this.backlist.feedback == null || this.backlist.feedback == undefined;
            this.timelist.actualStart = this.backlist.actualStart;
            this.timelist.actualEnd = this.backlist.actualEnd;
            this.backlist.extendProperty.costTime = this.secondsToTimeFormat(this.backlist.extendProperty.costTime); //上课用时
          }
          // 学习效率
          if (this.isFeedback) {
            this.backlist.studyRate = '';
          }
        }
      }
    },

    methods: {
      //数值转化为时分秒，入参为秒
      secondsToTimeFormat(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const remainingSeconds = seconds % 60;
        const formattedHours = String(hours).padStart(2, '0');
        const formattedMinutes = String(minutes).padStart(2, '0');
        const formattedSeconds = String(remainingSeconds).padStart(2, '0');
        return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
      },
      //评估课雷达图数据处理
      async reviewInit() {
        let indicator = [];
        let dataValue = [];
        let max = null;
        this.triallist.extendProperty.assessmentFeedbackDto.resultSubjectList.map((el) => {
          max = el.sum;
          indicator.push({ text: el.subjectName + '\n' + '(' + el.score + ')', max: el.sum });
          dataValue.push(el.score);
        });
        if (indicator.length < 5) {
          let tmp = 5 - indicator.length;
          for (let i = 0; i < tmp; i++) {
            indicator.push({ text: '', max });
            dataValue.push(0);
          }
        }
        console.log(dataValue);
        setTimeout(() => {
          this.init('reviewChartRef', dataValue, indicator);
        }, 500);
      },
      //体验课雷达图数据处理
      async experienceInit() {
        let indicator = [];
        let dataValue = [];
        let max = null;
        this.triallist.extendProperty.experienceFeedbackDto.resultSubjectList.map((el) => {
          max = el.sum;
          indicator.push({ text: el.subjectName, max: el.sum });
          dataValue.push(el.score);
        });
        if (indicator.length < 5) {
          let tmp = 5 - indicator.length;
          for (let i = 0; i < tmp; i++) {
            indicator.push({ text: '', max: max });
            dataValue.push(0);
          }
        }
        setTimeout(() => {
          this.init('experienceChartRef', dataValue, indicator);
        }, 1000);
      },
      //正式课雷达图数据处理
      async formalInit() {
        if (!this.backlist) return;
        let indicator = [];
        let dataValue = [];
        let max = null;
        this.backlist.extendProperty.resultSubjectList.map((el) => {
          max = el.sum;
          indicator.push({ text: el.subjectName, max: el.sum });
          dataValue.push(el.score);
        });
        if (indicator.length < 5) {
          let tmp = 5 - indicator.length;
          for (let i = 0; i < tmp; i++) {
            indicator.push({ text: '', max });
            dataValue.push(0);
          }
        }
        setTimeout(() => {
          this.init('formalChartRef', dataValue, indicator);
        }, 500);
      },
      //雷达图初始化
      async init(chartRef, dataValue, Intention) {
        let option = {
          radar: [
            {
              indicator: Intention,
              axisName: {
                color: 'rgba(41, 38, 38, 1)'
                // align: 'center'
              },
              splitArea: {
                // 坐标轴在 grid 区域中的分隔区域，默认不显示。
                show: true,
                areaStyle: {
                  // 分隔区域的样式设置。
                  color: ['rgba(211, 245, 206,1)']
                  // 分隔区域颜色。分隔区域会按数组中颜色的顺序依次循环设置颜色。默认是一个深浅的间隔色。
                }
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: 'rgba(29, 176, 36, 1)'
                }
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: 'rgba(95, 224, 89, 1)' //分割线颜色
                }
              },
              radius: 80
            }
          ],
          series: [
            {
              type: 'radar',
              data: [
                {
                  value: dataValue,
                  name: 'A Software'
                }
              ],

              symbol: 'none',
              lineStyle: {
                color: 'rgba(95, 224, 89, 1)',
                width: 0.5
              },
              areaStyle: {
                color: 'rgba(72, 214, 111,1)'
              }
            }
          ]
        };
        // chart 图表实例不能存在data里
        if (this.$refs[chartRef]) {
          const chart = await this.$refs[chartRef].init(echarts);
          chart.setOption(option);
        }
      },
      //保存雷达图图片
      saveImg(res) {
        let that = this;
        return new Promise((resolve, reject) => {
          res.canvasToTempFilePath({
            success: function (res) {
              uni.uploadFile({
                url: DXHost + '/zxAdminCourse/common/uploadFile', // 你的上传服务器地址
                filePath: res.tempFilePath,
                name: 'file', // 这里根据服务器要求设置
                formData: {
                  user: 'test' // 其他要传的参数
                },
                success: function (uploadFileRes) {
                  resolve(JSON.parse(uploadFileRes.data).data.fileUrl);
                },
                fail: function (uploadFileErr) {
                  reject(uploadFileErr);
                }
              });
            }
          });
        });
      },
      getNormalReviewTime(arr) {
        if (!arr) {
          arr = [];
        }
        if (arr.length >= 3) {
          return arr;
        }
        for (let i = 0; i < 3; i++) {
          if (!arr[i]) {
            arr.push('');
          }
        }
        return arr;
      },
      //清空
      cleanFeedback() {
        this.feedback = '';
      },
      //修改按钮
      editOption() {
        this.recall.minutes = this.triallist.memoryTime;
        this.recall.words = this.triallist.memoryNum;
        this.Intention = this.getIntentionIndex(this.triallist.studyIntention);
        this.isEdit = true;
      },
      getIntentionIndex(date) {
        for (let i = 0; i < this.intendToStudy.length; i++) {
          if (this.intendToStudy[i].text == date) {
            return this.intendToStudy[i].value;
          }
        }
        return '0';
      },
      // 获取试课详情
      getDetail() {
        if (this.trialclass) {
          //试课
          this.trialData(this.subject);
        } else {
          //学习反馈
          this.backData();
        }
      },
      // 确定按钮（关闭弹框）
      async refresh() {
        let that = this;
        if (that.flag) return;
        that.flag = true;
        this.$refs.loadingPopup.open();
        try {
          let res = await uni.$http.get('/deliver/app/teacher/refreshFeedbackInfo', {
            id: this.subject.id,
            actualStart: this.timelist.actualStart,
            actualEnd: this.timelist.actualEnd,
            type: 1
          });
          this.$refs.loadingPopup.close();
          setTimeout(() => {
            that.flag = false;
          }, 60000);
          if (res.data.success) {
            that.backlist = res.data.data;
            that.isFeedback = that.backlist.feedback === '' || that.backlist.feedback == null || that.backlist.feedback == undefined;
            that.feedback = that.backlist.feedback;
            that.timelist.actualStart = that.backlist.actualStart;
            that.timelist.actualEnd = that.backlist.actualEnd;
            that.backlist.extendProperty.costTime = that.secondsToTimeFormat(that.backlist.extendProperty.costTime); //上课用时
          }
        } catch {
          this.$refs.loadingPopup.close();
        }
      },
      // 确定按钮（关闭弹框）
      addOrLook() {
        if (this.isEdit) {
          this.editFeedback();
          return;
        }
        if (this.subject.experience == false) {
          this.addbackData(false); // 新增反馈  // experience = true 是试课反馈
        } else {
          this.addtrialData(); // 新增试课反馈  // experience = false 是学习反馈
        }
      },
      getValidReviewArr() {
        let dateArr = [];
        for (var i = 0; i < this.reviewTimeArr.length; i++) {
          if (this.reviewTimeArr[i]) {
            dateArr.push(this.reviewTimeArr[i]);
          }
        }
        return dateArr;
      },
      async editFeedback() {
        let that = this;
        that.$refs.loadingPopup.open();
        let postUrl = {};
        if (!this.trialclass) {
          postUrl = {
            feedback: that.feedback,
            studyId: that.subject.id,
            studyIntention: '',
            memoryNum: '',
            memoryTime: '',
            reviewDateList: null
          };
        } else {
          // if (that.triallist.moduleType != 3 && that.triallist.moduleType != 2) {
          //   if (that.recall.minutes == '' || that.recall.words == '') {
          //     that.$util.alter('记忆特点字段未填写');
          //     return;
          //   }
          // }
          // if (that.Intention == '') {
          //   that.$util.alter('体验后学习意愿未填写');
          //   return;
          // }
          let reviewArr = this.getValidReviewArr();
          let intention = that.intendToStudy[that.Intention].text;
          postUrl = {
            feedback: that.feedback,
            studyId: that.subject.id,
            studyIntention: intention,
            memoryNum: that.recall.words,
            memoryTime: that.recall.minutes,
            reviewDateList: reviewArr
          };
        }
        try {
          let res = await uni.$http.post('/deliver/app/teacher/updateFeedback', postUrl);
          that.$refs.loadingPopup.close();
          if (res.data.success) {
            uni.showToast({
              title: '修改反馈成功',
              duration: 1000
            });
            that.isEdit = false;
            that.getDetail();
          }
        } catch {
          that.$refs.loadingPopup.close();
        }
      },
      // 新增学习反馈
      async addbackData(isConfirm) {
        let that = this;

        that.$refs.loadingPopup.open();
        let postUrl = `/deliver/app/teacher/addSimpleFeedback?actualStart=${that.timelist.actualStart}&actualEnd=${that.timelist.actualEnd}&feedBack=${encodeURI(
          encodeURI(that.feedback)
        )}&id=${that.subject.id}&type=1&studyRate=${that.backlist.studyRate}`;
        if (isConfirm) {
          postUrl = postUrl + '&confirm=true';
        }
        try {
          let res = await uni.$http.post(postUrl);
          that.$refs.loadingPopup.close();
          if (res.data.code == 40047) {
            uni.showModal({
              title: '提示',
              content: res.data.message,
              success: function (res) {
                if (res.confirm) {
                  that.addbackData(true);
                } else if (res.cancel) {
                  console.log('用户点击取消');
                }
              }
            });
            return;
          }
          // 成功提示
          if (res.data.success) {
            uni.showToast({
              title: '反馈成功',
              duration: 1000
            });
            that.backlist.feedback = that.feedback;
            //显示分享按钮
            that.isFeedback = false;
            that.getDetail();
          }
        } catch {
          that.$refs.loadingPopup.close();
        }
      },
      // 新增试课反馈
      async addtrialData() {
        let that = this;
        that.$refs.loadingPopup.open();
        let reviewArr = this.getValidReviewArr();
        // let intention = that.intendToStudy[that.Intention].text;
        let memoryTime = '';
        let memoryNum = '';
        if (that.triallist.moduleType == 3 || that.triallist.moduleType == 2) {
          memoryTime = 0;
          memoryNum = 0;
        } else {
          memoryTime = that.recall.minutes;
          memoryNum = that.recall.words;
        }
        let data = {
          feedback: that.feedback,
          memoryTime: memoryTime,
          memoryNum: memoryNum,
          studyId: that.subject.id,
          // studyIntention: intention,
          actualStart: that.timelist.actualStart,
          actualEnd: that.timelist.actualEnd,
          reviewDateList: reviewArr
        };
        let postUrl = `/deliver/app/teacher/addSimpleExperienceFeedback`;
        try {
          let res = await uni.$http.post(postUrl, data);
          that.$refs.loadingPopup.close();
          if (res.data.success) {
            uni.showToast({
              title: '反馈成功',
              duration: 1000
            });
            //显示分享按钮
            that.Feedback = false;
            that.getDetail();
          }
        } catch {
          that.$refs.loadingPopup.close();
        }
      },
      //分享
      async shareJump() {
        let sendData = {};
        sendData.trialclass = this.trialclass;
        sendData.isStudy = true;

        if (this.trialclass) {
          this.triallist.reviewTimeArr = this.reviewTimeArr;
          if (this.triallist.extendProperty.assessmentFeedbackDto)
            //是否有评估课
            this.triallist.reviewRadaImg = await this.saveImg(this.$refs.reviewChartRef);
          this.triallist.experienceRadaImg = await this.saveImg(this.$refs.experienceChartRef);
          sendData.detailsData = this.triallist;
        } else {
          this.backlist.formalRadaImg = await this.saveImg(this.$refs.formalChartRef);
          sendData.detailsData = this.backlist;
        }
        let newstr=''
        // #ifdef MP-WEIXIN
        newstr =JSON.stringify(sendData)
        // #endif
        // #ifdef APP-PLUS
        newstr =JSON.stringify(sendData).replace(/%/g,'%25');
        // #endif
        uni.navigateTo({
          url: '/share/dxn_share_feedback?sendData=' + encodeURIComponent(newstr)
        });
      },
      Back() {
        uni.$emit('onCalendarRefresh', this.dates);
        uni.navigateBack();
      },
      // 获取反馈详情
      async backData() {
        let that = this;
        let res = await uni.$http.get('/deliver/app/teacher/getFeedbackInfo', {
          id: this.subject.id,
          actualStart: this.timelist.actualStart,
          actualEnd: this.timelist.actualEnd,
          type: 1
        });
        if (res.data.success) {
          that.backlist = res.data.data;
          that.isFeedback = that.backlist.feedback === '' || that.backlist.feedback == null || that.backlist.feedback == undefined;
          that.feedback = that.backlist.feedback;
          that.timelist.actualStart = that.backlist.actualStart;
          that.timelist.actualEnd = that.backlist.actualEnd;
          that.backlist.extendProperty.costTime = that.secondsToTimeFormat(that.backlist.extendProperty.costTime); //上课用时
        }
      },
      // 试课反馈详情
      async trialData(item) {
        let that = this;
        let res = await uni.$http.post(`/deliver/app/teacher/getExperienceInfo/${that.subject.id}?actualStart=${that.timelist.actualStart}&actualEnd=${that.timelist.actualEnd}`);
        that.triallist = res.data.data;
        if (res.data.success) {
          that.Feedback = that.triallist.feedback === '' || that.triallist.feedback == null || that.triallist.feedback == undefined;
          that.feedback = that.triallist.feedback;
          that.Intention = that.triallist.studyIntention;
          that.timelist.actualStart = that.triallist.actualStart;
          that.timelist.actualEnd = that.triallist.actualEnd;
          that.triallist.extendProperty.experienceFeedbackDto.costTime = that.secondsToTimeFormat(that.triallist.extendProperty.experienceFeedbackDto.costTime); //上课用时
          setTimeout(function () {
            that.$refs.chanelTimetry.setLists(that.timelist);
          }, 500);
        }
      }
    }
  };
</script>

<template>
  <view :style="{ height: useHeight + 'rpx' }">
    <u-navbar :title="title" bgColor="#f3f8fc" placeholder>
      <view class="u-nav-slot" slot="left" @click="goBack">
        <u-icon name="arrow-left" size="20" color="#000" bold v-if="type > currentType && type != 1"></u-icon>
        <view class=""></view>
      </view>
    </u-navbar>
    <view class="container">
      <view class="pd-30" v-if="list.length == 0">
        <u-empty mode="list" icon="https://document.dxznjy.com/automation/1728442200000"></u-empty>
      </view>
      <view class="pd-30" v-else>
        <view class="item" v-for="(item, index) in list" :key="index">
          <view class="title">
            <view class="name">{{ item.title }}</view>
            <view class="time">{{ item.time }}</view>
          </view>
          <view class="msg">{{ item.importNotSendCount }}条重要消息未发</view>
          <view class="msg">{{ item.notImportNotSendCount }}条普通消息未发，{{ item.notImportDelayCount }}条普通消息超时发送</view>
          <view class="msg">考勤试课迟到{{ item.experienceLateCount }}次，正式课迟到{{ item.learnLateCount }}次。早退{{ item.earlyleaveCount }}次</view>
          <view class="btn" @click="goDetail(item)">查看详情</view>
          <view class="line"></view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        useHeight: 0,
        type: 1,
        currentType: 1,
        time: '',
        endTime: '',
        queryData: {},
        title: '交付中心总结通知',
        list: [],
        lastKey: ''
      };
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 30;
        }
      });
    },

    watch: {
      type: function (val) {
        console.log(val);
        if (val == 1) {
          this.title = '交付中心总结通知';
        } else if (val == 2) {
          this.title = '小组总结通知';
        } else if (val == 3) {
          this.title = '教练反馈总结';
        }
      }
    },
    onLoad(e) {
      if (e.type) {
        this.type = e.type;
        this.currentType = e.type;
        this.time = e.time;
        this.endTime = e.endTime;
        this.initData(e.key, e.time, e.endTime, e.type);
      }
    },
    onShow() {
      if (this.type == 4) {
        this.type = 3;
      }
    },
    methods: {
      goBack() {
        let that = this;
        if (that.type == 1) {
          return;
        } else {
          that.type = Number(that.type) - 1;
          that.initData(that.lastKey, that.time, that.endTime, that.type);
        }
        // if (that.type == 1) {
        //   return;
        // } else {
        //   let type = Number(that.type) - 1;
        //   let currentType = Number(that.currentType);
        //   if (type < currentType) {
        //     return;
        //   } else {
        //     that.initData(that.lastKey, that.time, that.endTime, type);
        //   }
        // }
      },
      // 复制
      goDetail(item) {
        this.queryData = item;
        this.type = item.type;
        if (item.type == 4) {
          uni.navigateTo({
            url: `/qyWechat/msgCountDetail?item=${encodeURIComponent(JSON.stringify(item))}&endTime=${this.endTime}`
          });
        } else if (item.type == 2) {
          uni.setNavigationBarTitle({
            title: '小组总结通知'
          });
        } else if (item.type == 3) {
          uni.setNavigationBarTitle({
            title: '教练反馈总结'
          });
          this.initData(item.key, this.time, this.endTime, item.type);
        }
      },
      initData(key, time, endTime, type) {
        uni.showLoading({ title: '加载中' });
        this.$scrmHttp
          .get('/scrm/evaluate/statisticsDetail', {
            key,
            time,
            endTime,
            type
          })
          .then(({ data }) => {
            // console.log(data.data);
            // this.list = data.data;
            this.list = Object.freeze(data.data.teacherStatisticsVos);
            this.lastKey = data.data.lastKey;
            uni.hideLoading();
          })
          .catch((err) => {
            uni.hideLoading();
          });
      }
    }
  };
</script>

<style lang="scss">
  .container {
    margin-top: 20rpx;
    width: 690rpx;
    min-height: 1000rpx;
    background: #ffffff;
    border-radius: 14rpx;
    margin: 30rpx auto;
    position: relative;
  }
  .pd-30 {
    padding: 30rpx;
    line-height: 1.4;
  }
  .item {
    margin-top: 20rpx;
  }

  .title {
    display: flex;
    justify-content: space-between;

    .name {
      font-size: 30rpx;
      color: #000;
    }
    .time {
      color: #666;
      font-size: 28rpx;
    }
  }
  .msg {
    color: #666;
    font-size: 28rpx;
    margin-top: 20rpx;
  }
  .btn {
    color: #2e896f;
    font-size: 28rpx;
    margin-top: 30rpx;
  }
  .line {
    width: 630rpx;
    border: 1rpx solid #efefef;
    margin: 30rpx auto;
  }
</style>

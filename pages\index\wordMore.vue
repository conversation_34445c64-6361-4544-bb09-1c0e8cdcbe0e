<template>
  <view class="bg p-30" :style="'height:' + useHeight + 'rpx;'">
    <view class="bg-ff radius-15 plr-30 ptb-40" :style="'height:' + (useHeight - 60) + 'rpx;'">
      <view class="t-l" v-for="(item, index) in courseDataList">
        <view class="mb-40 c-33 f-30 text-hide">{{ item.courseName }}</view>
      </view>
    </view>

    <uni-popup ref="loadingPopup" type="center" :is-mask-click="false">
      <view class="bg-ff radius-15 t-c">
        <view class="loadingpadding">
          <image src="https://document.dxznjy.com/Assistant/loading.png" class="loadingImg"></image>
          <view class="mt-30">加载中...</view>
          <view>请耐心等候</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        useHeight: 0,

        merchantCode: '',
        studentCode: '',

        courseDataList: []
      };
    },

    onLoad(option) {
      if (option.merchantCode) {
        this.merchantCode = option.merchantCode;
      }
      if (option.studentCode) {
        this.studentCode = option.studentCode;
      }
    },
    onReady() {
      let that = this;
      uni.getSystemInfo({
        //调用uni-app接口获取屏幕高度
        success(res) {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          that.useHeight = h;
        }
      });
    },
    // #ifdef MP-WEIXIN
    onShow() {
      this.getStudentCourseList();
    },
    // #endif
    // #ifdef APP-PLUS
    mounted() {
      this.getStudentCourseList();
    },
    // #endif
    methods: {
      async getStudentCourseList() {
        if (!this.merchantCode || !this.studentCode) {
          uni.showToast({
            icon: 'none',
            title: '数据获取失败'
          });
          return;
        }

        this.$refs.loadingPopup.open('center');
        console.log(this.$refs, '调用getStudentCourseList');
        try {
          let res = await uni.$http.get('/deliver/app/teacher/selStudentCourseList', {
            merchantCode: this.merchantCode,
            studentCode: this.studentCode
          });
          this.$refs.loadingPopup.close();
          console.log(res, '获取参数');
          if (res.data.success) {
            this.courseDataList = res.data.data;
          }
        } catch (e) {
          this.$refs.loadingPopup.close();
        }
      }
    }
  };
</script>

<style>
  .bg {
    background: #f3f8fc;
  }
  .loadingImg {
    width: 120rpx;
    height: 120rpx;
  }
  .loadingpadding {
    padding: 85rpx 250rpx;
  }
  .text-hide {
    text-overflow: ellipsis; /* 当文本溢出时显示省略号 */
    white-space: nowrap; /* 禁止文本换行 */
    overflow: hidden; /* 隐藏溢出部分文本 */
  }
</style>

<template>
  <view class="contaiter">
    <view class="paddingb">
      <view class="potop">
        <view class="top">
          <text class="timeFn">日期</text>
          <text class="wordsFn">复习单词数</text>
          <text>正确率</text>
          <text>查看详情</text>
        </view>
      </view>
      <u-empty v-if="historylist.length == 0" mode="history" icon="http://cdn.uviewui.com/uview/empty/history.png"></u-empty>
      <view v-if="historylist.length !== 0" class="history-item" style="padding-bottom: 100rpx">
        <view class="list" v-for="(item, index) in historylist" :key="index">
          <text class="list-timer">{{ item.studyData }}</text>
          <text>{{ item.wordCount }}</text>
          <text>{{ item.rate }}%</text>
          <image src="https://document.dxznjy.com/applet/newimages/xiangqing.png" mode="" @click="goUrl(item)" class="list-img"></image>
          <!-- 				<view class="detail_btn" @click="goUrl(item.id)">查看详情</view> -->
        </view>
        <!-- <view class="zanwu" :status="loadingType">没有更多数据了~</view> -->
      </view>
      <view class="zanwu" :status="loadingType">没有更多数据了~</view>
      <view class="bottomFixed" v-if="sendType">
        <view class="sendButton" @click="sendMsg">发送</view>
      </view>
    </view>
    <!-- <uni-load-more :status="loadingType"></uni-load-more> -->
  </view>
</template>

<script>
  export default {
    components: {},
    data() {
      return {
        loadingType: 'more', //加载更多状态
        historylist: [],
        pageindex: 1,
        pageSize: 20,
        studentCode: '',
        sendType: false,
        queryData: {},
        isShare: false,
        reviewType: 0
      };
    },
    methods: {
      sendMsg() {
        let that = this;
        // that.sendData.isSend = 0;
        // 发文字
        wx.qy.getContext({
          success: () => {
            //    var entry = data.entry, //返回进入小程序的入口类型
            // var shareTicket = data.shareTicket;
            wx.qy.sendChatMessage({
              msgtype: 'miniprogram', //消息类型，必填
              enterChat: false, //为true时表示发送完成之后顺便进入会话，仅移动端3.1.10及以上版本支持该字段
              text: {
                content: '' //文本内容
              },
              image: {
                mediaid: '' //图片的素材id
              },
              video: {
                mediaid: '' //视频的素材id
              },
              file: {
                mediaid: '' //文件的素材id
              },
              news: {
                link: '', //H5消息页面url 必填
                title: '', //H5消息标题
                desc: '', //H5消息摘要
                imgUrl: '' //H5消息封面图片URL
              },
              miniprogram: {
                appid: 'wxce2bd1113a024ff6', //小程序的appid
                title: '往期复习记录', //小程序消息的title
                imgUrl: 'https://document.dxznjy.com/manage/1716255999000', //小程序消息的封面图
                page: `/antiForgetting/history.html?studentCode=${that.studentCode}&isSend=0` //小程序消/小程序消息打开后的路径，注意要以.html作为后缀，否则在微信端打开会提示找不到页面
                // page: `/pages/index/study_feedback?sendData=${encodeURIComponent(JSON.stringify(that.sendData))}` //小程序消息打开后的路径，注意要以.html作为后缀，否则在微信端打开会提示找不到页面
              },
              success: (res) => {
                that.$http
                  .get('/deliver/teacher/notify/details', {
                    id: that.queryData.id,
                    isSend: 1,
                    boundInfo: that.queryData.boundInfo,
                    message: that.queryData.msgType
                  })
                  .then(() => {
                    uni.showToast({
                      title: '发送成功'
                    });
                    setTimeout(() => {
                      uni.redirectTo({
                        url: '/qyWechat/notice'
                      });
                    }, 800);
                  })
                  .catch((err) => {
                    uni.showToast({
                      title: '发送失败',
                      icon: 'none'
                    });
                  });
              },
              fail: (err) => {
                console.log(err, '=============================');
              }
            });
          },
          fail: (err) => {
            console.log(err, '111111111111111');
          }
        });
      },
      async loadMyMember(type = 'add', loading) {
        if (type === 'add') {
          if (this.loadingType === 'nomore') {
            return;
          }
          this.loadingType = 'loading';
        } else {
          this.loadingType = 'more';
        }
        var mindex = this.pageindex;
        let result = await uni.$http.get('/znyy/review/query/student/word/review/' + mindex + '/' + this.pageSize + '/' + this.studentCode);
        // let result = await uni.$http.get(`/znyy/review/query/student/word/review/${mindex}/${this.pageSize}/${this.studentCode}?type=${this.reviewType}`);
        if (type === 'refresh') {
          this.historylist = [];
        }
        if (result) {
          // if (mindex <= 1 && result.data.data.data.length == 0) {
          if (result.data.data.data.length == 0) {
            // this.historylist = [];
            this.loadingType = 'nodata';
          } else {
            if (result.data.data.data.length) {
              this.historylist = this.historylist.concat(result.data.data.data);
            }
            this.loadingType = this.pageindex >= result.data.data.totalPage ? 'nomore' : 'more';
          }
          if (type === 'refresh') {
            if (loading == 1) {
              uni.hideLoading();
            } else {
              uni.stopPullDownRefresh();
              this.loadingType = 'nomore';
            }
          }
        }
      },
      goUrl(item) {
        // return console.log(item);
        if (item.type == 1) {
          uni.redirectTo({
            url: `/antiForgetting/aiReviewReport?reviewId=${item.id}`
          });
        } else {
          let url = '';
          if (this.sendType) {
            url = `/antiForgetting/historyReviewReport?reviewId=${item.id}&isShare=true&isSend=0`;
          } else {
            url = `/antiForgetting/historyReviewReport?reviewId=${item.id}`;
          }
          uni.redirectTo({
            url: url
          });
        }
      }
    },
    onLoad(options) {
      let that = this;
      that.sendType = options.isSend == 1 ? true : false;
      that.isShare = options.isShare == 'true' ? true : false;
      if (options.item) {
        that.queryData = JSON.parse(decodeURIComponent(options.item));
      }
      this.studentCode = options.studentCode;
      // this.reviewType = options.reviewType;
      this.loadMyMember();
    },
    onPageScroll(e) {
      if (e.scrollTop >= 0) {
        this.headerPosition = 'fixed';
      } else {
        this.headerPosition = 'absolute';
      }
    },
    onPullDownRefresh() {
      this.pageindex = 1;
      this.loadMyMember('refresh');
    },
    //加载更多
    onReachBottom() {
      this.pageindex++;
      this.loadMyMember();
    }
  };
</script>

<style>
  .contaiter {
    /* 		margin-top: 10rpx; */
    /* '		height: 1311rpx;' */
  }

  page {
    background: #f3f8fc !important;
  }

  .paddingb {
    padding: 100rpx 0;
    width: 690rpx;
    /* position: relative; */
    margin: 0 auto;
    background: #ffffff;
    border-radius: 14rpx;
    min-height: 600rpx;
  }

  .potop {
    position: fixed;
    top: 0;
    width: 690rpx;
    height: 100rpx;
    background-color: #fff;
    z-index: 999;
  }

  .top {
    height: 100rpx;
    display: flex;
    align-items: center;
    justify-content: space-around;
    font-size: 30rpx;
    border-bottom: 1px solid #e5e5e5;
    padding-left: 20rpx;
    /* 不放大不缩小固定100rpx */
  }

  .topG {
    height: 100rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 30rpx;
    border-bottom: 1px solid #e5e5e5;
    padding-left: 40rpx;
  }

  .list {
    display: flex;
    align-items: center;
    font-size: 30rpx;
    border-bottom: 1px solid #e5e5e5;
    height: 100rpx;
    color: #666;
    padding-left: 30rpx;
  }

  .list-img {
    width: 29rpx;
    height: 34rpx;
    margin-left: 70rpx;
  }

  .list text {
    display: block;
    width: 25%;
    text-align: center;
  }

  .zanwu {
    margin: 0 auto;
    margin-top: 20rpx;
    margin-bottom: 20rpx;
    text-align: center;
    font-size: 28rpx;
    color: #b3b7ba;
  }

  .timeFn {
    width: 80rpx;
    margin-left: 40rpx;
  }

  .wordsFn {
    margin-left: 39rpx;
  }

  .list-timer {
    width: 182rpx !important;
  }

  .detail_btn {
    width: 140rpx;
    height: 58rpx;
    background-color: #007aff;
    text-align: center;
    line-height: 58rpx;
    color: #fff;
    border-radius: 50%;
  }

  .bottomFixed {
    z-index: 999;
    position: fixed;
    bottom: 10rpx;
    width: 690rpx;
    /* background-color: #fff; */
    /* border-radius: 20rpx 20rpx 0 0; */
  }

  .sendButton {
    width: 586rpx;
    height: 90rpx;
    line-height: 90rpx;
    color: #ffffff;
    font-size: 30rpx;
    text-align: center;
    background: #2e896f;
    border-radius: 45rpx;
    margin: 10rpx auto 0;
  }
</style>

<template>
  <view class="plr-30" :style="{ height: useHeight + 'rpx' }">
    <view class="c-99 mb-30 f-32 mt-20">试听</view>
    <view class="radius-15 bg-ff p-30">
      <view class="f-46 mb-30">
        <view class="flex-c bold c-33">
          <text class="mr-20">{{ word }}</text>
          <image :src="imgHost + 'dxSelect/fourthEdition/refresh.png'" class="refresh_icon" @click="randomWords"></image>
        </view>
        <view class="mt-40 flex-c" @click="sayWord">
          <image :src="imgHost + 'dxSelect/fourthEdition/icon_audition.png'" class="icon_audition"></image>
          <text class="audition f-28 ml-15">试听</text>
        </view>
      </view>
    </view>

    <view class="c-99 mtb-30 f-32" v-if="playType == 2">音色</view>
    <view class="radius-15 bg-ff plr-30" v-if="playType == 2">
      <view class="flex-s ptb-30 line" @click="changeTimbre('M')">
        <view>男声</view>
        <image v-if="timbre == 'M'" :src="imgHost + 'dxSelect/fourthEdition/icon_fyxz.png'" class="icon_audition"></image>
      </view>
      <view class="flex-s ptb-30" @click="changeTimbre('W')">
        <view>女声</view>
        <image v-if="timbre == 'W'" :src="imgHost + 'dxSelect/fourthEdition/icon_fyxz.png'" class="icon_audition"></image>
      </view>
    </view>

    <view class="c-99 mtb-30 f-32">英文发音</view>
    <view class="radius-15 bg-ff plr-30">
      <view class="flex-s ptb-30 line" @click="changeType(1)">
        <view>英式</view>
        <image v-if="pronunciationType == 1" :src="imgHost + 'dxSelect/fourthEdition/icon_fyxz.png'" class="icon_audition"></image>
      </view>
      <view class="flex-s ptb-30" @click="changeType(0)">
        <view>美式</view>
        <image v-if="pronunciationType == 0" :src="imgHost + 'dxSelect/fourthEdition/icon_fyxz.png'" class="icon_audition"></image>
      </view>
    </view>
    <!-- #ifdef MP-WEIXIN -->
    <view class="switch c-ff t-c" @click="switchType">{{ playType == 2 ? '切换至1.0' : '切换至2.0' }}</view>
    <!-- #endif -->

    <!-- 单词错误 -->
    <uni-popup ref="notifyPopup" type="top" mask-background-color="rgba(0,0,0,0)">
      <view class="plr-60">
        <view class="t-c bg-ff flex-c ptb-25 radius-50 mt-30 notify">
          <image :src="imgHost + 'dxSelect/err_tip.png'" class="err_tip"></image>
          <view class="f-34 ml-15">单词有误，请重新输入</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>
<script>
import CryptoJS from 'crypto-js';
var innerAudioContext;
export default {
  data() {
    return {
      imgHost: 'https://document.dxznjy.com/',
      word: 'strawberry',
      timbre: 'W', // 音色默认女声 M  W
      pronunciationType: 0, // 1英式  0美式  默认美式
      voiceModel: '',
      useHeight: 0,
      playType: 2, // 版本 默认2.0
      isplay: false,
      stsList: {},
      studentCode: '',
      linkUrl: '',
      Expires: '',
      canonicalString: '',
      signature: '',
      SignatureStr: '',
      otherWord: ['strawberry', 'keep pace with', 'kangaroo', 'foreign', 'by means of', 'as a result of', 'word'], // 随机单词
      sg: ''
    };
  },
  onReady() {
    uni.getSystemInfo({
      success: (res) => {
        // 可使用窗口高度，将px转换rpx
        let h = res.windowHeight * (750 / res.windowWidth);
        this.useHeight = h - 30;
      }
    });
  },
  onLoad(options) {
    let that = this;
    let data = JSON.parse(decodeURIComponent(options.list));
    that.studentCode = data.studentCode;
    // #ifdef MP-WEIXIN
    that.playType = data.v ? data.v : 2;
    // #endif
    // #ifdef APP-PLUS
    that.playType = 1
    console.log('-------------------------------------------------------------')
    // #endif

    that.pronunciationType = data.rq ? data.rq : 0;
    that.timbre = data.sex ? data.sex : 'W';
    innerAudioContext = uni.createInnerAudioContext();
    innerAudioContext.onPlay(() => {
      console.log('开始播放');
    });
    innerAudioContext.onStop(function () {
      console.log('播放结束');
      that.isplay = false;
    });
    innerAudioContext.onPause(function () {
      console.log('播放暂停');
      that.isplay = false;
    });
    innerAudioContext.onError((res) => {
      console.log(res.errMsg);
      console.log(res.errCode);
      that.isplay = false;
    });
  },
  onShow() {
    let data = this.studentCode + 'L0anhf';
    this.sg = CryptoJS.SHA1(data).toString();
  },
  methods: {
    // 随机单词
    randomWords() {
      let that = this;
      const randomIndex = Math.floor(Math.random() * that.otherWord.length);
      that.word = that.otherWord[randomIndex];
    },
    changeTimbre(item) {
      this.timbre = item;
      this.savePronunciation();
    },

    changeType(item) {
      this.pronunciationType = item;
      this.savePronunciation();
    },

    switchType() {
      if (this.playType == 1) {
        this.playType = 2;
        this.timbre = 'W';
      } else {
        this.playType = 1;
      }
      this.savePronunciation();
    },

    async getStS() {
      this.$http.get('/znyy/alibaba/oss/privateRead/sts').then((res) => {
        if (res.data.success) {
          this.stsList = res.data.data;
        } else {
          uni.showToast({
            title: res.data.message,
            icon: 'none'
          });
        }
      });
    },

    // 试听
    sayWord() {
      var that = this;
      that.$http
        .get('/znyy/app/query/word/voice', {
          word: that.word,
          v: that.playType,
          rp: that.pronunciationType == 1 ? true : false,
          sex: that.timbre,
          studentCode: that.studentCode,
          sg: that.sg
        })
        .then((res) => {
          // console.log(res)
          if (res.data.success) {
            // var w = encodeURIComponent(res.data.data);
            let voiceUrl;
            //privatedoc.dxznjy.com/new/audio/word/en/strawberry.mp3?Expires=1740559038&OSSAccessKeyId=STS.NUjZrEw3PqnNSzU25AEhD1LsL&Signature=wKYDfLfQt5oaEGsSTccIXhtttDg%3D&security-token=CAISugJ1q6Ft5B2yfSjIr5bfEcjxmuxxxqylUVzk1jUUSedo3onYrjz2IH1OfXBsB%2BEWs%2FUynmBT6P4elqV%2FW8EfGBSeNJAuscsPVdN7gDab6aKP9rUhpMCPOwr6UmzWvqL7Z%2BH%2BU6muGJOEYEzFkSle2KbzcS7YMXWuLZyOj%2BwMDL1VJH7aCwBLH9BLPABvhdYHPH%2FKT5aXPwXtn3DbATgD2GM%2BqxsmsP%2FkmZPBs0aG1gWjlrdMnemrfMj4NfsLFYxkTtK40NZxcqf8yyNK43BIjvwr1vUdpWmW7ozMXgMBuEveb%2FC%2F6cF0PN6u2lTwqkcuRpHd4h4Y0oS7%2F%2Byfo50d5vlD4FmUDQYslH5O%2FE99qjKVUfeaXTqEm5uEDfKu6GAHSmkAL3hhM4AYIkd6gBZUQCnSRMdIXwPMPG%2FLIxqAAarjgudufrU7RuR0kq76xM%2FlYWBT%2F2e0J3ZiSmwF0acsdYcUXBrtS1FY%2Fpqht7x%2FEXR%2FZoYxPBcjO5SgjQu7EwECn6u1RtZqZ0TCIZ1CNGVTeVD22jp%2BQn9mIb26WB5aFigwcX8fEKzdjp5cr7K3Xn1XRaPkgeoIwRNV%2BT3wrJOBIAA%3Dt url;
            lehttps: if (that.playType == 1) {
              console.log(res.data.data);
              console.log('---------------------------------------------');
              voiceUrl = 'https://document.dxznjy.com/' + encodeURIComponent(res.data.data);
              this.linkUrl = voiceUrl;
            } else {
              voiceUrl = res.data.data;
              this.linkUrl = voiceUrl;
              console.log(res.data.data);
              console.log('+++++++++++++++++++++++++++++++++++++++++');
            }
            // innerAudioContext.obeyMuteSwitch = false;
            innerAudioContext.src = this.linkUrl;
            innerAudioContext.play();
          } else {
            that.$util.alter(res.data.message);
          }
        });
    },
    // 保存发音设置
    savePronunciation() {
      var that = this;
      let v = this.playType;
      let rp = this.pronunciationType;
      let sex = this.timbre;
      let voiceModel = v + '#' + rp + '#' + sex;
      console.log(voiceModel);
      that.$http
        .put('/znyy/course/updateStudentInfo', {
          studentCode: that.studentCode,
          voiceModel: voiceModel
        })
        .then((res) => {
          console.log(res);
          if (res.data.success) {
            console.log('保存成功');
          } else {
            that.$util.alter(res.data.message);
          }
        });
    },

    getImg() {
      uni.uploadFile({
        url: aliyunServerURL, //开发者服务器 url
        filePath: filePath, //要上传文件资源的路径
        name: 'file', //必须填file
        formData: {
          key: aliyunFileKey,
          policy: env.policy,
          OSSAccessKeyId: accessid,
          signature: env.AccessKeySecret,
          success_action_status: '200'
        },
        success: function (res) {
          if (res.statusCode != 200) {
            failc(new Error('上传错误:' + JSON.stringify(res)));
            return;
          }

          //SignatureStr 就是你签名出来的东西
          //最后拼接起来
          store.dispatch('ali/getAliOssSTS').then((data) => {
            const EXPIRES = Date.parse(new Date()) / 1000 + 3600; //当前时间的3600秒以后，必须是时间戳格式
            let canonicalString = `GET\n\n\n${EXPIRES}\n/${data.Bucket}/${aliyunFileKey}?security-token=${data.SecurityToken}`;
            //签名拼接的字符串按照官方的要求。
            //要注意的是，如果你的图片下载需要临时授权的STS凭证，要把这个凭证也放进来签名。不然签名校验会失败
            let signature = crypto2.createHmac('sha1', data.AccessKeySecret);
            let SignatureStr = encodeURIComponent(signature.update(Buffer.from(canonicalString, 'utf-8')).digest('base64'));
            // const store = new OSS({
            //   accessKeyId: data.AccessKeyId,
            //   accessKeySecret: data.AccessKeySecret,
            //   stsToken: data.SecurityToken,
            //   // region表示Bucket所在地域。以华东1（杭州）为例，设置region为oss-cn-hangzhou
            //   region: 'oss-cn-qingdao',
            //   bucket: data.Bucket})

            let updataImageUrl = `${env.uploadImageUrl}/${aliyunFileKey}?OSSAccessKeyId=${data.AccessKeyId
              }&Expires=${EXPIRES}&Signature=${SignatureStr}&security-token=${encodeURIComponent(data.SecurityToken)}`;
            successc({ url: updataImageUrl, urls: aliyunFileKey });
          });
        },
        fail: function (err) {
          err.wxaddinfo = aliyunServerURL;
          failc(err);
        }
      });
    },

    getPolicyBase64() {
      let date = new Date();
      date.setHours(date.getHours() + env.timeout);
      let srcT = date.toISOString();
      const policyText = {
        expiration: srcT, //设置该Policy的失效时间，超过这个失效时间之后，就没有办法通过这个policy上传文件了
        conditions: [
          ['content-length-range', 0, 100 * 1024 * 1024] // 设置上传文件的大小限制,5mb
        ]
      };

      const policyBase64 = base64.encode(JSON.stringify(policyText));
      return policyBase64;
    },

    getSignature(policyBase64) {
      const accesskey = env.AccessKeySecret;

      const bytes = Crypto.HMAC(Crypto.SHA1, policyBase64, accesskey, {
        asBytes: true
      });
      const signature = Crypto.util.bytesToBase64(bytes);
      return signature;
    },

    playAudioWithSts() {
      try {
        let apiUrl = `${Config.DXHost}znyy/app/query/word/voice`; // 音频资源接口地址
        let audioUrl = this.fetchAudioUrlWithSts(apiUrl);
        console.log('获取到的地址');
        console.log(audioUrl);
        console.log(Config.DXHost + audioUrl);
        // 创建并初始化音频组件
        let audioCtx = uni.createInnerAudioContext();
        // 设置音频源为获取到的URL
        audioCtx.src = 'https://document.dxznjy.com/' + audioUrl;
        console.log('播放地址');
        console.log(audioCtx.src);
        console.log('播放地址');

        // 监听音频就绪事件
        audioCtx.onCanplay(() => {
          // 开始播放音频
          console.log('开始音频播放');
          audioCtx.play();
        });
        // 其他音频控制逻辑...
      } catch (error) {
        console.error('Error fetching or playing audio with STS:', error);
      }
    },

    fetchAudioUrlWithSts(apiUrl, method = 'GET') {
      // 创建请求配置并添加STS临时凭证信息到headers中
      let token = uni.getStorageSync('token');
      console.log(this.stsList);
      const uniRequestConfig = {
        url: apiUrl,
        method,
        data: {
          word: this.word,
          v: this.playType,
          rp: this.pronunciationType,
          sex: this.timbre
        },
        header: {
          'x-acs-access-key-id': this.stsList.accessKeyId,
          'x-acs-security-token': this.stsList.securityToken,
          'content-type': 'application/json;charset=UTF-8',
          'dx-source': 'ZHEN_XUAN##WX##MINIAPP',
          'x-www-iap-assertion': token,
          'dx-app-version': uni.getStorageSync('appVersion')
        }
      };
      // 对于需要签名的请求，此处假设有个signRequest函数处理签名
      // signRequest(uniRequestConfig, stsCredentials.AccessKeySecret);
      return new Promise((resolve, reject) => {
        uni
          .request(uniRequestConfig)
          .then((res) => {
            if (res[1].data.code === 20000 && res[1].data.success) {
              resolve(res[1].data.data);
            } else {
              reject(new Error('Failed to fetch audio URL'));
            }
          })
          .catch(reject);
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.icon_audition {
  width: 30rpx;
  height: 30rpx;
}

.audition {
  color: #14806c;
}

.uni-input {
  color: #b7b7b7;
  font-size: 28rpx;
  // width: 100%;
  height: 90rpx;
  border: 1px solid #e9e9e9;
  overflow: visible;
  border-radius: 8rpx;
  padding: 0 30rpx;
  transform: scale(0.995); /* 解决ios上圆角失效 */
}

.icon_fyxz {
  width: 30rpx;
  height: 20rpx;
}

.line {
  border-bottom: 1px solid #eee;
}

.notify {
  box-shadow: 0rpx 0rpx 20rpx #e0e0e0;
}

.err_tip {
  width: 38rpx;
  height: 38rpx;
}

.switch {
  position: absolute;
  left: 10%;
  bottom: 30rpx;
  width: 600rpx;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  background: linear-gradient(to bottom, #88cfba, #2f8c70);
}

.refresh_icon {
  width: 36rpx !important;
  height: 36rpx !important;
}
</style>

import store from '@/store/index.js'
import {
	mapGetters
} from 'vuex'
export default {
	// 页面打开的时候
	computed: {
		...mapGetters(['goodsCount'])
	},
	async onshow() {
		if (!store.getters.token) return
		await store.dispatch('cart/getCartData')
		// 设置角标
		uni.setTabBarBadge({
			index: 3,
			text: this.goodsCount + ''
		})
	},
	watch:{
		goodsCount(){
			uni.setTabBarBadge({
				index:3,
				text:this.goodsCount+''
			})
		}
	}
}

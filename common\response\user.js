// 学情数据分析
import { http } from '@/utils/luch-request/index.js'; // 全局挂载引入
import { Util } from '@/utils/util.js';
import store from '@/store/index.js';

//获取学员信息(nickname)
async function getUserInfo(studentCode) {
  uni.showLoading();
  let splitArr = '';
  await http.get(`/xi/wap/studentinfo?studentCode=${studentCode}`).then((res) => {
    uni.hideLoading();
    if (res.data.success) {
      splitArr = res.data.data;
    }
  });
  return splitArr;
}

// 获取当前拖管师下面的学员
async function getStudentByMerchant(merchantCode) {
  let splitArr = '';
  await http.post(`/xi/wap/manager/list?merchantCode=${merchantCode}`).then((res) => {
    if (res.data.success) {
      splitArr = res.data.data;
    }
  });
  return splitArr;
}

//获取学员信息(studentName) 从数组匹配
async function getStudentInfo(merchantCode, studentCode) {
  let studentArr = await getStudentByMerchant(merchantCode);
  let studentInfo = studentArr.find(function (item) {
    return item.studentCode == studentCode;
  });
  return studentInfo;
}

// 根据studentCode 查询学员信息
async function getStudentInfoByCode(studentCode) {
  let splitArr = '';
  await http.post(`/xi/wap/manager/queryStudent?studentCode=${studentCode}`).then((res) => {
    if (res.data.success) {
      splitArr = res.data.data;
    }
  });
  return splitArr;
}

// 存储用户登录信息
async function storageUserInfo(data) {
  store.commit('setToken', data.token);
  store.commit('setUserRole', data.role);

  // 保存用户信息
  let userHeaderNickname = {
    avatar: data.avatar,
    nickName: data.nickName
  };

  if (data.role != 'assistant') {
    let saveSuccess = setStorageInfo(userHeaderNickname);
    if (saveSuccess) {
      uni.redirectTo({
        url: '/pages/main/index'
      });
    }
  } else {
    uni.redirectTo({
      url: '/pages/main/index'
    });
  }
}

// 保存用户头像和微信昵称
async function setStorageInfo(data) {
  let request = false;
  // 保存用户信息
  await http.post(`/xi/web/sysUser/saveWxInfo?nickName=${data.nickName}&avatar=${data.avatar}`).then((res) => {
    if (res.data.success) {
      request = true;
    }
  });
  return request;
}

// 获取用户信息
async function getUserNameInfo() {
  let splitArr = '';
  // 保存用户信息
  await http.get(`/xi/wap/learnHome/user`).then((res) => {
    if (res.data.success) {
      splitArr = res.data.data;
    }
  });
  return splitArr;
}

// 是否可以播放视频
async function isPlayVedio(parm) {
  let splitArr = true;
  // 保存用户信息
  await http.get(`/xi/wap/standbyconfig/detailByName?name=${parm}`).then((res) => {
    if (res.data.success) {
      splitArr = res.data.data.value;
      uni.setStorageSync('isPlayVedio', splitArr);
    } else {
      splitArr = false;
    }
  });
  return splitArr;
}

module.exports = {
  getUserInfo: getUserInfo,
  getStudentByMerchant: getStudentByMerchant,
  getStudentInfo: getStudentInfo,
  getStudentInfoByCode: getStudentInfoByCode,
  storageUserInfo: storageUserInfo,
  setStorageInfo: setStorageInfo,
  getUserNameInfo: getUserNameInfo,
  isPlayVedio: isPlayVedio
};

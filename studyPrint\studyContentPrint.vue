<template>
  <view class="study_content_print_page">
    <view class="bg-ff radius-15 plr-30 ptb-40 f-30 c-33">
      <view class="content_hd flex">
        <view style="width: 148upx; text-align: left">{{ studentName }}</view>
        <view style="width: 148upx">{{ typeName }}</view>
        <view style="width: 280upx; text-align: right" v-if="type !== 'NewGrammar'">{{ submitTime }}</view>
        <view style="width: 280upx; text-align: right" v-if="type === 'NewGrammar'">{{ createTime }}</view>
      </view>
      <view class="radio flex" v-if="type === 'Word'">
        <view class="raido_label">打印内容：</view>
        <view style="flex: 1; display: flex">
          <view class="radio_item" v-for="(item, index) in printWayList" :key="item.value" @click="printIndex = index">
            <view class="radio_margin check_btn" :class="index === printIndex ? 'checked' : ''">
              <view v-if="index === printIndex" class="check_circle"></view>
            </view>
            <view class="c-66">{{ item.name }}</view>
          </view>
        </view>
      </view>
      <view class="radio flex">
        <view class="raido_label">下载格式：</view>
        <view style="flex: 1; display: flex">
          <view class="radio_item" v-for="(item, index) in downTypeList" :key="item.value" @click="downTypeIndex = index">
            <view class="radio_margin check_btn" :class="index === downTypeIndex ? 'checked' : ''">
              <view v-if="index === downTypeIndex" class="check_circle"></view>
            </view>
            <view class="c-66">{{ item.name }}</view>
          </view>
        </view>
      </view>
    </view>
    <view v-if="type !== 'NewGrammar'" class="bg-ff radius-15 p-30 mt-30" :style="{ height: useHeight + 'rpx' }">
      <view class="f-28 radio" v-if="type === 'Word'">
        预览：
        <text>(中英文不支持预览，直接下载即可)</text>
      </view>
      <view class="preview_box">
        <view class="image_class">
          <view v-for="(previewItem, index) in previewImages" :key="index">
            <image :src="previewItem" mode="cover" style="width: 610rpx"></image>
          </view>
        </view>
      </view>
      <view class="confirm_btn" @click="confirmPrint" v-if="!sendType">
        <text>一键下载</text>
      </view>
      <view class="confirm_btn" @click="sendMsg()" v-if="sendType">
        <text>发送</text>
      </view>
      <canvas canvas-id="TextToPicture" style="width: 750px; height: 6000px"></canvas>
      <view class="confirm_btn" @click="confirmPrint">
        <text>一键下载</text>
      </view>
    </view>
    <view v-else class="bg-ff radius-15 p-30 mt-30" :style="{ height: useHeight + 'rpx' }">
      <view class="f-28 radio">
        预览：
        <text>(中英文不支持预览，直接下载即可)</text>
      </view>
      <view class="preview_box" style="margin-bottom: 1000px">
        <view class="image_class">
          <view v-for="(item, index) in noteList" :key="index">
            <view class="f-35 flex">知识点：{{ knowledgeName }}</view>
            <view class="f-30 p-10">{{ index + 1 }}.{{ item.topic }}</view>
            <view class="f-24 ml-30">{{ item.content }}</view>
          </view>
        </view>
      </view>
      <canvas canvas-id="TextToPicture" style="width: 750px; height: 6000px"></canvas>
      <view class="confirm_btn" @click="confirmPrint">
        <text>一键下载</text>
      </view>
    </view>
  </view>
</template>

<script>
  // import uniPopup from '@/components/uni-popup/uni-popup.vue'
  export default {
    components: {
      // uniPopup
    },
    data() {
      return {
        studentName: '',
        studentCode: '',
        type: '',
        printCode: '',
        submitTime: '',
        typeName: '',
        downPdfSrc: '',
        createTime: '',
        downImgSrc: [],
        previewImages: [], // 预览数据
        printIndex: 0,

        printWayList: [
          {
            value: 'ALL', // 仅type == words时候展示
            name: '全打印',
            show: false
          },
          {
            value: 'EN',
            name: '英文',
            show: true
          },
          {
            value: 'CN',
            name: '中文',
            show: true
          }
        ],
        downTypeIndex: 0,
        downTypeList: [
          {
            value: 'PNG',
            name: '图片'
          },
          {
            value: 'PDF',
            name: 'pdf'
          }
        ],

        useHeight: 0, //除头部之外高度
        imgHost: 'https://document.dxznjy.com/',

        isAllDay: false,
        queryData: {},
        sendType: false,
        printID: '', //打印id
        noteList: [], //笔记列表
        showPreview: false,
        pdfId: '', //pdf id
        pageNum: 1, // 当前页
        pageSize: 20, // 页数
        knowledgeName: '',
        printUrlList: []
      };
    },

    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 440;
        }
      });
    },

    onLoad(option) {
      var that = this;
      that.sendType = option.isSend == 1 ? true : false;
      if (option.item) {
        that.queryData = JSON.parse(decodeURIComponent(option.item));
      }
      that.isAllDay = option.allDay;
      that.studentName = option.studentName;
      that.studentCode = option.studentCode;
      that.type = option.type;
      that.submitTime = option.submitTime;
      that.printCode = option.printCode;
      // that.typeName = option.type === 'Word' ? '单词' : option.type === 'Reading' ? '阅读理解' : '语法'
      that.typeName = option.type === 'Word' ? '单词' : option.type === 'Reading' ? '阅读理解' : option.type === 'SuperRead' ? '新阅读理解' : '语法';
      that.printID = option.id;
      if (that.type === 'NewGrammar') {
        that.typeName = '新版语法';
        that.getNewGrammarContent();
      } else {
        if (that.isAllDay == 1) {
          that.getPrintAllContent();
        } else {
          that.getPrintContent();
        }
      }
      uni.showLoading({
        title: '加载中...'
      });
    },

    methods: {
      sendMsg() {
        let that = this;
        // 发文字
        wx.qy.getContext({
          success: () => {
            //    var entry = data.entry, //返回进入小程序的入口类型
            // var shareTicket = data.shareTicket;
            wx.qy.sendChatMessage({
              msgtype: 'miniprogram', //消息类型，必填
              enterChat: false, //为true时表示发送完成之后顺便进入会话，仅移动端3.1.10及以上版本支持该字段
              text: {
                content: '' //文本内容
              },
              image: {
                mediaid: '' //图片的素材id
              },
              video: {
                mediaid: '' //视频的素材id
              },
              file: {
                mediaid: '' //文件的素材id
              },
              news: {
                link: '', //H5消息页面url 必填
                title: '', //H5消息标题
                desc: '', //H5消息摘要
                imgUrl: '' //H5消息封面图片URL
              },
              miniprogram: {
                appid: that.$config.WXAppId, //小程序的appid
                title: '学习打印', //小程序消息的title
                imgUrl: 'https://document.dxznjy.com/manage/1716255999000', //小程序消息的封面图
                page: `/studyPrint/studyContentPrint.html?studentName=${that.studentName}&studentCode=${that.studentCode}&type=${
                  that.type === 'Handouts' ? 'Grammar' : that.type
                }&printCode=${that.printCode}&submitTime=${that.submitTime}&allDay=${that.isAll}&isSend=0` //小程序消/小程序消息打开后的路径，注意要以.html作为后缀，否则在微信端打开会提示找不到页面
              },
              success: (res) => {
                that.$http
                  .get('/deliver/teacher/notify/details', {
                    id: that.queryData.id,
                    isSend: 1,
                    boundInfo: that.queryData.boundInfo,
                    message: that.queryData.msgType
                  })
                  .then(() => {
                    uni.showToast({
                      title: '发送成功'
                    });
                  })
                  .catch((err) => {
                    uni.showToast({
                      title: '发送失败',
                      icon: 'none'
                    });
                  });
              },
              fail: (err) => {
                console.log(err, '=============================');
              }
            });
          },
          fail: (err) => {
            console.log(err, '111111111111111');
          }
        });
      },
      // 语法下载图片
      getNewGrammarContent() {
        this.$http
          .get('/dyf/wap/applet/print', {
            id: this.printID,
            studentCode: this.studentCode,
            pageNum: this.pageNum,
            pageSize: this.pageSize
          })
          .then((result) => {
            this.noteList = result.data.data.noteList;
            this.createTime = result.data.data.createTime;
            this.pdfId = result.data.data.pdfId;
            this.knowledgeName = result.data.data.knowledgeName;
            this.printUrlList = result.data.data.picUrlList;
          });
      },

      calculateCanvasSize() {
        const ctx = uni.createCanvasContext('TextToPicture', this);
        ctx.setFontSize(18); // 设置字体大小
        const padding = 30; // 内边距

        // const canvasWidth = (uni.upx2px(614) - padding * 2) * 3 // 与 .image_class 的宽度一致，减去内边距 todo
        const canvasWidth = uni.upx2px(1500) - padding * 2; // 与 .image_class 的宽度一致，减去内边距 todo
        let canvasHeight = padding; // 初始高度为内边距

        this.noteList.forEach((item) => {
          const knowledgeNameLines = this.getTextLines(ctx, `${this.knowledgeName}`, canvasWidth);
          canvasHeight += knowledgeNameLines.length * 22; // 每行22px
          const topicLines = this.getTextLines(ctx, `${item.topic}`, canvasWidth);
          canvasHeight += topicLines.length * 20; // 每行20px
          const contentLines = this.getTextLines(ctx, `${item.content}`, canvasWidth);
          canvasHeight += contentLines.length * 18; // 每行18px
          canvasHeight += padding; // 每条记录之间的间距
        });

        canvasHeight += padding; // 加上底部内边距
        return { canvasWidth, canvasHeight };
      },

      getTextLines(ctx, text, maxWidth) {
        let txtList = [];
        let str = '';
        for (let i = 0, len = text.length; i < len; i++) {
          str += text.charAt(i);
          if (ctx.measureText(str).width > maxWidth) {
            txtList.push(str.substring(0, str.length - 1));
            str = '';
            i--;
          }
        }
        txtList.push(str);
        return txtList;
      },

      convertTextToImage() {
        uni.hideLoading();
        uni.showLoading({ title: '准备生成图片...', mask: true });
        const ctx = uni.createCanvasContext('TextToPicture', this);
        const { canvasWidth, canvasHeight } = this.calculateCanvasSize();
        const padding = 25;
        const lineHeight = 25;
        // 先计算 y
        let actulHeight = padding;
        this.noteList.forEach((item, index) => {
          const knowledgeNameLines = this.getTextLines(ctx, `${this.knowledgeName}`, canvasWidth);
          actulHeight += knowledgeNameLines.length * 22; // 每行22px
          const topicLines = this.getTextLines(ctx, `${index + 1}. ${item.topic}`, canvasWidth - 2 * padding);
          actulHeight += topicLines.length * lineHeight;
          const contentParagraphs = item.content.split('\n');
          contentParagraphs.forEach((paragraph) => {
            const contentLines = this.getTextLines(ctx, paragraph, canvasWidth - 2 * padding);
            actulHeight += contentLines.length * lineHeight * 0.8;
          });
          actulHeight += padding;
        });
        ctx.setFillStyle('#FFFFFF'); // 设置白色背景
        ctx.fillRect(0, 0, canvasWidth, actulHeight + padding); // 绘制背景
        ctx.setFontSize(18);
        ctx.setFillStyle('#000000'); // 设置文本颜色
        ctx.textAlign = 'start';
        ctx.font = '18px sans-serif';
        let yPos = padding;
        console.log('333333333333333333333333333333333333');
        this.noteList.forEach((item, index) => {
          ctx.fillText(`知识点：${this.knowledgeName}`, padding, yPos, canvasWidth - 2 * padding);
          yPos += lineHeight;

          const topicLines = this.getTextLines(ctx, `${index + 1}. ${item.topic}`, canvasWidth - 2 * padding);
          topicLines.forEach((line) => {
            ctx.fillText(line, padding, yPos, canvasWidth - 2 * padding);
            yPos += lineHeight;
          });

          // const contentParagraphs = item.content.split(/(?<=\.)\s+/)
          const contentParagraphs = item.content.split('\n');
          contentParagraphs.forEach((paragraph) => {
            const contentLines = this.getTextLines(ctx, paragraph, canvasWidth - 2 * padding);
            contentLines.forEach((line) => {
              ctx.fillText(line, padding, yPos, canvasWidth - 2 * padding);
              yPos += lineHeight * 0.8;
            });
          });

          yPos += padding;
        });
        ctx.draw(false, () => {
          uni.hideLoading();
          uni.showToast({ title: '图片生成中...', duration: 2000, icon: 'none' });
          this.saveTextToImage(canvasWidth, yPos);
        });
      },

      saveTextToImage(width, height) {
        console.log('saveTextToImagewidth', width);
        console.log('saveTextToImageheight', height);
        const that = this;
        uni.canvasToTempFilePath({
          x: 0,
          y: 0,
          width: width, // 使用计算的宽度
          height: height, // 使用计算的高度
          destWidth: width * 3,
          destHeight: height * 3,
          fileType: 'png',
          quality: 1,
          canvasId: 'TextToPicture',
          success(res) {
            that.saveImageToPhotos(res.tempFilePath); // 调用保存图片方法
          },
          fail(err) {
            console.error('保存图片失败：', err);
            uni.hideLoading();
            uni.showToast({ title: '保存失败，稍后再试', duration: 2000, icon: 'none' });
          }
        });
      },

      // 新增保存图片的方法
      saveImageToPhotos(filePath) {
        const that = this;
        // #ifdef APP-PLUS
        that.saveImageToPhotosAlbum(filePath);
        // #endif
        // #ifdef MP-WEIXIN
        uni.authorize({
          scope: 'scope.writePhotosAlbum',
          success() {
            // 用户已经授权，继续保存图片的操作
            that.saveImageToPhotosAlbum(filePath);
          },
          fail() {
            // 用户拒绝了授权
            uni.showModal({
              title: '提示',
              content: '您需要授权相册权限才能保存图片，是否前往设置页面授权？',
              success(res) {
                if (res.confirm) {
                  // 用户同意前往设置页面
                  uni.openSetting({
                    success(settingRes) {
                      if (settingRes.authSetting['scope.writePhotosAlbum']) {
                        // 用户打开了相册权限
                        that.saveImageToPhotosAlbum(filePath);
                      } else {
                        // 用户依然拒绝了权限
                        uni.showToast({
                          title: '授权失败，无法保存图片',
                          icon: 'none'
                        });
                      }
                    },
                    fail(err) {
                      console.error('打开设置页面失败：', err);
                    }
                  });
                }
              }
            });
          }
        });
        // #endif
      },

      saveImageToPhotosAlbum(filePath) {
        uni.saveImageToPhotosAlbum({
          filePath: filePath,
          success(res) {
            uni.showToast({
              title: '图片保存成功，可以去分享啦~',
              duration: 2000,
              icon: 'none'
            });
          },
          fail(err) {
            console.error('保存图片到相册失败：', err);
            uni.showToast({ title: '保存失败，稍后再试', duration: 2000, icon: 'none' });
          }
        });
      },

      // 获取打印信息
      async getPrintContent() {
        let that = this;
        await uni.$http
          .get('/znyy/student/print/detail', {
            printCode: that.printCode,
            type: that.type,
            studentName: that.studentName
          })
          .then((result) => {
            if (result.data != {} && result.data.data.previewImages.length > 0) {
              result.data.data.previewImages.forEach((item) => {
                that.previewImages.push(this.getBase64ImageUrl(item));
              });
            }
            setTimeout(function () {
              uni.hideLoading();
            }, 200);
          });
      },

      async getPrintAllContent() {
        let that = this;
        await uni.$http
          .get('/znyy/student/print/detail/day', {
            printCode: that.printCode,
            wordPrintCode: that.printCode,
            courseContentType: that.type,
            type: that.type,
            preview: true,
            studentName: that.studentName,
            name: that.studentName
          })
          .then((result) => {
            if (result.data != {} && result.data.data) {
              if (result.data.data.previewImages.length > 0) {
                result.data.data.previewImages.forEach((item) => {
                  that.previewImages.push(this.getBase64ImageUrl(item));
                });
              }
              that.printCode = result.data.data.printCode;
              console.log(that.printCode);
            }
            setTimeout(function () {
              uni.hideLoading();
            }, 200);
          });
      },

      //把base64转换成图片
      getBase64ImageUrl(base64Url) {
        /// 获取到base64Data
        var base64Data = base64Url;
        /// 通过微信小程序自带方法将base64转为二进制去除特殊符号，再转回base64
        base64Data = wx.arrayBufferToBase64(wx.base64ToArrayBuffer(base64Data));
        /// 拼接请求头，data格式可以为image/png或者image/jpeg等，看需求
        const base64ImgUrl = 'data:image/png;base64,' + base64Data;
        /// 得到的base64ImgUrl直接给图片:src使用即可
        return base64ImgUrl;
      },

      // 确认打印
      async confirmPrint() {
        console.log('1111111111111111111111111111111111');
        let that = this;
        let requestData = {
          contentType: that.type === 'Word' ? that.printWayList[that.printIndex].value : '',
          fileType: that.downTypeList[that.downTypeIndex].value,
          printCode: that.printCode,
          studentName: that.studentName,
          type: that.type
        };
        if (that.isAllDay == 1) {
          requestData.day = true;
        }
        if (that.type !== 'NewGrammar') {
          await that.$http.get('/znyy/student/print/download/file', requestData).then((result) => {
            // debugger
            if (result.data.success) {
              if (that.downTypeList[that.downTypeIndex].value == 'PDF') {
                that.downPdfSrc = result.data.data.files[0];
                uni.showModal({
                  title: '这是一个外部链接',
                  content: '暂不支持永久下载pdf类文件，可点击复制链接后在浏览器中粘贴查看',
                  confirmText: '复制链接',
                  success: function (res) {
                    if (res.confirm) {
                      that.copyMessage(that.downPdfSrc);
                    }
                  }
                });
              } else {
                that.downImgSrc = result.data.data.files;
                that.downLoadImg();
              }
            }
          });
        } else {
          if (this.downTypeList[this.downTypeIndex].value == 'PDF') {
            this.$http.get(`/media/web/ossFile/seeDetails/${this.pdfId}`).then((result) => {
              console.log(result, 'result');
              that.downPdfSrc = result.data.data.url;
              uni.showModal({
                title: '这是一个外部链接',
                content: '暂不支持永久下载pdf类文件，可点击复制链接后在浏览器中粘贴查看',
                confirmText: '复制链接',
                success: function (res) {
                  if (res.confirm) {
                    that.copyMessage(that.downPdfSrc);
                  }
                }
              });
            });
          } else {
            if (this.type === 'NewGrammar') {
              // console.log('22222222222222222222222222222222');
              // that.convertTextToImage();
              that.downImgSrc = that.printUrlList;
              that.downLoadImg();
            }
          }
        }
      },

      // 复制链接(pdf)
      copyMessage(value) {
        uni.setClipboardData({
          data: value,
          success: function (res) {
            uni.getClipboardData({
              success: function (res) {
                uni.showToast({
                  title: '复制成功'
                });
              }
            });
          }
        });
      },

      // 下载地址(图片)
      downLoadImg() {
        let that = this;
        // #ifdef APP-PLUS
        if (that.downImgSrc && that.downImgSrc.length > 0) {
          that.downImgSrc.forEach((item) => {
            // 1.1 调用下载api方法
            uni.downloadFile({
              url: item,
              success: (res) => {
                // 1.2 获取远程图片地址后,将图片地址缓存到本地
                if (res.statusCode === 200) {
                  uni.saveImageToPhotosAlbum({
                    filePath: res.tempFilePath, // 把远程的图片地址及图片保存到本地
                    success: function (res) {
                      // 1.3保存成功后弹框提示保存成功
                      uni.showToast({
                        title: '保存成功',
                        icon: 'none'
                      });
                    },
                    fail: function (res) {
                      // 1.4保存失败给用户弹框提示
                      uni.showToast({
                        title: '保存失败',
                        icon: 'none'
                      });
                      if (res.errMsg == 'saveImageToPhotosAlbum:fail cancel') {
                        return;
                      }
                    }
                  });
                }
              },
              fail(err) {
                console.log(err);
              }
            });
          });
        } else {
          uni.showToast({
            title: '暂无数据',
            icon: 'none'
          });
        }
        // #endif
        // #ifdef MP-WEIXIN
        uni.authorize({
          scope: 'scope.writePhotosAlbum',
          success() {
            // 1 授权成功遍历所有要下载的图片
            debugger;
            if (that.downImgSrc && that.downImgSrc.length > 0) {
              that.downImgSrc.forEach((item) => {
                // 1.1 调用下载api方法
                console.log(22222);
                uni.downloadFile({
                  url: item,
                  success: (res) => {
                    // 1.2 获取远程图片地址后,将图片地址缓存到本地
                    if (res.statusCode === 200) {
                      uni.saveImageToPhotosAlbum({
                        filePath: res.tempFilePath, // 把远程的图片地址及图片保存到本地
                        success: function (res) {
                          // 1.3保存成功后弹框提示保存成功
                          uni.showToast({
                            title: '保存成功',
                            icon: 'none'
                          });
                        },
                        fail: function (res) {
                          // 1.4保存失败给用户弹框提示
                          uni.showToast({
                            title: '保存失败',
                            icon: 'none'
                          });
                          if (res.errMsg == 'saveImageToPhotosAlbum:fail cancel') {
                            return;
                          }
                        }
                      });
                    }
                  },
                  fail(err) {
                    console.log(err);
                  }
                });
              });
            } else {
              uni.showToast({
                title: '暂无数据',
                icon: 'none'
              });
            }
          },
          fail(error) {
            // 2、授权失败 弹框再次要求授权
            uni.showModal({
              title: '您需要授权相册权限',
              success(res) {
                // 2.1点击确认按钮就调取授权设置页面
                if (res.confirm) {
                  // 2.2 开启授权设置页面
                  uni.openSetting({
                    success(res) {},
                    fail(err) {
                      console.log(err);
                    }
                  });
                }
              },
              fail(error) {
                console.log(error + '授权失败回调');
              }
            });
            console.log(error + '相册授权失败');
          },
          complete(res) {
            console.log(res);
          }
        });
        // #endif
      }
    }
  };
</script>

<style>
  .study_content_print_page {
    height: 100vh;
    color: rgba(236, 236, 236, 1);
    padding: 0 30upx;
    position: relative;
  }

  .content_hd {
    color: rgba(51, 51, 51, 1);
    font-size: 28upx;
    justify-content: space-between;
    margin-bottom: 40upx;
  }

  .radio {
    color: rgba(102, 102, 102, 1);
    margin-bottom: 40upx;
    display: flex;
  }

  .radio_margin {
    margin-right: 19upx;
  }

  .raido_label {
    width: 180upx;
    color: #333333;
  }

  .radio_item {
    width: 145upx;
    display: flex;
    align-items: center;
    margin-right: 8upx;
  }
  .radio_item:last-child {
    margin: 0;
  }

  .check_btn {
    width: 26upx;
    height: 26upx;
    border-radius: 26upx;
    border: 1upx solid #d3d3d3;
  }

  .checked {
    border: 1upx solid #2e896f !important;
  }

  .check_circle {
    width: 22upx;
    height: 22upx;
    margin: 2upx auto;
    border-radius: 22upx;
    background: #2e896f;
  }

  .confirm_btn {
    position: absolute;
    bottom: 60rpx;
    width: 84.5%;
    height: 90upx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 45upx;
    line-height: 90upx;
    font-size: 30upx;
    color: #ffffff;
    text-align: center;
  }

  .preview_box {
    color: #999999;
  }

  .image_class {
    overflow-y: scroll;
    width: 614upx;
    height: calc(100vh - 600upx);
    /* height: 650upx; */
    border-radius: 14upx;
    border: 1px solid rgba(199, 199, 199, 1);
  }
  .content {
    width: 530rpx;
    height: 300rpx;
    background: #fff;
    border-radius: 12upx;
    padding: 30upx;
    text-align: center;
  }

  .title {
    color: #000000;
  }

  .text {
    margin-top: 40upx;
    color: #666666;
    font-size: 28upx;
  }

  .bottom_btn {
    margin-top: 40upx;
    color: #646464;
  }
</style>

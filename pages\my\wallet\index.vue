<template>
  <view class="positionRelative w100">
    <view class="hear_img">
      <image src="http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/1743558378000" class="wallet-bgc"></image>
      <view class="amount f-30">
        <view class="positionAbsolute">
          账期余额
          <uni-icons type="help" size="16" color="#fefffe" class="balance" @click="openExplain"></uni-icons>
          <view class="f-28 c-33 balance_explain" v-if="explainShow">
            <view>账期余额=可提现+已结算</view>
            <view class="mt-10 lh-50">注：每上完1学时课程，则结算20佣金到账期余额上，当月的绩效金额：为次月15日前00：00统一结算并发放到账期余额。</view>
          </view>
        </view>
        <view class="flex-s mt-30 pt-30">
          <view class="money">
            {{ balance || 0.0 }}
          </view>
          <view class="withdrawal" @click="goWallet()">提现</view>
        </view>
        <view class="flex-a-c mt-30 border-t pt-50">
          <view class="c-ea positionAbsolute">
            总收益
            <uni-icons type="help" size="16" color="#fefffe" class="total_money" @click="openIncome"></uni-icons>
            <text class="c-ff ml-40">{{ withdrawalDetails.totalMoney || 0.0 }}</text>
            <view class="total_explain c-33 f-28" v-if="incomePrompt">总收益=账期余额+累计提现</view>
          </view>
          <view class="c-ea positionAbsolute" style="padding-left: 350rpx">
            累计提现
            <text class="c-ff ml-40">{{ totalWithdrawal || 0.0 }}</text>
            <uni-icons type="help" size="16" color="#fefffe" class="taking" @click="openWithdrawal"></uni-icons>
            <view class="withdrawal_explain c-33 f-28" v-if="withdrawalPrompt">累计提现的金额</view>
          </view>
        </view>
      </view>
    </view>

    <view class="nav-title">
      <view class="status_bar">
        我的钱包
        <uni-icons type="left" size="18" class="title-icon" @click="goback"></uni-icons>
      </view>
    </view>
    <view class="tab-bg">
      <u-tabs
        :list="list"
        @click="checkIndex"
        lineWidth="40rpx"
        lineHeight="3"
        lineColor="green"
        :inactiveStyle="{ color: '#666666', fontSize: '30rpx' }"
        :activeStyle="{ color: 'green', fontWeight: 'bold', fontSize: '32rpx' }"
      ></u-tabs>
    </view>
    <view class="bill">
      <view class="flex-x-b flex-a-c">
        <view class="flex-a-c">
          <picker @change="bindPickerChange" :value="index" :range="columns" range-key="label">
            <view class="mr-15 f-32 bold">{{ columns[index].label }}</view>
          </picker>
          <!-- <view class="mr-15 f-32 bold" @click="show=true">{{bill?bill :'全部账单'}}</view> -->
          <u-icon name="arrow-down-fill" color="#000" size="16"></u-icon>
          <!-- <u-picker :show="show" :columns="columns" keyName="label" confirmColor="#2E896F" :immediateChange="true"
						@cancel="cancel" @confirm="confirm"></u-picker> -->
        </view>
        <!-- 	lineWidth="40rpx"
					lineHeight="4"
					lineColor="green"  fontWeight: 'bold',-->
        <view style="width: 420rpx; margin-right: -43rpx">
          <u-tabs
            :list="tabsList"
            @click="tabClick"
            lineWidth="40rpx"
            lineHeight="3"
            lineColor="green"
            :activeStyle="{
              color: 'green',
              fontWeight: 'bold',
              transform: 'scale(1.05)'
            }"
            :inactiveStyle="{
              color: '#606266',
              transform: 'scale(1)'
            }"
            itemStyle="padding-left: 15px; padding-right: 15px; height: 34px;font-size:14px;"
          ></u-tabs>
        </view>
      </view>

      <view class="flex-s f-30 mt-30">
        <view class="flex-a-c">
          <text class="mr-10" @click="openTime">{{ date }}</text>
          <uni-icons type="bottom" size="16" color="#000"></uni-icons>
          <u-datetime-picker
            ref="datetimePicker"
            :show="showTime"
            v-model="value"
            mode="year-month"
            confirmColor="#2E896F"
            @cancel="cancelTime"
            @confirm="confirmTime"
          ></u-datetime-picker>
        </view>
        <view class="c-66">
          <text>提现￥{{ priceDetails.outAmount || 0.0 }}</text>
          <text class="ml-20">
            <text>收入￥</text>
            <text style="color: green; font-weight: bold">{{ priceDetails.inAmount || 0.0 }}</text>
          </text>
        </view>
      </view>

      <view v-if="showPages" class="scrollView" :style="{ height: useHeight + 'rpx' }" scroll-y="true" :scroll-top="scrollTop">
        <view class="f-30 bg-ff radius-15 plr-30 mt-30" v-for="(item, index) in infoLists.data" :key="index">
          <view class="f-30 bg-ff radius-15">
            <view class="ptb-30" @click="goBillDetails(item)">
              <view class="flex-s">
                <view class="c-33">{{ item.content }}</view>
                <view class="f-32 bold" :class="item.orderId == '' ? 'c-00' : 'c-fea'">
                  {{ item.orderId == '' ? '-' : '+' }}
                  <text class="bold">{{ item.amount }}</text>
                </view>
              </view>
              <!-- <view class="c-99 mt-15">{{item.createTime}}</view> -->
              <view class="c-99 mt-15">
                <text v-if="item.orderId && item.type != 7">
                  {{ item.type == 6 ? '复习时间' : '上课时间' }} ：
                  <text v-if="item.startStudyTime">
                    <text v-if="item.type != 9">{{ item.startStudyTime }}</text>
                    <text v-if="item.type != 6 && item.type != 9">-</text>
                    <text v-if="item.type == 9">{{ item.startStudyTime.slice(5, 11) }}~ {{ item.endStudyTime.slice(5, 11) }}</text>
                    <text v-if="item.endStudyTime && item.type != 6 && item.type != 9">{{ item.endStudyTime.slice(11, 17) }}</text>
                  </text>
                  <text v-else>
                    <text>-</text>
                  </text>
                </text>
              </view>
              <view class="c-99 mt-15 flex-a-c flex-x-s">
                <view class="order-style-width">
                  <text v-if="item.orderId == ''">提现时间：</text>
                  <text v-else>到账时间：</text>
                  {{ item.orderId == '' ? item.createTime : item.accountTime.slice(0, 11) }}
                </view>
                <!-- v-if="item.orderId" -->
                <view style="margin-left: 30rpx" :class="item.accountStatus == 1 ? 'c-67C23A' : 'c-E6A23C'">
                  <text class="c-99">状态：</text>
                  {{ item.accountStatus == 1 ? '已到账' : '未到账' }}
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- <button v-if="showPages" class="export-btn-style" @click="exportBill()">导出账单</button> -->

      <view v-if="!showPages" class="bg-ff radius-15 mt-30 no-data" :style="{ height: useHeight + 'rpx' }">
        <image src="https://document.dxznjy.com/alading/correcting/no_data.png" class="mb-20 img_s"></image>
        <view style="color: #bdbdbd">暂无数据</view>
      </view>
    </view>
    <uni-popup ref="copyExcolPopup" type="center" :maskClick="true">
      <view class="copy-excol-popup">
        <view>点击复制按钮至浏览器可下载Excel表格，也可在线预览</view>
        <view>
          <button class="btn-style left-btn-style" @click="excelCopy()" style="margin-right: 40rpx">复制链接</button>
          <button class="btn-style" @click="excelPreview()">在线预览</button>
        </view>
      </view>
    </uni-popup>
    <uni-popup ref="coinPopup" type="center">
      <view class="popup-content">
        <view class="">提现须知</view>

        <view class="webview-container">
          请阅读
          <span @click="goToAgreement" style="color: blue">{{ signTittle }}</span>
          后，点击'同意'按钮
        </view>
        <u-button type="primary" @click="seeKnow" :customStyle="{ width: '300rpx', height: '70rpx' }" shape="circle" text="同意"></u-button>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import dayjs from 'dayjs';
  import Config from '@/utils/config.js';
  const { http } = require('@/utils/luch-request/index.js');
  import Util from '@/utils/util.js';
  export default {
    data() {
      return {
        useHeight: 0,
        show: false,
        index: 0,
        status: 2,
        list: [
          {
            name: '我的收益',
            key: 2
          },
          {
            name: '历史收益',
            key: 1
          }
        ],
        tabsList: [
          {
            name: '全部',
            key: 2
          },
          {
            name: '已到账',
            key: 1
          },
          {
            name: '未到账',
            key: 0
          }
        ],
        columns: [
          {
            label: '全部账单',
            // 其他属性值
            id: 0
            // ...
          },
          {
            label: '收入明细',
            id: 1
          },
          {
            label: '提现明细',
            id: 2
          }
        ],
        range: '',
        showTime: false,
        value: Number(new Date()),
        datetime: '',
        date: '',
        downFileUrl: '', //导出文件地址
        explainShow: false, // 账期余额提示
        incomePrompt: false, // 总收益
        withdrawalPrompt: false, // 提现

        no_more: false,
        infoLists: {},
        month: '',
        page: 1,
        userCode: '',
        type: '',
        balance: '', // 账期余额
        withdrawalDetails: {}, // 提现明细
        // bill: "",
        totalWithdrawal: '', // 累计提现

        priceDetails: {}, // 收支明细
        agreementUrl: '',
        teacherName: '',
        signTittle: '',
        scrollTop: 0,
        tabKey: 2,
        showPages: false
      };
    },

    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 875;
        }
      });
      // 微信小程序需要用此写法
      this.$refs.datetimePicker.setFormatter(this.formatter);
    },
    onShow() {
      this.clickHandle();
      let withdrawal = uni.getStorageSync('withdrawal');
      if (withdrawal) {
        this.getAccount();
        this.getTeacherAccount();
        this.getAccountFlow();
        uni.removeStorage({
          key: 'withdrawal',
          success: (res) => {
            console.log('withdrawal');
          }
        });
      }
    },
    onLoad(options) {
      this.userCode = options.userCode;
      this.teacherName = options.teacherName;
      this.month = dayjs().format('YYYY-MM');
      this.date = this.month.slice(0, 4) + '年' + this.month.slice(5, 7) + '月';
      this.getAccount();
      this.getTeacherAccount();
      this.getAccountFlow();
    },
    onReachBottom() {
      if (this.page >= this.infoLists.totalPage) {
        this.no_more = true;
        return false;
      }
      this.page++;
      this.getTeacherAccount(true, this.page);
    },
    methods: {
      checkIndex(e) {
        this.explainShow = false; // 关闭弹窗
        this.tabKey = e.key;
        this.balance = 0;
        this.infoLists = {};
        this.priceDetails.outAmount = 0;
        this.priceDetails.inAmount = 0;
        this.totalWithdrawal = 0;
        this.getAccount();
        this.getTeacherAccount();
        this.getAccountFlow();
      },

      clickHandle() {
        this.scrollTop = this.scrollTop === 0 ? 1 : 0;
      },
      // scrolltolower() {
      //   if (this.page >= this.infoLists.totalPage) {
      //     this.no_more = true;
      //     return false;
      //   }
      //   this.page++;
      //   this.getTeacherAccount(true, this.page);
      // },
      //账单状态切换
      tabClick(e) {
        console.log(e);
        this.status = e.key;
        this.infoLists = {};
        this.getTeacherAccount();
        console.log('---------------------------------');
      },
      goback() {
        uni.navigateBack();
      },
      cancel() {
        this.show = false;
      },
      confirm(e) {
        console.log(e);
        this.type = e.value[0].id;
        this.bill = e.value[0].label;
        this.show = false;
        this.getTeacherAccount();
      },
      openTime() {
        this.showTime = true;
      },

      formatter(type, value) {
        if (type === 'year') {
          return `${value}年`;
        }
        if (type === 'month') {
          return `${value}月`;
        }

        return value;
      },

      bindPickerChange(e) {
        console.log(e);
        this.index = e.detail.value;
        this.type = e.detail.value;
        this.getTeacherAccount();
      },

      cancelTime() {
        this.showTime = false;
      },

      confirmTime(e) {
        console.log(e);
        this.month = dayjs(e.value).format('YYYY-MM');
        this.showTime = false;
        if (this.month != '') {
          this.date = this.month.slice(0, 4) + '年' + this.month.slice(5, 7) + '月';
        }
        this.getTeacherAccount();
        this.getAccountFlow();
      },

      openExplain() {
        this.explainShow = !this.explainShow;
      },

      openIncome() {
        this.incomePrompt = true;
        setTimeout(() => {
          this.incomePrompt = false;
        }, 1500);
      },

      openWithdrawal() {
        this.withdrawalPrompt = true;
        setTimeout(() => {
          this.withdrawalPrompt = false;
        }, 1500);
      },

      // 收入流水列表
      async getTeacherAccount(isPage, page) {
        console.log('ppppppppppppppppppppppppp');
        this.page = page || 1;
        let _this = this;
        let data = {
          month: this.month,
          pageNum: page || 1,
          pageSize: 100,
          userCode: this.userCode,
          type: this.type == 0 ? '' : this.type,
          accountStatus: this.status == 2 ? '' : this.status
        };
        let url = this.tabKey == 1 ? '/deliver/app/teacher/getTeacherAccountFlowList' : '/deliver/app/teacher/getFlexPayTeacherAccountFlowList';
        let tabActive = this.tabKey;
        let res = await http.get(url, data);

        if (res) {
          if (tabActive !== this.tabKey) return;
          if (isPage) {
            let old = _this.infoLists.data;
            _this.infoLists.data = [...old, ...res.data.data.data];
          } else {
            _this.infoLists = res.data.data;
          }

          console.log(_this.infoLists.data.length, 'ppppppppppppp');
          if (_this.infoLists.data.length > 0) {
            this.showPages = true;
          } else {
            this.showPages = false;
          }
        }
      },

      // 获取俱乐部金额
      async getAccount() {
        let that = this;
        that.withdrawalDetails = {};
        console.log(that.withdrawalDetails, 'that.withdrawalDetails');
        let url = that.tabKey == 1 ? '/mps/account/list/login/code/byCode' : '/flexpay/userAccount/getUserAccountInfo';
        let res = await http.get(url, {
          userCode: that.userCode
        });
        if (res.data.success) {
          if (this.tabKey == 1) {
            for (let i = 0; i < res.data.data.length; i++) {
              if (res.data.data[i].userCode == this.userCode) {
                that.withdrawalDetails = res.data.data[i] || {};
              }
            }
            if (Object.keys(this.withdrawalDetails).length === 0) {
            } else {
              this.withdrawalDetails.totalMoney = Util.Fen2Yuan(this.withdrawalDetails.totalMoney || 0); // 总收益
              this.withdrawalDetails.availableCashAmount = Util.Fen2Yuan(this.withdrawalDetails.availableCashAmount || 0); // 可提现

              // this.withdrawalDetails.paymentIn = Util.Fen2Yuan(this.withdrawalDetails.paymentIn || 0); // 已结算
              this.getWithdrawal();
            }
          } else {
            console.log(res.data.data, 'ppppppppppppppppppppppp');
            that.withdrawalDetails = res.data.data || {};
            this.withdrawalDetails.totalMoney = Util.Fen2Yuan(this.withdrawalDetails.totalMoney || 0); // 总收益
            this.withdrawalDetails.availableCashAmount = Util.Fen2Yuan(this.withdrawalDetails.availableCashAmount || 0); // 可提现
            console.log(this.withdrawalDetails.availableCashAmount, 'this.withdrawalDetails.availableCashAmount');
            // this.withdrawalDetails.paymentIn = Util.Fen2Yuan(this.withdrawalDetails.paymentIn || 0); // 已结算
            this.getWithdrawal();
          }
        }
      },
      //导出账单
      async exportBill() {
        let url = this.tabKey == 1 ? '/deliver/app/teacher/exportTeacherAccountFlow' : '/deliver/app/teacher/exportFlexPayTeacherAccountFlow';
        let res = await http.get(url + `?userCode=${this.userCode}&type=${this.type == 0 ? '' : this.type}&month=${this.month}&status=${this.status}`, {
          responseType: 'arraybuffer'
        });
        if (res) {
          this.loadByRes(res.data, 'xlsx');
        }
      },
      loadByRes(res, type) {
        let arr = ['doc', 'xls', 'ppt', 'pdf', 'docx', 'xlsx', 'pptx'];
        if (!arr.includes(type)) {
          return uni.showToast({
            icon: 'none',
            title: '文件类型不支持'
          });
        }
        const arrayBuffer = res;
        // 将 ArrayBuffer 转换为 Blob 对象（微信小程序没有直接支持 Blob，但可以通过 base64 转换）
        const blob = uni.arrayBufferToBase64(arrayBuffer);
        const now = '账单列表' + new Date().getTime();
        // 创建一个临时文件路径（需要确保路径有效） 加入时间戳保证文件独立性
        const filePath = `${wx.env.USER_DATA_PATH}/${now}`; // 替换为你的文件名和扩展名
        console.log(wx.env.USER_DATA_PATH);
        // 将 base64 字符串转换为 ArrayBuffer，然后写入文件
        const uint8Array = uni.base64ToArrayBuffer(blob);
        // 使用 wx.getFileSystemManager().writeFile 写入文件
        const fs = wx.getFileSystemManager();
        console.log(filePath);
        fs.writeFile({
          filePath: filePath + '.' + type,
          data: uint8Array,
          encoding: 'binary',
          success: (info) => {
            setTimeout(() => {
              uni.uploadFile({
                url: `${Config.DXHost}/zx/common/uploadFile`,
                filePath: filePath + '.' + type,
                name: 'file',
                formData: {
                  user: 'test'
                },
                header: {
                  Token: uni.getStorageSync('token')
                },
                success: (res) => {
                  let data = JSON.parse(res.data);
                  console.log(data.data.fileUrl);
                  this.downFileUrl = data.data.fileUrl;
                  this.$refs.copyExcolPopup.open();
                }
              });
            }, 500);
          },
          fail: (err) => {
            console.error('文件保存失败', err);
          }
        });
      },
      excelCopy() {
        let that = this;
        uni.setClipboardData({
          data: that.downFileUrl,
          success: function (res) {
            uni.getClipboardData({
              success: function (res) {
                uni.showToast({
                  title: '已复制到剪贴板'
                });
                that.$refs.copyExcolPopup.close();
              }
            });
          }
        });
      },
      //预览
      excelPreview() {
        let that = this;
        uni.downloadFile({
          //下载文件资源到本地,返回文件的本地临时路径
          url: that.downFileUrl, //网络图片路径
          success: (res) => {
            console.log(res, 'downloadFile');
            uni.openDocument({
              filePath: res.tempFilePath,
              fileType: 'xlsx', // 可选，指定文件类型，有助于系统更好地处理文件
              showMenu: true, //可选 是否开启保存转发等功能
              success: (openInfo) => {
                console.log(openInfo);
                console.log('文件打开成功');
                that.$refs.copyExcolPopup.close();
              },
              fail: (err) => {
                console.error('文件打开失败', err);
              }
            });
          }
        });
      },
      // 获取累计提现金额
      async getWithdrawal() {
        let that = this;

        let url = that.tabKey == 1 ? '/deliver/app/teacher/getTeacherWithdrawMoney' : '/deliver/app/teacher/getFlexPayTeacherWithdrawMoney';
        let res = await http.get(url, {
          userCode: that.userCode
        });
        if (res.data.success) {
          that.totalWithdrawal = Number(res.data.data).toFixed(2); // 累计提现
          that.balance = (Number(that.withdrawalDetails.totalMoney) - Number(that.totalWithdrawal)).toFixed(2); // 账期余额
        }
      },

      //获取教练某月的收入和支出
      async getAccountFlow() {
        let that = this;
        let url = that.tabKey == 1 ? '/deliver/app/teacher/getTeacherAccountFlow' : '/deliver/app/teacher/getFlexPayTeacherAccountFlow';
        let res = await http.get(url, {
          userCode: that.userCode,
          month: that.month
        });
        if (res.data.success) {
          that.priceDetails = res.data.data;
          that.priceDetails.outAmount = Number(that.priceDetails.outAmount).toFixed(2);
          that.priceDetails.inAmount = Number(that.priceDetails.inAmount).toFixed(2);
        }
      },

      goBillDetails(item) {
        if (item.orderId == '') {
          uni.navigateTo({
            url: `/pages/my/wallet/billDetails?info=${JSON.stringify(item)}`
          });
        } else {
          uni.navigateTo({
            url: `/pages/my/wallet/billDetails?orderId=${item.orderId}&type=${item.type}&teacherName=${this.teacherName}&merit_pay=${item.is_merit_pay}`
          });
        }
      },
      goWallet() {
        // uni.showModal({
        //   title: '提现提示',
        //   content: 'APP暂不支持提现，请前往助教端小程序进行提现',
        //   showCancel: false,
        //   success: function (res) {
        //     if (res.confirm) {
        //       console.log('用户点击确定');
        //     }
        //   }
        // });
        if (this.tabKey == 1) {
          uni.navigateTo({
            url: `/pages/my/wallet/wallet?withdrawable=${this.withdrawalDetails.availableCashAmount}&userCode=${this.userCode}&key=${this.tabKey}`
          });
        } else {
          //实名认证校验
          this.getRealName();
        }
      },

      async getRealName() {
        // 判断用户是否实名认证
        let that = this;
        let res = await http.get('/mps/user/info/user/code', {
          userCode: this.userCode
        });
        console.log(res.data);
        if (res.data.success) {
          if (res.data.data.signContractStatus == 1) {
            //签约校验
            console.log('签约校验');
            //已实名 去签约
            let res = await http.get('/flexpay/user/sign-status', {
              userType: 'Member',
              userCode: this.userCode
            });

            if (res.data.data.signStatus == 1) {
              if (this.withdrawalDetails?.availableCashAmount == null) {
                uni.showToast({
                  title: '暂无提现金额',
                  icon: 'none'
                });
              } else {
                console.log(this.withdrawalDetails.availableCashAmount, 'this.withdrawalDetails.availableCashAmount');
                uni.navigateTo({
                  url: `/pages/my/wallet/wallet?withdrawable=${this.withdrawalDetails.availableCashAmount}&userCode=${this.userCode}&key=${this.tabKey}`
                });
              }
            } else {
              //首次展示弹窗
              this.$refs.coinPopup.open();
              console.log(res, 'oooooooooooooo');
              this.signTittle = res.data.data.agreementTitle;
              this.agreementUrl = res.data.data.agreementUrl;
            }
          } else {
            uni.showModal({
              title: '提示',
              content: '实名认证未完成，前往认证',
              showCancel: false,
              success: function (res) {
                if (res.confirm) {
                  uni.redirectTo({
                    url: '/authen/authen?userCode=' + that.userCode
                  });

                  // uni.redirectTo({
                  //   url: '/splitContent/authen/webview?url=' + encodeURIComponent(_this.signurl)
                  // });
                } else if (res.cancel) {
                  console.log('用户点击取消');
                }
              }
            });
          }
        }
      },
      goToAgreement() {
        uni.navigateTo({
          url: `/pages/my/agreement/agreement?agreementUrl=${this.agreementUrl}`
        });
      },
      async seeKnow() {
        let res = await http.put(`/flexpay/user/sign-time?userCode=${this.userCode}`);
        if (res.success) {
          uni.navigateTo({
            url: `/pages/my/wallet/wallet?withdrawable=${this.withdrawalDetails.availableCashAmount}&userCode=${this.userCode}&key=${this.tabKey}`
          });
        }
      }
    }
  };
</script>
<style>
  page {
    background-color: #fafcfe;
  }
</style>

<style lang="scss" scoped>
  .hear_img {
    position: fixed;
    top: 0;
    width: 100%;
    height: 590rpx;
    z-index: 99;
  }

  .popup-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: column;
    width: 500rpx;
    height: 400rpx;
    padding: 20rpx;
    border-radius: 20rpx;
    background: #fff;
  }

  .tab-bg {
    // background-color: #f3f8fc;
    position: fixed;
    top: 140rpx;
    width: 100%;
    height: 100rpx;
    display: flex;
    justify-content: center;
    z-index: 100;
  }

  .nav-title {
    font-weight: bold;
    width: 100%;
    font-size: 34rpx;
    padding-top: 80rpx;
    position: fixed;
    left: 40%;
    z-index: 999;
  }

  .status_bar {
    position: relative;
  }

  .title-icon {
    position: absolute;
    top: 0;
    left: -270rpx;
  }

  .wallet-bgc {
    width: 100%;
    height: 650rpx;
  }

  .bill {
    width: 92%;
    position: absolute;
    top: 640rpx;
    padding: 0 30rpx;
    padding-bottom: 30rpx;
  }

  .border-b {
    border-bottom: 1px solid #eeeeee;
  }

  // /deep/.u-tabs__wrapper__nav__line{
  // 	left:-9rpx !important;
  // }
  /deep/.u-tabs__wrapper__nav__item__text {
    font-size: 14px !important;
  }

  .amount {
    color: #fff;
    width: 84%;
    position: absolute;
    top: 300rpx;
    left: 60rpx;
  }

  .balance {
    position: absolute;
    top: -20rpx;
    right: -40rpx;
  }

  .balance_explain {
    position: absolute;
    top: 0;
    width: 355rpx;
    height: 300rpx;
    border-radius: 15rpx;
    background-color: #fff;
    padding: 30rpx;
    right: -460rpx;
    z-index: 9;
    box-shadow: 0rpx 3rpx 10rpx #c7c7c7;
  }

  .total_money {
    position: absolute;
    top: -20rpx;
    // right: 70rpx;
    left: 90rpx;
  }

  .order-style-width {
    width: 420rpx;
  }

  .total_explain {
    position: absolute;
    top: 50rpx;
    right: -240rpx;
    width: 360rpx;
    height: 70rpx;
    line-height: 70rpx;
    background-color: #fff;
    border-radius: 10rpx;
    box-shadow: 0rpx 3rpx 10rpx #c7c7c7;
    padding: 0 15rpx;
  }

  .withdrawal_explain {
    position: absolute;
    top: 50rpx;
    right: -70rpx;
    width: 230rpx;
    height: 70rpx;
    line-height: 70rpx;
    text-align: center;
    background-color: #fff;
    border-radius: 10rpx;
    box-shadow: 0rpx 3rpx 10rpx #c7c7c7;
  }

  .taking {
    position: absolute;
    top: -20rpx;
    // right: 70rpx;
    left: 470rpx;
  }

  .money {
    font-size: 46rpx;
  }

  .withdrawal {
    width: 170rpx;
    height: 60rpx;
    color: #2f8c70;
    line-height: 60rpx;
    text-align: center;
    border-radius: 30rpx;
    background-image: linear-gradient(to right, #f2f8f6, #becec7);
  }

  .border-t {
    border-top: 1px solid #7ab09f;
  }

  /deep/.u-toolbar {
    border-bottom: 1px solid #e0e0e0;
  }

  /deep/.u-line-1 {
    line-height: 88rpx !important;
    background-color: #f4f4f4 !important;
  }

  /deep/.u-picker__view {
    height: 440rpx !important;
  }

  /deep/.u-picker__view__column {
    border-radius: 12rpx;
  }

  /deep/.u-popup__content {
    border-radius: 12rpx;
    margin: 0 20rpx 20rpx 20rpx;
  }

  /deep/.u-picker__view__column__item {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 30rpx !important;
    margin-left: 10rpx;
  }

  /deep/.u-picker__view__column__item {
    margin-left: 0 !important;
  }

  .c-67C23A {
    color: green;
  }

  .c-E6A23C {
    color: #e6a23c;
  }

  .img_s {
    width: 160rpx;
    height: 160rpx;
  }

  .no-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .export-btn-style {
    position: fixed;
    bottom: 15px;
    left: 50%;
    z-index: 999;
    transform: translateX(-50%);
    width: 580rpx;
    line-height: 80rpx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 35rpx;
    text-align: center;
    color: #ffffff;
    font-size: 30rpx;
  }

  .copy-excol-popup {
    background: #fff;
    text-align: center;
    padding: 38rpx 20rpx 20rpx 20rpx;
    border-radius: 15rpx;
    width: 500rpx;
    line-height: 50rpx;

    .btn-style {
      margin-top: 50rpx;
      display: inline-block;
      padding: 0 25rpx;
      line-height: 65rpx;
      background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
      border-radius: 15rpx;
      text-align: center;
      color: #ffffff;
      font-size: 26rpx;
    }

    .left-btn-style {
      color: #1d755c;
      background: #fff;
      border: 2rpx solid #1d755c;
    }

    .scrollView {
      overflow: hidden;
    }
  }
</style>

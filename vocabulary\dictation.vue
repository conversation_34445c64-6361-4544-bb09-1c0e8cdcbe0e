<!-- 听写检测报告列表页面 -->
<template>
	<view class="study_print_page">
		<view class="bg_box" style="background-image:url('https://document.dxznjy.com/applet/newimages/reportImport.png')"></view>
		<!-- 学员选择框 -->
		<view class="list_box">
			<view class="student_pick row_flex">
				<view>学员：</view>
				<view v-if="studentCode==''" class="pick">
					<picker class="btnchange" @change="bindPickerStudent" :value="studentIndex" :range="studentArray"
						range-key="realName" name="grade">
						<view>{{studentArray[studentIndex].realName}}</view>
					</picker>
					<image src="/static/images/arrow.png" class="jiantou arrow" mode="widthFix"></image>
				</view>
				<view class="pick" v-else>{{studentName}}（{{studentCode}})</view>
			</view>
		</view>
		<!-- 学员选择框 -->

		<!-- 下部内容 -->
        <view v-if="detectionList.length>0">
            <view class="subject" v-for="(item, index) in detectionList" :key="index">
            	<view class="subcard" @click="reportFn(item)">
            		<view class="title">
            			<text>姓名：{{item.realName || ''}}</text>
            			<image src="/static/images/arrow.png" class="jiantou arrow" mode="widthFix">
            			</image>
            		</view>
            		<view class="title">
            			<text>评测日期：{{item.levelTime || ''}}</text>
            		</view>
            		<view class="title">
            			<!-- <text>能力水平：{{ ('单词发音水平'+item.readWordRank + ',单词拼写水平' + item.writeWordRank) || ''}}</text> -->
            			<text>能力水平：{{ ('单词发音水平'+item.readWordRank + ',单词拼写水平' + item.writeWordRank) || ''}}</text>
            		</view>
            	</view>
            </view>
        </view>
        
        <view v-else class="t-c flex-col mlr-30 mt-30 bg-ff radius-15" :style="{height: useHeight- 600+'rpx'}">
        	<image src="https://document.dxznjy.com/alading/correcting/no_data.png" class="mb-20" style="width: 160rpx;" mode="widthFix"></image>
        	<view style="color: #BDBDBD;">暂无数据</view>
        </view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				studentCode: '',
				studentName:'',
				studentIndex: 0,
				studentArray: [
                    {
                        studentCode: '',
                        realName: '请选择学员',
                    },
                ],
				detectionList: [], // 检测列表
				pageNum: 1, // 当前页
				pageSize: 10, // 页数
				loadingType: 'nodata', //加载更多状态
				urlId: '' ,//跳转带过去的当前id
                useHeight: 0, //除头部之外高度
				reportId: '' ,//跳转带过去的当前id
				isPageAdd:false, //触底加载页码是否增加
			}
		},
        onReady() {
            uni.getSystemInfo({
                success: (res) => {
                    // 可使用窗口高度，将px转换rpx
                    let h = (res.windowHeight * (750 / res.windowWidth));
                    this.useHeight = h - 560;
                }
            })
			const pages = getCurrentPages();
			const currentPage = pages[pages.length - 1]; // 假设数组最后一个是当前页面  
        },
		onLoad(option) {
			var that = this;
			// that.getStudentCode();
			if (option != null && option.studentCode) {
				that.studentCode = option.studentCode;
				that.studentName = option.studentName;
			} else {
				that.getStudentCode();
			}
		},
		
		onShow() {
			this.detectionList = [];
			this.pageNum = 1;
			this.getDataList();
		},

		//加载更多
		onReachBottom() {
			if (this.studentCode != '') {
				if (!this.isPageAdd) {
					this.pageNum++;
				}
				this.getDataList();
			}
		},
		watch: {
			studentCode(val) {
				if (val != '') {
					/* this.pageNum = 1;
					this.pageSize = 10;
					this.getDataList(true); */
				}
			},
		},


		methods: {
			// 选择学员code
			bindPickerStudent(e) {
				this.studentIndex = e.target.value;
				this.studentCode = this.studentArray[this.studentIndex].studentCode;
				this.getDataList();
			},
			// 查询studentCode
			async getStudentCode() {
				let result = await uni.$http.get("/znyy/review/query/my/student");
				if (result != undefined && result != '') {
					if (result.data.data != null) {
						this.studentArray = [
                            {
							studentCode: '',
							realName: '选择学员',
                            },
                        ]
						if (result.data.data.length > 0) {
							if (result.data.data.length == 1) {
								this.studentArray = [];
								this.index = 0;
								this.studentArray = this.studentArray.concat(result.data.data);
								this.studentCode = this.studentArray[this.index].studentCode;
							} else {
								var that = this;
								that.studentArray = that.studentArray.concat(result.data.data);
							}
						}
					}
				}
			},
			// 获取学员信息内容
			async getDataList(change) {
				let that = this;
			    if(that.studentCode == ""){
			        that.detectionList = [];
			        return;
			    }
			    if(change){
			        that.detectionList = [];
			    }
				let result = await uni.$http.get('/znyy/pd/mobile/getStudentLevelList/' + that
					.pageNum + '/' + that
					.pageSize, {
						studentCode: that.studentCode,
						pageNum: that.pageNum,
						pageSize: that.pageSize,
					}).then(result => {
					if (result) {
						if (result.data.data.data.data.length == 0) {
							that.isPageAdd = true
							that.loadingType = 'nodata';
							uni.showToast({
								icon: "none",
								title: '暂无更多内容了！',
								duration: 2000
							});
						} else {
							that.isPageAdd = false
							if (result.data.data.data.data.length > 0) {
								that.detectionList = that.detectionList.concat(result.data.data.data.data);
							}
			                console.log(that.detectionList, 'that.detectionList');
							that.loadingType = that.pageNum >= Number(result.data.data.data.totalPage) ?
								'nomore' : 'more';
						}
					}
				})
			},

			// 跳转到报告页面
			reportFn(item) {
				console.log('item',item)
				// return
				uni.navigateTo({
					url: '/vocabulary/dictationSingleReport?id=' + item.id +'&studentCode='+this.studentCode
				})
			},
		},
	}
</script>

<style lang="scss">
	.study_print_page {
		background-color: #f3f8fc;
		padding-bottom: 60rpx;
	}

	.bg_box {
		width: 690rpx;
		height: 330rpx;
		margin: 0 auto;
		background-image: url('https://document.dxznjy.com/applet/newimages/ciliang.png');
		background-size: cover;
		background-repeat: no-repeat;
	}

	.list_box {
		margin: 30upx 30upx 0upx 26upx;
		border-radius: 14upx;
		background: #ffffff;
		padding: 40upx;
		font-size: 26upx;
		color: rgba(102, 102, 102, 1);
		position: relative;
	}

	.row_flex {
		display: flex;
		align-items: center;
		/* 		justify-content: center; */
	}

	.pick {
		position: relative;
		width: 520upx;
		height: 60upx;
		line-height: 60upx;
		border: 1px solid rgba(199, 199, 199, 1);
		border-radius: 12upx;
		text-align: center;
		overflow: visible;
		transform: scale(0.995); /* 解决ios上圆角失效 */
	}

	.jiantou {
        width: 20rpx;
        height: 20rpx;
		position: absolute;
		right: 15upx;
		top: 15upx;

	}

	// 下面内容
	.subject {
		margin: 30rpx;
		width: 630rpx;
		height: 225rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		line-height: 70rpx;
		background: #ffffff;
		border-radius: 14rpx;
		padding-left: 30rpx;
		padding-right: 30rpx;
		font-size: 30rpx;
		font-family: AlibabaPuHuiTiR;
		color: #333333;
	}

	.subcard {
		position: relative;
	}
</style>
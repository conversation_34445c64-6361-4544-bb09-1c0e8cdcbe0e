<template>
  <view>
    <view class="container">
      <!-- 正式学员反馈 -->
      <view class="card">
        <view class="white-content" style="position: relative">
          <view v-if="!trialclass" @click="refresh(id)" class="refresh-view">
            <image class="width30" src="/static/images/shuaxin.png" mode=""></image>
            <view class="f-28" style="color: #2e896f; margin-left: 4rpx">刷新</view>
          </view>
          <view class="text-content">日期：{{ feedbackList.dateTime }}</view>
          <view class="text-content">姓名：{{ feedbackList.studentName }}</view>
          <view class="text-content">年级：{{ feedbackList.gradeName }}</view>
          <view class="text-content">学员编号：{{ feedbackList.studentCode }}</view>
          <view class="text-content">时间：{{ feedbackList.studyTime }}</view>
          <!-- 实际时间 -->
          <view class="text-content reality">
            实际时间：
            <view class="begin">{{ timelist.actualStart }}</view>
            至
            <view class="finish">{{ timelist.actualEnd }}</view>
          </view>
          <view class="text-content">学习学时：{{ feedbackList.studyHour }}个小时</view>
          <view class="text-content" v-if="feedbackList.extendProperty.totalCourseHours">已购拼音法学时：{{ feedbackList.extendProperty.totalCourseHours }}个小时</view>
          <view class="text-content" v-if="feedbackList.extendProperty.totalCourseHours">剩余拼音法学时：{{ feedbackList.extendProperty.haveCourseHours }}个小时</view>
          <view class="text-content">所学课程类型：{{ feedbackList.curriculumName }}{{ feedbackList.extendProperty.courseTypeName }}</view>
          <view class="text-content">所学课程名称：{{ feedbackList.extendProperty.courseNames }}</view>
          <view class="text-content">学习元辅音个数：{{ feedbackList.extendProperty.consonantCounts }}</view>
          <view class="text-content">学习音节个数：{{ feedbackList.extendProperty.syllableCounts }}</view>
          <view class="text-content">学习单词个数：{{ feedbackList.extendProperty.wordCounts }}</view>
        </view>

        <view class="white-content marginTop30" style="height: 420rpx">
          <view class="flex-view">
            <view class="text-content">教练评语：</view>
            <view v-if="isConfirmShow || isEdit" class="flex-view-text" @click="cleanFeedback()">清空</view>
          </view>
          <!-- 反馈 -->
          <view style="max-height: 300upx">
            <u--textarea
              wrap="soft"
              v-if="isConfirmShow || isEdit"
              :disabled="!isConfirmShow && !isEdit"
              class="feedback"
              v-model="feedback"
              :value="feedback"
              placeholder="请输入"
              count
              placeholderStyle="color:#b0b0b6"
              height="165"
              maxlength="200"
            ></u--textarea>
            <view v-else class="font26 color_black33 feedbackBoxNew marginBottom10" style="overflow-wrap: break-word">
              {{ feedback }}
            </view>
          </view>
        </view>
        <view class="button-sp-area marginTop40 flexs">
          <button v-if="!isConfirmShow && !isEdit" class="mini-btn refresh" type="default" size="mini" @click="editOption()">修改</button>
          <button v-if="isConfirmShow || isEdit" class="mini-btn background_green_two" type="default" size="mini" @click="addOrLook(id)">确定</button>
          <button v-else class="mini-btn background_green_two" type="default" size="mini" @click="shareJump">分享</button>
        </view>
      </view>
    </view>

    <uni-popup ref="loadingPopup" type="center" :is-mask-click="false">
      <view class="bg-ff radius-15 t-c">
        <view class="loadingpadding">
          <image src="https://document.dxznjy.com/Assistant/loading.png" class="loadingImg"></image>
          <view class="mt-30">加载中...</view>
          <view>请耐心等候</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>
<script>
  import dateTime from '@/components/lanxiujuan-dyDateTime/lanxiujuan-dyDateTime.vue';
  import dayjs from 'dayjs';
  import longDate from '@/components/long-date/long-date.vue';
  export default {
    name: 'StudyDetails',
    components: {
      dateTime,
      longDate
    },
    data() {
      return {
        backlist: '', // 获取反馈详情列表（正式）
        subject: '', // 课程列表详情
        timelist: {
          actualStart: '',
          actualEnd: ''
        },
        isFeedback: false, // 是否显示反馈详情确定按钮
        //反馈详情列表
        trialclass: false, // 是否是试课反馈
        Feedback: false, // 是否显示试课反馈详情确定按钮
        feedback: '', // 弹出层文本框输入的内容
        dates: '',
        flag: false, // 防止多次请求
        isEdit: false, //是否修改
        //复习时间
        reviewTimeArr: ['', '', '']
      };
    },

    onLoad(e) {
      let that = this;
      this.dates = e.dates;
      console.log('dates', this.dates);
      let sendData = JSON.parse(decodeURIComponent(e.sendData));
      if (sendData != null && sendData != undefined) {
        this.trialclass = sendData.trialclass;
        this.isFeedback = sendData.isFeedback;
        this.triallist = sendData.triallist;
        this.subject = sendData.subject;
        this.backlist = sendData.backlist;
        // this.getDetail();
        if (this.trialclass) {
          // this.feedbackList = JSON.parse(JSON.stringify(this.triallist))
          if (this.triallist != null && this.triallist != undefined) {
            // that.selExperienceMemoryRecord();
            this.Feedback = this.triallist.feedback === '' || this.triallist.feedback == null || this.triallist.feedback == undefined;
            this.feedback = this.triallist.feedback;
            this.Intention = this.triallist.studyIntention;
            this.timelist.actualStart = this.triallist.actualStart;
            this.timelist.actualEnd = this.triallist.actualEnd;
            this.reviewTimeArr = this.getNormalReviewTime(this.triallist.reviewDateList);
          }
        } else {
          // this.feedbackList = JSON.parse(JSON.stringify(this.backlist))
          if (this.backlist != null && this.backlist != undefined) {
            this.feedback = this.backlist.feedback;
            this.isFeedback = this.backlist.feedback === '' || this.backlist.feedback == null || this.backlist.feedback == undefined;
            this.timelist.actualStart = this.backlist.actualStart;
            this.timelist.actualEnd = this.backlist.actualEnd;
          }
          // 学习效率
          if (this.isFeedback) {
            this.backlist.studyRate = '';
          }
        }
      }
      console.log('feedbackList', this.feedbackList);
    },
    onReady() {
      console.log('feedbackList', this.feedbackList);
    },
    onShow() {
      console.log('feedbackList', this.feedbackList);
    },
    computed: {
      feedbackList() {
        if (this.trialclass) {
          console.log('feedbackList1');
          return this.triallist;
        } else {
          console.log('feedbackList2');
          return this.backlist;
        }
      },
      isConfirmShow() {
        if (this.trialclass) {
          return this.Feedback;
        } else {
          return this.isFeedback;
        }
      }
    },

    methods: {
      getNormalReviewTime(arr) {
        if (!arr) {
          arr = [];
        }
        if (arr.length >= 3) {
          return arr;
        }
        for (let i = 0; i < 3; i++) {
          if (!arr[i]) {
            arr.push('');
          }
        }
        return arr;
      },
      //清空
      cleanFeedback() {
        this.feedback = '';
      },
      //修改按钮
      editOption() {
        this.isEdit = true;
      },

      // 获取试课详情
      getDetail() {
        if (this.trialclass) {
          //试课
          this.trialData(this.subject);
        } else {
          //学习反馈
          this.backData();
        }
      },

      // 确定按钮（关闭弹框）
      async refresh() {
        let that = this;
        if (that.flag) return;
        that.flag = true;
        this.$refs.loadingPopup.open();
        try {
          let res = await uni.$http.get('/deliver/app/teacher/refreshFeedbackInfo', {
            id: this.subject.id,
            actualStart: this.timelist.actualStart,
            actualEnd: this.timelist.actualEnd,
            type: 1
          });
          this.$refs.loadingPopup.close();
          setTimeout(() => {
            that.flag = false;
          }, 60000);
          // if (res.data.code == 70001) {}
          if (res.data.success) {
            that.backlist = res.data.data;
            that.isFeedback = that.backlist.feedback === '' || that.backlist.feedback == null || that.backlist.feedback == undefined;
            that.feedback = that.backlist.feedback;
            that.timelist.actualStart = that.backlist.actualStart;
            that.timelist.actualEnd = that.backlist.actualEnd;
          }
        } catch {
          this.$refs.loadingPopup.close();
        }
      },

      // 确定按钮（关闭弹框）
      addOrLook() {
        if (this.isEdit) {
          this.editFeedback();
          return;
        }
        if (this.subject.experience == false) {
          console.log(this.trialclass);
          this.addbackData(false); // 新增反馈  // experience = true 是试课反馈
        } else {
          this.addtrialData(); // 新增试课反馈  // experience = false 是学习反馈
        }
      },

      // 反馈更新
      async editFeedback() {
        let that = this;
        that.$refs.loadingPopup.open();
        let postUrl = {};
        postUrl = {
          feedback: that.feedback,
          studyId: that.subject.id
        };
        try {
          let res = await uni.$http.post('/deliver/app/teacher/updateFeedback', postUrl);
          console.log(res);
          that.$refs.loadingPopup.close();
          if (res.data.success) {
            uni.showToast({
              title: '修改反馈成功',
              duration: 1000
            });
            that.isEdit = false;
            that.getDetail();
          }
        } catch {
          that.$refs.loadingPopup.close();
        }
      },

      // 新增学习反馈
      async addbackData(isConfirm) {
        let that = this;
        that.$refs.loadingPopup.open();
        let postUrl = `/deliver/app/teacher/addSimpleFeedback?actualStart=${that.timelist.actualStart}&actualEnd=${that.timelist.actualEnd}&feedBack=${encodeURI(
          encodeURI(that.feedback)
        )}&id=${that.subject.id}&type=1`;
        if (isConfirm) {
          postUrl = postUrl + '&confirm=true';
        }
        try {
          let res = await uni.$http.post(postUrl);
          that.$refs.loadingPopup.close();
          if (res.data.code == 40047) {
            uni.showModal({
              title: '提示',
              content: res.data.message,
              success: function (res) {
                if (res.confirm) {
                  that.addbackData(true);
                } else if (res.cancel) {
                  console.log('用户点击取消');
                }
              }
            });
            return;
          }
          // 成功提示
          if (res.data.success) {
            uni.showToast({
              title: '反馈成功',
              duration: 1000
            });
            that.backlist.feedback = that.feedback;
            that.feedbackList.feedback = that.feedback;
            //显示分享按钮
            that.isFeedback = false;
            that.getDetail();
          }
        } catch {
          that.$refs.loadingPopup.close();
        }
      },

      // 新增试课反馈
      async addtrialData() {
        let that = this;
        that.$refs.loadingPopup.open();
        let data = {
          feedback: that.feedback,
          studyId: that.subject.id,
          actualStart: that.timelist.actualStart,
          actualEnd: that.timelist.actualEnd
        };
        let postUrl = `/deliver/app/teacher/addSimpleExperienceFeedback`;
        try {
          let res = await uni.$http.post(postUrl, data);
          console.log(res);
          that.$refs.loadingPopup.close();
          if (res.data.success) {
            uni.showToast({
              title: '反馈成功',
              duration: 1000
            });
            //显示分享按钮
            that.Feedback = false;
            that.getDetail();
          }
        } catch {
          that.$refs.loadingPopup.close();
        }
      },

      //分享
      shareJump() {
        let sendData = {};
        sendData.isStudy = true;
        if (this.trialclass) {
          sendData.detailsData = this.triallist;
        } else {
          sendData.detailsData = this.backlist;
        }
        let newstr=''
        // #ifdef MP-WEIXIN
        newstr =JSON.stringify(sendData)
        // #endif
        // #ifdef APP-PLUS
        newstr =JSON.stringify(sendData).replace(/%/g,'%25');
        // #endif
        // sendData.detailsData = this.feedbackList;
        uni.navigateTo({
          url: '/share/pinyin_share_feedback?sendData=' + encodeURIComponent(newstr)
        });
      },

      Back() {
        uni.$emit('onCalendarRefresh', this.dates);
        uni.navigateBack();
      },

      // 获取反馈详情
      async backData() {
        let that = this;
        let res = await uni.$http.get('/deliver/app/teacher/getFeedbackInfo', {
          id: this.subject.id,
          actualStart: this.timelist.actualStart,
          actualEnd: this.timelist.actualEnd,
          type: 1
        });
        if (res.data.success) {
          that.backlist = res.data.data;
          that.isFeedback = that.backlist.feedback === '' || that.backlist.feedback == null || that.backlist.feedback == undefined;
          that.feedback = that.backlist.feedback;
          that.timelist.actualStart = that.backlist.actualStart;
          that.timelist.actualEnd = that.backlist.actualEnd;
        }
      },

      // 试课反馈详情
      async trialData(item) {
        let that = this;
        let res = await uni.$http.post(`/deliver/app/teacher/getExperienceInfo/${that.subject.id}?actualStart=${that.timelist.actualStart}&actualEnd=${that.timelist.actualEnd}`);
        that.triallist = res.data.data;
        if (res.data.code == 70001) {
          return;
        }
        if (res.data.success) {
          that.Feedback = that.triallist.feedback === '' || that.triallist.feedback == null || that.triallist.feedback == undefined;
          that.feedback = that.triallist.feedback;
          that.Intention = that.triallist.studyIntention;
          that.timelist.actualStart = that.triallist.actualStart;
          that.timelist.actualEnd = that.triallist.actualEnd;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .container {
    background-color: #f3f8fc;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 40rpx;
    padding-bottom: 70rpx;
  }

  .page {
    width: 100%;
  }

  .details {
    display: flex;
    font-size: 25rpx;
    padding: 0 30rpx 50rpx;
    margin-top: 20rpx;

    /deep/.u-row {
      margin-bottom: 20rpx !important;
    }

    /deep/.u-demo-block__content {
      width: 98% !important;
      margin-left: 5rpx;
    }

    /deep/.u-col {
      padding: 0 !important;
    }
  }

  .left {
    font-size: 26rpx;
    align-items: center;
  }

  .right {
    position: relative;
    padding: 20rpx 20rpx 40rpx;
    border-radius: 30rpx;
    background-color: #fff;
  }

  .pic {
    position: absolute;
    left: 300rpx;
    top: 30rpx;
    height: 50rpx;
    width: 50rpx;
  }

  .rightbtn {
    float: right;
    position: absolute;
    top: 40rpx;
    right: 40rpx;
  }

  .icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 10rpx;
  }

  .right-content {
    margin-top: 10rpx;
  }

  .tickling {
    text-align: center;
    font-weight: 700;
    font-size: 30rpx;
  }

  .text-content {
    font-size: 28rpx;
    color: #000;
    margin-bottom: 30rpx;
    display: flex;
  }

  .widthAHalf {
    width: 50%;
  }

  .text_content_inner {
    width: 450rpx;
    white-space: pre-line;
    line-height: 45upx;
  }

  /deep/.u-textarea__field {
    background-color: #f7f7f7;
    border: 1rpx solid #d9d9d9;
    padding: 10rpx;
  }

  /deep/.u-textarea--disabled {
    background-color: #fff !important;
  }

  .button-sp-area {
    display: flex;
  }

  .cancel-btn {
    font-size: 24rpx;
    width: 120rpx;
    height: 55rpx;
    margin-bottom: 30rpx;
  }

  .refresh {
    margin-right: 30rpx;
    color: #2e896f !important;
    border: 1px solid #2e896f;
  }

  .refresh-view {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    right: 30rpx;
    top: 40rpx;
  }

  .mini-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 40rpx;
    font-size: 30rpx;
    color: #fff;
    width: 690rpx;
    height: 90rpx;
    line-height: 90rpx;
  }

  .border-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 30rpx;
    font-size: 24rpx;
    color: #1a8eff;
    background-color: #fff;
    border: 1.4rpx solid #1a8eff;
    width: 160rpx;
    height: 60rpx;
    transform: scale(0.995); /* 解决ios上圆角失效 */
  }

  // 弹框的样式设置
  /deep/.card {
    width: 680rpx;

    .white-content {
      background-color: #fff;
      border-radius: 14rpx;
      padding: 40rpx 30rpx 40rpx 30rpx;
      // overflow-y: auto;
    }

    .reality {
      display: flex;
      align-items: center;
      margin-top: 10rpx;
      line-height: 40rpx;
      font-size: 26rpx;
    }

    .begin {
      border: 1px solid #999;
      border-radius: 10rpx;
      height: 40rpx;
      line-height: 40rpx;
      width: 220rpx;
      text-align: center;
      margin-right: 6upx;
      overflow: visible;
      font-size: 24rpx;
      transform: scale(0.995); /* 解决ios上圆角失效 */
    }

    .finish {
      border: 1px solid #999;
      margin-left: 6upx;
      border-radius: 10rpx;
      height: 40rpx;
      line-height: 40rpx;
      width: 220rpx;
      text-align: center;
      font-size: 24rpx;
      overflow: visible;
      transform: scale(0.995); /* 解决ios上圆角失效 */
    }
  }

  .flexs {
    display: flex;
    align-items: center;
  }

  .borderInput {
    width: 100upx;
    border-bottom: 1upx solid #b1b1b1;
    text-align: center;
  }

  .border-bottom {
    border-bottom: 1rpx solid #efefef;
  }

  .loadingImg {
    width: 120rpx;
    height: 120rpx;
  }

  .loadingpadding {
    padding: 85rpx 250rpx;
  }

  .flex-view {
    display: flex;
    justify-content: space-between;
  }

  .flex-view-text {
    font-size: 28rpx;
    color: rgb(46, 137, 111);
  }
</style>

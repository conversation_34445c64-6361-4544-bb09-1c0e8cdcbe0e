<template>
  <view class="">
    <view class="container">
      <view class="content">
        <view class="title">{{ detailData.subject || '' }}</view>
        <view class="time">
          <view>
            {{ getDateChinese(Number(detailData.startTime)) }}{{ getWeek(Number(detailData.startTime)) }}
            {{ getHours(Number(detailData.startTime)) + '-' + getHours(Number(detailData.endTime)) }}
          </view>
          <view class="icon icon1" v-if="detailData.state == 1">待开始</view>
          <view class="icon icon2" v-if="detailData.state == 2">进行中</view>
          <view class="icon icon3" v-if="detailData.state == 3">已结束</view>
          <view class="icon icon3" v-if="detailData.state == 4">已取消</view>
          <view class="icon icon3" v-if="detailData.state == 5">已销毁</view>
        </view>
        <view class="menber flex-start">
          <view class="name">上课人:</view>
          <view class="flex-start">
            <!--            <image src="../static/images/shike.png" style="width: 45rpx; height: 45rpx; margin-right: 21rpx" mode="" v-if="type == 1"></image>
            <image src="../static/images/img_formal_flag.png" style="width: 45rpx; height: 45rpx; margin-right: 21rpx" mode="" v-if="type == 2"></image> -->
            <view class="">{{ detailData.userNickName || '' }}</view>
          </view>
        </view>
        <view class="menber flex-start">
          <view class="name">参会人:</view>
          <view class="flex-start">
            <!--            <image src="../static/images/shike.png" style="width: 45rpx; height: 45rpx; margin-right: 21rpx" mode="" v-if="type == 1"></image>
            <image src="../static/images/img_formal_flag.png" style="width: 45rpx; height: 45rpx; margin-right: 21rpx" mode="" v-if="type == 2"></image> -->
            <view class="">{{ detailData.studentName || '' }}</view>
            <!-- <view class="marginright16">共2人</view> -->
            <!-- <image src="../static/images/right.png" style="height: 24rpx" mode="heightFix"></image> -->
          </view>
        </view>
        <view class="btns">
          <button class="shareb" open-type="share">
            <view class="share">
              <image src="https://document.dxznjy.com/manage/1719834292000" style="width: 34rpx; height: 34rpx" mode=""></image>
              <view class="marginTop10">分享</view>
            </view>
          </button>
          <!-- <button class="entry-b" @click.prevent="goweburl(detailData.inviteUrl)"> -->
          <button class="entry-b" @click.prevent="openPopup()">
            <view class="entry-m">
              <!-- <image src="../static/images/enterMeeting.png" style="width: 34rpx; height: 34rpx" mode=""></image> -->
              <image src="https://document.dxznjy.com/manage/1718782649000" style="width: 34rpx; height: 34rpx" mode=""></image>
              <view class="marginTop10" style="color: #427ce8">进入会议</view>
            </view>
          </button>
        </view>
        <!--     <view class="btns" v-if="isQy && token">
          <button class="shareb shareb1" open-type="share">
            <view class="share share1">
              <image src="../static/images/share.png" style="width: 34rpx; height: 34rpx" mode=""></image>
              <view class="marginTop10">分享</view>
            </view>
          </button>
        </view>
        <view class="btns" v-else>
          <button class="shareb" open-type="share">
            <view class="share">
              <image src="../static/images/share.png" style="width: 34rpx; height: 34rpx" mode=""></image>
              <view class="marginTop10">分享</view>
            </view>
          </button>

          <button class="entry-b" @click.prevent="goweburl(detailData.inviteUrl)">
            <view class="entry-m">
              <image src="../static/images/enterMeeting.png" style="width: 34rpx; height: 34rpx" mode=""></image>
              <view class="marginTop10" style="color: #427ce8">进入会议</view>
            </view>
          </button>
        </view> -->
      </view>
    </view>
    <!-- pc 端 -->
    <u-popup ref="popup" :show="show" mode="center" v-if="isPc">
      <view class="role_popup" style="height: 600rpx">
        <view class="role_title">
          <text>选择角色</text>
        </view>
        <image class="closeImg" @click="hideMode()" src="/static/images/icon_close.png" mode="aspectFill"></image>
        <!-- 		<view class="relo_center" v-if="isMobile">
					<view class="choose_item">
						<view
							:class="[detailData.studentStatus == 1 ? 'student_status' : hasMeeting1 ? 'student_active' : 'student']"
							@click="chooseRole(1)">
							学生
							<view class="take_class" v-if="true">上课中</view>
						</view>
						<view :class="[hasMeeting2 ? 'student_active' : 'student']" @click="chooseRole(2)">家长
							<view class="take_class" v-if="true">上课中</view>
						</view>
						<view :class="[hasMeeting3 ? 'student_active' : 'student']" @click="chooseRole(3)">推荐人</view>
					</view>
				</view> -->
        <view class="onlyPc">
          <image
            src="https://document.dxznjy.com/manage/1721303455000"
            style="width: 34rpx; height: 34rpx; margin-right: 10rpx; vertical-align: middle; margin-bottom: 8rpx"
            mode=""
          ></image>
          为了保证课程流畅进行请使用Chrome浏览器进行上课，
          <a style="text-decoration: none; color: #269662" href="https://chrome-web.com/">点击下载Chrome</a>
        </view>
        <view class="relo_center">
          <view class="choose_item">
            <view :class="[detailData.studentStatus == 1 ? 'student_status' : hasMeeting1 ? 'student_active' : 'student']" @click="chooseRole(1)">
              学生
              <view class="take_class" v-if="detailData.studentStatus == 1">上课中</view>
            </view>
            <view :class="[hasMeeting2 ? 'student_active' : 'student']" @click="chooseRole(2)">家长</view>
          </view>
        </view>
        <view class="role_bottom">
          <view :class="[chooseMeetingRole ? 'left_btn' : 'left_btn_active']" @click="copy()">复制会议链接</view>
          <!-- <view class="right_btn" @click="closePopup()">取消</view> -->
          <view class="right_btn" @click="hideMode()">取消</view>
        </view>
        <!-- <view class="role_bottom" v-else>
					<view :class="[chooseMeetingRole ? 'left_btn' : 'left_btn_active']" @click="goweburl()">进入会议</view>
					<view class="right_btn" @click="closePopup()">取消</view>
				</view> -->
      </view>
    </u-popup>

    <!-- 移动端 -->
    <uni-popup ref="popup" type="center" v-else>
      <view class="role_popup">
        <view class="role_title">
          <text>选择角色</text>
        </view>
        <image class="closeImg" @click="closePopup()" src="/static/images/icon_close.png" mode="aspectFill"></image>
        <view class="relo_center">
          <view class="choose_item">
            <view :class="[detailData.studentStatus == 1 ? 'student_status' : hasMeeting1 ? 'student_active' : 'student']" @click="chooseRole(1)">
              学生
              <view class="take_class" v-if="detailData.studentStatus == 1">上课中</view>
            </view>
            <view :class="[hasMeeting2 ? 'student_active' : 'student']" @click="chooseRole(2)">家长</view>
          </view>
        </view>
        <view class="role_bottom">
          <view :class="[chooseMeetingRole ? 'left_btn' : 'left_btn_active']" @click="goweburl()">进入会议</view>
          <view class="right_btn" @click="closePopup()">取消</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      id: '',
      type: '1',
      detailData: {},
      isQy: true,
      token: uni.getStorageSync('token'),
      hasMeeting1: false,
      hasMeeting2: false,
      chooseMeetingRole: false,
      url: '',
      meetingId: '',
      isPc: false,
      isMobile: false,
      isWechat: false,
      show: false
    };
  },
  onLoad(e) {
    let that = this;
    that.isAs = uni.getStorageSync('userRole') == 'assistant' ? true : false;
    that.isQy = uni.getStorageSync('isQy');
    if (e.id) {
      that.meetingId = e.id;
      this.initData(e.id);
    }
    wx.getSystemInfo({
      success: function (res) {
        console.log(res, 'wwww');
        if (res.platform === 'android' || res.platform === 'ios' || res.platform === 'devtools') {
          // alert('当前设备是手机');
          that.isMobile = true;
          // alert('手机')
        } else {
          that.isPc = true;
        }
      },
      fail: function (err) {
        console.error('获取系统信息失败', err);
      }
    });
  },
  onShareAppMessage() {},
  methods: {
    hideMode() {
      this.hasMeeting1 = false;
      this.hasMeeting2 = false;
      this.chooseMeetingRole = false;
      this.show = false;
    },
    copy() {
      console.log('123e12');
      let item = this.url;
      if (!this.url) {
        return uni.showToast({
          title: '请选择身份',
          icon: 'none'
        });
      } else {
        uni.setClipboardData({
          data: item,
          success: (res) => {
            console.log(res);
            uni.showToast({
              title: '链接已复制至剪贴板'
            });
          },
          fail: (err) => {
            uni.showToast({
              title: '复制失败',
              icon: 'none'
            });
          }
        });
      }
    },

    // 打开弹窗
    openPopup() {
      let that = this;
      console.log(that.detailData.state);
      if (that.isPc) {
        if (this.detailData.state != 1 && this.detailData.state != 2) {
          let title = this.detailData.state == 3 ? '已结束' : this.detailData.state == 4 ? '已取消' : '已销毁';
          return uni.showToast({
            title: `会议${title}`,
            icon: 'none'
          });
        } else {
          let url = this.detailData.parentsInviteUrl;
          that.show = true;
          var ua = window.navigator.userAgent.toLowerCase();
          if (ua.match(/MicroMessenger/i) == 'micromessenger' && ua.match(/wxwork/i) == 'wxwork') {
            that.isWechat = true;
          } else if (ua.match(/micromessenger/i) == 'micromessenger') {
            that.isWechat = true;
          }
          // let userAgent = window.navigator.userAgent;
          // if (that.isPc) {
          // 	that.show = true;
          // 	var ua = window.navigator.userAgent.toLowerCase();
          // 	if (ua.match(/MicroMessenger/i) == 'micromessenger' && ua.match(/wxwork/i) == 'wxwork') {
          // 		that.isWechat = true;
          // 	} else if (ua.match(/micromessenger/i) == 'micromessenger') {
          // 		that.isWechat = true;
          // 	}
          // } else {
          // 	var ua = window.navigator.userAgent.toLowerCase();
          // 	if (ua.match(/MicroMessenger/i) == 'micromessenger' && ua.match(/wxwork/i) == 'wxwork') {
          // 		console.log('企业微信客户端');
          // 		that.isWechat = true;
          // 		window.location.href = url;
          // 	} else if (ua.match(/micromessenger/i) == 'micromessenger') {
          // 		console.log('微信客户端');
          // 		that.isWechat = true;
          // 		that.initData(that.meetingId);
          // 		that.show = true;
          // 	} else {
          // 		// return console.log(url);
          // 		window.location.href = url;
          // 	}
          // }
        }
      } else {
        if (that.detailData.state != 1 && that.detailData.state != 2) {
          let title = that.detailData.state == 3 ? '已结束' : that.detailData.state == 4 ? '已取消' : '已销毁';
          return uni.showToast({
            title: `会议${title}`,
            icon: 'none'
          });
        } else {
          let url = that.detailData.parentsInviteUrl;
          let _sInfo = uni.getStorageSync('isQy');
          if (_sInfo) {
            uni.navigateTo({
              url: `/pages/index/web?url=${encodeURIComponent(url)}`
            });
          } else {
            that.$refs.popup.open('center');
            that.initData(that.meetingId);
          }
        }
      }
    },
    /*    openPopup() {
			  let url = this.detailData.parentsInviteUrl;
			  let _sInfo = uni.getSystemInfoSync();
			  if ((_sInfo.environment && 'wxwork' == _sInfo.environment) || wx.qy) {
			    uni.navigateTo({
			      url: `/pages/index/web?url=${encodeURIComponent(url)}`
			    });
			  } else {
			    this.$refs.popup.open('center');
			    this.initData(this.meetingId);
			  }
			}, */

    // 关闭弹窗
    closePopup() {
      console.log('pc');
      this.hasMeeting1 = false;
      this.hasMeeting2 = false;
      this.chooseMeetingRole = false;
      this.show = false;
    },

    // 选择角色
    chooseRole(id) {
      let that = this;
      if (id === 1) {
        if (that.detailData.studentStatus === 1) return;
        that.chooseMeetingRole = true;
        that.hasMeeting1 = true;
        that.hasMeeting2 = false;
        that.url = that.detailData.inviteUrl;
      } else {
        that.chooseMeetingRole = true;
        that.hasMeeting2 = true;
        that.hasMeeting1 = false;
        that.url = that.detailData.parentsInviteUrl;
        console.log(that.url);
      }
    },
    /*    chooseRole(id) {
			  let that = this;
			  that.chooseMeetingRole = true;
			  if (id === 1) {
			    if (that.detailData.studentStatus === 1) return;
			    that.hasMeeting1 = true;
			    that.hasMeeting2 = false;
			    that.url = that.detailData.inviteUrl;
			  } else {
			    that.hasMeeting2 = true;
			    that.hasMeeting1 = false;
			    that.url = that.detailData.parentsInviteUrl;
			    console.log(that.url);
			  }

			  console.log(id);
			}, */

    // 跳转外部页面
    goweburl() {
      if (this.chooseMeetingRole) {
        uni.navigateTo({
          url: `/pages/index/web?url=${encodeURIComponent(this.url)}`
        });
      }
    },
    // 关闭弹窗
    closePopup() {
      this.$refs.popup.close('center');
      (this.hasMeeting1 = false), (this.hasMeeting2 = false), (this.chooseMeetingRole = false);
    },

    // 日期  -转年月日
    getDateChinese(dt) {
      if (!dt) {
        return '';
      } else {
        const date = new Date(dt);
        // let date = new Date(dt.replace(/-/g, '/')); //ios适配
        let year = date.getFullYear();
        let month = date.getMonth() + 1;
        let day = date.getDate();
        month = month > 9 ? month : '0' + month;
        day = day > 9 ? day : '0' + day;
        return `${year}年${month}月${day}日`;
      }
    },
    getWeek(dt) {
      if (!dt) {
        return '';
      } else {
        const date = new Date(dt);
        // let date = new Date(dt.replace(/-/g, '/')); //ios适
        let week = date.getDay();
        let arr = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
        return `${arr[week]}`;
      }
    },
    getHours(dt) {
      if (!dt) {
        return '';
      } else {
        const date = new Date(dt);
        // let date = new Date(dt.replace(/-/g, '/')); //ios适
        let HH = date.getHours();
        let mm = date.getMinutes();
        HH = HH > 9 ? HH : '0' + HH;
        mm = mm > 9 ? mm : '0' + mm;
        return `${HH}:${mm}`;
      }
    },
    initData(id) {
      let that = this;
      that.$http
        .get('/deliver/web/experience/meetingDetail', {
          id
        })
        .then(({ data }) => {
          console.log(data, '==============');
          this.detailData = data.data;
        });
    }
  }
};
</script>

<style lang="scss">
.flex-start {
  display: flex;
  justify-content: start;
  align-items: center;
}

.container {
  padding-top: 200rpx;
  background-color: #fff;
}

.content {
  // padding-top: 284rpx;
  padding: 36rpx;
  background-color: #fff;
}

.title {
  font-size: 40rpx;
  font-weight: 600;
  color: #090909;
  font-family: AlibabaPuHuiTiM;
}

.time {
  display: flex;
  margin-top: 25rpx;
  margin-bottom: 46rpx;
}

.icon {
  min-width: 77rpx;
  height: 36rpx;
  padding: 0 10rpx;
  border-radius: 10rpx;
  font-size: 26rpx;
  margin-left: 30rpx;
  color: #fff;
}

.icon1 {
  background-color: #35a8f7;
}

.icon2 {
  background-color: #5cb561;
}

.icon3 {
  background-color: #c2c2c2;
}

.menber {
  margin-bottom: 30rpx;

  .name {
    margin-right: 41rpx;
    font-weight: 700;
  }
}

button {
  all: unset;
}

.btns {
  position: relative;
  height: 140rpx;
}

.shareb {
  position: absolute;
  left: 0;
  width: 379rpx;
  height: 120rpx;
  font-size: 26rpx;
}

.share {
  width: 379rpx;
  height: 120rpx;
  background: #f3f3f3;
  border-radius: 12rpx;
  // margin: 0 auto 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.entry-b {
  position: absolute;
  right: 0;
  width: 276rpx;
  height: 120rpx;
}

.entry-m {
  width: 276rpx;
  height: 120rpx;
  background: #ebf2fd;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  // margin-left: 22rpx;
  font-size: 26rpx;
}

.entry {
  width: 750rpx;
  height: 96rpx;
  background: #ffffff;
  margin-top: 40rpx;
  display: flex;
  align-items: center;
}

.shareb1 {
  width: 677rpx !important;
  height: 120rpx;
}

.share1 {
  width: 677rpx !important;
  height: 120rpx;
}

.role_popup {
  width: 600rpx;
  height: 460rpx;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 20rpx 44rpx;
}

.role_title {
  width: 100%;
  height: 90rpx;
  // margin-top: 10rpx;
  letter-spacing: 4rpx;
  font-size: 36rpx;
  color: #090909;
  font-weight: bolder;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.closeImg {
  width: 42rpx;
  height: 42rpx;
  position: absolute;
  right: 40rpx;
  top: 30rpx;
}

.relo_center {
  width: 100%;
  height: 300rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

// .onlyPc {
// 	font-size: 28rpx;
// 	line-height: 40rpx;
// }

.choose_item {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-evenly;
  width: 100%;
  height: 240rpx;
}

.student_status {
  width: 100%;
  height: 40px;
  border-radius: 10rpx;
  border: 1rpx solid #f2f2f2;
  font-size: 32rpx;
  color: #b0b0b0;
  background-color: #f7f7f7;
  font-weight: 500;
  display: flex;
  justify-content: center;
  align-items: center;
}

.student {
  width: 100%;
  height: 40px;
  border-radius: 10rpx;
  border: 1rpx solid #c8c8c8;
  font-size: 32rpx;
  color: #000;
  font-weight: 500;
  display: flex;
  justify-content: center;
  align-items: center;
}

.student_active {
  width: 100%;
  height: 40px;
  border-radius: 10rpx;
  border: 1rpx solid #2e896f;
  font-size: 32rpx;
  color: #2e896f;
  font-weight: 500;
  display: flex;
  justify-content: center;
  align-items: center;
}

.take_class {
  position: absolute;
  right: 50rpx;
  width: 120rpx;
  height: 58rpx;
  background: #35a8f7;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #fff;
}

.role_bottom {
  display: flex;
  justify-content: space-around;
  align-items: center;
  // margin-top: 40rpx;
}

.left_btn {
  width: 220rpx;
  height: 60rpx;
  color: #fff;
  background-color: #2e896f;
  border-radius: 6rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.left_btn_active {
  width: 220rpx;
  height: 60rpx;
  border: 1rpx solid #f2f2f2;
  color: #b0b0b0;
  background-color: #f7f7f7;
  border-radius: 6rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.right_btn {
  width: 220rpx;
  height: 60rpx;
  color: #2e896f;
  border: 1rpx solid #2e896f;
  border-radius: 6rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>

1
<template>
  <view :style="{ height: useHeight + 'rpx' }">
    <u-navbar leftText="" title=" " :placeholder="true" bgColor="#f3f8fc">
      <view class="u-nav-slot" slot="left">
        <!-- @click="goBack" -->
        <!-- <u-icon name="arrow-left" size="19"></u-icon> -->
      </view>
      <view class="" slot="center">
        <u-tabs :list="tabs" lineWidth="40" lineColor="#2E896F" :current="currentIndex" @change="changeIndex"></u-tabs>
      </view>
    </u-navbar>
    <view class="pd-30" v-if="Data.length == 0">
      <u-empty mode="list" icon="https://document.dxznjy.com/automation/1728442200000"></u-empty>
    </view>
    <view class="pd-30" v-else>
      <view class="item" v-for="(item, index) in Data" :key="index" @click.prevent="goDetail(item)">
        <block v-if="item.sort == 1">
          <view class="public pd-30">
            <view class="title mb-20">
              {{ item.msgTypeName }}
            </view>
            <view class="line mb-20"></view>
            <view class="content mb-20">
              {{ item.messageText }}
            </view>

            <view class="flex mb-20">
              <view class="" style="color: #999999; font-size: 28rpx">请将该文案复制至群公告</view>
              <view class="btn flex1" @click.stop="paste(item)">
                <!-- <image src="../static/images/copy.png" style="height: 30rpx" class="marginRight10" mode="heightFix"></image> -->
                <image src="https://document.dxznjy.com/manage/1720513535000" style="height: 30rpx" class="marginRight10" mode="heightFix"></image>
                <view class="">{{ currentIndex == 0 ? '复制' : '再次复制' }}</view>
              </view>
            </view>
            <view class="mb-20 time">创建时间：{{ item.createTime }}</view>
            <view class="mb-20 time">截止时间：{{ item.deadline }}</view>
          </view>
        </block>
        <block v-else>
          <view class="nomal pd-30">
            <view class="flex mb-20">
              <view class="title">
                {{ item.msgTypeName }}
              </view>
              <view class="btn flex1" @click.stop="send(item)">
                <image src="https://document.dxznjy.com/manage/1719901323000" style="height: 30rpx" class="marginRight10" mode="heightFix"></image>
                <view class="">{{ currentIndex == 0 ? '发送' : '再次发送' }}</view>
              </view>
            </view>

            <view class="mb-20 time">创建时间：{{ item.createTime }}</view>

            <view class="mb-20 time">截止时间：{{ item.deadline }}</view>
          </view>
        </block>
      </view>
    </view>
    <uni-popup ref="popup" type="center" catchtouchmove="true">
      <!-- 试课反馈详情 -->
      <view class="card" v-if="trialclass">
        <view v-if="learneShow">
          <view class="text-content flexs mt-30 mb-30">实际时间：</view>
          <dateTime ref="chanelTime" @getStart="changeStart" @getEnd="changeEnd"></dateTime>
          <view class="flex-c mt-40">
            <view class="common-cancel-btn" @click="popupCancel()">取消</view>
          </view>
        </view>
      </view>
      <view class="card" v-else>
        <view class="tickling f-32">反馈</view>
        <view v-if="learneShow">
          <view class="text-content flexs mt-30 mb-30">实际时间：</view>
          <dateTime ref="chanelTime" @getStart="changeStart" @getEnd="changeEnd"></dateTime>
          <view class="flex-c mt-40">
            <view class="common-cancel-btn" @click="popupCancel()">取消</view>
          </view>
        </view>
      </view>
    </uni-popup>
    <uni-popup ref="loadingPopup" type="center" :is-mask-click="false">
      <view class="bg-ff radius-15 t-c">
        <view class="loadingpadding">
          <image src="https://document.dxznjy.com/Assistant/loading.png" class="loadingImg"></image>
          <view class="mt-30">加载中...</view>
          <view>请耐心等候</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import { Debounce } from '@/utils/debounce.js';
  export default {
    data() {
      return {
        useHeight: 0,
        currentIndex: 0,
        tabs: [
          {
            name: '未发通知'
          },
          {
            name: '已发通知'
          }
        ],
        Data: [],
        sort: 0,
        //查看填写反馈
        backlist: '', // 获取反馈详情列表
        subject: '', // 课程列表详情
        timelist: {
          actualStart: '',
          actualEnd: ''
        },
        feedback: '', // 弹出层文本框输入的内容
        isFeedback: false, // 是否显示反馈详情确定按钮

        learneShow: false,

        trialclass: false, // 是否是试课反馈
        triallist: '', // 试课反馈详情
        Feedback: false, // 是否显示试课反馈详情确定按钮
        chatId: ''
      };
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          // console.log(res);
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 30;
        }
      });
    },
    onLoad(e) {
      let that = this;
      // that.chatId = e.chatId;
      // that.initData1(e.chatId);
      // #ifdef MP-WEIXIN
      wx.qy.checkSession({
        success: function () {
          that.initData();
        },
        fail: function () {
          uni.removeStorageSync('token');
          uni.removeStorageSync('scrm_token');
          uni.setStorageSync('userRole', 'notLogin');
          uni.setStorageSync('isLogin', false);
          that.$store.commit('setType', 'notLogin');
          uni.navigateTo({
            url: '/qyWechat/login_qywx'
          });
        }
      });
      // #endif
    },
    onShow() {
      let loginType = uni.getStorageSync('token');
      if (loginType) {
        this.initData();
      }
      // this.initData1(this.chatId);
    },
    onHide() {
      this.chatId = '';
    },
    onUnload() {
      this.chatId = '';
    },
    methods: {
      send: Debounce(function (item) {
        let that = this;
        that.sort = item.sort;
        // uni.showLoading({ title: '发送中' });
        that.$http
          .get('/deliver/teacher/notify/details', {
            id: item.id,
            isSend: 0,
            boundInfo: item.boundInfo,
            message: item.msgType
          })
          .then(({ data }) => {
            let response = data;
            // uni.hideLoading();
            if (response.success) {
              // 发文字text
              if (response.data.type == 1) {
                wx.qy.getContext({
                  success: () => {
                    //    var entry = data.entry, //返回进入小程序的入口类型
                    // var shareTicket = data.shareTicket;
                    wx.qy.sendChatMessage({
                      msgtype: 'text', //消息类型，必填
                      enterChat: false, //为true时表示发送完成之后顺便进入会话，仅移动端3.1.10及以上版本支持该字段
                      text: {
                        content: response.data.data //文本内容
                      },
                      image: {
                        mediaid: '' //图片的素材id
                      },
                      video: {
                        mediaid: '' //视频的素材id
                      },
                      file: {
                        mediaid: '' //文件的素材id
                      },
                      news: {
                        link: '', //H5消息页面url 必填
                        title: '', //H5消息标题
                        desc: '', //H5消息摘要
                        imgUrl: '' //H5消息封面图片URL
                      },
                      miniprogram: {
                        appid: '', //小程序的appid
                        title: '', //小程序消息的title
                        imgUrl: '', //小程序消息的封面图
                        page: '' //小程序消息打开后的路径，注意要以.html作为后缀，否则在微信端打开会提示找不到页面
                      },
                      success: () => {
                        that.$http
                          .get('/deliver/teacher/notify/details', {
                            id: item.id,
                            isSend: 1,
                            boundInfo: item.boundInfo,
                            message: item.msgType
                          })
                          .then(() => {
                            that.initData();
                            uni.showToast({
                              title: '发送成功'
                            });
                          });
                      },
                      fail: (err) => {
                        console.log(err, '=============================');
                      }
                    });
                  },
                  fail: (err) => {
                    console.log(err, '111111111111111');
                  }
                });
              } else if (response.data.type == 2) {
                let imgUrl = item.messagePicture ? item.messagePicture : 'https://document.dxznjy.com/manage/1716892278000';
                wx.qy.getContext({
                  success: () => {
                    wx.qy.sendChatMessage({
                      msgtype: 'miniprogram', //消息类型，必填
                      enterChat: false, //为true时表示发送完成之后顺便进入会话，仅移动端3.1.10及以上版本支持该字段
                      text: {
                        content: '' //文本内容
                      },
                      image: {
                        mediaid: '' //图片的素材id
                      },
                      video: {
                        mediaid: '' //视频的素材id
                      },
                      file: {
                        mediaid: '' //文件的素材id
                      },
                      news: {
                        link: '', //H5消息页面url 必填
                        title: '', //H5消息标题
                        desc: '', //H5消息摘要
                        imgUrl: '' //H5消息封面图片URL
                      },
                      miniprogram: {
                        appid: that.$config.WXAppId, //小程序的appid
                        title: item.msgTypeName, //小程序消息的title
                        // imgUrl: 'https://document.dxznjy.com/manage/1715757128000',   //小程序消息的封面图
                        imgUrl: imgUrl, //小程序消息的封面图
                        page: response.data.data + '&isSend=0' //小程序消息打开后的路径，注意要以.html作为后缀，否则在微信端打开会提示找不到页面
                      },
                      success: () => {
                        console.log('发送成功231123123123');
                        that.$http
                          .get('/deliver/teacher/notify/details', {
                            id: item.id,
                            isSend: 1,
                            boundInfo: item.boundInfo,
                            message: item.msgType
                          })
                          .then(() => {
                            that.initData();
                            uni.showToast({
                              title: '发送成功'
                            });
                          })
                          .catch((err) => {
                            uni.showToast({
                              title: '发送失败',
                              icon: 'none'
                            });
                          });
                      },
                      fail: (err) => {
                        console.log(err, '=============================');
                      }
                    });
                  },
                  fail: (err) => {
                    console.log(err, '111111111111111');
                  }
                });
              } else {
                // let item1 = JSON.parse(response.data.data);/
                let json = response.data.data;
                json = json.replace(/\"id":(\d+)/g, '"id": "$1"');
                let item1 = JSON.parse(json);
                let item2 = item;
                that.open1(item1, item2);
              }
              // 发小程序miniprogram
            } else {
              console.log(response, '====================');
              // uni.hideLoading();
              uni.showToast({
                title: response.message,
                icon: 'none'
              });
            }
          })
          .catch((err) => {
            // uni.hideLoading();
            console.log(err, '==================');
          });
      }),
      goDetail: Debounce(function (item) {
        console.log(item, '111111111111111111111111111111111111');
        let that = this;
        that.sort = item.sort;
        uni.showLoading({ title: '加载中' });
        that.$http
          .get('/deliver/teacher/notify/details', {
            id: item.id,
            isSend: 0,
            boundInfo: item.boundInfo,
            message: item.msgType
          })
          .then(({ data }) => {
            console.log(data, '222222222222222222222222222222222222');
            let response = data;
            uni.hideLoading();
            if (response.success) {
              if (response.data.type == 1) {
                uni.navigateTo({
                  url: `/qyWechat/noticeDetail?item=${encodeURIComponent(JSON.stringify(item))}&content=${response.data.data}&sort=${this.sort}`
                });
              } else if (response.data.type == 2) {
                console.log(response.data.data, '小程序12333333333333333333333333');
                uni.navigateTo({
                  // url: `${response.data.data}&isSend=1&item=${encodeURIComponent(JSON.stringify(item))}`
                  url: response.data.nvUrl + '&isSend=1' + '&item=' + encodeURIComponent(JSON.stringify(item))
                });
              } else {
                let json = response.data.data;
                json = json.replace(/\"id":(\d+)/g, '"id": "$1"');
                let item1 = JSON.parse(json);
                let item2 = encodeURIComponent(JSON.stringify(item));
                console.log(item1, '444444444444444444444444444444444', item.id, '=================');
                that.open(item1, item2);
                // uni.navigateTo({
                //   url: '/qyWechat/copyIndex?item=' + encodeURIComponent(JSON.stringify(response.data.data))
                // });
              }
            } else {
              uni.showToast({
                title: response.message,
                icon: 'none'
              });
              // var reg = RegExp(/请先提交词汇量检测/);
              // if (reg.exec(response.message)) {
              //   uni.showToast({
              //     title: '请先提交词汇量检测',
              //     icon: 'none'
              //   });
              // }
            }
          })
          .catch((err) => {
            uni.hideLoading();
            console.log(err, '==================');
            // uni.showToast({
            //   title:''
            // })
          });
      }),
      open(item, item2) {
        this.subject = item;
        console.log(this.subject, '55555555555555555555555555555555555', item.id, '=================');
        this.timelist = {
          actualStart: '',
          actualEnd: ''
        };
        if (this.subject.experience == false) {
          this.trialclass = false;
          this.backData(item2); // experience = false 是学习反馈
        } else {
          this.trialclass = true;
          this.trialData(item2); // experience = true 是试课反馈
        }
      },
      open1(item, item2) {
        this.subject = item;
        this.timelist = {
          actualStart: '',
          actualEnd: ''
        };
        if (this.subject.experience == false) {
          this.trialclass = false;
          this.backData1(item2); // experience = false 是学习反馈
        } else {
          this.trialclass = true;
          this.trialData1(item2); // experience = true 是试课反馈
        }
      },
      // 获取反馈详情
      async backData(item) {
        let that = this;
        that.$refs.loadingPopup.open();
        try {
          let res = await uni.$http.get('/deliver/app/teacher/getFeedbackInfo', {
            id: this.subject.id,
            actualStart: this.timelist.actualStart,
            actualEnd: this.timelist.actualEnd,
            type: 1
          });
          console.log(res, 'kkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkk');
          that.$refs.loadingPopup.close();
          if (res.data.code == 70001) {
            that.learneShow = true;
            that.$refs.popup.open('center');
          }
          if (res.data.success) {
            that.backlist = res.data.data;
            that.learneShow = false;
            that.isFeedback = that.backlist.feedback === '' || that.backlist.feedback == null || that.backlist.feedback == undefined;
            that.feedback = that.backlist.feedback;
            that.timelist.actualStart = that.backlist.actualStart;
            that.timelist.actualEnd = that.backlist.actualEnd;
            //跳转页面
            that.jumpPage(item);
          }
          that.$refs.chanelTime.setLists(that.timelist);
        } catch (e) {
          that.$refs.loadingPopup.close();
        }
      },
      // 试课反馈详情
      async trialData(item) {
        let that = this;
        that.$refs.loadingPopup.open();
        try {
          let res = await uni.$http.post(`/deliver/app/teacher/getExperienceInfo/${that.subject.id}?actualStart=${that.timelist.actualStart}&actualEnd=${that.timelist.actualEnd}`);
          console.log(res, 'eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee');
          that.$refs.loadingPopup.close();
          if (res.data.code == 70001) {
            that.learneShow = true;
            await that.$refs.popup.open('center');
            that.$refs.chanelTime.setLists(that.timelist);
            return;
          }
          that.triallist = res.data.data;
          if (res.data.success) {
            that.learneShow = false;
            that.Feedback = that.triallist.feedback === '' || that.triallist.feedback == null || that.triallist.feedback == undefined;
            that.feedback = that.triallist.feedback;
            that.Intention = that.triallist.studyIntention;
            that.timelist.actualStart = that.triallist.actualStart;
            that.timelist.actualEnd = that.triallist.actualEnd;
            //跳转页面
            that.jumpPage(item);
          }
        } catch {
          that.$refs.loadingPopup.close();
        }
      },
      // 获取反馈详情
      async backData1(item) {
        let that = this;
        that.$refs.loadingPopup.open();
        try {
          let res = await uni.$http.get('/deliver/app/teacher/getFeedbackInfo', {
            id: this.subject.id,
            actualStart: this.timelist.actualStart,
            actualEnd: this.timelist.actualEnd,
            type: 1
          });
          console.log(res, 'kkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkk');
          that.$refs.loadingPopup.close();
          if (res.data.code == 70001) {
            that.learneShow = true;
            that.$refs.popup.open('center');
          }
          if (res.data.success) {
            that.backlist = res.data.data;
            that.learneShow = false;
            that.isFeedback = that.backlist.feedback === '' || that.backlist.feedback == null || that.backlist.feedback == undefined;
            that.feedback = that.backlist.feedback;
            that.timelist.actualStart = that.backlist.actualStart;
            that.timelist.actualEnd = that.backlist.actualEnd;
            //跳转页面
            that.sendPage(item);
          }
          that.$refs.chanelTime.setLists(that.timelist);
        } catch (e) {
          that.$refs.loadingPopup.close();
        }
      },
      // 试课反馈详情
      async trialData1(item) {
        let that = this;
        that.$refs.loadingPopup.open();
        try {
          let res = await uni.$http.post(`/deliver/app/teacher/getExperienceInfo/${that.subject.id}?actualStart=${that.timelist.actualStart}&actualEnd=${that.timelist.actualEnd}`);
          console.log(res, 'eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee');
          that.$refs.loadingPopup.close();
          if (res.data.code == 70001) {
            that.learneShow = true;
            await that.$refs.popup.open('center');
            that.$refs.chanelTime.setLists(that.timelist);
            return;
          }
          that.triallist = res.data.data;
          if (res.data.success) {
            that.learneShow = false;
            that.Feedback = that.triallist.feedback === '' || that.triallist.feedback == null || that.triallist.feedback == undefined;
            that.feedback = that.triallist.feedback;
            that.Intention = that.triallist.studyIntention;
            that.timelist.actualStart = that.triallist.actualStart;
            that.timelist.actualEnd = that.triallist.actualEnd;
            //跳转页面
            that.sendPage(item);
          }
        } catch {
          that.$refs.loadingPopup.close();
        }
      },
      sendPage(item) {
        let that = this;
        let sendData = {};
        this.$refs.popup.close();
        sendData.trialclass = this.trialclass;
        sendData.isFeedback = this.isFeedback;
        sendData.triallist = this.triallist;
        sendData.subject = this.subject;
        sendData.backlist = this.backlist;
        sendData.isSend = true;
        wx.qy.getContext({
          success: () => {
            wx.qy.sendChatMessage({
              msgtype: 'miniprogram', //消息类型，必填
              enterChat: false, //为true时表示发送完成之后顺便进入会话，仅移动端3.1.10及以上版本支持该字段
              text: {
                content: '' //文本内容
              },
              image: {
                mediaid: '' //图片的素材id
              },
              video: {
                mediaid: '' //视频的素材id
              },
              file: {
                mediaid: '' //文件的素材id
              },
              news: {
                link: '', //H5消息页面url 必填
                title: '', //H5消息标题
                desc: '', //H5消息摘要
                imgUrl: '' //H5消息封面图片URL
              },
              miniprogram: {
                appid: that.$config.WXAppId, //小程序的appid
                title: item.msgTypeName, //小程序消息的title
                imgUrl: item.messagePicture, //小程序消息的封面图
                // page: `/pages/index/study_feedback?sendData=${encodeURIComponent(JSON.stringify(sendData))}` //小程序消息打开后的路径，注意要以.html作为后缀，否则在微信端打开会提示找不到页面
                page: `/qyWechat/feedBack.html?sendData=${encodeURIComponent(JSON.stringify(sendData))}` //小程序消息打开后的路径，注意要以.html作为后缀，否则在微信端打开会提示找不到页面
              },
              success: () => {
                console.log('发送成功231123123123');
                that.$http
                  .get('/deliver/teacher/notify/details', {
                    id: item.id,
                    isSend: 1,
                    boundInfo: item.boundInfo,
                    message: item.msgType
                  })
                  .then(() => {
                    that.initData();
                    uni.showToast({
                      title: '发送成功'
                    });
                  })
                  .catch((err) => {
                    uni.showToast({
                      title: '发送失败',
                      icon: 'none'
                    });
                  });
              },
              fail: (err) => {
                console.log(err, '=============================');
              }
            });
          },
          fail: (err) => {
            console.log(err, '111111111111111');
          }
        });
      },
      //跳转页面
      jumpPage(item) {
        let sendData = {};
        this.$refs.popup.close();
        sendData.trialclass = this.trialclass;
        sendData.isFeedback = this.isFeedback;
        sendData.triallist = this.triallist;
        sendData.subject = this.subject;
        sendData.backlist = this.backlist;
        sendData.isSend = true;
        uni.navigateTo({
          // url: '/pages/index/study_feedback?sendData=' + encodeURIComponent(JSON.stringify(sendData))
          url: `/qyWechat/feedBack?sendData=${encodeURIComponent(JSON.stringify(sendData))}&item=${item}`
        });
      },
      goBack() {
        uni.navigateBack();
      },
      initData1(chatId) {
        let that = this;
        uni.showLoading({ title: '加载中' });
        this.$http
          .get('/deliver/teacher/notify/findMessage', {
            chatId: chatId,
            isSend: that.currentIndex,
            mobile: '15034740076'
            // mobile: uni.getStorageSync('tel')
          })
          .then(({ data }) => {
            console.log(data, '========================');
            if (data.success) {
              this.Data = data.data.data;
              this.Data.forEach((i) => {
                if (i.sort == 1) {
                  that.$http
                    .get('/deliver/teacher/notify/details', {
                      id: i.id,
                      isSend: 0,
                      boundInfo: i.boundInfo,
                      message: i.msgType
                    })
                    .then((res1) => {
                      i.messageText = res1.data.data.data;
                    });
                }
              });
              uni.hideLoading();
            }
          })
          .catch((err) => {
            console.log(err, '========================');
            uni.hideLoading();
          });
      },
      initData() {
        let that = this;
        let mobile = uni.getStorageSync('tel');
        uni.showLoading({ title: '加载中' });
        if (that.chatId) {
          that.$http
            .get('/deliver/teacher/notify/findMessage', {
              chatId: that.chatId,
              isSend: that.currentIndex,
              mobile: mobile
            })
            .then(({ data }) => {
              console.log(data, '========================');
              if (data.success) {
                that.Data = data.data.data;
                that.Data.forEach((i) => {
                  if (i.sort == 1) {
                    that.$http
                      .get('/deliver/teacher/notify/details', {
                        id: i.id,
                        isSend: 0,
                        boundInfo: i.boundInfo,
                        message: i.msgType
                      })
                      .then((res1) => {
                        // console.log(data.data.data, '==============');
                        i.messageText = res1.data.data.data;
                        // console.log(i);
                        // console.log(that.Data);
                      });
                  }
                });
                uni.hideLoading();
              }
            })
            .catch((err) => {
              console.log(err, '========================');
              uni.hideLoading();
            });
        } else {
          wx.qy.getCurExternalChat({
            success: (res) => {
              let chatId = res.chatId; //返回当前外部群的群聊ID
              that.chatId = res.chatId;
              that.$http
                .get('/deliver/teacher/notify/findMessage', {
                  chatId: chatId,
                  isSend: that.currentIndex,
                  mobile: mobile
                })
                .then(({ data }) => {
                  console.log(data, '========================');
                  if (data.success) {
                    that.Data = data.data.data;
                    that.Data.forEach((i) => {
                      if (i.sort == 1) {
                        that.$http
                          .get('/deliver/teacher/notify/details', {
                            id: i.id,
                            isSend: 0,
                            boundInfo: i.boundInfo,
                            message: i.msgType
                          })
                          .then((res1) => {
                            // console.log(data.data.data, '==============');
                            i.messageText = res1.data.data.data;
                            // console.log(i);
                            // console.log(that.Data);
                          });
                      }
                    });
                    uni.hideLoading();
                  }
                })
                .catch((err) => {
                  console.log(err, '========================');
                  uni.hideLoading();
                });
            },
            fail: (err) => {
              uni.hideLoading();
              console.log(err, 'errrrrrrrrrrrrrrrrrrrrrrrrrrrr');
              // uni.showToast({
              //   title: err.errMsg,
              //   icon: 'none'
              // });
            }
          });
        }
      },
      changeIndex(e) {
        this.currentIndex = e.index;
        this.initData();
        // console.log(this.currentIndex);
        // this.initData1(this.chatId);
      },
      // 复制
      paste: Debounce(function (item) {
        let that = this;
        uni.setClipboardData({
          data: item.messageText,
          success: (res) => {
            console.log(res);
            that.$http
              .get('/deliver/teacher/notify/details', {
                id: item.id,
                isSend: 1,
                boundInfo: item.boundInfo,
                message: item.msgType
              })
              .then(() => {
                uni.showToast({
                  title: '内容已复制至剪贴板'
                });
                setTimeout(() => {
                  that.initData();
                }, 500);
              });
          }
        });
      })
    }
  };
</script>

<style lang="scss">
  .u-tabs__wrapper__nav__item {
    flex: 1 !important;
  }
  page {
    background-color: #f3f8fc;
  }
  .flex1 {
    display: flex;
    // justify-content: center;
    align-items: center;
  }
  .line {
    width: 630rpx;
    // height: 1rpx;
    border: 1rpx solid #eeeeee;
  }
  .pd-30 {
    padding: 30rpx;
  }
  .time {
    color: #666666;
    font-size: 28rpx;
  }
  .mb-20 {
    margin-bottom: 20rpx;
  }
  .item {
    width: 690rpx;
    // height: 934rpx;
    background: #ffffff;
    border-radius: 14rpx;
    margin-bottom: 30rpx;
  }
  .public {
    // width: 690rpx;
    // height: 934rpx;
    background: #ffffff;
    border-radius: 14rpx;
  }
  .nomal {
    // width: 690rpx;
    // height: 218rpx;
    background: #ffffff;
    border-radius: 14rpx;
  }
  .title {
    font-size: 32rpx;
    color: #000;
  }
  .content {
    font-size: 30rpx;
    color: #666666;
    line-height: 42rpx;
  }
  .btn {
    color: #1d755c;
    font-size: 32rpx;
  }
  .loadingImg {
    width: 120rpx;
    height: 120rpx;
  }

  .loadingpadding {
    padding: 85rpx 250rpx;
  }
</style>

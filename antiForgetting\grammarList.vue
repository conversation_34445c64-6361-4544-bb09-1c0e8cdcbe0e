<template>
  <view>
    <!-- <view class="header2">
			<uni-icons v-if="!isShare" type="arrowleft" class="backIcon" size="20" color="#000"
				@click="back()"></uni-icons>
		</view> -->
    <view class="plr-30 pb-30">
      <view class="list_box mt-30">
        <view>
          <uv-list v-if="phaseColorListKnow.length !== 0">
            <uv-list-item>
              <view class="listBorder" v-for="(item, index) in phaseColorListKnow" :key="index" @tap="gotoKnowledge(item)">
                <view class="flex-a-c">
                  <text class="p-30">{{ item.knowledgeName }}</text>
                  <span class="boxl-50 lh-30 b-g f-18 t-c radius-6" :style="item.phaseColor">{{ item.phase }}</span>
                </view>
                <view class="flex-a-c">
                  <u-icon name="arrow-right"></u-icon>
                </view>
              </view>
            </uv-list-item>
          </uv-list>
          <view v-else>
            <u-empty mode="data"></u-empty>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
  export default {
    data() {
      return {
        studentCode: '',
        pageNum: 1,
        pageSize: 15,
        total: '',
        // 列表数据
        listKnow: []
      };
    },
    onLoad(options) {
      this.studentCode = options.studentCode;
      this.getKonwList();
    },
    onShow() {},
    onReachBottom() {
      let allTotal = this.pageNum * this.pageSize;
      console.log('已加载全部数据');
      if (allTotal < this.total) {
        // 当前条数小于总条数，则增加请求页数
        this.pageNum++;
        this.getKonwList(); // 调用加载数据方法
      } else {
        uni.showToast({
          title: '已加载全部数据',
          icon: 'none'
        });
      }
    },
    computed: {
      // ... 其他计算属性
      phaseColorListKnow() {
        return this.listKnow.map((item) => ({
          ...item,
          phaseColor: this.getPhaseColorClass(item.phase)
        }));
      }
    },
    methods: {
      async getKonwList() {
        try {
          let res = await uni.$http.get('/dyf/wap/applet/todayReview', {
            studentCode: this.studentCode,
            pageNum: this.pageNum,
            pageSize: this.pageSize
          });
          this.total = res.data.data.totalItems;
          if (this.pageNum === 1) {
            // 如果是第一页，清空列表并添加新数据
            this.listKnow = res.data.data.data;
          } else {
            // 否则追加新数据
            this.listKnow = [...this.listKnow, ...res.data.data.data];
          }
        } catch (error) {
          console.error('获取数据失败:', error);
        }
      },

      // 定义一个方法来根据阶段值返回颜色类名
      getPhaseColorClass(phase) {
        console.log('phase:', phase);
        if (phase.includes('小学')) {
          return 'color: #81e2af;'; // 绿色
        } else if (phase.includes('初中')) {
          return 'color: #ffd593;'; // 橘色
        } else if (phase.includes('高中')) {
          return 'color: #6de2ff;'; // 蓝色
        }
        return ''; // 默认颜色
      },
      gotoKnowledge(item) {
        if (!uni.getStorageSync('token')) {
          this.goToLogin();
          return;
        }
        uni.redirectTo({
          url: `/antiForgetting/handout?title=${item.knowledgeName}&id=${item.id}&studentCode=${this.studentCode}&knowledgeId=${item.knowledgeId}`
        });
      },
      back() {
        uni.navigateBack({
          delta: 1
        });
      }
    }
  };
</script>

<style>
  .header2 {
    height: 172rpx;
    background-color: #fff;
  }
  .list_box {
    border-radius: 14upx;
    background: #ffffff;
    box-shadow: 0rpx 4rpx 16rpx 0rpx #f5f7f9;
    padding: 30upx;
    font-size: 26upx;
    color: rgba(102, 102, 102, 1);
    position: relative;
  }

  .pick {
    flex: 1;
    height: 60upx;
    line-height: 60upx;
    border: 1px solid #c8c8c8;
    overflow: visible;
    border-radius: 20upx;
    text-align: center;
    padding: 0 30upx;
    position: relative;
    transform: scale(0.995); /* 解决ios上圆角失效 */
  }

  .pick_right {
    position: absolute;
    right: 20upx;
    top: -5upx;
  }

  /* 弹层宽度 */
  .uv-dp__container {
    height: 150rpx;
    width: 690rpx;
    margin-left: 30rpx;
  }

  button {
    width: 40% !important;
  }

  .dropButton {
    display: flex;
    justify-content: space-around;
    align-items: center;
  }

  .tagItem {
    display: flex;
    margin: 20rpx 30rpx;
    flex-wrap: wrap;
  }

  .listBorder {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-flow: row wrap;
    border-bottom: 2rpx dashed #efefef;
  }

  .badge {
    width: 50rpx;
    height: 50rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    background-color: #edeced;
    font-size: 18rpx;
  }
  .backIcon {
    position: absolute;
    left: 30rpx;
    top: 90rpx;
  }
</style>

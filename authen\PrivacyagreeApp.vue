<template>
  <view>
    <web-view :webview-styles="webviewStyles" :src="tempUrl"></web-view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        tempUrl: '',
        webviewStyles: {
          progress: {
            color: '#FF3333'
          }
        }
      };
    },
    onLoad: function () {
      var url = 'https://document.dxznjy.com/applet/agreeon/djj_privacypolicy.html';
      //#ifdef APP-PLUS
      this.tempUrl = decodeURIComponent(url);
      //#endif
      //#ifdef MP-WEIXIN
      this.tempUrl = decodeURIComponent(url);
      //#endif
      // #ifdef H5
      this.tempUrl = url;
      // #endif
    },
    methods: {}
  };
</script>

<style></style>

<template>
  <view>
    <view class="nav-title">
      <view class="status_bar">
        <uni-icons @click="Back" type="left" color="#a8a8a8" size="24" class="icon_img"></uni-icons>
        复习反馈
      </view>
    </view>

    <view class="container">
      <view class="card">
        <view class="white-content">
          <view class="text-content">日期：{{ backlist.dateTime }}</view>
          <view class="text-content">姓名：{{ backlist.studentName }}</view>
          <view class="text-content">年级：{{ backlist.gradeName }}</view>

          <!-- 实际时间 -->
          <view class="text-content reality">
            实际时间：
            <view>
              <view class="begin" @click="isStartshow = true">{{ timelist.actualStart }}</view>
              <w-picker
                v-if="isFeedback"
                :visible.sync="isStartshow"
                mode="date"
                :startYear="new Date(date).getFullYear()"
                endYear="2040"
                :value="timelist.actualStart"
                fields="minute"
                @confirm="onStartConfirm"
                :disabled-after="false"
                ref="dateTimeFlag"
              ></w-picker>
            </view>
            至
            <view>
              <view class="finish" @click="isEndShow = true">{{ timelist.actualEnd }}</view>
              <w-picker
                v-if="isFeedback"
                :visible.sync="isEndShow"
                mode="date"
                :startYear="new Date(date).getFullYear()"
                endYear="2040"
                :value="timelist.actualEnd"
                fields="minute"
                @confirm="onEndConfirm"
                :disabled-after="false"
                ref="dateTimeFlag"
              ></w-picker>
            </view>
          </view>
          <view class="text-content">复习词汇：{{ backlist.reviewWords }}</view>
          <view class="text-content">遗忘词汇：{{ backlist.forgetWords }}</view>
          <view class="text-content">遗忘率：{{ backlist.forgetRate }}%</view>
          <view class="marginTop10" style="max-height: 340upx">
            <u--textarea
              v-if="isFeedback"
              :disabled="!isFeedback"
              class="feedback"
              v-model="feedback"
              count
              :value="feedback"
              placeholder="请输入"
              placeholderStyle="color:#b0b0b6"
              maxlength="200"
              height="135"
            ></u--textarea>
            <view v-else class="font26 color_black33 feedbackBoxNew marginBottom10">
              {{ feedback }}
            </view>
          </view>
        </view>

        <view class="button-sp-area marginTop40">
          <view v-if="isFeedback" class="mini-btn background_green_two" type="default" size="mini" @click="close(id)">确定</view>
          <view v-else class="mini-btn background_green_two" type="default" size="mini" @click="shareJump">分享</view>
        </view>
      </view>
    </view>

    <uni-popup ref="loadingPopup" type="center" :is-mask-click="false">
      <view class="bg-ff radius-15 t-c">
        <view class="loadingpadding">
          <image src="https://document.dxznjy.com/Assistant/loading.png" class="loadingImg"></image>
          <view class="mt-30">加载中...</view>
          <view>请耐心等候</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>
<script>
import dayjs from 'dayjs';
import dateTime from '@/components/lanxiujuan-dyDateTime/lanxiujuan-dyDateTime.vue';
export default {
  name: 'ReviseDetails',
  components: {
    dateTime
  },
  data() {
    return {
      // 实际时间
      isStartshow: false,
      isEndShow: false,
      backlist: '', // 获取反馈详情列表
      id: '',
      timelist: {
        actualStart: '',
        actualEnd: ''
      },
      isFeedback: false, // 是否显示反馈详情确定按钮
      feedback: '', // 弹出层文本框输入的内容
      Intention: '', // 学习意愿
      dates: ''
    };
  },

  onLoad(e) {
    this.dates = e.dates;
    let sendData = JSON.parse(decodeURIComponent(e.sendData));
    if (sendData != null && sendData != undefined) {
      this.isFeedback = sendData.isFeedback;
      this.backlist = sendData.backlist;
      this.id = sendData.id;
      if (this.backlist != null && this.backlist != undefined) {
        this.feedback = this.backlist.feedback;
        this.isFeedback = this.backlist.feedback === '' || this.backlist.feedback == null || this.backlist.feedback == undefined;
        this.timelist.actualStart = this.backlist.actualStart;
        this.timelist.actualEnd = this.backlist.actualEnd;
      }
    }
  },
  onReady() {},

  methods: {
    // 实际时间弹窗确定
    async onStartConfirm(val) {
      let time = await dayjs(val.value).format('YYYY-MM-DD HH:mm');
      console.log('confirm', time);
      this.timelist.actualStart = time;
      this.show = false;
      if (this.timelist.actualStart != '' && this.timelist.actualEnd != '') {
        this.backData();
      }
    },

    // 实际时间弹窗确定2
    async onEndConfirm(val) {
      let time = await dayjs(val.value).format('YYYY-MM-DD HH:mm');
      this.timelist.actualEnd = time;
      this.isShow = false;
      if (this.timelist.actualStart != '' && this.timelist.actualEnd != '') {
        this.backData();
      }
    },

    // 确定按钮（关闭弹框）
    close() {
      this.addbackData();
    },

    // 新增学习反馈
    async addbackData(e) {
      let that = this;
      that.$refs.loadingPopup.open();
      let data = {
        actualStart: that.timelist.actualStart,
        actualEnd: that.timelist.actualEnd,
        feedBack: that.feedback,
        id: that.id,
        type: 2,
        studyRate: 0
      };
      let postUrl = `/deliver/app/teacher/addFeedback?actualStart=${that.timelist.actualStart}
      &actualEnd=${that.timelist.actualEnd}&feedBack=${encodeURI(that.feedback)}&id=${that.id}&type=2&studyRate=0`;
      try {
        let res = await uni.$http.post(postUrl);
        // let res = await that.$http.post('/deliver/app/teacher/addFeedback', data);
        that.$refs.loadingPopup.close();
        // console.log(res);
        if (res.data.success) {
          uni.showToast({
            title: '反馈成功',
            duration: 500
          });
          that.backlist.feedback = that.feedback;
          // that.feedback = '';
          // that.timelist.actualStart = "";
          // that.timelist.actualEnd = "";
          //显示分享按钮
          that.isFeedback = false;
          // that.$refs.popup.close()
        }
      } catch {
        that.$refs.loadingPopup.close();
      }
    },
    //分享
    shareJump() {
      let sendData = {};
      sendData.isStudy = false;
      sendData.detailsData = this.backlist;
      let newstr=''
      // #ifdef MP-WEIXIN
      newstr =JSON.stringify(sendData)
      // #endif
      // #ifdef APP-PLUS
      newstr =JSON.stringify(sendData).replace(/%/g,'%25');
      // #endif
      uni.navigateTo({
        url: '/share/share_feedback?sendData=' + encodeURIComponent(newstr)
      });
    },
    Back() {
      uni.$emit('onCalendarRefresh', this.dates);
      uni.navigateBack();
    },

    async backData() {
      let that = this;
      that.$refs.loadingPopup.open();
      try {
        let res = await uni.$http.get('/deliver/app/teacher/getFeedbackInfo', {
          id: this.id,
          actualStart: this.timelist.actualStart,
          actualEnd: this.timelist.actualEnd,
          type: 2
        });
        that.$refs.loadingPopup.close();
        if (res.data.code == 70001) {
          return;
        }
        if (res.data.success) {
          that.backlist = res.data.data;
          that.isFeedback = that.backlist.feedback === '' || that.backlist.feedback == null || that.backlist.feedback == undefined;
          that.feedback = that.backlist.feedback;
          that.timelist.actualStart = that.backlist.actualStart;
          that.timelist.actualEnd = that.backlist.actualEnd;
        }
      } catch {
        that.$refs.loadingPopup.close();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.nav-title {
  background-color: #fff;
  position: fixed;
  height: 150rpx;
  width: 100%;
}

.status_bar {
  text-align: center;
  font-size: 40rpx;
  color: #000;
  line-height: 220rpx;
}

.icon_img {
  position: absolute;
  left: 10rpx;
}
.container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 170rpx;
}

.page {
  width: 100%;
}

.details {
  display: flex;
  font-size: 25rpx;
  padding: 0 30rpx 50rpx;
  margin-top: 20rpx;

  /deep/.u-row {
    margin-bottom: 20rpx !important;
  }

  /deep/.u-demo-block__content {
    width: 98% !important;
    margin-left: 5rpx;
  }

  /deep/.u-col {
    padding: 0 !important;
  }
}

.left {
  font-size: 26rpx;
  align-items: center;
}

.right {
  position: relative;
  padding: 20rpx 20rpx 40rpx;
  border-radius: 30rpx;
  background-color: #fff;
}

.pic {
  position: absolute;
  left: 300rpx;
  top: 30rpx;
  height: 50rpx;
  width: 50rpx;
}

.rightbtn {
  float: right;
  position: absolute;
  top: 40rpx;
  right: 40rpx;
}

.icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 10rpx;
}

.right-content {
  margin-top: 10rpx;
}

.tickling {
  text-align: center;
  font-weight: 700;
  font-size: 30rpx;
}

.text-content {
  font-size: 28rpx;
  color: #000;
  margin-bottom: 30rpx;
  display: flex;
}

/deep/.u-textarea__field {
  background-color: #f7f7f7;
  border: 1rpx solid #d9d9d9;
  padding: 10rpx;
}

/deep/.u-textarea--disabled {
  background-color: #fff !important;
}

.button-sp-area {
  display: flex;
}

.cancel-btn {
  font-size: 24rpx;
  width: 120rpx;
  height: 55rpx;
  margin-bottom: 30rpx;
}

.mini-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 45rpx;
  font-size: 30rpx;
  color: #fff;
  background-color: #1a8eff;
  width: 650rpx;
  height: 90rpx;
  line-height: 90rpx;
}

.border-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #1a8eff;
  background-color: #fff;
  border: 1.4rpx solid #1a8eff;
  width: 160rpx;
  height: 60rpx;
  transform: scale(0.995); /* 解决ios上圆角失效 */
}

// 弹框的样式设置
/deep/.card {
  width: 670rpx;

  .white-content {
    background-color: #fff;
    border-radius: 14rpx;
    padding: 40rpx 30rpx 40rpx 30rpx;
    // overflow-y:auto;
  }

  .reality {
    display: flex;
    align-items: center;
    margin-top: 10rpx;
    line-height: 40rpx;
    font-size: 26rpx;
  }

  .begin {
    border: 1px solid #999;
    border-radius: 10rpx;
    height: 40rpx;
    line-height: 40rpx;
    width: 220rpx;
    text-align: center;
    font-size: 24rpx;
    overflow: visible;
    transform: scale(0.995); /* 解决ios上圆角失效 */
  }

  .finish {
    border: 1px solid #999;
    overflow: visible;
    border-radius: 10rpx;
    height: 40rpx;
    line-height: 40rpx;
    width: 224rpx;
    text-align: center;
    font-size: 24rpx;
    transform: scale(0.995); /* 解决ios上圆角失效 */
  }
}

.flexs {
  display: flex;
  align-items: center;
}

.loadingImg {
  width: 120rpx;
  height: 120rpx;
}

.loadingpadding {
  padding: 85rpx 250rpx;
}
</style>

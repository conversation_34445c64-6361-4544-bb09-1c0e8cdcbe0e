import {
    http
} from '@/utils/luch-request/index.js' // 全局挂载引入
import CryptoJS from 'crypto-js';
const playVoice = (word, isWord, timbre,pronunciationType,playType,studentCode) => {
    const innerAudioContext = uni.createInnerAudioContext();
    // #ifdef MP-WEIXIN
    innerAudioContext.obeyMuteSwitch = false;
    // #endif
    if (isWord) { //不是单词的音频
        var linkUrl = 'https://document.dxznjy.com/word/local/dflasfa' + word;
        innerAudioContext.src = linkUrl;
        innerAudioContext.play();
        return;
    }
    console.log(http);
    // 单词播放
	let data = studentCode+ 'L0anhf';
	let sg = CryptoJS.SHA1(data).toString();
    http.get('/znyy/app/query/word/voice', {
        word: word,
        v: playType,
        rp: pronunciationType == 1 ? true : false,
        sex: timbre,
        sg: sg
    }).then(result => {
        if (result.data.success) {
			let linkUrl;
			if (playType == 1) {
				linkUrl = 'https://document.dxznjy.com/' + encodeURIComponent(result.data.data);
			} else {
				linkUrl = result.data.data;
			}
			// #ifdef MP-WEIXIN
            innerAudioContext.obeyMuteSwitch = false;
            // #endif
			innerAudioContext.src = linkUrl;
			innerAudioContext.play();
            // var w = encodeURIComponent(result.data.data);
            // var linkUrl = 'https://document.dxznjy.com/' + w + '.mp3';
            // innerAudioContext.src = linkUrl;
            // innerAudioContext.play();
        } else {
            uni.showToast({
                title: result.data.message,
                icon: "none"
            })
        }
    });

}

export {
    playVoice
};
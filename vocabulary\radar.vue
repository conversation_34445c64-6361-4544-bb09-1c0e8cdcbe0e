<template>
  <view class="charts-box">
    <qiunDataCharts 
      type="radar"
      :opts="opts"
      :chartData="chartData"
    />
  </view>
</template>

<script>
import qiunDataCharts from './components/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue'
export default {
  components:{qiunDataCharts},
	props:{
		// radarData: {
		//     type: Object,
		// },
	},
  data() {
    return {
      chartData: {},
      //您可以通过修改 config-ucharts.js 文件中下标为 ['radar'] 的节点来配置全局默认参数，如都是默认参数，此处可以不传 opts 。实际应用过程中 opts 只需传入与全局默认参数中不一致的【某一个属性】即可实现同类型的图表显示不同的样式，达到页面简洁的需求。
      opts: {
        color: ["#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272"],
        padding: [5,5,5,5],
        dataLabel: false,
        enableScroll: false,
        legend: {
          show: false,
        },
        extra: {
          radar: {
			// radius:80, //雷达图半径
			dataLabel: false,
			dataPointShape: false,
			enableScroll: false,
            gridType: "radar",
            gridColor: "#E1F9E7", //雷达图网格颜色
            gridCount: 3,//雷达图网格数量 
            opacity: 1,
            max: 100,
            labelShow: true,
            border: true
          }
        }
      }
    };
  },
  onReady() {
	  // this.chartData = this.radarData
	  // console.log('radarData',this.chartData);
    // this.setRadarData();
  },
  methods: {
    setRadarData(data) {
		
         this.chartData = data
		console.log('radarData',this.chartData);
    },
  }
};
</script>

<style scoped>
  /* 请根据实际需求修改父元素尺寸，组件自动识别宽高 */
  .charts-box {
    width: 100%;
    height: 200px;
  }
</style>
<template>
  <view>
	  <view class="bg-h">
	  	<view class="positioning" @click="goback">
	  		<uni-icons type="left" size="24" color="#000" ></uni-icons>
	  	</view>
	  	<view class="word-position t-c col-12">
	  		<view  class="f-34">今日复习</view>
	  	</view>
	  </view>
    <view class="center-banner">
      <!-- <view :class="topShow ? 'center_boxs' : 'center-box'" :style="{ height: useHeight + 'rpx' }"> -->
      <view :class="topShow ? 'center_boxs' : 'center-box'">
        <image
          src="https://document.dxznjy.com/applet/newimages/fuxi@2x_new.png"
          mode="widthFix"
          class="niuimg"
          style="width: 434rpx"
        ></image>
        <view class="f-32 green" :class="topShow ? 'words-tops' : 'words-top'">
          <view class="remind mb-20 c-00">今日共有{{ pyfList.sumReviewCount || 0 }}节课需复习</view>
          <view v-if="pyfList.todayReviewCount !=0" @click="getWordPreview(1)"> 当日{{ pyfList.todayReviewCount }}节课程 </view>
          <!-- <view @click="getWordPreview(1)"> 当日{{ pyfList.todayReviewCount }}节课程 </view> -->
          <view v-if="pyfList.pastReviewCount !=0" style="margin-top: 40rpx" @click="getWordPreview(2)"> 往期{{ pyfList.pastReviewCount }}节课程 </view>
        </view>
        <view class="plr-50" :class="topShow ? 'start-tops' : 'start_top'">
          <button class="start" @click="startReview(0)">开始复习</button>
          <view class="temporarily mt-30" @click="closeReview()">暂不复习</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      pyfList: {}, // 单词
      studentCode: '', //学员Code
      topShow: false, //判断向上高度
      useHeight: 0,
    }
  },
  onReady() {
    uni.getSystemInfo({
      success: (res) => {
        // 可使用窗口高度，将px转换rpx
        let h = res.windowHeight * (750 / res.windowWidth)
        if (h < 1500) {
          this.topShow = true
        }

        // this.useHeight = h - 190;
      },
    })
  },
  onLoad(options) {
    // this.studentCode = options.studentCode
    this.studentCode = uni.getStorageSync('pyfStudentCode')
    this.getPyfDetails()
  },
  methods: {
    // 获得pyf数据详情
    async getPyfDetails() {
      uni.showLoading({
        title: '加载中',
      })
      let res = await this.$http.post('/znyy/pd/planReview/queryReviewedCount', {
        studentCode: this.studentCode,
        // studentCode: '6231216111',
      })
      uni.hideLoading()
      console.log(res)
      if (res.data.success) {
        this.pyfList = res.data.data
      }
    },

    // 单词页
    getWordPreview(type) {
      uni.navigateTo({
        url: '/PYFpages/dayLessonPreview?type=' + type,
      })
    },

    closeReview() {
      uni.navigateBack()
    },
    goback(){
    	uni.redirectTo({
    	  url: "/PYFpages/forgetReview",
    	});
    },
    // 开始复习
    startReview(type) {
      if (type == 0) {
        if (this.wordCount == 0) {
          this.$util.alter('暂无可复习内容！')
        } else {
          uni.navigateTo({
            url: '/PYFpages/lessonPreview?studentCode=' + this.studentCode,
          })
        }
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.positioning {
  position: fixed;
  top: 100rpx;
  left: 30rpx;
}
.center-banner {
  margin: 0 auto;
  margin-bottom: 50rpx;
  // width: 690rpx;
  // height: 1408rpx;
  min-height: 82vh;
  background: #ffffff;
  border-radius: 20rpx;
  padding-bottom: 40rpx;
  padding-top: 180rpx;
}
.center-box {
  width: 100%;
  text-align: center;
  margin-top: 200rpx;
  margin-bottom: 50rpx;
}

.center_boxs {
  width: 100%;
  text-align: center;
  // margin-top: 260rpx;
}

.words-tops {
  margin-top: 60rpx;
}

.words-top {
  margin-top: 120rpx;
}

.start {
  border-radius: 50rpx;
  color: #fff;
  font-size: 30rpx;
  height: 90rpx;
  background: #2e896f;
  line-height: 90rpx;
}

.start_top {
  margin-top: 208rpx;
}

.start-tops {
  margin-top: 148rpx;
}

.temporarily {
  font-size: 30rpx;
  color: #999;
  text-align: center;
}

.green {
  color: #2e896f;
}
.remind {
  font-weight: bold;
  font-size: 32rpx;
}
.niuimg {
  // margin-top: 180rpx;
}
// 导航栏样式
.positioning {
	position: fixed;
	top: 110rpx;
	left: 30rpx;
	z-index: 9;
}

.word-position {
	position: fixed;
	top: 0;
	left: 0;
	background-color: #f3f8fc;
	height: 190rpx;
	padding-top: 110rpx;
	box-sizing: border-box;
}
</style>

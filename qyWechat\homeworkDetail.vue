<template>
  <view class="container">
    <web-view
      :src="`https://document.dxznjy.com/applet/dxn/miniReport/index.html?reportId=${reportId}&studentCode=${studentCode}&correctStatus=${correctStatus}&title=${title}&tempT=${uuid}`"
    ></web-view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        reportId: '',
        studentCode: '',
        correctStatus: '',
        title: '一课一练报告',
        uuid: '',
        isWechat: true
      };
    },
    async onShow() {
      console.log('onShow');
      if (!this.uuid) {
        let uuid = await uni.$http.post(`/paper/wap/token?token=${uni.getStorageSync('token')}`);
        this.uuid = uuid.data.data;
        console.log('🚀 ~ onShow ~ this.uuid:', this.uuid);
      }
    },
    async onLoad(option) {
      console.log(option);

      if (option.isWechat == true) {
        this.uuid = option.uuid;
      } else {
        console.log('不是微信');
        uni.navigateTo({
          url: '/qyWechat/login_qywx'
        });
      }

      this.reportId = option.reportId;
      this.studentCode = option.studentCode;
      this.correctStatus = option.correctStatus;
    },

    methods: {
      reloadWebView() {
        this.$forceUpdate();
      }
    }
  };
</script>

<style scoped></style>

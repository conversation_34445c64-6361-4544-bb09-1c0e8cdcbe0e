<template>
  <view>
    <view style="width: 100%; text-align: center; position: absolute; top: 118rpx">
      <text style="color: #000000; line-height: 44rpx; font-size: 34rpx; font-weight: bold">登录</text>
    </view>
    <view class="from">
      <view class="loginTitle">
        <image src="https://document.dxznjy.com/app/images/zhujiaoduan/logo.png" mode=""></image>
      </view>
      <view class="login-box">
        <view class="phone-input mt-20">
          <image style="width: 30rpx; height: 30rpx" class="marginright16" src="/static/images/login_phone.png"></image>
          <uni-combox
            class="input"
            style="z-index: 999"
            :border="false"
            :candidates="phoneArr"
            placeholder="请输入手机号"
            emptyTips="无账号"
            v-model="mobile"
            @input="changeInput($event)"
            @select="selectInput($event)"
          ></uni-combox>

          <!-- <input type="number" name="mobile" :value="mobile" placeholder="请输入手机号"
						placeholder-class="phClass" class="input" maxlength="11" @input="changeInput($event)" /> -->
          <!-- <uni-icons type="clear" v-if="mobile!=''" class="marginright16" color="#c7c7c7" size="18" @click="mobile=''" /> -->
        </view>

        <view class="phone-input mt-30">
          <image style="width: 30rpx; height: 30rpx" class="marginright16" src="/static/images/login_mima.png"></image>
          <input v-if="!showpwd" type="password" name="pwd" placeholder="请输入密码" placeholder-class="phClass" class="input" maxlength="20" v-model="pwd" />
          <input v-if="showpwd" type="text" name="pwd" placeholder="请输入密码" placeholder-class="phClass" class="input" maxlength="20" v-model="pwd" />
          <!-- <uni-icons type="clear" v-if="pwd!=''" color="#c7c7c7" size="18" class="marginright16"
						@click="pwd=''" /> -->
          <image
            class="eyeicon marginright16"
            :src="!showpwd ? '/static/images/eyeclose.png' : '/static/images/eye.png'"
            mode=""
            style="margin-right: 16rpx"
            @click="showpwd = !showpwd"
          ></image>
        </view>

        <view class="agreement mt-40" @click="savePwd">
          <label class="radio" style="display: inline-block; transform: scale(0.6)">
            <radio value="r1" :checked="isSave" color="#137265" />
          </label>
          <text style="color: #666666">保存密码</text>
        </view>

        <button class="login" :class="mobile != '' && mobile.length == 11 && pwd != '' && pwd.length >= 6 ? '' : 'default'" type="default" @click="login">登录</button>

        <view class="bottom_center agreement">
          <label class="radio" style="display: inline-block; transform: scale(0.6)">
            <radio value="r1" :checked="isChecked" color="#137265" @click="changeischecked" />
          </label>
          <text style="color: #999999">我已阅读并同意</text>
          <text @click="goweburl('https://document.dxznjy.com/applet/agreeon/teacheragreement.html')">《用户服务协议》</text>
          <!-- <text @click="goweburl('https://document.dxznjy.com/applet/agreeon/privacypolicy.html')">、《隐私政策》</text> -->
        </view>
      </view>
    </view>

    <uni-popup ref="popup" :mask-click="false">
      <view class="bg-ff radius-15 mlr-60 p-20">
        <view class="t-r" @click="close">
          <uni-icons type="closeempty" size="22" color="#999"></uni-icons>
        </view>
        <view class="">
          <view class="tit f-32 bold mb-40 t-c">用户隐私保护协议</view>
          <view class="pb-30 f-30 plr-40 lh-50">
            &nbsp;感谢您使用本产品，您使用本产品前应当仔细阅读并同意
            <text class="c-fea" @click="handleOpenPrivacyContract">{{ urlTitle }}</text>
            ，当您点击同意使用产品服务时，即表示您已理解并了解该条款内容。
          </view>
          <view class="flex-s f-32 c-99 flex-x-a mtb-30">
            <view class="close_btn" @click="close">拒绝</view>
            <button class="review_btn" open-type="agreePrivacyAuthorization" @bindagreeprivacyauthorization="handleAgreePrivacyAuthorization">同意</button>
          </view>
        </view>
      </view>
    </uni-popup>

    <uni-popup ref="loadingPopup" type="center" :is-mask-click="false">
      <view class="bg-ff radius-15 t-c">
        <view class="loadingpadding">
          <image src="https://document.dxznjy.com/Assistant/loading.png" class="loadingImg"></image>
          <view class="mt-30">加载中...</view>
          <view>请耐心等候</view>
        </view>
      </view>
    </uni-popup>

    <uni-popup ref="agreement" :mask-click="false">
      <view>
        <image class="cartoom_image" src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
      </view>
      <view class="bg-ff radius-15 agreementContent">
        <view class="agreementTitle">温馨提示</view>
        <view class="agreementText">
          请阅读并同意
          <text style="color: #e57126" @click="goweburl('https://document.dxznjy.com/applet/agreeon/teacheragreement.html')">《用户服务协议》</text>
          后进行登录！
        </view>
        <view class="f-30 mt-45">
          <button class="ok_btn" @click="agreementOk">同意并继续</button>
          <view class="no_ok_btn" @click="agreementNo">不同意</view>
        </view>
      </view>
    </uni-popup>
    <!-- 选择角色弹窗 -->
    <uni-popup ref="popopChooseStudent" type="center" :safe-area="false" :isMaskClick="false">
      <view class="dialogBG">
        <view class="dialog-all">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="dialog-small-close" @click="cancelStudent">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="dialog-center">
            <view class="dialog-title pb-10">选择角色</view>
            <view class="dialog-item" @click="chooseStudentlist(item, index)" v-for="(item, index) in roleArr" :key="index" :class="isactive == index ? 'selected' : 'not-selected'">
              {{ item.label }}
            </view>
            <view class="flex-c mt-40">
              <view class="common-sure-btn-1" @click="confirmStudent()">确定</view>
              <view class="common-cancel-btn ml-40" @click="cancelStudent()">取消</view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import util from '@/utils/util.js';
  export default {
    data() {
      return {
        keyName: '',
        mobile: '',
        pwd: '',
        role: '',
        wxCode: '',
        isactive: -1,
        userInfo: {}, //微信用户信息
        showpwd: false, //是否显示密码
        isLogin: 'islogin', //是否登陆
        isRegist: false, //是否是注册
        isChecked: false,
        roleArr: [],
        minAppUser: {
          role: 'assistant',
          token: '',
          scrm_token: ''
        },
        privacy: false, // 隐私保护提示
        urlTitle: '',

        isSave: false,
        phoneArr: [],
        phonePwdArr: []
      };
    },
    onLoad: function () {
      // wx.getPrivacySetting({
      //   success: res => {
      // 	console.log(res) // 返回结果为: res = { needAuthorization: true/false, privacyContractName: '《xxx隐私保护指引》' }
      // 	if (res.needAuthorization) {
      // 	  // 需要弹出隐私协议
      // 	  this.privacy = true;
      // 	  this.urlTitle = res.privacyContractName;
      // 	  this.$refs.popup.open()
      // 	} else {
      // 	  // 用户已经同意过隐私协议，所以不需要再弹出隐私协议，也能调用已声明过的隐私接口
      // 	  // wx.getUserProfile()
      // 	  // wx.chooseMedia()
      // 	  // wx.getClipboardData()
      // 	  // wx.startRecord()
      // 	}
      //   },
      //   fail: () => {},
      //   complete: () => {}
      // })

      this.getSavePhone();
    },
    onShow() {
      var islogin = uni.getStorageSync('isLogin');
      if (islogin != '') {
        this.isLogin = islogin;
      }
    },
    methods: {
      chooseStudentlist(item, index) {
        this.isactive = index;
        this.role = item.value;
      },
      chooseStudentlist(item, index) {
        this.isactive = index;
        this.role = item.value;
      },
      hasPrevPage() {
        let pages = getCurrentPages(); // 获取页面栈
        return pages.length > 1; // 页面栈长度大于1时，代表有上一层页面
      },
      cancelStudent() {
        this.cacheAddStudentRequest = {};
        this.$refs.popopChooseStudent.close();
        this.isactive = -1;
      },
      confirmStudent() {
        if (this.isactive == -1) {
          uni.showToast({
            icon: 'none',
            title: '请先选择角色~'
          });
          return;
        } else {
          let role = this.role;
          this.againLogin(role);
        }
      },
      handleOpenPrivacyContract() {
        // 打开隐私协议页面
        wx.openPrivacyContract({
          success: (res) => {
            console.log(res);
          }, // 打开成功
          fail: () => {
            that.$util.alter('出错了，请稍后再试');
          }, // 打开失败
          complete: () => {}
        });
      },
      handleAgreePrivacyAuthorization() {
        console.log('用户同意隐私协议');
        this.privacy = false;
        this.$refs.popup.close();
        // 用户同意隐私协议事件回调
        // 用户点击了同意，之后所有已声明过的隐私接口和组件都可以调用了
        // wx.getUserProfile()
        // wx.chooseMedia()
        // wx.getClipboardData()
        // wx.startRecord()
      },
      close() {
        // this.promptUsers = false;
        this.$refs.popup.close();
      },
      // 跳转H5用户服务协议，隐私政策
      goweburl(url) {
        uni.navigateTo({
          url: `/pages/index/web?url=${url}`
        });
      },

      //验证表单
      validateForm: function () {
        if (this.mobile == '') {
          222222222222221;
          this.$util.alter('手机号码不能为空');
          return false;
        }
        if (this.mobile.length != 11) {
          this.$util.alter('手机号码必须是11位');
          return false;
        }
        if (this.pwd == '') {
          this.$util.alter('密码不能为空');
          return false;
        }
        if (this.pwd.length < 6) {
          this.$util.alter('密码不能少于6位');
          return false;
        }

        return true;
      },

      // 登录获取用户信息
      getUserInfo() {
        const that = this;
        // that.$refs.loadingPopup.open();
        uni.getUserProfile({
          desc: '用于完善用户资料',
          success: function (infoRes) {
            that.userInfo = infoRes.userInfo;
            if (!that.validateForm()) {
              return false;
            }
            // 登录接口
            that.login();
          },
          fail(err) {
            this.$util.alter(err.message);
          }
        });
      },

      agreementOk() {
        this.$refs.agreement.close();
        this.isChecked = true;
        this.login();
      },
      agreementNo() {
        this.$refs.agreement.close();
      },

      // 登录
      async login() {
        if (!this.validateForm()) {
          return false;
        }
        if (!this.isChecked) {
          this.$refs.agreement.open();
          return false;
        }
        // this.$http.config.header['www-cid'] =  'dx_deliver_resource';
        // this.$http.config.header['dx-source'] = "DELIVER##WX##MINIAPP";
        // var that = this;
        // uni.login({
        // 	provider: 'weixin',
        // 	success: function(loginRes) {
        // 		that.wxCode = loginRes.code;
        this.loginHandle();
        // 	}
        // });
      },

      // // 获取key
      // async sendLoginWxReq(code) {
      // 	if (!code) return;
      // 	let that = this;
      // 	that.$refs.loadingPopup.open();
      // 	let res = await uni.$http.get(`/deliver/app/wechat/login/${this.$config.WXAppId}`, {
      // 		code:code,
      // 		phone:that.mobile
      // 	});
      // 	console.log(res);
      // 	that.$refs.loadingPopup.close();
      // 	let data = res.data.data.key;
      // 	this.loginHandle(data);
      // },

      // 登录处理流程
      async loginHandle() {
        let that = this;
        // that.$refs.loadingPopup.open();
        let { data } = await that.$http.get('/new/security/v2/login/qwInner/mini', {
          mobile: that.mobile
          // usePwd: true,
          // password: that.pwd
        });
        if (data.code == 50003) {
          setTimeout(() => {
            uni.navigateTo({
              url: '/qyWechat/bindQyWechat'
            });
          }, 1000);
        } else {
          // uni.hideLoading();
          uni.setStorageSync('scrm_token', data.data.token);
          that.minAppUser.scrm_token = data.data.token;
          // 获取手机号
          that.$scrmHttp
            .get('/scrm/web/common/getUserInfo')
            .then(async (tel) => {
              let mobile = tel.data.data.username;
              that.tel = mobile;
              uni.setStorageSync('tel', that.tel);
              console.log(that.tel, 'that.tel');
              let roles = await uni.$http.get(`/deliver/web/common/getTeamRole/${that.tel}`);
              console.log('🚀 ~ .then ~ roles:', roles);
              let role = '';
              if (roles.data.data.length > 1) {
                that.roleArr = roles.data.data;
                that.$refs.popopChooseStudent.open();
              } else if (roles.data.data.length == 1) {
                role = roles.data.data[0].value;
                that.againLogin(role);
              } else {
                uni.showToast({
                  title: '您暂无任何权限登录',
                  icon: 'none'
                });
              }
            })
            .catch((err) => {
              console.log(err);
            });
        }
        // that.$refs.loadingPopup.close();
      },
      againLogin(role) {
        let that = this;
        // 二次登录
        that.$scrmHttp
          .get('/new/security/v2/login/deliver/mini', {
            username: that.tel,
            role: role,
            usePwd: false
          })
          .then((login) => {
            console.log(login);
            uni.setStorageSync('token', login.data.data.token);
            that.minAppUser.token = login.data.data.token;
            that.$user.storageUserInfo(that.minAppUser);
            uni.showToast({
              icon: 'success',
              title: '登录成功',
              duration: 1500
            });
            // 跳转到首页
            setTimeout(() => {
              if (this.hasPrevPage()) {
                uni.navigateBack();
              } else {
                uni.switchTab({
                  url: '/pages/index/index'
                });
              }
            }, 500);
          })
          .catch((err) => {
            uni.showToast({
              icon: 'none',
              title: '登录失败',
              duration: 1500
            });
          });
      },

      // 同意已阅读用户服务协议
      changeischecked() {
        this.isChecked = !this.isChecked;
      },

      savePwd() {
        this.isSave = !this.isSave;
      },

      changeInput(e) {
        console.log('changeInput');
        this.mobile = e;
        if (this.mobile.length == 11) {
          for (let i = 0; i < this.phonePwdArr.length; i++) {
            if (this.phonePwdArr[i].phone == this.mobile) {
              if (this.phonePwdArr[i].pwd) {
                this.pwd = this.phonePwdArr[i].pwd;
              } else {
                this.pwd = '';
              }
            }
          }
        } else {
          this.pwd = '';
        }
      },

      selectInput(e) {
        console.log('selectInput');
        this.mobile = e;
        for (let i = 0; i < this.phonePwdArr.length; i++) {
          if (this.phonePwdArr[i].phone == this.mobile) {
            if (this.phonePwdArr[i].pwd) {
              this.pwd = this.phonePwdArr[i].pwd;
            } else {
              this.pwd = '';
            }
          }
        }
      },

      getSavePhone() {
        let phonePwdArr = uni.getStorageSync('SavePhonePwd');
        if (phonePwdArr) {
          this.phonePwdArr = phonePwdArr;
          for (let i = 0; i < phonePwdArr.length; i++) {
            this.phoneArr.push(phonePwdArr[i].phone);
          }
        } else {
          this.phonePwdArr = [];
          this.phoneArr = [];
        }
        if (this.phonePwdArr.length == 1) {
          this.mobile = this.phonePwdArr[0].phone;
          this.pwd = this.phonePwdArr[0].pwd ? this.phonePwdArr[0].pwd : '';
        }
        console.log(this.phonePwdArr);
        console.log(this.phoneArr);
      },

      savePwdOption() {
        let that = this;
        let phonePwdArr = uni.getStorageSync('SavePhonePwd');
        if (phonePwdArr) {
          for (let i = 0; i < phonePwdArr.length; i++) {
            if (phonePwdArr[i].phone == that.mobile) {
              if (this.isSave) {
                phonePwdArr[i].pwd = that.pwd;
              }
              uni.setStorageSync('SavePhonePwd', phonePwdArr);
              return;
            }
          }
          let data = { phone: that.mobile };
          if (this.isSave) {
            data.pwd = that.pwd;
          }
          phonePwdArr.push(data);
          uni.setStorageSync('SavePhonePwd', phonePwdArr);
        } else {
          let data = { phone: that.mobile };
          if (this.isSave) {
            data.pwd = that.pwd;
          }
          uni.setStorageSync('SavePhonePwd', [data]);
        }
      },

      gourl: function (url) {
        uni.redirectTo({
          url: `/pages/login/` + url
        });
      }
    },
    onUnload() {
      if (this.isLogin != '') {
        uni.setStorageSync('isLogin', this.isLogin);
      }
    }
  };
</script>

<style>
  uni-page-body,
  page {
    padding-bottom: 0px !important;
    background-image: url('https://document.dxznjy.com/applet/newimages/login-banner.png');
    background-repeat: no-repeat;
    background-size: cover;
  }

  .loginTitle {
    font-weight: bold;
    padding: 0 50rpx;
    text-align: center;
  }
  .selected {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx !important;
    font-size: 30rpx;
    color: #2e896f;
    border: 1px solid #2e896f;
    border-radius: 35rpx;
    overflow: visible;
  }

  .not-selected {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx !important;
    font-size: 30rpx;
    color: #000;
    border: 1px solid #c8c8c8;
    border-radius: 35rpx;
    overflow: visible;
  }
  .loginTitle image {
    width: 130rpx;
    height: 130rpx;
  }

  .container {
    width: 100%;
    height: 100%;
  }
  .dialogBG {
    margin: 0 30rpx 30rpx 30rpx;
    background-color: #fff;
    border-radius: 14rpx;
  }
  .dialog-all {
    width: 670rpx;
    position: relative;
  }
  .common-sure-btn-1 {
    width: 250rpx;
    height: 80rpx;
    background: #2e896f;
    border-radius: 40rpx;
    font-size: 30rpx;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(0.995); /* 解决ios上圆角失效 */
  }
  .common-cancel-btn {
    width: 250rpx;
    height: 80rpx;
    border-radius: 40rpx;
    border: 1rpx solid #2e896f;
    font-size: 30rpx;
    color: #2e896f;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(0.995); /* 解决ios上圆角失效 */
  }
  .dialog-all image {
    width: 100%;
    height: 100%;
  }
  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 170rpx;
    z-index: -1;
  }
  .dialog-small-close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .dialog-center {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 50upx 55upx;
    box-sizing: border-box;
  }
  .dialog-title {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    font-weight: 600;
    justify-content: center;
  }
  .dialog-item {
    box-sizing: border-box;
    font-size: 32upx;
    line-height: 45upx;
    text-align: center;
    margin-top: 40rpx;
  }
  .from {
    width: 690rpx;
    height: 1000rpx;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    background-color: #fff;
    border-radius: 33rpx;
    padding-top: 104rpx;
    top: 300rpx;
  }
  .bottom_center {
    position: absolute;
    margin-bottom: 50rpx;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 270px;
  }
  .login-top {
    display: block;
    width: 240rpx;
    height: 240rpx;
    margin: 0 auto !important;
  }

  .login-box {
    padding-top: 50rpx;
    padding-bottom: 40rpx;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#7Fd2f9f6, endColorstr=#7Fd2f9f6);
    border-radius: 10rpx;
    margin-top: 60rpx;
  }

  .phone-input {
    width: 560rpx;
    height: 90rpx;
    line-height: 90rpx;
    background-color: #f6f6f6;
    margin-left: 50rpx;
    margin-right: 50rpx;
    border-radius: 10rpx;
    display: flex;
    align-items: center;
    position: relative;
    padding-bottom: 16upx;
    padding: 0 20rpx 0 40rpx;
    /* border-bottom: 2rpx solid #ececea; */
  }

  .eyeicon {
    width: 40rpx;
    height: 30rpx;
  }

  .phClass {
    color: #c7c7c7;
    font-size: 30rpx;
  }

  .login {
    margin-left: 50rpx;
    margin-right: 50rpx;
    background: rgb(46, 137, 111) !important;
    color: #fff !important;
    font-size: 32rpx;
    border-radius: 45rpx;
    text-align: center;
    height: 90rpx;
    line-height: 90rpx;
    margin-top: 80rpx;
  }

  .login.default {
    background: #c8c8c8 !important;
  }

  .touying-box {
    margin-left: 40rpx;
    margin-right: 40rpx;
    margin-top: -8rpx;
  }

  .touying {
    height: 28rpx;
    width: 100%;
  }

  .phone {
    width: 34rpx;
    height: 52rpx;
    margin-left: 20rpx;
    margin-right: 20rpx;
  }

  .input {
    flex-grow: 1;
  }

  .ml-20 {
    margin-left: 30rpx;
  }

  button::after {
    border: none;
  }

  .mt-20 {
    margin-top: 20rpx;
  }

  .text {
    padding: 0 50rpx;
    height: 120rpx;
    line-height: 120rpx;
    display: flex;
    justify-content: space-between;
    color: #0398ef;
    font-size: 28rpx;
  }

  .agreement {
    display: flex;
    align-items: center;
    font-size: 26rpx;
    padding: 0 0 0 40rpx;
    box-sizing: border-box;
    color: #c7c7c7;
  }

  .agreement text {
    color: #225449;
  }

  .marginright16 {
    margin-right: 16rpx;
  }

  .review_btn {
    width: 240upx;
    height: 80upx;
    margin: 0 !important;
    border-radius: 45upx;
    font-size: 30upx;
    color: #ffffff;
    line-height: 80upx;
    justify-content: center;
    text-align: center;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
  }

  .close_btn {
    width: 240upx;
    height: 80upx;
    color: #2e896f;
    font-size: 30upx;
    line-height: 80upx;
    text-align: center;
    border-radius: 45upx;
    box-sizing: border-box;
    border: 1px solid #2e896f;
    overflow: visible;
  }

  .loadingImg {
    width: 120rpx;
    height: 120rpx;
  }

  .loadingpadding {
    padding: 85rpx 250rpx;
  }

  .agreementContent {
    padding: 35rpx 0 32rpx 0;
    margin: 0 24rpx 0 24rpx;
  }

  .agreementTitle {
    text-align: center;
    font-weight: bold;
    font-size: 34rpx;
    margin-bottom: 35rpx;
  }

  .agreementText {
    padding: 0 108rpx 0 108rpx;
    margin-top: 44rpx;
    text-align: center;
    font-size: 32rpx;
  }

  .ok_btn {
    width: 400rpx;
    height: 80rpx;
    color: #ffffff;
    font-size: 30rpx;
    text-align: center;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 40rpx;
  }

  .no_ok_btn {
    margin-top: 24rpx;
    color: #666666;
    text-align: center;
  }

  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 185rpx;
    z-index: -1;
  }

  .uni-combox {
    padding: 0rpx 0rpx !important;
  }
  .uni-combox__input-plac {
    color: #c7c7c7;
    font-size: 30rpx;
  }
</style>

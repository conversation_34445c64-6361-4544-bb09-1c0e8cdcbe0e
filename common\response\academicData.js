// 学情数据分析
import { http } from '@/utils/luch-request/index.js' // 全局挂载引入
import { Util } from '@/utils/util.js'


// 首页每日考勤学习任务专注学情评分反馈
async function getHomeIndex(merchantCode,date){
	uni.showLoading();
	let needData="";
	await http.get(`/xi/wap/learnHome/learnData?merchantCode=${merchantCode}&date=${date}`).then((res) => {
		uni.hideLoading();
		if (res.data.success) {
			needData = res.data.data;
		} else {
			uni.showToast({
				title:"获取数据失败"
			});
		}
	});
	return needData;
}

//每日考勤
async function getLearnHomeInfo(date,studentCode){
	uni.showLoading();
	let needData="";
	await http.get(`/xi/wap/learnHome/info?date=${date}&studentCode=${studentCode}`).then((res) => {
		uni.hideLoading();
		if (res.data.success) {
			needData = res.data.data;
		} else {
			uni.showToast({
				title:"获取数据失败"
			});
		}
	});
	return needData;
}


// 每日考勤根据数据分为正常数据和异常数据
function divideNormal(arr) {
	let splitArr={
		normalList:[],	
		abnormalList:[],		// 异常数据（ 迟到 缺勤）
		allList:arr
	};
	//type	0正常 1缺勤 2迟到
	if(arr.length>0){
		arr.forEach(item=>{
			if(item.exception){
				splitArr.abnormalList.push(item)
			}else{
				splitArr.normalList.push(item)
			}
		})
	}
	return splitArr;
}

 
 
 //学习任务
async function getLearnHomeTask(date,studentCode){
	let needData = "";
 	await http.get(`/xi/wap/learnHome/learnTask?date=${date}&studentCode=${studentCode}`).then((res) => {
 		if (res.data.success) {
 			needData = res.data.data;
 		} else {
 			uni.showToast({
 				title:"获取数据失败"
 			});
 		}
 	});
	return needData;
 }
 
 // 学习任务详情页
 async function getLearnHomeTaskDetail(date,studentCode){
 	let needData = "";
  	await http.get(`/xi/wap/learnHome/learnTaskTwo?date=${date}&studentCode=${studentCode}`).then((res) => {
  		if (res.data.success) {
  			needData = res.data.data;
  		} else {
  			uni.showToast({
  				title:"获取数据失败"
  			});
  		}
  	});
 	return needData;
  }
 
 
 // 学习任务根据数据分为正常数据和异常数据
  function divideNormalTask(arr) {
 	let splitArr={
 		normalList:[],	
 		abnormalList:[],		// 异常数据
		allList:arr
 	};
 	//learnException 根据learnException有没有数据判断是否是正常数据
 	if(arr.length>0){
 		arr.forEach(item=>{
 			if(item.learnException!=null&&item.learnException.length>0){
				splitArr.abnormalList.push(item)	
 			}else{
 				splitArr.normalList.push(item)
 			}
 		})
 	}
 	return splitArr;
 }
 

 
 /*
 评分反馈
 */ 


// 评分反馈信息   type  1获取正常异常和总共数据  2获取是否评分数据 3数据不处理直接返回
 async function getSelScoreFeedBackInfo(type,date,merchantCode){
 	let splitArr = "";
  	await http.get(`/xi/wap/feedback/selScoreFeedBackInfo?date=${date}&merchantCode=${merchantCode}`).then((res) => {
  		if (res.data.success) {
			if(type==1){
				splitArr = divideNormalTask(res.data.data);
			}else if(type==2) {
				splitArr = divideisFeedback(res.data.data,'isScore');
			}else {
				splitArr = res.data.data;
			}
  			
  		} else {
  			uni.showToast({
  				title:"获取数据失败"
  			});
  		}
  	});
 	return splitArr;
  }
  
// 查询每日作业详情
 async function getDailyHomeworkDetailVo(date,studentCode){
  	uni.showLoading();
 	let newData = "";
  	await http.get(`/xi/wap/record/selDailyHomeworkDetailVo?date=${date}&studentCode=${studentCode}`).then((res) => {
  		uni.hideLoading();
  		if (res.data.success) {
  			newData = res.data.data;
  		} else {
  			uni.showToast({
  				title:"获取数据失败"
  			});
  		}
  	}); 
 	return newData;
  }
  
// 查询点评语列表
 async function getSelectXiScoreContentList(){
  	uni.showLoading();
 	let newData = "";
  	await http.get("/xi/wap/feedback/selectXiScoreContentList").then((res) => {
  		uni.hideLoading();
  		if (res.data.success) {
  			newData = res.data.data;
  		} else {
  			uni.showToast({
  				title:"获取数据失败"
  			});
  		}
  	});
 	return newData;
  }
  
// 查询备注列表
 async function getSelectXiRemarkContentList(){
  	uni.showLoading();
 	let newData = "";
  	await http.get("/xi/wap/feedback/selectXiRemarkContentList").then((res) => {
  		uni.hideLoading();
  		if (res.data.success) {
  			newData = res.data.data;
  		} else {
  			uni.showToast({
  				title:"获取数据失败"
  			});
  		}
  	});
 	return newData;
  }
  
  
  
  
  
  // 查询学员某天的评分反馈
 async function getSelScoreFeedBackByStudentCode(date,studentCode){
  	uni.showLoading();
 	let newData = "";
  	await http.get(`/xi/wap/feedback/selScoreFeedBackByStudentCode?date=${date}&studentCode=${studentCode}`).then((res) => {
  		uni.hideLoading();
  		if (res.data.success) {
  			newData = res.data.data;
  		} else {
  			uni.showToast({
  				title:"获取数据失败"
  			});
  		}
  	});
 	return newData;
  } 
 
 
 
 // 满足某个条件评分，返回数组
  function divideisFeedback(arr,param) {
 	let newArr = [];
 	if(arr.length>0){
 		arr.forEach(item=>{
 			if(item[param]){
 				newArr.push(item)	
 			}
 		})
 	}
 	return newArr;
 }
 
 
 
 //获取正常异常 数量  百分比
 function getNoAndAbnoNum(normal,abnormal){
	let list = [{"label":"正常"},{"label":"异常"}];
	list[0].value = normal;
	list[1].value = abnormal;
	list[0].percent = accDiv(normal,normal+abnormal);   
	list[1].percent = accDiv(abnormal,normal+abnormal)
	return list; 
 }
 
 
 function accDiv(ele1,ele2){
 	let needData;
 	if(ele1==0||ele2==0) {
 		needData = 0;
 	}else{
 		needData = parseInt(ele1*100/ele2)
 	}
 	return needData;
 }
 
 
 
 //保存学员每日台灯使用记录
 async function saveLearnHome(merchantCode){
 	let splitArr = "";
  	await http.get(`/xi/wap/learnHome/save?merchantCode=${merchantCode}`).then((res) => {
  		uni.hideLoading();
  		if (!res.data.success) {
  			uni.showToast({
  				title:"获取数据失败"
  			});
  		}
  	});
 	return splitArr;
  }
  
 
 
 
 
 
 
 module.exports = {
	getHomeIndex:getHomeIndex,
	getLearnHomeInfo:getLearnHomeInfo,
	divideNormal:divideNormal,
	getLearnHomeTask:getLearnHomeTask,
	getLearnHomeTaskDetail:getLearnHomeTaskDetail,
	divideNormalTask:divideNormalTask,
	getSelScoreFeedBackInfo:getSelScoreFeedBackInfo,
	getSelectXiScoreContentList:getSelectXiScoreContentList,
	getSelectXiRemarkContentList:getSelectXiRemarkContentList,
	getDailyHomeworkDetailVo:getDailyHomeworkDetailVo,
	getSelScoreFeedBackByStudentCode:getSelScoreFeedBackByStudentCode,
	getNoAndAbnoNum:getNoAndAbnoNum,
	accDiv:accDiv,
	saveLearnHome:saveLearnHome
 }
 
 
 
 
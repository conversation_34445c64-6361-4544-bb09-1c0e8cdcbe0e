<template>
	<view class="interesting_popupCenter" :class="isReview?'interesting_popupCenter1':''" >
		
		<view class="interet_head">
			<image  v-if="!isReview" class="interet_popupTitle" src='https://document.dxznjy.com/applet/interesting/intert_popupTitle.png' mode=""></image>
			<image v-if="isReview" class="interet_popupTitle" src='https://document.dxznjy.com/applet/interesting/dialog_interesting_review_title.png' mode=""></image>
			<text v-if="!isReview">{{title}}</text>
		</view>
		
				
		<view class="popup_content" v-if="!isReview"><view :style="isRed?'color:red;':''">{{txtContent}}</view></view>
		
		<!-- 复习报告复习词数，错误词数，正确率 -->
		<view class="popup_content" v-if="isReview">
			<view class="reviewPopup_list">
				<text>复习词数</text>
				<text class="DesignFont">{{reviewWordNum.length+errorWordNum.length}}</text>
			</view>
			<view class="reviewPopup_list">
				<text>错误词数</text>
				<text class="DesignFont" style="color: #ff3c3c;">{{errorWordNum.length}}</text>
			</view>
			<view class="reviewPopup_list">
				<text>正确率</text>
				<text class="DesignFont" style="color: #3c6c3c;">{{correctRate}}%</text>
			</view>
		</view>
		
		<view class="popBtnGroup" v-if="isSingle">
			<view class="popBtnGroupNowBuy" @click="nowBuy()">立即购买</view>
		</view>
		<view class="popBtnGroup" :class="isReview?'popBtnGroup1':''" v-if="!isSingle">
			<view class="popBtnGroup_list" @click="confirm()">确认</view>
			<view class="popBtnGroup_list" @click="cancel()">取消</view>
		</view>
		
		<image @click="closeDialog()" class="interesting_close" src="https://document.dxznjy.com/applet/interesting/dialog_closed.png" mode=""></image>
	</view>
		
		
	
</template>

<script>
	export default {
		// isReview:true,//是否是复习弹窗  isSingle:false,//是否是单个按钮  isRed:true,//字体颜色是否是红色
		props:{title:'', isReview:true,isSingle:false,isRed:true,txtContent:String,reviewWordNum:[],errorWordNum:[],correctRate:0},
		data() {
			return {
			}
		},
		methods: {
			//立即购买
			nowBuy(){
				this.$emit('nowBuy')
			},
			//确认
			confirm(){
				this.$emit('confirm')
			},
			//取消
			cancel(){
				this.closeDialog();
			},
			//关闭弹窗
			closeDialog(){
				this.$emit('closeDialog')
			}
		}
	}
</script>

<style>

.popBtnGroup { display: flex;font-size: 30rpx; justify-content: center;padding: 0 50rpx;height: 90rpx;line-height: 90rpx;margin-top: 10rpx; }
.popBtnGroupNowBuy { width: 292rpx;overflow-y: auto; color: #ffffff; background: url('https://document.dxznjy.com/applet/interesting/interestNow_buy.png') center center no-repeat;background-size: 100% 100%; }
.popBtnGroup_list { width: 48%;margin: 0 1%;color: #ffffff; }

.popupBottom { background: #ffffff;position: absolute;bottom: 120rpx;left: 0;width: 100%;padding-bottom: 30rpx;border-radius: 50rpx; }

.popBtnGroup .popBtnGroup_list:first-child { background: url('https://document.dxznjy.com/applet/interesting/dialog_btn_left.png') center center no-repeat;background-size: 100% 100%; }
.popBtnGroup .popBtnGroup_list:last-child { background: url('https://document.dxznjy.com/applet/interesting/dialog_btn_right.png') center center no-repeat;background-size: 100% 100%; }
.popBtnGroup.popBtnGroup1 .popBtnGroup_list:first-child { background: url('https://document.dxznjy.com/applet/interesting/dialog_i_btn_left.png') center center no-repeat;background-size: 100% 100%; }
.popBtnGroup.popBtnGroup1 .popBtnGroup_list:last-child { background: url('https://document.dxznjy.com/applet/interesting/dialog_i_btn_right.png') center center no-repeat;background-size: 100% 100%; }

.reviewPopup_list { font-size: 26rpx;padding-top: 42rpx;box-sizing: border-box;border-radius: 16rpx; width: 152rpx;height: 175rpx;justify-content: center;align-items: center; border: 1rpx solid #411c0c;margin: 0 10rpx;color: #411c0c; }
.reviewPopup_list .DesignFont { display: block;width: 100%;margin-top: 15rpx;font-size: 45rpx; }
.reviewPopup_list .DesignFont.DesignFontBg { font-size: 50rpx;font-weight: bold; }

.interesting_close { width: 80rpx;height: 80rpx;position: absolute;bottom: 0;left: 240rpx; }
		

</style>

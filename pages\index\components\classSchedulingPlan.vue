<template>
  <div @click="handleClick">
    <view class="input-border mb-30 flex-center plr-30" @click="chooseCourseType(true)">
      <image style="width: 38rpx; height: 38rpx" src="/static/images/create_img_user.png" mode="aspectFill"></image>
      <text class="search-dialog-student-input ml-30 text-hide" :class="dialogSearchPeople != null ? 'no-null-text-color' : 'null-text-color'">
        {{ courseType.curriculumId ? courseType.curriculumName : '请选择班级' }}
      </text>
      <uni-icons type="right" color="#999999" size="20"></uni-icons>
    </view>
    <view class="mb-30" v-if="studentlist.length > 0">
      <selectCy ref="selectCyRef" :zindex="10" svalue="val" slabel="label" placeholder="请选择学员" :value="students" :options="studentlist" @change="changeStudents"></selectCy>
    </view>
    <view class="input-border mb-30 flex-center plr-30" @click="dialogChoseDate">
      <image style="width: 38rpx; height: 38rpx" src="/static/images/create_img_date.png" mode="aspectFill"></image>
      <text class="search-dialog-student-input ml-30 text-hide" :class="schedulingPlan.calendarDate.length > 0 ? 'no-null-text-color' : 'null-text-color'">
        {{ schedulingPlan.calendarDate.length > 0 ? getCalendarDateText() : '请选择日期' }}
      </text>
      <uni-icons type="right" color="#999999" size="20"></uni-icons>
    </view>
    <view class="flex-s mb-30">
      <view class="input-border-s flex-c" :class="schedulingPlan.startTime == '' ? 'null-text-color' : 'no-null-text-color'" @click="showChoseCourseTime(true)">
        {{ schedulingPlan.startTime == '' ? '开始时间' : schedulingPlan.startTime }}
      </view>
      <uni-number-box v-model="schedulingPlan.hour" :min="1" :max="1" @change="numberBoxBindChange()"></uni-number-box>
      <view class="input-border-s flex-c" :class="schedulingPlan.startTime == '' ? 'null-text-color' : 'no-null-text-color'" @click="showChoseCourseTime(schedulingPlan.startTime)">
        {{ schedulingPlan.endTime == '' ? '结束时间' : schedulingPlan.endTime }}
      </view>
    </view>
    <view class="flex-c" style="margin-top: 86rpx">
      <view class="common-cancel-btn" @click="createOption(false)">取消</view>
      <view class="common-sure-btn ml-40" @click="createOption(true)">确定</view>
    </view>
    <!-- 选择课程类型弹窗 -->
    <uni-popup ref="courseShow" type="center">
      <view class="dialogBG">
        <view class="dialog-all">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="dialog-small-close" @click="closeCourse">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="dialog-center">
            <view class="dialog-title bold pb-20">选择课程类型</view>
            <view class="dialog-list">
              <view
                class="dialog-item"
                @click="chooseCourse(item, index)"
                v-for="(item, index) in courseList"
                :key="index"
                :class="isActive == index ? 'selected' : 'not-selected'"
              >
                {{ item.enName }}
              </view>
            </view>
            <view class="flex-c mt-40">
              <view class="common-sure-btn" @click="chooseCourseType(false)">确定</view>
              <view class="common-cancel-btn ml-40" @click="closeCourse">取消</view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
    <!-- 选择日期弹窗 -->
    <wu-calendar
      ref="calendar"
      @confirm="calendarConfirm"
      :insert="false"
      mode="multiple"
      :date="schedulingPlan.calendarDate"
      :useToday="false"
      :startDate="calendarStartDate"
      color="#2E896F"
      :showMonth="false"
      :selected="selectCalendarArr"
      @monthSwitch="monthSwitch"
    ></wu-calendar>
    <!-- 选择日期弹窗单选 -->
    <wu-calendar
      ref="editcalendar"
      @confirm="calendarConfirm1"
      :insert="false"
      mode="single"
      :date="schedulingPlan.calendarDate"
      :useToday="false"
      :startDate="calendarStartDate"
      color="#2E896F"
      :showMonth="false"
      :selected="selectCalendarArr"
      @monthSwitch="monthSwitch"
    ></wu-calendar>
    <!-- 选择开始时间弹窗 -->
    <u-datetime-picker
      ref="createTimePicker"
      :show="showCreateTimePicker"
      mode="time"
      itemHeight="40"
      confirmColor="#2e896f"
      @cancel="createTimePickerCancel"
      @confirm="createTimePickerSure"
      :immediateChange="true"
    ></u-datetime-picker>
    <u-datetime-picker
      ref="createTimePicker1"
      :show="showCreateTimePicker1"
      mode="time"
      itemHeight="40"
      confirmColor="#2e896f"
      @cancel="createTimePickerCancel1"
      @confirm="createTimePickerSure1"
      :immediateChange="true"
    ></u-datetime-picker>
    <!-- loading弹窗 -->
    <uni-popup ref="loadingPopup" type="center" :is-mask-click="false">
      <view class="bg-ff radius-15 t-c">
        <view class="loadingpadding">
          <image src="https://document.dxznjy.com/Assistant/loading.png" class="loadingImg"></image>
          <view class="mt-30">加载中...</view>
          <view>请耐心等候</view>
        </view>
      </view>
    </uni-popup>
  </div>
</template>

<script>
  import selectCy from './select-cy.vue';
  import dayjs from 'dayjs';

  export default {
    components: {
      selectCy
    },
    props: {
      editForm: {
        type: Object,
        default: () => null
      }
    },
    data() {
      return {
        isEdit: false, // 是否是编辑
        students: [], // 选择的学生列表
        studentlist: [], // 当前班级学生列表
        courseType: { extraSecond: '', curriculumId: '', curriculumName: '' }, // 当前选择的课程类型
        isActive: -1, // 当前选择的课程类型索引
        courseList: [], // 课程类型列表
        calendarStartDate: '', //选择日期截止开始时间
        showCreateTimePicker: false, //显示选择开始时间弹框
        showCreateTimePicker1: false, //显示选择结束时间弹框
        schedulingPlan: { startTime: '', endTime: '', hour: 1, calendarDate: [], students: [] } // 排课计划表单
      };
    },
    created() {
      this.getCourseList();
      this.calendarStartDate = dayjs().format('YYYY-MM-DD');

      // editForm
      if (this.editForm) {
        this.isEdit = true;
        this.schedulingPlan = this.editForm;
        this.courseType.curriculumId = this.editForm.curriculumId;
        let startTime = this.editForm.startTime.split(' ');
        let endTime = this.editForm.endTime.split(' ');
        this.schedulingPlan.calendarDate = [this.editForm.date]; // 时间
        console.log('🚀 ~ created ~  this.editForm.date:', this.editForm.date);
        this.schedulingPlan = { ...this.schedulingPlan, ...{ hour: this.editForm.hours, startTime: startTime[1], endTime: endTime[1] } };
        return;
      }
      this.isEdit = false;
    },
    methods: {
      // 查看上课时间是否和停服时间 / 关闭弹窗
      createOption(isSure) {
        if (isSure) {
          //   if (!this.dialogSearchPeople) {
          //     uni.showToast({
          //       icon: 'none',
          //       title: '请选择学员'
          //     });
          //     return;
          //   }
          //   if (!this.choseSchoolItem) {
          //     uni.showToast({
          //       icon: 'none',
          //       title: '请选择门店~'
          //     });
          //     return;
          //   }
          if (this.schedulingPlan.calendarDate.length == 0) {
            uni.showToast({
              icon: 'none',
              title: '请选择日期'
            });
            return;
          }
          if (!this.schedulingPlan.startTime) {
            uni.showToast({
              icon: 'none',
              title: '请选择开始时间'
            });
            return;
          }
          //   if (this.courseType.extraSecond == 1) {
          //     if (this.deliverClass == '') {
          //       uni.showToast({
          //         title: '请输入班级',
          //         icon: 'none'
          //       });
          //       return;
          //     }
          //   }
          this.$emit('createPlanCourse', this.schedulingPlan);
          return;
        } else {
          this.$emit('createOption', false);
        }
      },
      // 创建排课计划
      async createPlanCourse() {
        let that = this;
        that.$refs.loadingPopup.open();
        try {
          let data = {
            dateList: that.schedulingPlan.calendarDate,
            startTime: that.schedulingPlan.startTime,
            endTime: that.schedulingPlan.endTime,
            curriculumId: that.courseType.curriculumId
          };
          console.log(data, '班级排课');
          // TODO: 创建排课计划
          //   that.$refs.loadingPopup.open();
          //   let res = await uni.$http.post('/deliver/app/teacher/batchAddPlanCourse', data);
          //   that.$refs.loadingPopup.close();
          //   uni.setStorageSync('lastDeliverClass', this.deliverClass);
          //   console.log(res);
          //   if (res.data.success) {
          //     this.getNormalStudyList();
          //     this.$refs.createSuccess.open();
          //     this.$refs.createPopup.close();
          //     this.closeCourse();
          //   }
        } catch (e) {
          that.$refs.loadingPopup.close();
        }
      },
      // 改变选择的学生
      changeStudents(item, value) {
        console.log(item, value, '改变');
        this.schedulingPlan.students = value;
      },
      // 获取学生列表
      async getStudentList() {
        this.studentlist = [
          {
            val: 'label1',
            label: '学员1'
          },
          {
            val: 'label2',
            label: '学员2'
          },
          {
            val: 'label3',
            label: '学员3'
          },
          {
            val: 'label4',
            label: '学员4'
          },
          {
            val: 'label5',
            label: '学员5'
          },
          {
            val: 'label6',
            label: '学员6'
          }
        ];
        // TODO: 获取学生列表
        // let { data } = await uni.$http.get('/znyy/student/list');
      },
      // 打开选择课程类型弹窗
      chooseCourseType(flag) {
        if (flag) {
          this.$refs.courseShow.open();
        } else {
          this.$refs.courseShow.close();
        }
      },
      // 关闭选择课程类型弹窗
      closeCourse() {
        this.isActive = -1;
        this.courseType.curriculumName = '';
        this.courseType.curriculumId = '';
        this.courseType.extraSecond = 0;
        this.$refs.courseShow.close();
      },
      getCalendarDateText() {
        return this.schedulingPlan.calendarDate.join(',');
      },
      // 获取课程类型
      async getCourseList() {
        let { data } = await uni.$http.get('/znyy/curriculum/all');
        console.log(data.data);
        this.courseList = data.data;
        // console.log('🚀 ~ getCourseList ~ this.courseList:', this.courseList);
      },
      // 选择课程类型
      chooseCourse(item, index) {
        if (this.courseType.curriculumId == item.id) {
          this.courseType.curriculumId = '';
          this.courseType.curriculumName = '';
          this.courseType.extraSecond = 0;
          this.isActive = -1;
        } else {
          this.isActive = index;
          this.courseType.curriculumId = item.id;
          this.courseType.curriculumName = item.enName;
          this.courseType.extraSecond = item.extraSecond;
          //   this.getStudentHaveDeliverHours();
          this.getStudentList();
          setTimeout(() => {
            this.students = ['label1'];
            this.schedulingPlan.students = ['label1'];
          }, 200);
        }
      },
      // 获取学生已上课时间
      monthSwitch(date) {
        console.log('monthSwitch', date);
        // if (this.isShowCreatePopup) {
        this.getStudentStudyDateList(`${date.year}-${date.month}`);
        // }
      },
      //日历确定
      calendarConfirm(e) {
        console.log(e);
        this.schedulingPlan.calendarDate = e.multiple;
      },
      //日历确定
      calendarConfirm1(e) {
        console.log(e);
        this.schedulingPlan.calendarDate = e.fulldate;
      },
      //选择日期
      dialogChoseDate() {
        // if (this.dialogSearchPeople && this.dialogSearchPeople.studentCode) {
        this.selectCalendarArr = [];
        this.$refs.calendar.open();
        this.getStudentStudyDateList(dayjs().format('YYYY-MM'));
        // } else {
        //   uni.showToast({
        //     icon: 'none',
        //     title: '请先选择学员'
        //   });
        // }
      },
      ///日历数据
      async getStudentStudyDateList(date) {
        let that = this;
        // that.$refs.loadingPopup.open();
        try {
          let res = await uni.$http.get('/deliver/app/teacher/getStudentStudyDateList?date=' + date + '&type=1&studentCode=' + that.dialogSearchPeople.studentCode);
          // that.$refs.loadingPopup.close();
          if (res.data && res.data.success) {
            for (var i = 0; i < res.data.data.length; i++) {
              let data = {
                date: res.data.data[i],
                info: '已排课',
                infoColor: '#2E896F'
              };
              that.selectCalendarArr.push(data);
            }
            console.log('🚀 ~ getStudentStudyDateList ~ that.selectCalendarArr:', that.selectCalendarArr);
          }
        } catch {
          // that.$refs.loadingPopup.close();
        }
      },
      // 打开开始时间弹窗
      showChoseCourseTime(isStart) {
        if (!isStart) {
          uni.showToast({
            icon: 'none',
            title: '请选择开始时间'
          });
          return;
        }
        this.showCreateTimePicker = true;
      },
      // 打开结束时间弹窗
      showChoseCourseTime1(isStart) {
        if (!isStart) {
          uni.showToast({
            icon: 'none',
            title: '请选择开始时间'
          });
          return;
        }
        this.showCreateTimePicker1 = true;
      },
      createTimePickerSure(e) {
        this.showCreateTimePicker = false;
        this.schedulingPlan.startTime = e.value;
        // this.schedulingPlan.startTime = e.value + ':00';
        this.schedulingPlan.endTime = this.getAutoEndTime(e.value, this.schedulingPlan.hour);
        // this.schedulingPlan.endTime += ':00';
      },
      createTimePickerSure1(e) {
        console.log(e);
        this.showCreateTimePicker1 = false;
        this.schedulingPlan.endTime = e.value;
        // this.schedulingPlan.endTime = e.value += ':00';
        console.log(this.schedulingPlan.endTime, 'this.schedulingPlan.endTime');
        // this.schedulingPlan.endTime += ':00';
      },
      // 取消选择开始时间
      createTimePickerCancel() {
        this.showCreateTimePicker = false;
      },
      createTimePickerCancel1() {
        this.showCreateTimePicker1 = false;
      }, //自动获取结束时间
      getAutoEndTime(startTime, addHour) {
        let timeParts = startTime.split(':');
        let hours = parseInt(timeParts[0], 10);
        let minutes = parseInt(timeParts[1], 10);

        hours += addHour;

        if (hours >= 24) {
          hours -= 24;
        }
        let endTime = `${hours < 10 ? '0' + hours : hours}:${minutes < 10 ? '0' + minutes : minutes}`;
        return endTime;
      },
      handleClick() {
        this.$refs.selectCyRef?.close();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .hear_img {
    position: relative;
    width: 100%;
    height: 506rpx;
    z-index: 99;
  }
  .wallet-bgc {
    width: 100%;
    height: 506rpx;
  }
  .banner-bgc {
    text-align: center;
    width: 100%;
    position: absolute;
    top: 180rpx;
  }
  .nav-title {
    position: absolute;
    font-weight: bold;
    font-size: 34rpx;
    top: 130rpx;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 999;
  }

  .bottom-view {
    background-color: #f3f8fc;
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .search-view {
    width: 690rpx;
    height: 130rpx;
    background-color: #fff;
    border-radius: 14rpx 14rpx 0 0;
    display: flex;
    justify-content: space-around;
  }
  .search-view1 {
    width: 690rpx;
    height: 110rpx;
    background-color: #fff;
    border-radius: 0 0 14rpx 14rpx;
    display: flex;
    justify-content: center;
  }
  .search-date {
    margin-top: 30rpx;
    width: 300rpx;
    height: 80rpx;
    display: flex;
    border-radius: 8rpx;
    background-color: rgba(200, 200, 200, 0.18);
    justify-content: space-around;
    align-items: center;
  }
  .search-class {
    width: 390rpx;
    height: 80rpx;
    display: flex;
    border-radius: 8rpx;
    background-color: rgba(200, 200, 200, 0.18);
    justify-content: space-around;
    align-items: center;
  }
  .search-class-input {
    color: #999999;
    margin-left: 6rpx;
    width: 160rpx;
    padding-left: 10rpx;
    height: 40rpx;
    font-size: 30rpx;
  }
  .search-class-text {
    padding: 0 10rpx;
    color: #2e896f;
    font-size: 30rpx;
    border-left: 1rpx solid #d9d9d9;
  }
  .search-student {
    // width: 390rpx;
    width: 650rpx;
    height: 80rpx;
    display: flex;
    border-radius: 8rpx;
    background-color: rgba(200, 200, 200, 0.18);
    justify-content: space-between;
    align-items: center;
  }
  .search-student-input {
    color: #999999;
    margin-left: 6rpx;
    width: 510rpx;
    height: 40rpx;
    font-size: 30rpx;
  }
  .search-student-text {
    padding-right: 20rpx;
    color: #2e896f;
    font-size: 30rpx;
    // border-left: 1rpx solid #d9d9d9;
  }

  .tab-bg {
    background-color: #f3f8fc;
    width: 100%;
    height: 64rpx;
    padding-top: 10rpx;
    padding-bottom: 20rpx;
    display: flex;
    justify-content: center;

    &.type-list {
      background-color: transparent;
      padding-bottom: 60rpx;
      margin-top: -70rpx;
    }
  }
  .list-bg {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #f3f8fc;
    width: 100%;
  }
  .list-item-bg {
    width: 690rpx;
    // height: 500rpx;
    background: #ffffff;
    border-radius: 14rpx;
    margin-bottom: 30rpx;
    padding-bottom: 28rpx;
  }
  .time-pic {
    height: 35rpx;
    width: 38rpx;
  }
  .pic {
    height: 50rpx;
    width: 50rpx;
  }
  .flex-between {
    margin-right: 20rpx;
    margin-left: 20rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .flex-center {
    display: flex;
    align-items: center;
    justify-content: start;
  }
  .border-bottom {
    border-bottom: 2rpx dashed #eeeeee;
  }
  .border-top {
    border-top: 2rpx dashed #eeeeee;
  }
  .studyClass {
    width: 90rpx;
    height: 36rpx;
    background: #edbd58;
    border-radius: 4rpx;
    border: 1rpx solid #edbd58;
    font-size: 26rpx;
    color: #ffffff;
    text-align: center;
  }
  .item-content {
    margin-left: 20rpx;
    margin-top: 30rpx;
    font-size: 30rpx;
  }
  .copy {
    background-color: #c8c8c8;
    text-align: center;
    width: 80rpx;
    height: 40rpx;
  }
  .border-btn {
    border-radius: 30rpx;
    border: 1rpx solid #2e896f;
    padding: 8rpx 16rpx 8rpx 16rpx;
    // font-size: 30rpx;
    font-size: 24rpx;
    color: #2e896f;
  }
  .border-orange-btn {
    border-radius: 30rpx;
    border: 1rpx solid #e57126;
    padding: 8rpx 20rpx 8rpx 20rpx;
    font-size: 30rpx;
    color: #e57126;
  }
  .loadingImg {
    width: 120rpx;
    height: 120rpx;
  }
  .loadingpadding {
    padding: 85rpx 250rpx;
  }

  .tickling {
    text-align: center;
    font-weight: 700;
    font-size: 30rpx;
  }

  .text-content {
    font-size: 30rpx;
    margin-top: 20rpx;
    margin-left: 20rpx;
  }

  /deep/.u-textarea__field {
    background-color: #f7f7f7;
    border: 1rpx solid #d9d9d9;
    padding: 10rpx;
  }

  /deep/.u-textarea--disabled {
    background-color: #fff !important;
  }

  .button-sp-area {
    display: flex;
    justify-content: space-around;
    padding: 0 60rpx;
  }

  .cancel-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 30rpx;
    width: 160rpx;
    height: 60rpx;
    font-size: 24rpx;
    margin-right: 60rpx;
    background-color: #e7e7e7;
  }

  .mini-btn {
    color: #fff;
    width: 160rpx;
    height: 60rpx;
    font-size: 24rpx;
    line-height: 60rpx;
    text-align: center;
    border-radius: 30rpx;
    background-color: #2e896f;
  }
  // 弹框的样式设置
  /deep/.card {
    width: 600rpx;
    background-color: #fff;
    border-radius: 14rpx;
    padding: 30rpx;
    overflow-y: scroll;

    .reality {
      display: flex;
      align-items: center;
      margin-top: 10rpx;
      line-height: 40rpx;
      font-size: 25rpx;
    }

    .begin {
      border: 1px solid #999;
      border-radius: 10rpx;
      height: 40rpx;
      line-height: 40rpx;
      width: 220rpx;
      text-align: center;
    }

    .finish {
      border: 1px solid #999;
      border-radius: 10rpx;
      height: 40rpx;
      line-height: 40rpx;
      width: 220rpx;
      text-align: center;
    }
  }
  .course-btn-bg {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 120rpx;
    background-color: #ffffff;
  }
  .course-btn {
    color: #ffffff;
    font-size: 30rpx;
    width: 586rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 45rpx;
  }

  .delete-bg {
    padding: 45rpx 88rpx 50rpx 88rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .delete-newbg {
    padding: 45rpx 50rpx 50rpx 50rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .common-sure-btn {
    width: 250rpx;
    height: 80rpx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 45rpx;
    font-size: 30rpx;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .common-cancel-btn {
    width: 250rpx;
    height: 80rpx;
    border-radius: 45rpx;
    border: 1rpx solid #2e896f;
    font-size: 30rpx;
    color: #2e896f;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .date-title {
    font-size: 32rpx;
    color: #333333;
    line-height: 44rpx;
    font-weight: 600;
  }
  .empty-content {
    padding: 150rpx 0;
    margin: 0 30rpx 0 30rpx;
    z-index: 9;
    border-radius: 14rpx;
    background-color: #fff;
    display: flex;
    flex-direction: column; /* 垂直布局，子视图按列排列 */
    justify-content: center; /* 水平居中 */
    align-items: center; /* 垂直居中 */
  }
  .empty-img {
    width: 114rpx;
    height: 107rpx;
  }
  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 185rpx;
    z-index: -1;
  }
  .input-border {
    height: 80rpx;
    border-radius: 14rpx;
    border: 1rpx solid rgba(0, 0, 0, 0.2);
  }
  .input-border-s {
    width: 170rpx;
    height: 70rpx;
    font-size: 28rpx;
    border-radius: 14rpx;
    border: 1rpx solid rgba(0, 0, 0, 0.2);
  }

  .null-text-color {
    color: #666666;
  }
  .no-null-text-color {
    color: #000;
  }
  .search-dialog-student-input {
    width: 460rpx;
    height: 40rpx;
    font-size: 28rpx;
  }
  .text-hide {
    text-overflow: ellipsis; /* 当文本溢出时显示省略号 */
    white-space: nowrap; /* 禁止文本换行 */
    overflow: hidden; /* 隐藏溢出部分文本 */
  }

  .text-enter {
    word-wrap: break-word;
  }

  /deep/ .wu-calendar__content {
    margin: 20rpx;
    border-radius: 14rpx;
  }

  .chose-student-picker {
    width: 100%;
    height: 510rpx;
    overflow: hidden;
    transition: height 0.3s;
    padding-top: 40rpx;
  }
  .chose-student-item {
    text-align: center;
    height: 80rpx;
    line-height: 80rpx;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 30upx;
  }

  .chose-text-style {
    font-weight: 600;
    font-size: 32rpx;
  }
  .dialogBG {
    margin: 0 30rpx 30rpx 30rpx;
    background-color: #fff;
    border-radius: 14rpx;
  }
  .dialog-all {
    width: 670rpx;
    position: relative;
  }

  .dialog-all image {
    width: 100%;
    height: 100%;
  }

  .dialog-center {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 50upx 55upx;
    box-sizing: border-box;
  }
  .dialog-small-close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .dialog-title {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    font-weight: 600;
    justify-content: center;
  }
  .dialog-item {
    box-sizing: border-box;
    font-size: 32upx;
    line-height: 45upx;
    text-align: center;
    margin-top: 40rpx;
  }

  .selected {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #2e896f;
    border: 1px solid #2e896f;
    border-radius: 35rpx;
  }

  .not-selected {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #000;
    border: 1px solid #c8c8c8;
    border-radius: 35rpx;
  }

  .refund_img {
    position: absolute;
    top: 105rpx;
    right: 65rpx;
    width: 165rpx;
    height: 130rpx;
  }
  .callBtn {
    // min-width: 150rpx;
    // height: 50rpx;
    padding: 8rpx 20rpx 8rpx 20rpx;
    // padding: 8rpx;
    // line-height: 60rpx;
    text-align: center;
    color: #fff;
    font-size: 24rpx;
    background: #2e896f;
    border-radius: 30rpx;
    border: 1rpx solid #2e896f;
  }
  .orderinfo {
    position: relative;
    width: 670rpx;
    height: 400rpx;
    background: #ffffff;
    border-radius: 24rpx;
    font-size: 30rpx;
    border-radius: 24rpx;
    // padding-top: 160rpx;
    font-size: 28rpx;
    .color {
      color: #333333;
      font-size: 30rpx;
    }
    .phone {
      color: #000000;
      font-size: 50rpx;
      font-weight: 600;
    }
    .btns {
      display: flex;
      justify-content: center;
      margin-top: 20rpx;
    }
    .btn {
      width: 250rpx;
      height: 80rpx;
      border-radius: 45rpx;
      line-height: 80rpx;
      text-align: center;
      font-size: 30rpx;
    }
    .btn1 {
      color: #64a795;
      background-color: #ffffff;
      border: 1px solid #64a795;
      // margin-right: 40rpx;
      margin-left: 40rpx;
    }
    .btn2 {
      background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
      color: #fff;
    }
    .close-icon {
      position: absolute;
      top: 20rpx;
      right: 20rpx;
      width: 42rpx;
      height: 42rpx;
      line-height: 42rpx;
      text-align: center;
      border-radius: 50%;
      background: #b1b1b1;
    }
  }
  .dialog-list {
    height: 450rpx;
    overflow-y: auto;
  }
  .tag {
    background-color: #9d5bf0;
    padding: 0 10rpx;
    height: 36rpx;
    border-radius: 4rpx;
    border: 1rpx solid #9d5bf0;
    font-size: 26rpx;
    color: #ffffff;
    text-align: center;
    margin-right: 10rpx;
  }
</style>

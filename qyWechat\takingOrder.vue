<template>
  <view>
    <view class="" :style="{ height: useHeight + 'rpx' }">
      <u-navbar leftText="" title=" " :placeholder="true" bgColor="#f3f8fc">
        <view class="u-nav-slot" slot="left" @click="goBack">
          <u-icon name="arrow-left" size="19"></u-icon>
        </view>
        <view class="" slot="center">
          <u-tabs :list="tabs" lineWidth="40" lineColor="#2E896F" :current="currentIndex" @change="changeIndex"></u-tabs>
        </view>
      </u-navbar>
      <block v-if="orderList.length == 0">
        <u-empty mode="list" icon="https://document.dxznjy.com/automation/1728442200000"></u-empty>
      </block>
      <block v-else>
        <block v-if="currentIndex == 0">
          <view class="box" v-for="(item, index) in orderList" :key="index" @click="goDetail(item)">
            <view class="pd-30">
              <view class="title">
                <text
                  class="text_12"
                  :class="{
                    color4: item.sourceName.includes('主交付中心派单'),
                    color3: item.sourceName.includes('次交付中心派单'),
                    color2: item.sourceName.includes('轮排抢单'),
                    color1: item.sourceName.includes('更换小组派单')
                  }"
                >
                  {{ item.sourceName || '来新单啦～' }}
                </text>
              </view>
              <view class="item" v-for="(val, idx) in arr" :key="idx">
                <view class="label">
                  <text class="text_13">{{ val.label }}</text>
                </view>
                <view class="value">
                  <text class="text_16">{{ item[val.value] || '' }}</text>
                </view>
              </view>
            </view>
          </view>
        </block>
        <block v-if="currentIndex == 1">
          <view class="box1" v-for="(item, index) in orderList" :key="index" @click.prevent="goDetail(item)">
            <image :src="item.hasFollow ? icon2 : icon1" class="poIcon" mode="aspectFit"></image>
            <view class="pd-30">
              <view class="item" v-for="(val, idx) in arr1" :key="idx">
                <block v-if="!item.hasFollow">
                  <view class="label">
                    <text class="text_13">{{ val.label }}</text>
                  </view>
                  <view class="value">
                    <text class="text_16">{{ item[val.value] || '' }}</text>
                  </view>
                  <!-- 退单 -->
                  <!-- <view class="btn" style="justify-content: flex-end" v-if="val.value == 'expectTime'" @click.stop="backGroup(item)">退单</view> -->
                </block>
                <block v-else>
                  <view class="label">
                    <text class="text_13">{{ val.label }}</text>
                  </view>
                  <view class="value">
                    <text class="text_16">{{ item[val.value] || '' }}</text>
                  </view>
                  <!--  <view class="btn" style="justify-content: flex-end" v-if="val.value == 'expectTime'" @click.stop="setGroup(item)">建群</view> -->
                </block>
              </view>
            </view>
          </view>
        </block>
        <block v-if="currentIndex == 2">
          <view class="box2" v-for="(item, index) in orderList" :key="index" @click="goDetail(item)">
            <!-- <image :src="icon3" class="poIcon" mode="aspectFit"></image> -->
            <view class="pd-30">
              <view class="title">
                <text class="text_12">已接单,已建群～</text>
              </view>
              <view class="item" v-for="(val, idx) in arr1" :key="idx">
                <view class="label">
                  <text class="text_13">{{ val.label }}</text>
                </view>
                <view class="value">
                  <text class="text_16">{{ item[val.value] || '' }}</text>
                </view>
              </view>
            </view>
          </view>
        </block>
        <!-- 退单 -->
        <block v-if="currentIndex == 3">
          <view class="box1" v-for="(item, index) in orderList" :key="index" @click.prevent="goDetail(item)">
            <view class="pd-30">
              <view class="item" v-for="(val, idx) in arr2" :key="idx">
                <view class="label">
                  <text class="text_13">{{ val.label }}</text>
                </view>
                <view class="value">
                  <text class="text_16">{{ item[val.value] || '' }}</text>
                </view>
              </view>
            </view>
          </view>
        </block>
        <view style="height: 100rpx"></view>
      </block>
    </view>
  </view>
</template>

<script>
  import { Debounce } from '@/utils/debounce.js';
  export default {
    data() {
      return {
        currentIndex: 0,
        tabs: [
          {
            name: '未接单'
          },
          {
            name: '已接单'
          },
          {
            name: '已建群'
          },
          {
            name: '已退单'
          }
        ],
        orderList: [],
        arr: [
          {
            label: '课程名称：',
            value: 'courseName'
          },
          {
            label: '课程类型：',
            value: 'type'
          },
          {
            label: '学生姓名：',
            value: 'studentName'
          },
          {
            label: '时间：',
            value: 'expectTime'
          },
          {
            label: '接单截止：',
            value: 'expireTime'
          }
        ],
        arr1: [
          {
            label: '课程名称：',
            value: 'courseName'
          },
          {
            label: '课程类型：',
            value: 'type'
          },
          {
            label: '学生姓名：',
            value: 'studentName'
          },
          {
            label: '时间：',
            value: 'expectTime'
          }
        ],
        arr2: [
          {
            label: '课程名称：',
            value: 'courseName'
          },
          {
            label: '课程类型：',
            value: 'type'
          },
          {
            label: '学生姓名：',
            value: 'studentName'
          },
          {
            label: '时间：',
            value: 'expireTime'
          }
        ],
        icon1: 'https://document.dxznjy.com/manage/1717141601000',
        icon2: 'https://document.dxznjy.com/manage/1717141643000',
        icon3: 'https://document.dxznjy.com/manage/1717387132000',
        sendData: {},
        useHeight: 0,
        app: 0
      };
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 30;
        }
      });
    },
    onLoad(e) {
      // let that = this;
      // // #ifdef APP-PLUS
      // this.app = e.app ? e.app : '';
      // // console.log('app----------------', e.tel);
      // // if (e.token) {
      // //   uni.setStorageSync('token', e.token.includes(',') ? e.token.split(',')[0] : e.token);
      // //   uni.setStorageSync('tel', e.tel.includes(',') ? e.tel.split(',')[0] : e.tel);
      // // }
      // console.log('app----------------', uni.getStorageSync('token'));
      // // #endif
    },
    onShow() {
      let loginType = uni.getStorageSync('token');
      if (loginType) {
        this.initData();
      }
    },
    onBackPress() {
      // #ifdef APP-PLUS
      plus.runtime.quit(); // 调用退出应用
      return true; // 阻止默认返回行为
      // #endif
    },
    onUnload() {
      // #ifdef APP-PLUS
      // if (this.app) {
      plus.runtime.quit();
      // }
      // #endif
    },
    methods: {
      goBack() {
        plus.runtime.quit();
      },
      changeIndex(e) {
        this.currentIndex = e.index;
        this.orderList = [];
        uni.pageScrollTo({
          scrollTop: 0,
          duration: 300 // 动画时长，默认300ms，可以设置为0表示无动画直接跳转
        });
        this.initData();
      },
      backGroup: Debounce(function (item) {
        let that = this;
        console.log(123);
        let mobile = uni.getStorageSync('tel');
        that.$http
          .post('/deliver/web/experience/chargeback', {
            id: item.id,
            type: item.orderType,
            mobile: mobile
          })
          .then(({ data }) => {
            console.log(data);
            if (data.success) {
              uni.showToast({
                title: '操作成功'
              });
              setTimeout(() => {
                that.initData();
              }, 500);
            } else {
              uni.showToast({
                title: data.message,
                icon: 'none'
              });
            }
          });
      }, 1000),
      setGroup: Debounce(function (item) {
        let that = this;
        // uni.showLoading({
        //   title: '正在建群,请耐心等候~'
        // });
        that.$http
          .get('/deliver/web/experience/noChatDetail', {
            id: item.id,
            type: item.orderType
          })
          .then(({ data }) => {
            let obj = data.data.data;
            that.sendData = data.data.data;
            let mobile = uni.getStorageSync('tel');
            let userId = '';
            let externalUserId = '';
            if (obj.userId.length > 0) {
              userId = obj.userId.join(';');
            }
            if (obj.externalUserId.length > 0) {
              externalUserId = obj.externalUserId.join(';');
            }
            if (obj.userId.length + obj.externalUserId.length <= 2) {
              return uni.showToast({
                title: '两个人无法建立群聊,请检查人员信息',
                icon: 'none'
              });
            } else {
              that.creteGroup(userId, externalUserId, obj.chatName);
            }
          })
          .catch((err) => {
            uni.hideLoading();
          });
      }, 1000),
      // 创建群聊
      creteGroup(userId, externalUserId, chatName) {
        let that = this;
        // console.log(userId, externalUserId, chatName);
        wx.qy.openEnterpriseChat({
          // 注意：userIds和externalUserIds至少选填一个，且userIds+externalUserIds总数不能超过2000，如果externalUserIds有微信联系人，则总数不能超过40人。
          //参与会话的企业成员列表，格式为userid1;userid2;...，用分号隔开。
          userIds: userId + ';',
          // 参与会话的外部联系人列表，格式为userId1;userId2;…，用分号隔开。
          // externalUserIds: 'wm6t28KgAAWM25G-r9nX1Dwe-Un_LNxg;wm6t28KgAAKLHeTRKwKUaDAyusyLvfnw;wm6t28KgAASJJmvPheXaRfVROch0G9eQ;',
          externalUserIds: externalUserId + ';',
          groupName: chatName, // 必填，会话名称。单聊时该参数传入空字符串""即可。
          chatId: '', //新建会话时，chatId必须为空串
          success: (res) => {
            // 回调
            var chatId = res.chatId; //返回chatId仅在企业微信3.0.36及以后版本支持；
            // this.isCreate = !!chatId;
            // console.log(chatId, '1111111111111111111111111');
            that.sendData.chatId = res.chatId;
            that.$http.post('/deliver/web/learnManager/saveGroup', that.sendData).then((success) => {
              console.log(success, 'successsuccesssuccesssuccesssuccesssuccess');
              uni.showToast({
                title: '建群成功'
              });
              that.initData();
              uni.exitMiniProgram();
            });
          },
          fail: (err) => {
            // 失败处理
            uni.showToast({
              title: '建群失败',
              icon: 'none'
            });
            that.initData();
            console.log(err, '222222222222222222222222');
          }
        });
      },
      goDetail: Debounce(function (item) {
        let that = this;
        let index = that.currentIndex;
        if (item.orderType == 1) {
          uni.navigateTo({
            url: `/qyWechat/testClass?orderId=${item.id}&orderType=${index}`
          });
        } else {
          uni.navigateTo({
            url: `/qyWechat/formalClass?orderId=${item.id}&orderType=${index}`
          });
        }
      }),
      initData() {
        let that = this;
        this.$http
          .get('/deliver/web/experience/orderList', {
            type: that.currentIndex
          })
          .then(({ data }) => {
            if (data.success) {
              console.log(data.data.data);
              this.orderList = data.data.data;
              this.orderList.forEach((i) => {
                i.type = i.orderType == 1 ? '试课' : '正式课';
              });
            }
          })
          .catch((err) => {
            console.log(err);
          });
      }
    }
  };
</script>

<style lang="scss">
  page {
    background-color: #f3f8fc;
    padding-top: 40rpx;
  }

  .box {
    width: 680rpx;
    height: 375rpx;
    background: #ffffff;
    border-radius: 12rpx;
    margin: 10rpx auto;

    .item {
      display: flex;
      margin-top: 20rpx;
      color: #000;
      font-size: 28rpx;

      .label {
        min-width: 140rpx;
        margin-right: 40rpx;
      }

      .value {
        overflow: auto;
        flex: 1;
      }
    }
  }

  .box2 {
    width: 680rpx;
    height: 320rpx;
    background: #ffffff;
    border-radius: 12rpx;
    margin: 10rpx auto;
    position: relative;

    .poIcon {
      position: absolute;
      right: 0;
      width: 89rpx;
      height: 89rpx;
    }

    .item {
      display: flex;
      margin-top: 20rpx;
      color: #000;
      font-size: 28rpx;

      .label {
        min-width: 140rpx;
        margin-right: 40rpx;
      }

      .value {
        overflow: auto;
        flex: 1;
      }
    }
  }

  .box1 {
    width: 680rpx;
    height: 265rpx;
    background: #ffffff;
    border-radius: 12rpx;
    margin: 10rpx auto;
    position: relative;

    .poIcon {
      position: absolute;
      right: 0;
      width: 89rpx;
      height: 89rpx;
    }

    .item {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;
      color: #000;
      font-size: 28rpx;

      .label {
        min-width: 140rpx;
        margin-right: 10rpx;
      }

      .value {
        overflow: auto;
        flex: 1;
      }
    }

    .btn {
      width: 130rpx;
      height: 50rpx;
      line-height: 50rpx;
      text-align: center;
      font-size: 28rpx;
      color: #ffffff;
      background: #2e896f;
      border-radius: 8rpx;
      margin-left: 60rpx;
    }
  }

  .title {
    font-size: 28rpx;
    color: #000;
    font-weight: 600;
    margin-bottom: 30rpx;
  }

  .pd-30 {
    padding: 30rpx;
  }

  .color1 {
    color: #000;
  }

  .color2 {
    color: #ff4949;
  }

  .color3 {
    color: #2e896f;
  }

  .color4 {
    color: #f5a53a;
  }
</style>

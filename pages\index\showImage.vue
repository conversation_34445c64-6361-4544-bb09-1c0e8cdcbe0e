<template>
	<view class="displayFlex displayFlexCenter displayFlexVCenter" style="width: 100%;height: 100vh;">
		<image class="showImage" :src="showImage" @longpress="downloadfile" mode="widthFix"></image>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				showImage: "",
				
			}
		},
		onLoad: function(option) {
			if (option.url != undefined) {
				//#ifdef APP-PLUS
				this.showImage = decodeURIComponent(option.url);
				//#endif
				//#ifdef MP-WEIXIN
				this.showImage = decodeURIComponent(option.url);
				//#endif
				// #ifdef H5
				this.showImage = option.url;
				// #endif
			}
		},
		methods: {
			
			downloadfile(){
				this.$tool.downLoadImage(this,this.showImage)
			}
		}
	}
</script>

<style>
	.showImage{
		width: 90%;
		min-height: 300upx;
		display: flex;
	}
</style>
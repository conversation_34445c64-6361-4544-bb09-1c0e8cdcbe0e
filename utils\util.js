function formatTime(mdate, fmt) {
  if (fmt == undefined) {
    fmt = 'yyyy-MM-dd hh:mm:ss'
  }
  var date = new Date(mdate);
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
  }
  let o = {
    'M+': date.getMonth() + 1,
    'd+': date.getDate(),
    'h+': date.getHours(),
    'm+': date.getMinutes(),
    's+': date.getSeconds()
  };
  for (let k in o) {
    if (new RegExp(`(${k})`).test(fmt)) {
      let str = o[k] + '';
      fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? str : ('00' + str).substr(str.length));
    }
  }
  return fmt;
}

// 分转元
function Fen2Yuan(num) {
  num = Number(num);
  if (typeof num !== "number" || isNaN(num)) return null;
  console.log((num / 100).toFixed(2));
  return (num / 100).toFixed(2);
}

// 获取指定日期的后一天 day -1表示前一天  1表示后一天
function getNextDate(date, day) {
  var dd = new Date(date);
  dd.setDate(dd.getDate() + day);
  var y = dd.getFullYear();
  var m = dd.getMonth() + 1 < 10 ? "0" + (dd.getMonth() + 1) : dd.getMonth() + 1;
  var d = dd.getDate() < 10 ? "0" + dd.getDate() : dd.getDate();
  return y + "-" + m + "-" + d;
}

// 获取指定日期的后一个月
function getNextMonth(d) {
  let newYM;
  var date = new Date(d);
  var year = date.getFullYear();
  var month = date.getMonth() + 1;
  if (month == 12) {
    newYM = year + 1 + "-01";
  } else {
    month = (month + 1) > 9 ? month + 1 : "0" + month + 1;
    newYM = year + "-" + month;
  }
  return newYM;
}

function formatDate(mdate) {
  mdate = mdate.replace("-", "/").replace("-", "/").replace("T", " ").substr(0, 19);
  if (mdate.indexOf('1970') > -1) {
    mdate = '';
  }
  return mdate;
}
// 秒转分秒   
function formateSeconds(endTime, character) {
  console.log(endTime)
  let secondTime = parseInt(endTime) //将传入的秒的值转化为Number
  let min = 0 // 初始化分
  let result = ''
  if (secondTime >= 60) { //如果秒数大于等于60，将秒数转换成整数
    min = parseInt(secondTime / 60) //获取分钟，除以60取整数，得到整数分钟
    secondTime = parseInt(secondTime % 60) //获取秒数，秒数取佘，得到整数秒数
  }
  result = `${min.toString().padStart(2,'0')}:${secondTime.toString().padStart(2,'0')}`
  return result
}
// 秒转时分秒  character : 根据此符号分割   /// 则是时分秒汉字
function formateHour(endTime, character) {
  let secondTime = parseInt(endTime) //将传入的秒的值转化为Number
  let hour = 0,
    min = 0 // 初始化时分
  let result = ''
  if (secondTime >= 60) { //如果秒数大于等于60，将秒数转换成整数
    min = parseInt(secondTime / 60) //获取分钟，除以60取整数，得到整数分钟
    secondTime = parseInt(secondTime % 60) //获取秒数，秒数取佘，得到整数秒数
    if (min >= 60) {
      hour = parseInt(min / 60);
      min = parseInt(min % 60);
    }
  }
  if (hour > 0) {
    if (character.indexOf('///') > -1) {
      result = hour.toString() + '时' + min.toString() + '分' + secondTime.toString() + "秒";
    } else {
      result = hour.toString().padStart(2, '0') + ':' + min.toString().padStart(2, '0') + ':' + secondTime
        .toString().padStart(2, '0')
    }
  } else if (hour == 0 && min > 0) {
    if (character.indexOf('///') > -1) {
      result = min.toString() + '分' + secondTime.toString() + "秒";
    } else {
      result = '00:' + min.toString().padStart(2, '0') + ':' + secondTime.toString().padStart(2, '0')
    }
  } else {
    if (character.indexOf('///') > -1) {
      result = secondTime.toString() + "秒";
    } else {
      result = '00:00:' + secondTime.toString().padStart(2, '0')
    }
  }

  return result
}

//毫秒转时分秒
function formateHourByMillisecond(time) {
  let mill, needTime;
  if (time > 1000) {
    mill = Math.ceil(time / 1000);
    needTime = formateHour(mill, '///');
  } else {
    needTime = 0
  }
  return needTime;
}

function dateFormat(time) {
  if (time != null && time != "") {
    var time = time.replace("T", " ");
    var time_1 = time.substr(0, 19);
    if (time_1.indexOf('1970') > -1) {
      time_1 = '';
    }
    return time_1;
  }
  return "";
}

function formatLocation(longitude, latitude) {
  if (typeof longitude === 'string' && typeof latitude === 'string') {
    longitude = parseFloat(longitude)
    latitude = parseFloat(latitude)
  }

  longitude = longitude.toFixed(2)
  latitude = latitude.toFixed(2)

  return {
    longitude: longitude.toString().split('.'),
    latitude: latitude.toString().split('.')
  }
}



var dateUtils = {
  UNITS: {
    '年': 31557600000,
    '月': 2629800000,
    '天': 86400000,
    '小时': 3600000,
    '分钟': 60000,
    '秒': 1000
  },
  humanize: function(milliseconds) {
    var humanize = '';
    for (var key in this.UNITS) {
      if (milliseconds >= this.UNITS[key]) {
        humanize = Math.floor(milliseconds / this.UNITS[key]) + key + '前';
        break;
      }
    }
    return humanize || '刚刚';
  },
  format: function(dateStr) {
    var date = this.parse(dateStr)
    var diff = Date.now() - date.getTime();
    if (diff < this.UNITS['天']) {
      return this.humanize(diff);
    }
    var _format = function(number) {
      return (number < 10 ? ('0' + number) : number);
    };
    return date.getFullYear() + '/' + _format(date.getMonth() + 1) + '/' + _format(date.getDay()) + '-' +
      _format(date.getHours()) + ':' + _format(date.getMinutes());
  },
  parse: function(str) { //将"yyyy-mm-dd HH:MM:ss"格式的字符串，转化为一个Date对象
    var a = str.split(/[^0-9]/);
    return new Date(a[0], a[1] - 1, a[2], a[3], a[4], a[5]);
  }
};

function checkNetwork() {
  uni.getNetworkType({
    success: function(res) {
      if (res.networkType == 'none') {
        uni.showToast({
          icon: 'none',
          title: '咦？网去哪了',
          duration: 5000
        });
      }
    }
  });
}

function isWeixn() {
  var ua = navigator.userAgent.toLowerCase();
  if (ua.match(/MicroMessenger/i) == "micromessenger") {
    return true;
  } else {
    return false;
  }
}
// 保留两位小数
function returnFloat(value) {
  var value = Math.round(parseFloat(value) * 100) / 100;
  var xsd = value.toString().split(".");
  if (xsd.length == 1) {
    value = value.toString() + ".00";
    return value;
  }
  if (xsd.length > 1) {
    if (xsd[1].length < 2) {
      value = value.toString() + "0";
    }
    return value;
  }
}

// 数组删除(splice删除页面未刷新可以调此方法)
function deleteArrIndex(arr, index) {
  let newArr = [];
  for (let i = 0; i < arr.length; i++) {
    if (i != index) {
      newArr.push(arr[i])
    }
  }
  return newArr;
}

//复制数组
function deepCopy(arr) {
  let res = [];
  for (let i = 0; i < arr.length; i++) {
    res.push(arr[i])
  }
  return res
}

function downLoadImage(that, image) {
  uni.downloadFile({
    url: image,
    success: (result) => {
      if (result.statusCode === 200) {
        var img = result.tempFilePath;
        uni.getSetting({
          success(res) {
            if (res && res.authSetting && res.authSetting.hasOwnProperty(
                'scope.writePhotosAlbum')) {
              if (res.authSetting['scope.writePhotosAlbum']) {
                downImg(that, img);
              } else { // 拒绝授权，打开授权设置
                uni.openSetting({
                  success() {
                    downImg(that, img);
                  }
                })
              }
            } else {
              downImg(that, img);
            }
          }
        })
      }
    }
  })

}

function downImg(that, image) {
  uni.saveImageToPhotosAlbum({
    filePath: image,
    success: function() {
      that.$util.alter('海报已保存到相册')
    },
    fail: function(err) {
      that.$util.alter('保' + JSON.stringify(err))
    }
  });
}
//复制链接（pdf）
function copyMessage(value, text) {
  uni.showModal({
    icon: 'none',
    title: '这是一个外部链接',
    content: text,
    confirmText: '复制链接',
    success: function(res) {
      if (res.confirm) {
        uni.setClipboardData({
          data: value,
          success: function(res) {
            uni.getClipboardData({
              success: function(res) {
                uni.showToast({
                  icon: 'none',
                  title: "复制成功",
                });
              },
            });
          },
        });
      }
    }
  })

}





//跳转小程序展示图片
function toMiniShowImage(that, url) {
  that.$wx.miniProgram.navigateTo({
    url: `/pages/index/showImage?url=${url}`
  })
}

function countDown(bettime) {
  var sec = Math.floor(bettime / 1000);
  var day = Math.floor((Math.floor((Math.floor(sec / 60)) / 60)) / 24);
  var hours = (Math.floor((Math.floor(sec / 60)) / 60)) % 24;
  var minutes = (Math.floor(sec / 60)) % 60;
  var seconds = sec % 60;
  // if (hours.toString().length < 2) {
  // 	hours = "0" + hours;
  // }
  // if (minutes.toString().length < 2) {
  // 	minutes = "0" + minutes;
  // }
  // if (seconds.toString().length < 2) {
  // 	seconds = "0" + seconds;
  // }
  // if (day == 0) {
  // 	return hours + "时" + minutes + "分" + seconds + "秒";
  // } else {
  // 	return day + "天" + hours + "时" + minutes + "分" + seconds + "秒";
  // }
  return {
    day,
    hours,
    minutes,
    seconds
  };
}

function getWeekDay(mdate) {
  var date = new Date(mdate);
  var week = date.getDay();
  if (week == 1) {
    return "周一";
  } else if (week == 2) {
    return "周二";
  } else if (week == 3) {
    return "周三";
  } else if (week == 4) {
    return "周四";
  } else if (week == 5) {
    return "周五";
  } else if (week == 6) {
    return "周六";
  } else {
    return "周日";
  }
}

// 根据数字转周末
function getWeekDayByInt(week) {
  if (week == 1) {
    return "周一";
  } else if (week == 2) {
    return "周二";
  } else if (week == 3) {
    return "周三";
  } else if (week == 4) {
    return "周四";
  } else if (week == 5) {
    return "周五";
  } else if (week == 6) {
    return "周六";
  } else {
    return "周日";
  }
}

/**
 * 判断date1是否早于date2
 * **/
function dateCompare(date1, date2) {
  if (typeof date1 == "string") {
    date1 = new Date(date1.replace(/-/ig, "/"));
  }
  if (typeof date2 == "string") {
    date2 = new Date(date2.replace(/-/ig, "/"));
  }
  var stime = Date.parse(date1);
  var etime = Date.parse(date2);
  return stime <= etime ? true : false;
}

function sortNumber(a, b) { //升序
  return a - b
}

function getQueryValue(para, queryName) {
  var query = decodeURI(para);
  var vars = query.split("$");
  for (var i = 0; i < vars.length; i++) {
    var pair = vars[i].split("=");
    if (pair[0] == queryName) {
      return pair[1];
    }
  }
  return '';
}

function getWxCode(resolve, reject) {
  uni.login({
    provider: 'weixin',
    success: function(loginRes) {
      resolve(loginRes.code)
    },
    fail() {
      reject('fail');
    }
  });
}


function substr(title, limitLength) {
  if (title) {
    if (title.length <= limitLength) {
      return title;
    } else {
      return title.substring(0, limitLength) + "..."
    }
  }
}

import {
  http
} from '@/utils/luch-request/index.js'

function getShare(sharetype, sharecode, resolve, reject) {
  var that = this;
  uni.showLoading({
    title: '数据获取中...'
  })
  http.post('Vip/QueryShare', {
    "type": sharetype,
    "code": sharecode
  }).then(res => {
    uni.hideLoading();
    if (res != undefined) {
      resolve(res)
    } else {
      reject('fail');
    }
  })
}

// 小写数字转大写数字
function changeNumToHan(num) {
  var arr1 = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
  var arr2 = ['', '十', '百', '千', '万', '十', '百', '千', '亿', '十', '百', '千', '万', '十', '百', '千', '亿']
  if (!num || isNaN(num)) return '零'
  var english = num.toString().split('')
  var result = ''
  for (var i = 0; i < english.length; i++) {
    var des_i = english.length - 1 - i // 倒序排列设值
    result = arr2[i] + result
    var arr1_index = english[des_i]
    result = arr1[arr1_index] + result
  }
  result = result.replace(/零(千|百|十)/g, '零').replace(/十零/g, '十') // 将【零千、零百】换成【零】 【十零】换成【十】
  result = result.replace(/零+/g, '零') // 合并中间多个零为一个零
  result = result.replace(/零亿/g, '亿').replace(/零万/g, '万') // 将【零亿】换成【亿】【零万】换成【万】
  result = result.replace(/亿万/g, '亿') // 将【亿万】换成【亿】
  result = result.replace(/零+$/, '') // 移除末尾的零
  // 将【一十】换成【十】
  result = result.replace(/^一十/g, '十')
  return result
}

function openAd(opentype, openurl) {
  if (opentype == "url") {
    uni.navigateTo({
      url: '/pages/index/web?url=' + encodeURIComponent(openurl)
    })
  } else if (opentype == "mini") {
    uni.navigateTo({
      url: openurl
    })
  }
}

function getDate(type) {
  const date = new Date();

  let year = date.getFullYear();
  let month = date.getMonth() + 1;
  let day = date.getDate();

  if (type === 'start') {
    year = year - 10;
  } else if (type === 'end') {
    year = year + 10;
  }
  month = month > 9 ? month : '0' + month;;
  day = day > 9 ? day : '0' + day;

  return `${year}-${month}-${day}`;
}
// 数组去重 (字符串类型数组)
function arrDuplicateRemoval(arr) {
  let newArr = [];
  for (let i = 0; i < arr.length; i++) {
    let hasIndex = newArr.indexOf(arr[i]);
    if (hasIndex == -1) {
      newArr.push(arr[i])
    }
  }
  return newArr;
}

//数组根据长度分割成多个数组
function arrSplitLength(arr, len) {
  let index = 0;
  let newArr = [];
  while (index < arr.length) {
    newArr.push(arr.slice(index, index += len))
  }
  return newArr;
}
//根据数组转字符串用，隔开
function getStringByArr(arr, parm) {
  let newStr = "";
  if (arr.length == 0) return newStr;
  for (let i = 0; i < arr.length; i++) {
    if (parm != '') {
      newStr = newStr + arr[i][parm] + ',';
    } else {
      newStr = newStr + arr[i] + ',';
    }
  }
  newStr = newStr.substr(0, newStr.length - 1);
  return newStr;
}

//根据特殊字符切割字符串返回数组
function getArrBySymbol(str, ele) {
  let newArr = [];
  if (str.indexOf(ele) > -1) {
    newArr = str.split(ele);
  } else {
    newArr.push(str)
  }
  return newArr;
}


//计算百分比 ele1 占比数量   ele2 总数
function accDiv(ele1, ele2) {
  let needData;
  if (ele1 == 0 || ele2 == 0) {
    needData = 0;
  } else {
    needData = parseInt(ele1 * 100 / ele2)
  }
  return needData;
}

//封装get请求 请求地址  请求的参数  回调函数
function getParam(param) {
  let paramUrl = "";
  for (let key in param) {
    //url里面如果存在?
    if (paramUrl.includes('?')) {
      paramUrl += `&${key}=${param[key]}`
    } else {
      paramUrl += `?${key}=${param[key]}`
    }
  }
  return paramUrl;
}

//数组拆分
async function arrSplit(arr) {
  let list = [];
  for (let i = 0; i < arr.length; i++) {
    let item = arr[i];
    let au = "http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com";
    if (item.indexOf(au) > -1) {
      item = item.replaceAll("http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com",
        "https://document.dxznjy.com")
    }
    if (item.indexOf(',') > -1) {
      let newList = [];
      newList = item.split(',');
      list = list.concat(newList);
    } else {
      list.push(item);
    }
  }
  return list;
}
// 视频图片分开分别返回对应集合
async function separateImageVideo(arr) {
  let newArr = await arrSplit(arr);
  let afterDate = {
    'videoArr': [],
    'imageList': []
  };
  for (let i = 0; i < newArr.length; i++) {
    let item = newArr[i];
    if (item.indexOf('mp4') > -1) {
      afterDate.videoArr.push(item);
    } else {
      if (item != null && item != '') {
        afterDate.imageList.push(item);
      }
    }
  }
  return afterDate;
}


// 跳转其他小程序  
function toAtherMiniProgram(appId, url, data) {
  uni.navigateToMiniProgram({
    appId: appId,
    path: url,
    extraData: data,
    success(res) {
      // 打开成功
    },
    fail(err) {
      uni.showToast({
        icon: 'none',
        title: "打开失败，请重新打开"
      })
    }
  })
}


// 跳转登录界面  1 正常跳转  2关闭当前页跳转  3关闭所有页面跳转
function toLoginPage(isClose) {
  //debugger
  if (isClose == 1) {
    // #ifdef APP-PLUS
    uni.$exitLogin()
    plus.runtime.quit();
    // #endif
    // #ifdef MP-WEIXIN
    uni.navigateTo({
      url: '/qyWechat/login_qywx'
      // url: '/login/login_psd'
    })
    // #endif
  } else if (isClose == 2) {
    // #ifdef APP-PLUS
    uni.$exitLogin()
    plus.runtime.quit();
    // #endif
    // #ifdef MP-WEIXIN
    uni.redirectTo({
      url: '/qyWechat/login_qywx'
    })
    // #endif
  } else {
    // #ifdef APP-PLUS
    uni.$exitLogin()
    plus.runtime.quit();
    // #endif
    // #ifdef MP-WEIXIN
    uni.reLaunch({
      url: '/qyWechat/login_qywx'
    });
    // #endif
  }
}






module.exports = {
  toMiniShowImage: toMiniShowImage,
  copyMessage: copyMessage,
  formatTime: formatTime,
  getNextDate: getNextDate,
  getNextMonth: getNextMonth,
  formatDate: formatDate,
  formateSeconds: formateSeconds,
  formateHour: formateHour,
  formateHourByMillisecond: formateHourByMillisecond,
  dateFormat: dateFormat,
  getDate: getDate,
  formatLocation: formatLocation,
  dateUtils: dateUtils,
  checkNetwork: checkNetwork,
  returnFloat: returnFloat,
  deleteArrIndex: deleteArrIndex,
  deepCopy: deepCopy,
  isWeixn: isWeixn,
  downLoadImage: downLoadImage,
  downImg: downImg,
  countDown: countDown,
  dateCompare: dateCompare,
  sortNumber: sortNumber,
  getWeekDay: getWeekDay,
  getWeekDayByInt: getWeekDayByInt,
  getQueryValue: getQueryValue,
  getWxCode: getWxCode,
  substr: substr,
  getShare: getShare,
  openAd: openAd,
  arrDuplicateRemoval: arrDuplicateRemoval,
  arrSplitLength: arrSplitLength,
  getStringByArr: getStringByArr,
  getArrBySymbol: getArrBySymbol,
  accDiv: accDiv,
  getParam: getParam,
  changeNumToHan: changeNumToHan,
  separateImageVideo: separateImageVideo,
  toAtherMiniProgram: toAtherMiniProgram,
  toLoginPage: toLoginPage,
  Fen2Yuan: Fen2Yuan
}
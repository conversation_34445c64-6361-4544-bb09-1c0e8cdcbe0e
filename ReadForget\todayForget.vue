<template>
  <view>
    <view style="width: 546rpx; margin: 200rpx auto 0">
      <image src="https://document.dxznjy.com/applet/newimages/fuxi@2x_new.png" mode="widthFix" class="niuimg" style="width: 100%"></image>
    </view>
    <view class="title">今日共有{{ allNum }}节关卡需复习</view>
    <view class="checkpoint" v-if="obj.todayReviewNum != 0" @click="goCheckpoint(1)">当日{{ obj.todayReviewNum }}节关卡</view>
    <view class="checkpoint" v-if="obj.lastReviewNum != 0" @click="goCheckpoint(2)">往期{{ obj.lastReviewNum }}节关卡</view>
    <view class="plr-50 pb-50" style="margin-top: 200rpx">
      <button class="start" @click="startReview">开始复习</button>
      <view class="temporarily mt-30 f-30" @click="closeReview">暂不复习</view>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        studentCode: '',
        merchantCode: '',
        obj: {},
        allNum: 0
      };
    },
    onLoad(options) {
      this.studentCode = options.studentCode;
      this.merchantCode = options.merchantCode;
    },
    onShow() {
      this.init();
    },
    methods: {
      async init() {
        let { data } = await uni.$http.get('/znyy/superReadReview/getReviewNumber?studentCode=' + this.studentCode + '&merchantCode=' + this.merchantCode);
        if (data.success) {
          this.obj = data.data;
          this.allNum = data.data.todayReviewNum - 0 + (data.data.lastReviewNum - 0);
          if (this.allNum == 0) {
            uni.navigateBack();
          }
        }
      },
      closeReview() {
        uni.navigateBack();
      },
      startReview() {
        uni.navigateTo({
          url: `/ReadForget/ReviewCheckPoint?studentCode=${this.studentCode}&merchantCode=${this.merchantCode}`
        });
      },
      goCheckpoint(e) {
        console.log('---------------------------------------')
        if (e == 1 && this.obj.todayReviewNum < 1) {
          console.log('1111111111111111111111')
          return;
        }
        if (e == 2 && this.obj.lastReviewNum < 1) {
          console.log('2222222222222222222222222222')
          return;
        }
        uni.navigateTo({
          url: `/ReadForget/forgetReview?studentCode=${this.studentCode}&merchantCode=${this.merchantCode}&type=${e}`
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .title {
    margin-top: 20rpx;
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
  }
  .checkpoint {
    text-align: center;
    margin: 40rpx 0;
    font-size: 36rpx;
    color: #2e896f;
  }
  .start {
    border-radius: 50rpx;
    color: #fff;
    font-size: 30rpx;
    height: 90rpx;
    background: #2e896f;
    line-height: 90rpx;
  }

  .temporarily {
    text-align: center;
    color: #999;
  }
</style>

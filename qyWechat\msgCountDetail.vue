<template>
  <view :style="{ height: useHeight + 'rpx' }">
    <view class="container">
      <view class="pd-30">
        <view class="item" v-for="(item, index) in list" :key="index">
          <view class="title">
            <view class="name">{{ item.title }}</view>
            <view class="time">{{ item.time }}</view>
          </view>
          <block v-if="item.mistake && item.mistake.length > 0">
            <view class="msg" v-for="(val, idx) in item.mistake">{{ idx + 1 }}{{ '、' }} {{ val }}</view>
          </block>
          <block v-else>
            <view class="msg">无异常</view>
          </block>
          <view class="line"></view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      useHeight: 0,
      list: [],
      queryData: {}
    };
  },
  onReady() {
    uni.getSystemInfo({
      success: (res) => {
        // 可使用窗口高度，将px转换rpx
        let h = res.windowHeight * (750 / res.windowWidth);
        this.useHeight = h - 30;
      }
    });
  },
  onLoad(e) {
    // console.log(JSON.parse(decodeURIComponent(e.item)));
    this.queryData = JSON.parse(decodeURIComponent(e.item));
    this.queryData.endTime = e.endTime;
    this.initData();
  },
  methods: {
    // 复制
    initData() {
      this.$scrmHttp
        .get('/scrm/evaluate/statisticsDetail', {
          key: this.queryData.key,
          time: this.queryData.time,
          type: this.queryData.type,
          endTime: this.queryData.endTime
        })
        .then(({ data }) => {
          console.log(data.data);
          this.list = data.data.teacherStatisticsVos;
        });
    }
  }
};
</script>

<style lang="scss">
.container {
  width: 690rpx;
  min-height: 1000rpx;
  background: #ffffff;
  border-radius: 14rpx;
  margin: 30rpx auto;
  position: relative;
}
.pd-30 {
  padding: 30rpx;
  line-height: 1.4;
}
.item {
  margin-top: 20rpx;
}

.title {
  display: flex;
  justify-content: space-between;

  .name {
    font-size: 30rpx;
    color: #000;
  }
  .time {
    color: #666;
    font-size: 28rpx;
  }
}
.msg {
  color: #666;
  font-size: 28rpx;
  margin-top: 20rpx;
}
.btn {
  color: #2e896f;
  font-size: 28rpx;
  margin-top: 30rpx;
}
.line {
  width: 630rpx;
  border: 1rpx solid #efefef;
  margin: 30rpx auto;
}
</style>

<template>
	<view class="ctxt bg-ff"  :style="{ height: useHeight + 'rpx' }" >
		<!-- <view class="bg-h">
			<view class="positioning" @click="goback">
				<uni-icons type="left" size="24" color="#000" ></uni-icons>
			</view>
			<view class="word-position t-c col-12" style="">
				<view  class="f-34">拼音法抗遗忘</view>
			</view>
		</view> -->
		<view class="plr-15 bg-ff radius-20 ptb-30" >
			<view class="midTxt">
				<image :src="img1" mode="scaleToFill" style="width: 750rpx; height: 634rpx;"></image>
			</view>
		</view>
		<view class="botBtn pr-35 ptb-30" :style="{marginTop: marginTop + 'rpx'}">
			<view class="btn_b b_r f-30" @click="goToday">
				今日复习
			</view>
			<view class="btn_b b_l f-30" @click="goPast">
				往期复习
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				img1:"https://document.dxznjy.com/course/ab25daa95bff4cecbc6452fdfb5ac814.png",
				studentCode: '', //学员Code
				wordList:{},
				useHeight:0,
				showBtn:false,
				marginTop:0,
			}
		},
		onLoad() {
			this.studentCode = uni.getStorageSync('pyfStudentCode')
		},
		onReady() {
			uni.getSystemInfo({
				success: (res) => {
				// 可使用窗口高度，将px转换rpx
				 this.useHeight = res.windowHeight * (750 / res.windowWidth);
				 if(this.useHeight-1230>0){
					this.marginTop=this.useHeight-1230
				 }
				}
			});
		},
		methods: {
			async goToday() {
				let res =await this.$http.post('/znyy/pd/planReview/queryReviewedCount', {
					studentCode: this.studentCode
				});
				// console.log('this.wordList',this.wordList)
				// console.log('res------',res)
				if (res.data.success) {
					this.wordList = res.data.data
					console.log('this.wordList',this.wordList)
					if (this.wordList.sumReviewCount == 0) {
						uni.showToast({
							icon: 'none',
							title: '今日无可复习课程',
							duration: 2000
						});
					}else{
						uni.redirectTo({
							url: '/PYFpages/todayReview'
						})
					}
				}
				
				
			},
			goPast() {
				uni.navigateTo({
					url: '/PYFpages/pastReview'
				})
			},
			goback() {
				// 跳到复习课程表页面
				uni.switchTab({
				  url: "/pages/review/index",
				});
			},
		},
	}
</script>

<style scoped>
	.ctxt {
		background: #fff;
		overflow: hidden;
		overflow-y: scroll;
	}

	/* .bg-h{
		position: fixed; 
		  top: 0; 
		  left: 0;
		  width: 100%;
		  z-index: 8; 
	} */
	
	.positioning {
		position: fixed;
		top: 110rpx;
		left: 30rpx;
		z-index: 9;
	}
	
	.word-position {
		position: fixed;
		top: 0;
		left: 0;
		background-color: #f3f8fc;
		height: 190rpx;
		padding-top: 110rpx;
		box-sizing: border-box;
	}

	.midTxt {
		margin-top: 50%;
	}
	
	.midTit{
		font-size:40rpx ;
		line-height: 56rpx;
		color: #428A6F;
		font-weight: 900;
	}

	.lc_yellow {
		color: #FD9B2A;
		text-align: center;
		width: 116rpx;
		border: 1px solid #FFE1BE;
		background-color: #fdf6ed;
		overflow: visible;
		border-radius: 8rpx;
		margin-left: 32rpx;
	}

	.flex-self-s {
		margin-bottom: 40rpx;
	}

	.lc_gray{
		color: #CCCCCC;
		border: 1px solid #CCCCCC;
		background-color: #f7f7f7;
	}
	.botBtn{
		display: flex;
		flex-direction: row-reverse;
		
	}
	.btn-css{
		position: absolute;
		bottom: 60rpx;
		right: 32rpx; 
	}
	.btn_b{
		width: 328rpx;
		height: 92rpx;
		border-radius: 60rpx;
		line-height: 92rpx;
		text-align: center;
		
	}
	.b_l{
		background-color: #fff;
		color: #4E9F87;
		border: 1px solid #7BAEA0;
	}
	.b_r{
		background-image: linear-gradient(to bottom, #88CFBA, #1D755C);
		color: #FFFFFF ;
		margin-left: 32rpx;
	}
</style>
<template>
  <view>
    <view class="container" :style="{ height: useHeight + 'rpx' }">
      <movable-area class="movableArea">
        <movable-view class="movableView" :position="position" :x="x" :y="y" direction="all" damping="10" @change="onChange" @tap="onTap" @touchend="onTouchend">
          <view class="floatBox">
            <view class="floatText">选择</view>
            <view class="floatText">小组</view>
          </view>
        </movable-view>
      </movable-area>
      <!--      <view
        class="float-button"
        :style="{ top: buttonTop + 'rpx', left: buttonLeft + 'rpx' }"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
        @click="openPop"
      >
        <view class="floatBox">
          <view class="floatText">选择</view>
          <view class="floatText">小组</view>
        </view>
      </view> -->
      <u-navbar leftText="" title=" " :placeholder="true" bgColor="#f3f8fc" height="30">
        <view class="u-nav-slot" slot="left" @click="goBack">
          <u-icon name="arrow-left" size="19"></u-icon>
        </view>
        <view class="" slot="center">
          <u-tabs :list="experienceList" lineWidth="40" lineColor="#2E896F" :current="curNow" @change="changeType"></u-tabs>
        </view>
      </u-navbar>
      <view class="content" :style="{ height: useHeight - 200 + 'rpx' }">
        <view class="fixTop">
          <u-subsection :list="tabs" activeColor="#2E896F" mode="subsection" :current="currentIndex" @change="changeIndex"></u-subsection>
        </view>
        <view style="height: 60rpx"></view>
        <block v-if="Data.length == 0">
          <u-empty mode="list" icon="https://document.dxznjy.com/automation/1728442200000"></u-empty>
        </block>
        <block v-else>
          <block v-if="currentIndex == 0">
            <view class="box" v-for="(item, index) in Data" :key="index" @click="goDetail(item)">
              <view class="pd-30">
                <view class="item" v-for="(val, idx) in arr" :key="idx">
                  <view class="label">
                    <text class="text_13">{{ val.label }}</text>
                  </view>
                  <view class="value">
                    <text class="text_16">{{ item[val.value] || '' }}</text>
                  </view>
                </view>
              </view>
            </view>
          </block>
          <block v-if="currentIndex == 1">
            <view class="box1" v-for="(item, index) in Data" :key="index" @click.prevent="goDetail(item)">
              <image :src="item.hasChat == 1 ? icon3 : item.hasFollow ? icon2 : icon1" class="poIcon" mode="aspectFit"></image>
              <view class="pd-30">
                <view class="item" v-for="(val, idx) in arr1" :key="idx">
                  <view class="label">
                    <text class="text_13">{{ val.label }}</text>
                  </view>
                  <view class="value">
                    <text class="text_16" v-if="val.value == 'studentInfo'">
                      {{ item.studentName + '(' + item.studentCode + ')' || '' }}
                    </text>
                    <text class="text_16" v-else-if="val.value == 'referrerInfo'">
                      {{ item.referrerName + '(' + item.referrerMobile + ')' || '' }}
                    </text>
                    <text class="text_16" v-else>{{ item[val.value] || '' }}</text>
                  </view>
                </view>
              </view>
            </view>
          </block>
          <!-- 退单 -->
          <block v-if="currentIndex == 2">
            <view class="box" v-for="(item, index) in Data" :key="index" @click.prevent="goDetail(item)">
              <view class="pd-30">
                <view class="item" v-for="(val, idx) in arr2" :key="idx">
                  <view class="label">
                    <text class="text_13">{{ val.label }}</text>
                  </view>
                  <view class="value">
                    <text class="text_16">{{ item[val.value] || '' }}</text>
                  </view>
                </view>
              </view>
            </view>
          </block>
          <u-loadmore :status="status" @loadmore="loadmore" loadmoreText="点击加载更多 " dashed line />
          <view style="height: 100rpx"></view>
        </block>
      </view>
    </view>
    <!-- 选择角色弹窗 -->
    <uni-popup ref="popopChooseStudent" type="center" :safe-area="false" :isMaskClick="false">
      <view class="dialogBG">
        <view class="dialog-all">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="dialog-small-close" @click="cancelStudent">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="dialog-center">
            <view class="dialog-title pb-10">{{ dialogTitle }}</view>
            <view class="dialog-list">
              <u-empty v-if="teamList.length < 1" mode="list" text="暂无小组" icon="https://document.dxznjy.com/automation/1728442200000"></u-empty>
              <view
                v-else
                class="dialog-item"
                @click="chooseStudentlist(item, index)"
                v-for="(item, index) in teamList"
                :key="index"
                :class="isactive == index ? 'selected' : 'not-selected'"
              >
                {{ item.teamName }}
              </view>
            </view>
            <view class="flex-c mt-40" v-if="teamList.length > 0">
              <view class="common-sure-btn-1" @click="confirmStudent()">确定</view>
              <view class="common-cancel-btn ml-40" @click="cancelStudent()">取消</view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import { Debounce } from '@/utils/debounce.js';
  export default {
    data() {
      return {
        currentIndex: 0,
        tabs: [{ name: '未接单' }, { name: '已接单' }, { name: '已退单' }],
        curNow: 0,
        experienceList: [{ name: '试课' }, { name: '正式课' }],
        Data: [],
        arr: [
          { label: '课程名称：', value: 'courseName' },
          { label: '时间：', value: 'expectTime' },
          { label: '接单截止：', value: 'expireTime' }
        ],
        arr1: [
          { label: '学员姓名：', value: 'studentInfo' },
          { label: '首次课时间：', value: 'expectTime' },
          { label: '教练：', value: 'teacherName' },
          { label: '推荐人：', value: 'referrerInfo' }
        ],
        arr2: [
          { label: '课程名称：', value: 'courseName' },
          { label: '首次课时间：', value: 'expireTime' },
          { label: '退单教练：', value: 'teacherName' }
        ],
        queryData: {
          pageNum: 1,
          pageSize: 10,
          type: 0, //0 1 2
          experience: 1, //1 试课 2正课
          teamId: ''
        },
        dialogTitle: '选择小组',
        teamList: [],
        isactive: -1,
        teamId: '',
        icon1: 'https://document.dxznjy.com/manage/1717141601000',
        icon2: 'https://document.dxznjy.com/manage/1717141643000',
        icon3: 'https://document.dxznjy.com/manage/1717387132000',
        useHeight: 0,
        total: 0,
        buttonTop: 930, // 初始位置的垂直坐标
        buttonLeft: 630, // 初始位置的水平坐标
        startX: 0, // 触摸开始的水平坐标
        startY: 0, // 触摸开始的垂直坐标
        isMoving: false, // 是否正在移动
        x: 630,
        y: 430,
        x1: 0,
        x2: 0,
        y1: 0,
        y2: 0,
        move: {
          x: 0,
          y: 0
        },
        position: 4,
        status: 'loadmore'
      };
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          this.x1 = 0;
          this.x2 = parseInt(res.windowWidth) - 50;
          this.y1 = 0;
          this.y2 = parseInt(res.windowHeight) - 20;
          setTimeout(() => {
            if (this.position == 1 || this.position == 2) this.y = parseInt(this.y2 * 0.2);
            if (this.position == 3 || this.position == 4) this.y = parseInt(this.y2 * 0.8);
            if (this.position == 1 || this.position == 3) this.x = parseInt(this.x1);
            if (this.position == 2 || this.position == 4) this.x = parseInt(this.x2);
            this.move.x = this.x;
            this.move.y = this.y;
          }, 1000);
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 30;
        }
      });
    },
    onLoad() {
      let that = this;
      wx.qy.checkSession({
        success: function () {
          //session_key 未过期，并且在本生命周期一直有效
          // that.initData();
          that.getRole();
        },
        fail: function () {
          that.$store.commit('quit');
          // #ifdef APP-PLUS
          plus.runtime.quit();
          // #endif
          // #ifdef MP-WEIXIN
          uni.navigateTo({
            url: '/qyWechat/login_qywx'
          });
          // #endif
        }
      });
      uni.removeStorageSync('isCreate');
    },
    onShow() {
      uni.removeStorageSync('isCreate');
      let loginType = uni.getStorageSync('token');
      if (loginType) {
        if (this.teamId) {
          this.initData();
        } else {
          this.getRole();
        }
      }
    },
    onReachBottom() {
      this.loadmore();
    },
    methods: {
      loadmore() {
        let that = this;
        if (that.total == 0 || that.total == that.Data.length) {
          that.status = 'nomore';
          return;
        }
        if (that.total > that.Data.length) {
          that.status = 'loadmore';
          that.queryData.pageNum++;
          that.$http
            .get('/deliver/web/experience/leaderOrderList', that.queryData)
            .then(({ data }) => {
              if (data.success) {
                let arr = data.data.data;
                that.Data = that.Data.concat(arr.data);
                that.total = arr.totalItems * 1;
              }
            })
            .catch((err) => {
              console.log(err);
            });
        }
      },
      onChange(e) {
        if (e.detail.source === 'touch') {
          this.move.x = e.detail.x;
          this.move.y = e.detail.y;
        }
      },
      onTap(e) {
        // console.log('Tap event');
        this.$refs.popopChooseStudent.open();
        // 在这里处理单击事件的逻辑
        // 例如打开链接、执行动作等
      },
      onTouchend() {
        this.x = this.move.x;
        this.y = this.move.y;
        setTimeout(() => {
          if (this.move.x < this.x2 / 2) this.x = this.x1;
          else this.x = this.x2;
          // console.log('yuan' + this.x, this.y);
        }, 100);
      },
      openPop() {
        this.$refs.popopChooseStudent.open();
      },
      getRole() {
        let that = this;
        // #ifdef APP-PLUS
        if (uni.getStorageSync('isAppRoleValue') == 'DeliverTeamLeader') {
          that.initData();
        }
        // #endif
        // #ifdef MP-WEIXIN
        let mobile = uni.getStorageSync('tel');
        that.$http
          .get('/deliver/web/teacher/role/check', {
            mobile
            // mobile: '18073445472'
          })
          .then(({ data }) => {
            if (data.data) {
              that.initGroup();
            } else {
              uni.showToast({
                title: '请用小组组长身份进入',
                icon: 'none'
              });
              setTimeout(() => {
                uni.reLaunch({
                  url: '/pages/index/index'
                });
              }, 1000);
            }
          });
        // #endif
      },
      initGroup() {
        let that = this;
        let mobile = uni.getStorageSync('tel');
        that.$http
          .get('/deliver/web/team/getTeamByMobile', {
            // mobile: '18073445472'
            mobile
          })
          .then(({ data }) => {
            // console.log(data);
            let arr = data.data;
            if (arr.length > 0) {
              that.teamList = arr;
              that.$refs.popopChooseStudent.open();
            } else {
              uni.showToast({
                title: '暂无小组',
                icon: 'none'
              });
            }
          });
      },
      chooseStudentlist(item, index) {
        this.isactive = index;
        this.teamId = item.id;
        this.queryData.teamId = item.id;
      },
      cancelStudent: Debounce(function () {
        this.isactive = -1;
        this.$refs.popopChooseStudent.close();
      }),
      confirmStudent: Debounce(function () {
        let that = this;
        if (that.isactive == -1) {
          uni.showToast({
            icon: 'none',
            title: '请先选择小组~'
          });
          return;
        } else {
          that.queryData.pageNum = 1;
          that.initData();
          that.cancelStudent();
        }
      }),
      goBack() {
        uni.switchTab({
          url: '/pages/my/my'
        });
      },
      changeType(e) {
        // console.log(e);
        this.queryData.pageNum = 1;
        this.status = 'loadmore';
        this.curNow = e.index;
        this.queryData.experience = e.index + 1;
        this.initData();
      },
      changeIndex(e) {
        // console.log(e, '1111111111111');
        this.queryData.pageNum = 1;
        this.status = 'loadmore';
        this.currentIndex = e;
        this.queryData.type = e;
        this.Data = [];
        this.initData();
      },
      initData() {
        let that = this;
        if (that.teamId) {
          that.$http
            .get('/deliver/web/experience/leaderOrderList', that.queryData)
            .then(({ data }) => {
              if (data.success) {
                // console.log(data.data.data);
                let arr = data.data.data;
                that.Data = arr.data;
                that.total = arr.totalItems * 1;
              }
            })
            .catch((err) => {
              console.log(err);
            });
        } else {
          if (that.teamList.length < 1) {
            return uni.showToast({
              title: '暂无小组',
              icon: 'none'
            });
          }
        }
      },
      goDetail: Debounce(function (item) {
        let that = this;
        let index = that.currentIndex;
        if (item.orderType == 1) {
          uni.navigateTo({
            url: `/qyWechat/testClass?orderId=${item.id}&orderType=2`
          });
        } else {
          uni.navigateTo({
            url: `/qyWechat/formalClass?orderId=${item.id}&orderType=2`
          });
        }
      }),
      handleTouchMove(event) {
        if (this.isMoving) {
          const touch = event.touches[0];
          this.buttonTop = touch.clientY - this.startY;
          this.buttonLeft = touch.clientX - this.startX;
        }
      },
      handleTouchStart(event) {
        const touch = event.touches[0];
        this.startX = touch.clientX - this.buttonLeft;
        this.startY = touch.clientY - this.buttonTop;
        this.isMoving = true;
      },
      handleTouchEnd() {
        this.isMoving = false;
      }
    }
  };
</script>

<style lang="scss">
  page {
    background-color: #f3f8fc;
    padding-top: 40rpx;
  }
  .container {
    position: relative;
    width: 100%;
  }
  .content {
    height: 100vh;
    // margin: 60rpx auto 0;
    overflow-y: auto;
    position: relative;
    // padding-top: 50rpx;
    .fixTop {
      position: fixed;
      width: 100%;
      // top: 120rpx;
      background-color: #fff;
      z-index: 98;
    }
  }
  .float-button {
    position: absolute;
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
    background-color: #2e896f;
    color: #fff;
    text-align: center;
    line-height: 100rpx;
    user-select: none;
    touch-action: none;
    word-wrap: break-word;
    overflow-wrap: break-word;
    white-space: normal;
    .floatBox {
      width: 100rpx;
      height: 100rpx;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .floatText {
        font-size: 26rpx;
        height: 30rpx;
        line-height: 30rpx;
        text-align: center;
      }
    }
  }
  .box {
    width: 680rpx;
    height: 240rpx;
    background: #ffffff;
    border-radius: 12rpx;
    margin: 20rpx auto;
    .item {
      display: flex;
      margin-top: 20rpx;
      color: #000;
      font-size: 28rpx;
      .label {
        min-width: 170rpx;
        margin-right: 40rpx;
      }
      .value {
        flex: 1;
        overflow: auto;
      }
    }
  }
  .box1 {
    width: 680rpx;
    height: 280rpx;
    background: #ffffff;
    border-radius: 12rpx;
    margin: 20rpx auto;
    position: relative;
    .poIcon {
      position: absolute;
      right: 0;
      width: 89rpx;
      height: 89rpx;
    }
    .item {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;
      color: #000;
      font-size: 28rpx;
      .label {
        // min-width: 140rpx;
        min-width: 170rpx;
        margin-right: 10rpx;
      }
      .value {
        overflow: auto;
        flex: 1;
      }
    }
    .btn {
      width: 130rpx;
      height: 50rpx;
      line-height: 50rpx;
      text-align: center;
      font-size: 28rpx;
      color: #ffffff;
      background: #2e896f;
      border-radius: 8rpx;
      margin-left: 60rpx;
    }
  }
  .title {
    font-size: 28rpx;
    color: #000;
    font-weight: 600;
    margin-bottom: 30rpx;
  }

  .pd-30 {
    padding: 30rpx;
  }
  .color1 {
    color: #000;
  }
  .color2 {
    color: #ff4949;
  }
  .color3 {
    color: #2e896f;
  }
  .color4 {
    color: #f5a53a;
  }
  .dialogBG {
    margin: 0 30rpx 30rpx 30rpx;
    background-color: #fff;
    border-radius: 14rpx;
  }
  .dialog-all {
    width: 670rpx;
    position: relative;
  }

  .dialog-all image {
    width: 100%;
    height: 100%;
  }
  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 170rpx;
    z-index: -1;
  }
  .dialog-small-close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .dialog-center {
    position: relative;
    width: 100%;
    height: 100%;

    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 50upx 55upx;
    box-sizing: border-box;
  }
  .dialog-title {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    font-weight: 600;
    justify-content: center;
  }
  .dialog-list {
    height: 450rpx;
    overflow-y: auto;
  }
  .dialog-item {
    box-sizing: border-box;
    font-size: 32upx;
    line-height: 45upx;
    text-align: center;
    margin-top: 40rpx;
  }

  .selected {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #2e896f;
    border: 1px solid #2e896f;
    border-radius: 35rpx;
    overflow: visible;
  }

  .not-selected {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #000;
    border: 1px solid #c8c8c8;
    border-radius: 35rpx;
    overflow: visible;
    transform: scale(0.995); /* 解决ios上圆角失效 */
  }
  .common-sure-btn-1 {
    width: 250rpx;
    height: 80rpx;
    background: #2e896f;
    border-radius: 40rpx;
    font-size: 30rpx;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(0.995); /* 解决ios上圆角失效 */
  }
  .common-cancel-btn {
    width: 250rpx;
    height: 80rpx;
    border-radius: 40rpx;
    border: 1rpx solid #2e896f;
    font-size: 30rpx;
    color: #2e896f;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(0.995); /* 解决ios上圆角失效 */
  }
  .movableArea {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 999;
  }

  .movableView {
    pointer-events: auto;
    width: 80rpx;
    height: 80rpx;
    padding: 10rpx;
    border-radius: 100%;
    background-color: #2e896f;
    color: #fff;
    text-align: center;
    line-height: 100rpx;
    user-select: none;
    touch-action: none;
    word-wrap: break-word;
    overflow-wrap: break-word;
    white-space: normal;
    .floatBox {
      width: 80rpx;
      height: 80rpx;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .floatText {
        font-size: 26rpx;
        height: 30rpx;
        line-height: 30rpx;
        text-align: center;
      }
    }
  }
</style>

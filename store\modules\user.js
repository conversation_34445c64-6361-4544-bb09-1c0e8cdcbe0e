export default {
  state: {
    token: uni.getStorageSync('token'),
    scrm_token: uni.getStorageSync('scrm_token'),
    userInfo: uni.getStorageSync('userInfo') || {},
    userRole: "", // 用户角色 assistant 教练  acsdemicManagement学管师 teacher教师
  },
  mutations: {
    setToken(state, data) {
      uni.setStorageSync('token', data)
      state.token = data
    },
    setToken1(state, data) {
      uni.setStorageSync('scrm_token', data)
      state.token = data
    },
    setUserInfo(state, data) {
      uni.setStorageSync('userInfo', data)
      state.userInfo = data
    },
    setUserRole(state, data) {
      uni.setStorageSync('userRole', data)
      state.userRole = data
    },
    quit(state) {
      state.userInfo = {}
      state.token = null
      state.userRole = "";
      uni.removeStorageSync('token')
      uni.removeStorageSync('scrm_token')
      uni.removeStorageSync('userInfo')
      uni.removeStorageSync('userRole')
      uni.setStorageSync('isLogin', false);
    }
  }
}
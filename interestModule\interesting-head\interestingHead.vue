<template>
  <view class="funHead">
    <view class="funHead_left back_top backSize" @click="backPage()">
      <image
        style="height: 30rpx; width: 18rpx"
        :src="
          !closeWhite
            ? 'https://document.dxznjy.com/applet/newInteresting/interesting_back_black.png'
            : 'https://document.dxznjy.com/applet/newInteresting/interesting_back_white.png'
        "
        mode=""
      ></image>
    </view>

    <view v-if="title != ''" class="funHead_center">
      <text :class="hasTitleBg ? 'funHead_center_text_bg' : 'funHead_center_text'">{{ title }}</text>
    </view>
    <view v-if="hasRight" class="funHead_right" @click="clickHeadRight()">
      <text>{{ rightText }}</text>
    </view>
  </view>
</template>

<script>
  export default {
    props: {
      title: '',
      hasTitleBg: false,
      closeWhite: false,
      hasRight: false,
      rightText: ''
    },
    methods: {
      clickHeadRight() {
        this.$emit('clickHeadRight');
      },

      backPage() {
        this.$emit('backPage');
      }
    }
  };
</script>

<style>
  .backSize {
    height: 70rpx;
    width: 60rpx;
  }
  .back_top {
    /* #ifdef APP-PLUS */
    padding-top: 50rpx;
    /* #endif */
  }

  .funHead {
    padding-top: 50rpx;
    text-align: center;
    position: relative;
    height: 120rpx;
    line-height: 120rpx;
  }

  .funHead view {
    display: block;
    text-align: center;
    font-size: 26rpx;
    color: #ffffff;
  }

  .funHead_left {
    position: absolute;
    top: 55rpx;
    left: 0;
  }

  .funHead_center {
    /* #ifdef APP-PLUS */
    padding-top: 40rpx;
    /* #endif */
  }

  .funHead_center_text {
    font-size: 34rpx;
    color: #ffffff;
  }

  .funHead_center_text_bg {
    display: inline-block;
    height: 60rpx;
    width: 200rpx;
    margin-top: 10rpx;
    line-height: 60rpx;
    font-size: 34rpx;
    color: #ffffff;
    background-color: rgba(0, 0, 0, 0.4);
    border-radius: 50rpx;
  }

  .funHead_right {
    position: absolute;
    right: 0;
    top: 110rpx;
    width: 160rpx;
    height: 90rpx;
    line-height: 90rpx;
    background: url('https://document.dxznjy.com/applet/interesting/title_btn_l.png') no-repeat;
    background-size: 100% 100%;
    overflow-x: auto;
  }
</style>

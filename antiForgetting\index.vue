<template>
  <view>
    <view class="center-banner">
      <!-- 顶部文字 -->
      <view class="top">
        <view class="left-text">
          <text>21方格</text>
          <text>联动抗遗忘复习系统</text>
        </view>
        <view class="right-logo">
          <image src="https://document.dxznjy.com/applet/newimages/dingxiaologo.png" mode=""></image>
        </view>
      </view>
      <image src="https://document.dxznjy.com/applet/newimages/niuniu.png" class="images"></image>
      <!-- 底部今日复习和往期记录 -->
      <view class="footer">
        <view class="left-btn fot-btn" @click="gohistory()">往期记录</view>
        <view class="right-btn fot-btn" @click="golist()">今日复习</view>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        array: [
          {
            studentCode: '',
            realName: '选择学员'
          }
        ],
        index: 0,
        studentCode: '',
        StudentCodeKey: '',
        flag: false, //防止多次点击
        // 语法 21 天抗遗忘
        reviewTypes: [
          { name: '单词', type: 'word' },
          { name: '语法', type: 'grammar' }
        ],
        // { name: '阅读理解', type: 'reading' }  暂时不做
        reviewType: '', // 复习类型
        showMask: false, // 控制遮罩层的显示与隐藏
        activeIndex: -1,
        Buttonclick: '',
        buttonclickName: '',
        selectedType: 'word',
        deliverMerchant: '',
        merchantCode: ''
      };
    },
    mounted() {
      // this.$refs.reviewTypePopup.open()
    },
    onLoad(option) {
      const buttonClick = decodeURIComponent(option.buttonClick || '');
      this.selectedType = option.type;
      if (buttonClick && buttonClick != '') {
        this.studentCode = buttonClick;
        uni.setStorageSync('studentCode', buttonClick);
      } else {
        if (option.studentCode) {
          this.studentCode = option.studentCode;
          uni.setStorageSync('studentCode', buttonClick);
        } else {
          const storedStudentCode = uni.getStorageSync('studentCode');
          if (storedStudentCode) {
            this.studentCode = storedStudentCode;
          }
        }
      }
      const buttonclickName = decodeURIComponent(option.buttonclickName || '');
      if (buttonclickName) {
        uni.setStorageSync('21StudentName', option.buttonclickName);
      }

      this.merchantCode = option.merchantCode;
      this.deliverMerchant = option.deliverMerchant;

      // this.loadData();
      // var that = this;
      // uni.$on('LoginSuccess', function(data) {
      // 	that.loadData();
      // });
      // uni.$on('LoginOut', function(data) {
      // 	that.loadData();
      // });
    },

    // onUnload() {
    // 移除本地存储中的 studentCode 值
    // 	uni.removeStorageSync('studentCode');
    // },
    // onShow() {
    // 	// 从URL参数中获取传递的数据
    // 	const buttonClick = decodeURIComponent(option.buttonClick);
    // 	this.studentCode = buttonClick
    // 	console.log(buttonClick, 'buttonClick');

    // },
    methods: {
      // 处理选择按钮点击事件
      handleButtonClick(item, index) {
        this.activeIndex = index;
        this.Buttonclick = item.studentCode;
        this.buttonclickName = item.realName;
        this.reviewType = item.type;
      },
      // 选择学员弹窗清除之前选中样式
      handleClearData() {
        this.activeIndex = -1;
        this.Buttonclick = '';
        this.buttonclickName = '';
      },
      async loadData() {
        var loginMobile = uni.getStorageSync('LoginMobile');
        console.log(loginMobile);
        this.StudentCodeKey = 'review_' + loginMobile;
        let result = await uni.$http.get('/znyy/review/query/my/student');
        console.log(result, 'result');
        if (result != undefined && result != '') {
          if (result.data.data != null) {
            console.log(result.data.data, 'result.data.data');
            this.array = [
              {
                studentCode: '',
                realName: '选择学员'
              }
            ];
            if (result.data.data.length > 0) {
              if (result.data.data.length == 1) {
                this.array = [];
                this.index = 0;
                this.array = this.array.concat(result.data.data);
                this.studentCode = this.array[this.index].studentCode;
                uni.setStorageSync(this.StudentCodeKey, this.studentCode);
              } else {
                var that = this;
                that.array = that.array.concat(result.data.data);
                var array = that.array;
                if (uni.getStorageSync(this.StudentCodeKey) != '') {
                  var studentCode = uni.getStorageSync(this.StudentCodeKey);
                  array.forEach(function (item, index) {
                    if (item.studentCode == studentCode) {
                      that.index = index;
                      that.studentCode = studentCode;
                    }
                  });
                }
              }
            }
          }
        }
      },
      // 选择学员
      // bindPickerChange: function(e) {
      // 	this.index = e.target.value;
      // 	this.studentCode = this.array[this.index].studentCode;
      // 	uni.setStorageSync(this.StudentCodeKey, this.studentCode);
      // },
      golist() {
        if (this.studentCode == '') {
          this.$util.alter('请先选择学员');
          return;
        }
        if (this.flag) return;
        var token = uni.getStorageSync('token');
        if (token != '') {
          if (this.selectedType === 'word' || this.selectedType === undefined) {
            var wordCount = 0;
            this.flag = true;
            uni.showLoading();
            this.$http
              .get('/znyy/review/query/fun/word/count', {
                studentCode: this.studentCode
              })
              .then((wordCountResult) => {
                this.flag = false;
                uni.hideLoading();
                if (wordCountResult) {
                  if (wordCountResult.data.success) {
                    wordCount = wordCountResult.data.data.totalWordCount;
                  }
                  if (wordCount == 0) {
                    this.$util.alter('暂无可复习单词！');
                  } else {
                    if (!this.flag) {
                      uni.navigateTo({
                        url: '/antiForgetting/allWords?studentCode=' + this.studentCode + '&merchantCode=' + this.merchantCode + '&deliverMerchant=' + this.deliverMerchant
                      });
                    }
                  }
                }
              });
          } else {
            if (this.selectedType === 'grammar') {
              if (!this.flag) {
                this.$http
                  .get('/dyf/wap/applet/todayReviewStatistics', {
                    // todo 学员 code 固定值 用于测试
                    studentCode: this.studentCode
                    // studentCode: '6231217888',
                  })
                  .then((res) => {
                    if (res.data.data.totalNum > 0) {
                      uni.navigateTo({
                        url: `/antiForgetting/allWords?studentCode=${this.studentCode}&selectedType=${this.selectedType}`
                      });
                    } else {
                      this.$util.alter('暂无可复习语法！');
                    }
                  });
              }
            }
          }
        } else {
          uni.navigateTo({
            url: '/pages/login/login'
          });
        }
      },

      gohistory() {
        if (this.studentCode == '') {
          this.$util.alter('请先选择学员');
          return;
        }
        var token = uni.getStorageSync('token');
        if (token != '') {
          if (this.selectedType == 'word') {
            uni.navigateTo({
              url: '/antiForgetting/history?studentCode=' + this.studentCode
            });
          }
          if (this.selectedType == 'grammar') {
            uni.redirectTo({
              url: `/antiForgetting/grammarHistory?studentCode=${this.studentCode}&antiForgetting=1`
            });
          }
        } else {
          uni.navigateTo({
            url: '/pages/login/login'
          });
        }
      }
    }
  };
</script>

<style>
  .top {
    display: flex;
    justify-content: space-between;
  }

  .left-text {
    margin-top: 44rpx;
    display: flex;
    flex-direction: column;
  }

  .left-text text {
    font-size: 40rpx;
    font-family: AlibabaPuHuiTiM;
    color: #000000;
    font-weight: bold;
    line-height: 60rpx;
  }

  .right-logo {
    margin-top: 35rpx;
  }

  .center-banner {
    margin: 20rpx auto;
    width: 630rpx;
    height: 1348rpx;
    background: #fff;
    border-radius: 20rpx;
    padding: 0 30rpx;
    margin-bottom: 50rpx;
  }

  .right-logo image {
    width: 173rpx;
    height: 80rpx;
  }

  .btnchange {
    width: 160rpx;
    height: 60rpx;
    background: #fd7918;
    line-height: 60rpx;
    text-align: center;
    border-radius: 50%;
    color: #fff;
    font-size: 30rpx;
    position: absolute;
    right: 20rpx;
    top: 144rpx;
  }

  .selectchange {
    border-radius: 60rpx;
    position: absolute;
    right: 20rpx;
    top: 80rpx;
    font-size: 28rpx;
    color: #007d70;
  }

  .imgs {
    position: absolute;
    overflow: hidden;
    z-index: -10;
    background-size: cover;
    pointer-events: none;
    width: 100%;
    height: 1334rpx;
    /* right: 0px;
		top: 0px;
		bottom: 0px;
		left: 0px; */
  }

  uni-page-body {
    padding-bottom: 0px !important;
  }

  .logo {
    display: block;
    width: 530rpx;
    height: 112rpx;
    position: absolute;
    left: 50%;
    margin-left: -265rpx;
    top: 60rpx;
  }

  .image {
    display: block;
    width: 630rpx;
    height: 60rpx;
    position: absolute;
    left: 50%;
    margin-left: -315rpx;
    top: 200rpx;
  }

  .images {
    width: 630rpx;
    height: 912rpx;
    margin-top: 74rpx;
  }

  .footer {
    display: flex;
    justify-content: space-between;
    margin-top: 60rpx;
    margin-bottom: 60rpx;
  }

  .fot-btn {
    text-align: center;
    width: 290rpx;
    height: 80rpx;
    line-height: 80rpx;
    border-radius: 40rpx;
    border: 2rpx solid #2e896f;
    font-size: 32rpx;
    font-family: AlibabaPuHuiTiR;
  }

  .left-btn {
    color: #2e896f;
  }

  .right-btn {
    background: #2e896f;
    color: #ffffff;
  }

  /* 弹窗样式 */
  .dialogBG {
    width: 100%;
    /* height: 100%; */
  }

  .reviewCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 50upx 55upx;
    box-sizing: border-box;
  }

  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .reviewTitle {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    justify-content: center;
  }

  .dialogContent {
    box-sizing: border-box;
    font-size: 30upx;
    line-height: 70upx;
    text-align: center;
    margin-top: 40rpx;
  }

  .addclass {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #2e896f;
    border: 1px solid #2e896f;
    overflow: visible;
    border-radius: 35rpx;
    transform: scale(0.995); /* 解决ios上圆角失效 */
  }

  .not-selected {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #000;
    border: 1px solid #c8c8c8;
    overflow: visible;
    border-radius: 35rpx;
    transform: scale(0.995); /* 解决ios上圆角失效 */
  }

  /* 底部按钮样式 */
  .mask-footerType {
    margin-top: 20px;
    display: flex;
    justify-content: space-around;
  }

  .mask-footerType button + button {
    margin-left: 20rpx;
  }

  .mask-footerType button {
    width: 250rpx;
    height: 80rpx;
    font-size: 30rpx;
    border-radius: 40rpx;
  }

  .confirm-button {
    display: flex;
    justify-content: space-around;
    align-items: center;
    /* 文本垂直居中对齐 */
    background: #2e896f;
    color: #ffffff !important;
  }

  .cancel-button {
    display: flex;
    justify-content: space-around;
    align-items: center;
    /* 文本垂直居中对齐 */
    color: #2e896f !important;
    border: 1rpx solid #2e896f !important;
  }
</style>

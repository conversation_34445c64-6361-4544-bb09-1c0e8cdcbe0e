<template>
	<view class="plr-30 pt-30 radius-15">
		<view class="bg-ff plr-30" v-for="(item, index) in mystudentslist" :key="index">
			<view class="flex-s f-30 ptb-30" @click.stop="setUp(item)" :class="index!=mystudentslist.length-1?'line':''">
				<view>{{ item.realName }}<text class="c-66">{{'（'+ item.studentCode+'）' }}</text></view>
				<view>
					<text class="mr-15 c-66">{{ voiceName(item.voiceModel) }}</text>
					<uni-icons type="right" size="18" color="#666"></uni-icons>
				</view>
			</view>
		</view>
		
		<view v-if="mystudentslist.length==0 && mystudentslist!=null" :style="{height:useHeight+'rpx'}" class="t-c flex-col bg-ff radius-15 f-30">
			<image :src="imgHost+'alading/correcting/no_data.png'" class="mb-20 img_s"></image>
			<view style="color: #BDBDBD;">暂无数据</view>
		</view>
	</view>
</template>

<script>
	const { http } = require('@/utils/luch-request/index.js');
	// const {
	// 	$navigationTo,
	// 	$http
	// } = require("@/util/methods.js")
	export default {
		data() {
			return {
				ifshowmore: 1,
				ifshowzanwu: 1,
				mystudentslist: [],
				memberId: '', //当前用户membercode
				useHeight: 0, //屏幕高度
				imgHost: getApp().globalData.imgsomeHost,
			};
		},
		onLoad(option) {
			console.log('走到这里了')
			this.memberId = option.memberId;
		},
		onShow() {
			console.log('onShow方法')
			this.loadData();
		},
		onReady() {
			uni.getSystemInfo({
				success: (res) => {
					// 可使用窗口高度，将px转换rpx
					let h = (res.windowHeight * (750 / res.windowWidth));
					this.useHeight = h - 60;
				}
			})
		},
		methods:{
			goUrl(url){
				let token = uni.getStorageSync('token')
				if (token) {
					uni.navigateTo({
						url: url
					})
				} else {
					this.$tool.toLoginPage(2);
				}
			},
			async loadData() {
				this.mystudentslist = [];
				const that = this;
				uni.$http.get(`/znyy/course/queryStudentList/1/10`, {
					memberId: that.memberId
				}).then((res) => {
					if (res.data.success) {
						let result = res.data.data;
						if (result.data != null) {
							if (result.data.length == 0) {
								that.mystudentslist = [];
							} else {
								if (result.data.length > 0 && result.data.length > 0) {
									that.mystudentslist = that.mystudentslist.concat(result.data);
									if (result.data.length > 2) that.ifshowmore = 0;
									that.ifshowzanwu = 0;
									
								}
							}
						}
					} else {
						uni.showToast({
							title: res.data.message,
							icon: 'none'
						});
					}
				})
			},
			
			voiceName(item){
				console.log(item)
				if(item){
					let name = item.split("#");
					let rq = name[1]==1?'英式':'美式';
					let sex = name[2]=='M'?'男声':'女声';
					return rq +'-'+ sex;
				}
			},
			
			setUp(item){
				let list = item.voiceModel.split("#");
				// skintap('pages/home/<USER>/pronunciationSettings?studentCode='+item.studentCode)
				let data = {
					studentCode:item.studentCode,
					v:list[0],
					rq:list[1],
					sex:list[2]
				}
				uni.navigateTo({
					url:'/pages/my/pronunciationSettings/index?list='+ encodeURIComponent(JSON.stringify(data))
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.line{
		border-bottom: 1px solid #eee;
	}
	
	.img_s{
		width: 160rpx;
		height: 160rpx;
	}
	
	.add_text{
		color: #2E896F;
	}
</style>

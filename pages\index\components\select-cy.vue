<template>
  <view class="uni-select-cy" :style="{ 'z-index': zindex }">
    <view class="uni-select-cy-select" :class="{ active: active }" @click.stop="handleSelect">
      <!-- 禁用mask -->
      <view class="uni-disabled" v-if="disabled"></view>
      <!-- 清空 -->
      <view class="close-icon close-postion" v-if="realValue.length && !active && !disabled && showClearIcon">
        <text @click.stop="handleRemove(null)"></text>
      </view>
      <!-- 为空时的显示文案 -->
      <view v-if="realValue.length == 0 && showplaceholder" class="tip">
        <image style="width: 38rpx; height: 38rpx" src="/static/images/create_img_user.png" mode="aspectFill"></image>
        {{ placeholder }}
      </view>
      <!-- 显示框 -->
      <view class="uni-select-multiple" v-show="realValue.length">
        <view class="uni-select-multiple-item" v-for="(item, index) in changevalue" :key="index">
          <view class="uni-select-multiple-item-row">
            {{ item[slabel] }}
          </view>
          <view class="close-icon" v-if="showValueClear && !disabled" @click.stop="handleRemove(index)"><text></text></view>
        </view>
      </view>
      <!-- 禁用图标 -->
      <view class="uni-select-cy-icon" :class="{ disabled: disabled }">
        <!-- <text></text> -->
        <uni-icons type="right" color="#999999" size="20" v-if="!disabled"></uni-icons>
      </view>
    </view>
    <!-- 下拉选项 -->
    <view class="uni-select-cy-options" :scroll-y="true" v-show="active" :style="{ 'z-index': zindex }">
      <u-row v-for="(item, index) in options">
        <view class="uni-select-cy-item" :class="{ active: realValue.includes(item[svalue]) }" :key="index" @click.stop="handleChange(index, item)">
          {{ item[slabel] }}({{ item[svalue] }})
        </view>
      </u-row>
    </view>
  </view>
</template>

<script>
  export default {
    name: 'select-cy',
    props: {
      //是否显示全部清空按钮
      showClearIcon: {
        type: Boolean,
        default: false
      },
      //是否显示单个删除
      showValueClear: {
        type: Boolean,
        default: true
      },
      zindex: {
        type: Number,
        default: 999
      },
      //禁用组件
      disabled: {
        type: Boolean,
        default: false
      },
      options: {
        type: Array,
        default() {
          return [];
        }
      },
      value: {
        type: Array,
        default() {
          return [];
        }
      },
      placeholder: {
        type: String,
        default: '请选择'
      },
      showplaceholder: {
        type: Boolean,
        default: true
      },
      slabel: {
        type: String,
        default: 'label'
      },
      svalue: {
        type: String,
        default: 'value'
      },
      // 是否开启分页
      isPaging: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        active: false, //组件是否激活，
        changevalue: [], //搜索框同步
        realValue: []
      };
    },
    watch: {
      value() {
        //初始化
        this.init();
      },
      options() {
        // console.log('🚀 ~ options ~ 初始化:', '初始化');

        //初始化
        this.init();
      }
    },
    mounted() {
      this.init();
    },
    methods: {
      close() {
        this.active = false;
      },
      init() {
        // console.log(this.value, 'this.value');
        if (this.value.length > 0) {
          this.changevalue = this.options.filter((item) => {
            // console.log(item[this.svalue]);
            return this.value.includes(item[this.svalue]);
          });
          // // console.log(this.changevalue, 'this.changevalue');

          // console.log(this.changevalue, this.options, 'this.changevalue');
          this.realValue = this.value;
        } else {
          this.changevalue = [];
          this.realValue = [];
        }
      },
      scrolltolower() {
        if (this.isPaging) {
          this.$emit('scrolltolower');
        }
      },
      //点击展示选项
      handleSelect() {
        if (this.disabled) return;
        this.active = !this.active;
      },
      //移除数据
      handleRemove(index) {
        if (index === null) {
          this.realValue = [];
          this.changevalue = [];
        } else {
          this.realValue.splice(index, 1);
          this.changevalue.splice(index, 1);
        }
        this.$emit('change', this.changevalue, this.realValue);
      },
      //点击组件列
      handleChange(index, item) {
        let arrIndex = this.realValue.indexOf(item[this.svalue]);
        if (arrIndex > -1) {
          this.changevalue.splice(arrIndex, 1);
          this.realValue.splice(arrIndex, 1);
        } else {
          this.changevalue.push(item);
          this.realValue.push(item[this.svalue]);
        }
        // console.log(this.realValue, 'this.realValue');
        // console.log(this.changevalue, 'this.changevalue');
        this.$emit('change', this.changevalue, this.realValue);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .uni-select-cy {
      position: relative;
      // z-index: 999;
      width: 100%;

      .uni-select-mask {
          width: 100%;
          height: 100%;
      }

      /* 删除按钮样式*/
      .close-icon {
          height: 100%;
          width: 35rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 3;
          cursor: pointer;
          padding-left: 10rpx;

          text {
              position: relative;
              background: #fff;
              flex-shrink: 0;
              width: 35rpx;
              height: 35rpx;
              border-radius: 50%;
              border: 2rpx solid #bbb;

              &::before,
              &::after {
                  content: "";
                  position: absolute;
                  left: 20%;
                  top: 50%;
                  height: 2rpx;
                  width: 60%;
                  transform: rotate(45deg);
                  background-color: #bbb;
              }

              &::after {
                  transform: rotate(-45deg);
              }
          }
      }

      // 所有清空的定位
      .close-postion {
          position: absolute;
          right: 70rpx;
          top: 0;
          height: 100%;
          width: 30rpx;
      }

      .tip {
          padding-left: 30rpx;
          display: flex;
          color: #666666;

          image {
              padding-right: 30rpx;
          }

      }

      /* 多选盒子 */
      .uni-select-multiple {
          overflow-x: auto;
          display: flex;
          flex: 1;
          width: 0;
          flex-wrap: wrap;

          .uni-select-multiple-item {
              background: rgb(244, 244, 245);
              color: rgb(144, 147, 153);
              border: 2rpx solid rgb(233, 233, 235);
              border-radius: 4rpx;
              display: flex;
              flex: 0 0 140rpx;
              margin: 4rpx 0 4rpx 12rpx;
              padding: 10rpx 14rpx;

              .uni-select-multiple-item-row {
                  flex: 1;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  font-size: 30rpx;
              }
          }
      }


      // select部分
      .uni-select-cy-select {
          user-select: none;
          position: relative;
          z-index: 3;
          min-height: 95rpx;
          padding: 10rpx 60rpx 10rpx 0;
          box-sizing: border-box;
          border-radius: 14rpx;
          border: 1rpx solid rgba(0, 0, 0, 0.2);
          display: flex;
          align-items: center;
          font-size: 14px;
          color: #999;
          flex-wrap: nowrap;

          .uni-disabled {
              position: absolute;
              left: 0;
              width: 100%;
              height: 100%;
              z-index: 19;
              cursor: no-drop;
              background: rgba(255, 255, 255, .5);
          }


          .uni-select-cy-input {
              font-size: 28rpx;
              color: #999;
              display: block;
              width: 96%;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              line-height: 60rpx;
              box-sizing: border-box;

              &.active {
                  color: #333;
              }

          }

          .uni-select-cy-icon {
              cursor: pointer;
              position: absolute;
              right: 0;
              top: 0;
              height: 100%;
              width: 100rpx;
              display: flex;
              align-items: center;
              justify-content: center;

              // &::before {
              // 	content: "";
              // 	width: 2rpx;
              // 	height: 100%;
              // 	position: absolute;
              // 	left: 0;
              // 	top: 0;
              // 	background-color: #e5e5e5;
              // }

              // text {
              // 	display: block;
              // 	width: 0;
              // 	height: 0;
              // 	border-width: 15rpx 15rpx 0;
              // 	border-style: solid;
              // 	border-color: #bbb transparent transparent;
              // 	transition: .3s;
              // }

              &.disabled {
                  cursor: no-drop;

                  // text {
                  //     width: 20rpx;
                  //     height: 20rpx;
                  //     border: 4rpx solid #ff0000;
                  //     border-radius: 50%;
                  //     transition: .3s;
                  //     position: relative;
                  //     z-index: 999;

                  //     &::after {
                  //         content: "";
                  //         position: absolute;
                  //         top: 50%;
                  //         left: 0;
                  //         width: 100%;
                  //         height: 4rpx;
                  //         margin-top: -2rpx;
                  //         background-color: #ff0000;
                  //         transform: rotate(45deg);

                  //     }
                  // }
              }
          }

          &.active .uni-select-cy-icon {
              text {
                  transform: rotate(180deg);
              }
          }
      }

      // options部分
      .uni-select-cy-options {
          user-select: none;
          position: absolute;
          top: calc(100% + 10rpx);
          left: 0;
          width: 100%;
          max-height: 400rpx;
          border-radius: 8rpx;
          border: 1rpx solid rgba(0, 0, 0, 0.2);
          background: #fff;
          padding: 10rpx 0;
          box-sizing: border-box;
          z-index: 999;
          overflow-y: scroll;

          .uni-select-cy-item {
              width: 100%;
              padding: 0 30rpx;
              box-sizing: border-box;
              cursor: pointer;
              line-height: 2.5;
              transition: .3s;
              font-size: 30rpx;
              color: rgb(96, 98, 102);

              &.active {
                  font-weight: 550;
                  color: #3b8f77;

                  background-color: #f5f7fa &:hover {
                      color: #3b8f77;
                      background-color: rgb(245, 247, 250);
                  }

                  &::after {
                      position: absolute;
                      right: 50rpx;
                      font-family: element-icons;
                      content: "✔";
                      font-size: 30rpx;
                      font-weight: 700;
                      -webkit-font-smoothing: antialiased;
                  }
              }

              &:hover {
                  background-color: #f5f5f5;
              }
          }
      }
  }
</style>

<template>
  <view :style="{ height: useHeight + 'rpx' }">
    <view class="container">
      <view class="pd-30">
        <view class="content">
          {{ content || '' }}
        </view>
        <view class="button" @click="paste" v-if="sort == 1">复制</view>
        <view class="button" @click="sendMsg" v-if="sort == 0">发送</view>
      </view>
    </view>
    <view class="" style="height: 50rpx"></view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      useHeight: 0,
      queryData: {},
      content: '',
      sort: 0
    };
  },
  onReady() {
    uni.getSystemInfo({
      success: (res) => {
        // 可使用窗口高度，将px转换rpx
        let h = res.windowHeight * (750 / res.windowWidth);
        this.useHeight = h - 30;
      }
    });
  },
  onLoad(e) {
    let that = this;
    if (e.item) {
      that.queryData = JSON.parse(decodeURIComponent(e.item));
    }
    that.content = e.content;
    that.sort = e.sort;
    uni.setNavigationBarTitle({
      title: that.queryData.msgTypeName
    });
  },
  methods: {
    sendMsg() {
      let that = this;
      uni.showLoading({
        title: '正在发送'
      });
      // 发文字
      wx.qy.getContext({
        success: () => {
          //    var entry = data.entry, //返回进入小程序的入口类型
          // var shareTicket = data.shareTicket;
          wx.qy.sendChatMessage({
            msgtype: 'text', //消息类型，必填
            enterChat: false, //为true时表示发送完成之后顺便进入会话，仅移动端3.1.10及以上版本支持该字段
            text: {
              content: that.content //文本内容
            },
            image: {
              mediaid: '' //图片的素材id
            },
            video: {
              mediaid: '' //视频的素材id
            },
            file: {
              mediaid: '' //文件的素材id
            },
            news: {
              link: '', //H5消息页面url 必填
              title: '', //H5消息标题
              desc: '', //H5消息摘要
              imgUrl: '' //H5消息封面图片URL
            },
            miniprogram: {
              appid: '', //小程序的appid
              title: '', //小程序消息的title
              imgUrl: '', //小程序消息的封面图
              page: '' //小程序消息打开后的路径，注意要以.html作为后缀，否则在微信端打开会提示找不到页面
            },
            success: (res) => {
              uni.hideLoading();
              that.$http
                .get('/deliver/teacher/notify/details', {
                  id: that.queryData.id,
                  isSend: 1,
                  boundInfo: that.queryData.boundInfo,
                  message: that.queryData.msgType
                })
                .then(() => {
                  uni.showToast({
                    title: '发送成功'
                  });
                })
                .catch((err) => {
                  uni.showToast({
                    title: '发送失败',
                    icon: 'none'
                  });
                });
            },
            fail: (err) => {
              uni.hideLoading();
              console.log(err, '=============================');
            }
          });
        },
        fail: (err) => {
          uni.hideLoading();
          console.log(err, '111111111111111');
        }
      });
    },
    // 复制
    paste() {
      let that = this;
      uni.setClipboardData({
        data: that.content,
        success: (res) => {
          console.log(res);
          that.$http
            .get('/deliver/teacher/notify/details', {
              id: that.queryData.id,
              isSend: 1,
              boundInfo: that.queryData.boundInfo,
              message: that.queryData.msgType
            })
            .then(() => {
              uni
                .showToast({
                  title: '内容已复制至剪贴板'
                })
                .catch((err) => {
                  uni.showToast({
                    title: '复制失败',
                    icon: 'none'
                  });
                });
            });
        }
      });
    }
  }
};
</script>

<style lang="scss">
.container {
  width: 690rpx;
  min-height: 1000rpx;
  background: #ffffff;
  border-radius: 14rpx;
  margin: 30rpx auto;
  position: relative;
}
.pd-30 {
  padding: 30rpx;
  line-height: 1.4;
}
.content {
  width: 100%;
  word-wrap: break-word;
}
.button {
  width: 586rpx;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  color: #ffffff;
  font-size: 30rpx;
  background: #2e896f;
  border-radius: 45rpx;
  // position: absolute;
  // bottom: 40rpx;
  margin: 400rpx auto 0;
}
</style>

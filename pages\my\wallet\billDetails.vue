<template>
	<view class="p-30">
		<view class="bg-ff radius-15 ptb-30 plr-20 content-main-bill-details"  :style="{height:useHeight+'rpx'}">
			<view class="t-c">
				<image v-if="!show&&type==1||type==3||merit_pay=='4'||type==6" src="https://document.dxznjy.com/Assistant/commission_icon.png"
					class="commission_icon"></image>
				<image v-if="!show&&type==2||type==4" src="https://document.dxznjy.com/Assistant/performance.png"
					class="commission_icon"></image>
				<image v-if="!show&&type==5||type==7" src="https://document.dxznjy.com/Assistant/bonus.png"
					class="commission_icon"></image>
					<image v-if="!show&&type==9" src="https://document.dxznjy.com/course/5c2a2382028d49c2a2b98f85d3f48b96.png"
					class="commission_icon"></image>
				<image v-if="show" src="https://document.dxznjy.com/Assistant/balance.png" class="commission_icon">
				</image>
			</view>
			<view class="mt-30 t-c pb-60">
				<view class="pt-30 f-32">{{detailsList.content}}</view>
				<view class="mt-20 bold" style="font-size: 60rpx;">{{show?'-':'+'}}{{detailsList.amount || 0}}</view>
			</view>
			<view class="pt-60 c-66 f-30 border-t ml-35">
				<view v-if="!show"> <!-- 绩效记录 -->
					<view class="mb-30 flex-a-c" v-if="type!=7&&type!=9">
						<view class="w120">学员姓名</view>
						<text class="ml-40 c-33">{{detailsList.studentName}}</text>
					</view>
					<view class="mb-30 flex-a-c" v-if="type==1||type==2">
						<view class="w120">学时</view>
						<text class="ml-40 c-33">{{detailsList.hours}}/时</text>
					</view>
					<view class="mb-30 flex-a-c"  v-if="type==7">
						<view class="w120">时间</view>
						<text class="ml-40 c-33">{{detailsList.actualStart.slice(0,10) || ''}} <text class="text-wen-css" v-if="detailsList.actualEnd">至</text> {{detailsList.actualEnd.slice(0,10) || ''}}</text>
					</view>	
					<view class="mb-30 flex-a-c"  v-else-if="type==9">
						<view class="w120">上课时间</view>
						<text class="ml-40 c-33">{{detailsList.actualStart.slice(0,10) || ''}} <text class="text-wen-css" v-if="detailsList.actualEnd">至</text> {{detailsList.actualEnd.slice(0,10) || ''}}</text>
					</view>	
					<view class="mb-30 flex-a-c" v-else>
						<view class="w120">时间</view>
						<text class="ml-40 c-33">{{detailsList.actualStart.slice(0,10) + '  ' || ''}} {{detailsList.actualStart.slice(11,17) || ''}}<text v-if="type!=6">-{{detailsList.actualEnd.slice(11,17) || ''}}</text></text>
					</view>
					<view class="mb-30 flex-a-c">
						<view class="w120">教练</view>
						<text class="ml-40 c-33">{{type==1|| type==2 || merit_pay=='4'?detailsList.teacherName:teacherName }}</text>
					</view>
					<view class="mb-30 flex-a-c" v-if="type==1 || type==2||merit_pay =='4'|| type==6|| type==7||type==9">
						<view class="w120">所属学管</view>
						<text class="ml-40 c-33">{{detailsList.learnTubeName || '无'}}</text>
					</view>
					<view class="mb-30 flex-a-c" >
						<view class="w120">状态</view>
						<text :class="detailsList.accountStatus==1?'c-67C23A':'c-E6A23C'" class="ml-40">{{ detailsList.accountStatusDesc }}</text>
					</view>
					<view class="mb-30 flex-a-c"  v-if="type==9">
						<view class="w120">到账时间</view>
						<text class="ml-40 c-33">{{detailsList.accountTime}}</text>
					</view>
					<view class="mb-30 flex-a-c" v-if="type==2||type==4">
						<view class="w120">扣绩效</view>
						<text class="ml-40 c-33">{{detailsList.deductWage || 0}}元</text>
					</view>
					<view class="flex-a-c moreTextParent" v-if="type==2||type==4||type==7">
						<view class="w120 moreTextTitle">备注</view>
						<text v-if="type==7" class="c-33 moretext">{{ detailsList.remark||'无' }}</text>
						<text v-else class="c-33 moretext">{{detailsList.deductReason || '无'}}</text>
					</view>
				</view>
				<view v-if="show" class="ml-60"> <!-- 提现记录 -->
					<view class="mb-30 flex-a-c">
						<view class="w120">提现金额</view>
						<text class="ml-40 c-33">¥{{detailsList.amount}}</text>
					</view>
					<view class="mb-30 flex-a-c">
						<view class="w120">提现时间</view>
						<text class="ml-40 c-33">{{detailsList.createTime}}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const {
		http
	} = require('@/utils/luch-request/index.js');
	export default {
		data() {
			return {
				useHeight: 0,
				orderId: '',
				detailsList: {},
				type: '', // 1基本工资 2绩效
				show: false,
				teacherName: '',
				merit_pay:''

			};
		},
		onLoad(e) {
			if (e.orderId != undefined) {
				this.orderId = e.orderId;
				this.type =e.type;
				this.merit_pay = e.merit_pay;
				this.show = false;
				this.teacherName = e.teacherName;
				this.getTeacherAccount()
			} else {
				this.show = true;
				this.detailsList = JSON.parse(e.info);
			}
		},
		onShow() {},
		onReady() {
			uni.getSystemInfo({
				success: (res) => {
					// 可使用窗口高度，将px转换rpx
					let h = (res.windowHeight * (750 / res.windowWidth));
					this.useHeight = h - 120;
				}
			})
		},

		methods: {
			async getTeacherAccount() {
				// 判断用户是否实名认证
				let res = await http.get('/deliver/app/teacher/getTeacherAccountFlowDetail', {
					orderId: this.orderId
				});
				if (res.data.success) {
					this.detailsList = res.data.data;
					console.log(this.detailsList)
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.commission_icon {
		width: 120rpx;
		height: 120rpx;
		margin-top: 106rpx;
	}
	.content-main-bill-details{
		overflow: hidden;
		overflow-y: scroll;
	}
	.text-wen-css{
		display: inline-block;
		text-align: center;
		width: 50rpx;
	}
	.border-t {
		border-top: 1px solid #EEE;
	}

	.w120 {
		width: 120rpx;
	}

	.moretext {
		margin-left: 159rpx;
		width: 443rpx;
		line-height: 42rpx;
	}

	.moreTextParent {
		position: relative;
	}

	.moreTextTitle {
		position: absolute;
		top: 0;
		left: 0;
	}
	.c-67C23A{
		color:#67C23A;
	}
	.c-E6A23C{
		color:#E6A23C;
	}
</style>
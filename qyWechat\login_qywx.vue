<template>
  <view>
    <!-- <view class="container" style="min-height: 1180rpx"> -->
    <view class="container" :style="{ height: useHeight + 'rpx' }">
      <image @click="openCode" class="login_img" mode="widthFix" src="https://document.dxznjy.com/app/images/zhujiaoduan/logo.png"></image>
      <view style="margin-top: 130rpx">
        <view class="title">登录查看更多内容</view>
        <button class="card-btn" @click="qyLogin">企业微信一键登录</button>
      </view>

      <view class="tip">
        <label class="radio" style="display: inline-block; transform: scale(0.6)">
          <radio value="r1" :checked="isChecked" color="#2E896F" @click="changeischecked" />
        </label>
        我已阅读并同意
        <text @click="goweburl('https://document.dxznjy.com/applet/agreeon/useragreement.html')">《用户服务协议》、</text>
        <text @click="goweburl('https://document.dxznjy.com/applet/agreeon/privacypolicy.html')">《隐私政策》</text>
      </view>
    </view>

    <!-- 选择角色弹窗 -->
    <uni-popup ref="popopChooseStudent" type="center" :safe-area="false" :isMaskClick="false">
      <view class="dialogBG">
        <view class="dialog-all">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="dialog-small-close" @click="cancelStudent">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="dialog-center">
            <view class="dialog-title pb-10">选择角色</view>
            <view class="dialog-item" @click="chooseStudentlist(item, index)" v-for="(item, index) in roleArr" :class="isactive == index ? 'selected' : 'not-selected'">
              {{ item.label }}
            </view>
            <view class="flex-c mt-40">
              <view class="common-sure-btn-1" @click="confirmStudent()">确定</view>
              <view class="common-cancel-btn ml-40" @click="cancelStudent()">取消</view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
    <!-- 内部密钥弹窗 -->
    <uni-popup ref="propCode" type="center" :safe-area="false" :isMaskClick="false">
      <view class="dialogBG">
        <view class="dialog-all">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="dialog-small-close" @click="cancelCode">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="dialog-center">
            <view class="dialog-title pb-10">内部秘钥</view>
            <view class="inputCode">
              <input type="text" name="code" placeholder="请输入内部秘钥" placeholder-class="phClass" class="input" maxlength="20" v-model="code" />
            </view>
            <view class="flex-c mt-40">
              <view class="common-sure-btn-1" @click="confirmCode()">确定</view>
              <view class="common-cancel-btn ml-40" @click="cancelCode()">取消</view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        isChecked: false, //是否登录
        tel: '',
        useHeight: 0,
        minAppUser: {
          role: 'assistant',
          token: '',
          scrm_token: ''
        },
        role: '',
        roleArr: [],
        isactive: -1,
        codeNum: 0,
        time: '',
        code: ''
      };
    },
    onLoad() {
      uni.removeStorage({
        key: 'jump'
      });
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          console.log(res);
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 30;
        }
      });
    },
    watch: {
      codeNum(i) {
        if (i == 1) {
          clearTimeout(this.time);
          let that = this;
          this.time = setTimeout(() => {
            this.codeNum = 0;
          }, 10000);
        }
      }
    },
    methods: {
      openCode() {
        if (this.codeNum < 4) {
          this.codeNum++;
        } else {
          this.$refs.propCode.open();
          this.codeNum = 0;
        }
      },
      async confirmCode() {
        if (!this.code)
          return uni.showToast({
            icon: 'none',
            title: '内部秘钥不可为空~'
          });
        let { data } = await uni.$http.get('/new/security/v2/login/verify/secretKey?secretKey=' + this.code);
        if (data.code == 20000) {
          uni.redirectTo({
            url: '/qyWechat/login_psd'
          });
        }
      },
      cancelCode() {
        this.$refs.propCode.close();
        this.code = '';
      },
      chooseStudentlist(item, index) {
        this.isactive = index;
        this.role = item.value;
      },
      cancelStudent() {
        this.cacheAddStudentRequest = {};
        this.$refs.popopChooseStudent.close();
        this.isactive = -1;
      },
      confirmStudent() {
        if (this.isactive == -1) {
          uni.showToast({
            icon: 'none',
            title: '请先选择角色~'
          });
          return;
        } else {
          let role = this.role;
          this.againLogin(role);
        }
      },
      // 企业微信登录
      qyLogin() {
        let that = this;
        let isQy = uni.getStorageSync('isQy');
        if (!this.isChecked) {
          return this.$util.alter('请阅读并勾选下方协议');
        }
        // const res = wx.getSystemInfoSync();
        // if (res && res.environment == 'wxwork') {
        //   isQy = true;
        // } else {
        //   isQy = false;
        // }
        // console.log(isQy);
        // console.log(isQy);
        if (!isQy) {
          uni.showModal({
            title: '温馨提示',
            content: '请在企业微信环境下使用教练端登录',
            showCancel: false
          });
        } else {
          wx.qy.login({
            success: async ({ code }) => {
              let { data } = await that.$http.get('/new/security/v2/login/qw/mini', {
                code: code
              });
              if (data.code == 50003) {
                setTimeout(() => {
                  uni.navigateTo({
                    url: '/qyWechat/bindQyWechat'
                  });
                }, 1000);
              } else {
                console.log(data.data);
                uni.setStorageSync('scrm_token', data.data.token);
                that.minAppUser.scrm_token = data.data.token;
                // 获取手机号
                that.$scrmHttp
                  .get('/scrm/web/common/getUserInfo')
                  .then(async (tel) => {
                    let mobile = tel.data.data.username;
                    that.tel = mobile;
                    // that.tel = '13761941263';
                    uni.setStorageSync('tel', that.tel);
                    let roles = await uni.$http.get(`/deliver/web/common/getTeamRole/${that.tel}`);
                    let role = '';
                    if (roles.data.data.length > 1) {
                      that.roleArr = roles.data.data;
                      that.$refs.popopChooseStudent.open();
                    } else if (roles.data.data.length == 1) {
                      role = roles.data.data[0].value;
                      that.againLogin(role);
                    } else {
                      uni.showToast({
                        title: '您暂无任何权限登录',
                        icon: 'none'
                      });
                    }
                  })
                  .catch((err) => {
                    console.log(err);
                  });
              }
            },
            fail: (err) => {
              console.log(err, 'code错误');
            }
          });
        }
      },
      againLogin(role) {
        let that = this;
        // 二次登录
        that.$scrmHttp
          .get('/new/security/v2/login/deliver/mini', {
            username: that.tel,
            // username: '13761941263',
            role: role,
            usePwd: false
          })
          .then((login) => {
            console.log(login);
            uni.setStorageSync('token', login.data.data.token);
            that.minAppUser.token = login.data.data.token;
            that.$user.storageUserInfo(that.minAppUser);
            uni.showToast({
              icon: 'success',
              title: '登录成功',
              duration: 1500
            });
            // 跳转到首页
            setTimeout(() => {
              if (this.hasPrevPage()) {
                uni.navigateBack();
              } else {
                uni.switchTab({
                  url: '/pages/index/index'
                });
              }
            }, 500);
          })
          .catch((err) => {
            uni.showToast({
              icon: 'none',
              title: '登录失败',
              duration: 1500
            });
          });
      },
      hasPrevPage() {
        let pages = getCurrentPages(); // 获取页面栈
        return pages.length > 1; // 页面栈长度大于1时，代表有上一层页面
      },
      // 同意已阅读用户服务协议
      changeischecked() {
        this.isChecked = !this.isChecked;
      },
      // 跳转H5用户服务协议，隐私政策
      goweburl(url) {
        console.log(url);
        uni.navigateTo({
          url: `/pages/index/web?url=${url}`
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  $nav-height: 30px;

  .nav-title {
    display: flex;
    align-items: center;
    position: fixed;
    height: 135px;
    width: 100%;
    background-color: #fff;
  }

  .status_bar {
    display: flex;
    align-items: center;
    font-size: 40rpx;
    color: #000;
    // line-height: 230rpx;
  }

  .container {
    text-align: center;
    background-color: #fff;
    padding-top: 150rpx;
  }
  .checkUrl {
    position: fixed;
    top: 208rpx;
    height: 70rpx;
    width: 600rpx;
    left: 74rpx;
    display: flex;
    justify-content: space-around;
    .urlBox {
      width: 166rpx;
      border: 2rpx solid #1d755c;
      border-radius: 10rpx;
      text-align: center;
      color: #1d755c;
      line-height: 70rpx;
    }
    .activeUrl {
      width: 166rpx;
      background-color: #1d755c;
      border-radius: 10rpx;
      text-align: center;
      color: #fff;
      line-height: 70rpx;
    }
  }
  .title {
    padding-bottom: 30rpx;
    font-size: 30rpx;
    color: #babec2;
    // border-bottom: 1px solid #ccc;
  }

  .login_img {
    width: 260rpx;
    margin-top: 150rpx;
  }
  .phClass {
    color: #c7c7c7;
    font-size: 30rpx;
  }
  .inputCode {
    margin: 60rpx 0;
    height: 80rpx;
    display: flex;
    line-height: 80rpx;
    padding: 10rpx;
    box-sizing: border-box;
    border: 2rpx solid #ccc;
    border-radius: 10rpx;
    .input {
      flex-grow: 1;
      height: 100%;
    }
  }
  .box-bg {
    background-color: #f5f5f5;
    padding: 5px 0;
  }

  .img {
    width: 300rpx;
    height: 200rpx;
    margin: 60rpx auto;
    background-color: #f5f5f5;
  }

  .user-content {
    text-align: center;
    font-size: 36rpx;
  }

  .tip {
    padding: 20rpx 50rpx;
    font-size: 24rpx;
    text-align: center;
    color: #c7c7c7;
    margin-top: 10rpx;
  }

  .tip text {
    color: #2e896f;
  }

  /deep/.card-btn {
    width: 540rpx;
    height: 80rpx;
    line-height: 80rpx;
    border-radius: 40rpx;
    font-size: 30rpx;
    padding-left: 14rpx;
    padding-right: 14rpx;
    color: #fff;
    background-color: #2e896f;
    // border: 1rpx solid #eeeeee;
  }
  .dialogBG {
    margin: 0 30rpx 30rpx 30rpx;
    background-color: #fff;
    border-radius: 14rpx;
  }
  .dialog-all {
    width: 670rpx;
    position: relative;
  }

  .dialog-all image {
    width: 100%;
    height: 100%;
  }
  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 170rpx;
    z-index: -1;
  }
  .dialog-small-close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .dialog-center {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 50upx 55upx;
    box-sizing: border-box;
  }
  .dialog-title {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    font-weight: 600;
    justify-content: center;
  }
  .dialog-item {
    box-sizing: border-box;
    font-size: 32upx;
    line-height: 45upx;
    text-align: center;
    margin-top: 40rpx;
  }

  .selected {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #2e896f;
    border: 1px solid #2e896f;
    border-radius: 35rpx;
    overflow: visible;
  }

  .not-selected {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #000;
    border: 1px solid #c8c8c8;
    border-radius: 35rpx;
    overflow: visible;
  }
  .common-sure-btn-1 {
    width: 250rpx;
    height: 80rpx;
    background: #2e896f;
    border-radius: 40rpx;
    font-size: 30rpx;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(0.995); /* 解决ios上圆角失效 */
  }
  .common-cancel-btn {
    width: 250rpx;
    height: 80rpx;
    border-radius: 40rpx;
    border: 1rpx solid #2e896f;
    font-size: 30rpx;
    color: #2e896f;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(0.995); /* 解决ios上圆角失效 */
  }
</style>

<template>
  <view>
    <view class="info">
      <view class="" v-for="(item, index) in tableData" :key="index">
        <view class="formItem">
          <view class="label">{{ item.label }}</view>
          <view class="value" v-if="item.value == 'grade'">{{
            getGrade(formData[item.value])
          }}</view>
          <!-- <view class="value" v-else-if="item.value == 'gender'">{{ getSex(formData[item.value]) }}</view> -->
          <view class="value" v-else>{{ formData[item.value] }}</view>
        </view>
        <view class="border"></view>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        tableData: [
          {
            id: 1,
            label: "姓名",
            value: "realName",
          },
          {
            id: 2,
            label: "编号",
            value: "studentCode",
          },
          // {
          //   id: 3,
          //   label: '性别',
          //   value: 'gender'
          // },
          {
            id: 4,
            label: "年级",
            value: "grade",
          },
          {
            id: 5,
            label: "学校名称",
            value: "school",
          },
        ],
        gradeNameArr:
          "一年级_二年级_三年级_四年级_五年级_六年级_初一_初二_初三_高一_高二_高三_大一_大二_大三_大四_其他_幼儿园".split(
            "_"
          ),
        formData: {},
      };
    },
    onLoad(e) {
      let that = this;
      // #ifdef MP-WEIXIN
      wx.qy.checkSession({
        success: function () {
          //session_key 未过期，并且在本生命周期一直有效
          that.initData();
        },
        fail: function () {
          uni.removeStorageSync("token");
          uni.removeStorageSync("scrm_token");
          uni.setStorageSync("userRole", "notLogin");
          uni.setStorageSync("isLogin", false);
          that.$store.commit("setType", "notLogin");
          
          uni.navigateTo({
            url: "/qyWechat/login_qywx",
          });
         
        },
      });
       // #endif
    },
    onShow() {
      let token = uni.getStorageSync("token");
      if (token) {
        this.initData();
      }
    },
    methods: {
      getSex(val) {
        if (val) {
          let sexName = "";
          sexName = val == 1 ? "男" : "女";
          return sexName;
        } else {
          return "";
        }
      },
      getGrade(val) {
        if (val) {
          val -= 1;
          let index = 0;
          if (val <= 0) {
            index = 0;
          } else if (val >= this.gradeNameArr.length) {
            index = this.gradeNameArr.length - 1;
          } else {
            index = val;
          }
          return this.gradeNameArr[index];
        }
        return this.gradeNameArr[this.gradeNameArr.length - 1];
      },
      initData() {
        let that = this;
        // let chatId=0
        wx.qy.getCurExternalChat({
          success: (res) => {
            uni.showLoading({ title: "加载中" });
            let chatId = res.chatId; //返回当前外部群的群聊ID
            that.$http
              .get("/deliver/web/student/contact/info/getStudentInfoByChatId", {
                chatId,
              })
              .then(({ data }) => {
                uni.hideLoading();
                this.formData = data.data;
                console.log(data.data, "===============");
              })
              .catch((err) => {
                uni.hideLoading();
                console.log(err);
              });
          },
          fail: (err) => {
            uni.showToast({
              title: err.errMsg,
              icon: "none",
            });
            console.log(err, "errrrrrrrrrrrrrrrrrrrrrrrrrrrr");
          },
        });
      },
      initData1(chatId) {
        let that = this;
        // let chatId=0
        uni.showLoading({ title: "加载中" });
        that.$http
          .get("/deliver/web/student/contact/info/getStudentInfoByChatId", {
            chatId,
          })
          .then(({ data }) => {
            uni.hideLoading();
            this.formData = data.data;
            console.log(data.data, "===============");
          })
          .catch((err) => {
            uni.hideLoading();
            console.log(err);
          });
      },
    },
  };
</script>

<style lang="scss">
  page {
    background: #f3f8fc;
  }
  .info {
    width: 690rpx;
    min-height: 1100rpx;
    background: #ffffff;
    border-radius: 14rpx;
    margin: 30rpx auto;
  }
  .formItem {
    display: flex;
    padding: 30rpx;
    .label {
      width: 250rpx;
    }
    .value {
      flex: 1;
    }
  }
  .border {
    width: 650rpx;
    height: 0;
    border: 1rpx solid #efefef;
    margin: 0 auto;
  }
</style>

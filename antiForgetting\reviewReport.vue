<template>
  <view class="reviewBox">
    <view>
      <view class="header">
        <uni-icons type="arrowleft" class="backIcon" size="20" color="#fff" @click="goback()"></uni-icons>
        <text style="color: #fff">{{ todayDate }} 复习报告</text>
      </view>

      <view class="record_body plr-45" :style="'height: ' + bodyHeight + 'rpx;'">
        <view class="reviewUp_box">
          <view class="review_rate">
            <!-- 复习报告图 -->
            <image class="rate_bgImage" src="https://document.dxznjy.com/applet/interesting/review_rate.png" mode=""></image>
            <view class="rate_num">
              {{ showData.rate }}
              <text>%</text>
              <view class="review_rate_text">正确率</view>
            </view>
            <view class="review_task">本场作业完成情况统计</view>
            <view class="student-name-code">
              <view v-if="studentName">学生姓名：{{ studentName }}</view>
              <view style="margin-top: 14rpx" v-if="studentCode">学生编号：{{ studentCode }}</view>
            </view>
          </view>

          <view class="over_review_census">
            <view class="review-box">
              <view>
                <view class="review_litleTitle review_color1" style="margin-bottom: 30rpx">
                  <view style="color: #f1ac71; font-weight: 600">{{ showData.wordCount || 0 }}个</view>
                  <view style="font-size: 30rpx; color: #915617">需复习</view>
                </view>
              </view>
              <view>
                <view class="review_litleTitle review_color3">
                  <view style="color: #4ad0ff; font-weight: 600">{{ wrongList.length || 0 }}个</view>
                  <view style="font-size: 30rpx; color: #1b85a9">遗忘数</view>
                </view>
              </view>
            </view>
            <view class="review-box">
              <view>
                <view class="review_litleTitle review_color2" style="margin-bottom: 30rpx">
                  <view style="color: #07dcb2; font-weight: 600">{{ showData.reviewWordVM.length || 0 }}个</view>
                  <view style="font-size: 30rpx; color: #0a856d">已复习</view>
                </view>
              </view>
              <view>
                <view class="review_litleTitle review_color4">
                  <view style="color: #7676e9; font-weight: 600">{{ showData.wordCount - showData.reviewWordVM.length || 0 }}个</view>
                  <view style="font-size: 30rpx; color: #3e3ebf">未复习</view>
                </view>
              </view>
            </view>
          </view>
          <!-- 新加样式 -->
        </view>
        <view class="study_records" :style="'min-height: ' + recordHeight + 'rpx;'">
          <view class="study_record_titles pl-40">本次遗忘记录</view>

          <view class="record_listBox">
            <view v-if="wrongList && wrongList.length > 0" class="plr-40">
              <view class="recordList" @click="sayWord(item.word)" v-for="item in wrongList">
                <view class="recordWord">
                  <view>
                    <text class="recordIcon" :class="item.correct == 0 ? 'recordIcon1' : ''"></text>
                    <text class="record_title">{{ item.word }}</text>
                  </view>
                  <image style="width: 44rpx; height: 36rpx" src="https://document.dxznjy.com/applet/newimages/img_laba.png" mode="widthFix"></image>
                </view>
                <view class="recordTranst">
                  {{ item.translation }}
                </view>
              </view>
            </view>
            <view class="lake_page" v-if="wrongList && wrongList.length == 0">
              <image src="https://document.dxznjy.com/applet/newimages/zan.png" mode="widthFix"></image>
              <view class="lake_page-text">
                <view>本次没有遗忘单词哦</view>
                <view>请再接再厉~</view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="reviewBottom">
        <view class="reviewBottom-2">
          <view class="sendButton" @click="goUrl(`/antiForgetting/history?studentCode=${showData.studentCode}`)">往期复习</view>
        </view>
      </view>
      <!--   <view class="reviewBottom">
        <view class="reviewLeft_btn reviewBtn" @click="goUrl(`/antiForgetting/history?studentCode=${showData.studentCode}`)">往期复习</view>
                       <button class="reviewRight_btn_1 reviewBtn" hover-class="none" open-type="share">
                    分享链接给家长
                </button>
      </view> -->
    </view>
  </view>
</template>

<script>
  var innerAudioContext;
  export default {
    data() {
      return {
        echartsList: 0,
        src: 'http://*************:5000/v/b88b6ff1fa28125e',
        showData: {
          studentCode: '',
          reviewWordVM: [],
          rate: 0,
          wordCount: 0
        },
        postImage: getApp().globalData.postHost,
        recordHeight: 0,
        bodyHeight: 0,
        recordInnerHeight: 250,
        wrongList: null,

        studentName: '',
        studentCode: '',
        todayDate: '',

        isplay: false,
        reviewId: ''
      };
    },
    onLoad(options) {
      console.log(options, 'options');
      this.showData = JSON.parse(decodeURIComponent(options.data));
      this.echartsList = this.showData.rate;
      this.reviewId = this.showData.reviewId;
      console.log(this.echartsList);
      console.log(this.showData);

      this.wrongList = this.showData.reviewWordVM.filter((ele) => ele.correct == 0);
      let nowScreenHeight = this.$util.pxTorpx();
      this.bodyHeight = nowScreenHeight - 240;
      this.recordHeight = nowScreenHeight - 940;
      this.recordInnerHeight = nowScreenHeight - 1000;

      this.studentName = uni.getStorageSync('21StudentName');
      this.studentCode = this.showData.studentCode;

      var currentDate = new Date();
      var year = currentDate.getFullYear();
      var month = currentDate.getMonth() + 1;
      var day = currentDate.getDate();
      this.todayDate = year + '-' + month + '-' + day;

      let that = this;
      innerAudioContext = uni.createInnerAudioContext();
      innerAudioContext.onPlay(() => {
        console.log('开始播放');
      });
      innerAudioContext.onStop(function () {
        console.log('播放结束');
        that.isplay = false;
      });
      innerAudioContext.onPause(function () {
        console.log('播放暂停');
        that.isplay = false;
      });
      innerAudioContext.onError((res) => {
        console.log(res.errMsg);
        console.log(res.errCode);
        that.isplay = false;
      });
    },
    onShareAppMessage() {
      return {
        title: '21天抗遗忘打卡',
        imageUrl: '', //分享封面
        path: '/antiForgetting/historyReviewReport?reviewId=' + this.reviewId + '&isShare=true&isType=0'
      };
    },
    methods: {
      updateEchartsList(value) {
        this.echartsList = value;
      },
      //返回上一页
      goback() {
        uni.navigateBack({
          delta: 1
        });
      },
      // back() {
      // 	uni.navigateTo({
      // 		url:'/antiForgetting/history?studentCode=' + this.studentCode
      // 	})
      // },
      // uni.reLaunch({
      // 	url: '/antiForgetting/index?studentCode=' + this.studentCode
      // });
      goUrl(url) {
        uni.redirectTo({
          url: url
        });
      },

      sayWord(word) {
        var that = this;
        uni.$http
          .get('/znyy/app/query/word/voice', {
            word: word
          })
          .then((result) => {
            if (result.data.success) {
              var w = encodeURIComponent(result.data.data);
              var linkUrl;
              if (w.endsWith('.mp3')) {
                linkUrl = 'https://document.dxznjy.com/' + w;
              } else {
                linkUrl = 'https://document.dxznjy.com/' + w + '.mp3';
              }
              // #ifdef MP-WEIXIN
              innerAudioContext.obeyMuteSwitch = false;
              // #endif
              innerAudioContext.src = linkUrl;
              innerAudioContext.play();
            } else {
              that.$util.alter(result.data.message);
            }
          });
      },

      //获取海报图片
      shareReviewReport() {
        console.log('分享海报');
        let that = this;
        uni.request({
          url: that.postImage + 'api/link',
          data: {
            'zt#syh_rightRate': that.showData.rate,
            'aRig_zt#syh_alreadyReview': that.showData.reviewWordVM.length,
            'aRig_zt#syh_noReview': that.showData.wordCount - that.showData.reviewWordVM.length,
            'aRig_zt#syh_needReview': that.showData.wordCount,
            accessKey: 'ApfrIzxCoK1DwNZO',
            secretKey: 'EJCwlrnv6QZ0PCdvrWGi',
            posterId: 7
          },
          method: 'POST',
          header: {
            'Content-Type': 'application/json'
          },
          success: (res) => {
            console.log(res.data);
            let urlString = res.data.url;
            that.src = that.postImage + 'v/' + urlString.split('/v/')[1];
            uni.navigateTo({
              url: '/antiForgetting/playbill?url=' + that.src
            });
          }
        });
      }
    }
    // onShareAppMessage(res) {
    // 	if (res.from === 'button') { // 来自页面内分享按钮
    // 		console.log(res.target)
    // 	}
    // 	return {
    // 		title: '自定义分享标题',
    // 		path: '/pages/test/test?id=123'
    // 	}
    // }
  };
</script>

<style lang="less">
  page {
    height: 100%;
  }

  .review_litleTitle {
    width: 290rpx;
    height: 160rpx;
    border-radius: 14rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
  }
  .over_review_census {
    margin: 0 auto;
    width: 610rpx;
    // height: 350rpx;
    display: flex;
    justify-content: space-between;
  }

  .header {
    padding: 84rpx 0 20rpx 30rpx;
    box-sizing: border-box;
    text-align: center;
    position: relative;
  }

  .backIcon {
    position: absolute;
    left: 30rpx;
    top: 90rpx;
  }

  .study_records {
    width: 100%;
    padding-top: 15rpx;
    // margin-left: 45rpx;
    background: white;
    margin-top: 30rpx;
    box-shadow: 2px 4px 20px 1px rgba(0, 0, 0, 0.12);
    border-radius: 14rpx;
    // padding-left: 40rpx;
    box-sizing: border-box;
  }

  .study_record_titles {
    font-size: 32rpx;
    font-family: AlibabaPuHuiTiM;
    color: #000000;
    font-weight: bold;
    margin-top: 20rpx;
  }

  .reviewUp_box {
    width: 658rpx;
    left: 0;
    background: white;
    margin-top: 44rpx;
    margin-bottom: 44rpx;
    box-shadow: 2px 4px 20px 1px rgba(0, 0, 0, 0.12);
    border-radius: 14rpx;
    position: relative;
  }

  .student-name-code {
    width: 100%;
    text-align: center;
    position: absolute;
    bottom: -20rpx;
    font-size: 30rpx;
  }

  .record_listBox {
    width: 100%;
    overflow-y: auto;
    padding-bottom: 20rpx;
  }

  .ml-32 {
    margin-left: 32rpx;
  }

  .lake_page {
    margin-top: 120rpx;
    display: flex;
    flex-direction: column;
    align-items: center;

    image {
      width: 200rpx;
      height: 160rpx;
      margin-bottom: 30rpx;
    }
  }

  .lake_page-text {
    text-align: center;
    font-size: 30rpx;
    font-family: SourceHanSansSC-Regular, SourceHanSansSC;
    font-weight: 400;
    color: #666666;
    line-height: 45rpx;
  }
  .reviewBottom-2 {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .sendButton {
    width: 586rpx;
    height: 90rpx;
    line-height: 90rpx;
    color: #ffffff;
    font-size: 30rpx;
    text-align: center;
    background: #2e896f;
    border-radius: 45rpx;
    margin: 10rpx auto 0;
  }
</style>

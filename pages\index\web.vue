<template>
  <view>
    <web-view :webview-styles="webviewStyles" :src="url"></web-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      url: '',
      webviewStyles: {
        progress: {
          color: '#FF3333'
        }
      }
    };
  },
  onReady() {},
  /*  onBackPress: function (e) {
    if (e.from == 'backbutton') {
      this.clickBack = true;
      this.outTipsShow = true;
      // return true;
      return false;
    }
    // 弹窗确认回退
    if (e.from == 'navigateBack') {
      return false;
    }
    // return true 表示禁止默认返回
  }, */
  onHide() {
    if (this.data.webview) {
      console.log('重新加载webview中的页面', this.data);
      this.data.webview.reload(); // 重新加载webview中的页面
    }
  },
  onShow() {},
  onLoad(option) {
    if (option.url != undefined) {
      //#ifdef APP-PLUS
      this.url = decodeURIComponent(option.url);
      // console.log(1);
      //#endif
      //#ifdef MP-WEIXIN
      this.url = decodeURIComponent(option.url);
      /*    if (this.url.includes('views/dologin.html')) {
        // console.log(2, this.url);
        uni.enableAlertBeforeUnload({
          message: '退出会议后无法再次进入会议哦，确认退出吗？',
          success: function (res) {
            console.log('成功：', res);
          },
          fail: function (err) {
            console.log('失败：', err);
          }
        });
      } */
      //#endif
      // #ifdef H5
      this.url = option.url;
      // console.log(3);
      // #endif
    }
  },
  onUnload() {
    //成功之后  取消返回监听
    // uni.disableAlertBeforeUnload(); //取消返回监听
  },
  methods: {}
};
</script>

<style></style>

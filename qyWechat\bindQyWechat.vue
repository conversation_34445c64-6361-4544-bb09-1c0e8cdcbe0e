<template>
  <view>
    <view style="width: 100%; text-align: center; position: absolute; top: 118rpx">
      <text style="color: #000000; line-height: 44rpx; font-size: 34rpx; font-weight: bold">绑定手机号</text>
    </view>
    <view class="from">
      <view class="loginTitle">
        <image src="https://document.dxznjy.com/app/images/zhujiaoduan/logo.png" mode=""></image>
      </view>
      <view class="login-box">
        <view class="phone-input mt-20">
          <image style="width: 30rpx; height: 30rpx" class="marginright16" src="/static/images/login_phone.png"></image>
          <input type="number" style="width: 100upx" name="smsCode" placeholder="请输入手机号" placeholder-class="phClass" class="input" maxlength="11" v-model="mobile" />
          <uni-icons type="clear" v-if="mobile != ''" class="marginright16" color="#c7c7c7" size="18" @click="mobile = ''" />
        </view>

        <view class="phone-input mt-20">
          <image src="/static/images/login_mima.png" style="width: 30rpx; height: 30rpx" class="marginright16"></image>
          <input type="number" style="width: 100upx" name="smsCode" placeholder="请输入验证码" placeholder-class="phClass" class="input" maxlength="6" v-model="smsCode" />
          <button class="get-yzm" :class="codeBtn.waitingCode ? 'disableCodeBtn' : 'active'" :disabled="codeBtn.waitingCode" @tap="verifiConfirm">{{ codeBtn.text }}</button>
          <uni-icons type="clear" v-if="smsCode != ''" color="#c7c7c7" size="18" class="marginright16" @click="smsCode = ''" />
        </view>

        <button class="login" :class="mobile != '' && mobile.length == 11 && smsCode != '' && smsCode.length >= 6 ? '' : 'default'" type="default" @click="bindMobile">绑定</button>
      </view>
    </view>
    <uni-popup ref="loadingPopup" type="center" :is-mask-click="false">
      <view class="bg-ff radius-15 t-c">
        <view class="loadingpadding">
          <image src="https://document.dxznjy.com/Assistant/loading.png" class="loadingImg"></image>
          <view class="mt-30">加载中...</view>
          <view>请耐心等候</view>
        </view>
      </view>
    </uni-popup>

    <!-- 图形验证弹窗 -->
    <view v-if="codeIsShow" style="height: 100vh; width: 100vw; background-color: rgba(0, 0, 0, 0.75); position: fixed; z-index: 199; top: 0; left: 0" @click.prevent="closePopup">
      <view class="code" @click.stop="clear">
        <view style="display: flex; align-items: center; height: 80rpx; padding: 0 48rpx; justify-content: space-between">
          <view style="font-weight: bold; font-size: 26rpx">请输入图形验证码</view>
          <view @click.stop="closePopup">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
        </view>
        <view>
          <pt-images-verification
            :uuid="uuid"
            :imgHeight="codeObj.imgHeight"
            :imgWidth="codeObj.imgWidth"
            :left="codeObj.top"
            :bgImg="codeObj.bgImg"
            :maskImg="codeObj.maskImg"
            @refresh="refresh"
            @success="success"
          ></pt-images-verification>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import util from '@/utils/util.js';
  import ptImagesVerification from './components/pt-images-verification.vue';
  // import Rsa from '@/utils/rsa.js';
  export default {
    components: {
      ptImagesVerification
    },
    data() {
      return {
        mobile: '',
        smsCode: '',
        seconds: 60,
        codeBtn: {
          text: '发送验证码',
          waitingCode: false,
          count: this.seconds
        },
        role: 'Teacher',
        showpwd: false, //是否显示验证码
        isLogin: 'islogin', //是否登陆
        codeIsShow: false,
        uuid: '',
        codeObj: {
          bgImg: '',
          maskImg: '',
          imgHeight: '',
          imgWidth: '',
          top: 0 // 凹陷区距离背景图左边的距离
        }
      };
    },
    onLoad() {},
    onShow() {},
    methods: {
      async bindMobile() {
        if (!this.validateForm()) {
          return false;
        } else {
          uni.showLoading({
            title: '绑定中'
          });
          wx.qy.login({
            success: async ({ code }) => {
              await this.$http
                .post('/scrm/qywechat/bind', {
                  code: code,
                  mobile: this.mobile,
                  verificationCode: this.smsCode
                })
                .then((res) => {
                  console.log(res);
                  uni.hideLoading();
                  if (res.data.success) {
                    uni.showToast({
                      title: '绑定成功'
                    });
                    setTimeout(() => {
                      uni.navigateBack();
                    }, 1000);
                  } else {
                    return uni.showToast({
                      title: res.data.errMessage,
                      icon: 'none'
                    });
                  }
                });
            }
          });
        }
      },
      //验证表单
      validateForm() {
        if (this.mobile == '') {
          this.$util.alter('手机号码不能为空');
          return false;
        }
        if (!/^1[3456789]\d{9}$/.test(this.mobile)) {
          this.$util.alter('请输入正确的手机号');
          return false;
        }
        if (this.smsCode == '') {
          this.$util.alter('验证码不能为空');
          return false;
        }
        if (this.smsCode.length < 6) {
          this.$util.alter('验证码不能少于6位');
          return false;
        }
        return true;
      },
      // 发送验证码前图形校验
      async verifiConfirm() {
        if (!/^1[3456789]\d{9}$/.test(this.mobile)) {
          this.$util.alter('请输入正确的手机号');
          return false;
        }
        await this.getNum();
        this.codeIsShow = true;
      },
      //发送手机校验码
      async GetsmsCode(e) {
        let result = await this.$http.post('/znyy/user/sms/bind/' + this.mobile + '?code=' + e + '&uuid=' + this.uuid);
        if (result.data.success) {
          this.$util.alter('发送成功,请注意查收');
          this.codeBtn.waitingCode = true;
          this.codeBtn.count = this.seconds;
          this.codeBtn.text = this.codeBtn.count + 's';
          let countdown = setInterval(() => {
            this.codeBtn.count--;
            this.codeBtn.text = this.codeBtn.count + 's';
            if (this.codeBtn.count < 0) {
              clearInterval(countdown);
              this.codeBtn.text = '重新发送';
              this.codeBtn.waitingCode = false;
            }
          }, 1000);
        } else {
          this.$util.alter(result.data.message);
        }
      },
      generateUUID() {
        var d = new Date().getTime(); //Timestamp
        // var d2 = (performance && performance.now && performance.now() * 1000) || 0; //Time in microseconds since page-load or 0 if unsupported
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
          var r = Math.random() * 16; //random number between 0 and 16
          // if (d > 0) {
          //Use timestamp until depleted
          r = (d + r) % 16 | 0;
          d = Math.floor(d / 16);
          // } else {
          // log
          // //Use microseconds since page-load if supported
          // r = (d2 + r) % 16 | 0;
          // d2 = Math.floor(d2 / 16);
          // }
          return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16);
        });
      },
      getNum() {
        let that = this;
        return new Promise((resolve, reject) => {
          this.uuid = this.generateUUID();

          that.$http.get('/new/security/captcha/image/slide?uuid=' + that.uuid).then((res) => {
            if (res.data.success) {
              let a = res.data.data;
              that.codeObj.maskImg = 'data:image/png;base64,' + a.cutImage;
              that.codeObj.bgImg = 'data:image/png;base64,' + a.oriImage;
              that.codeObj.imgWidth = a.cutImageWidth / 3.2 + '%';
              that.codeObj.imgHeight = a.cutImageHeight / 1.6 + '%';
              // that.codeObj.left = a.ypos;
              // that.codeObj.top = a.ypos / 1.6 + 1;
              that.codeObj.top = a.ypos / 1.6 + 1.4;
              resolve();
            }
          });
        });
      },
      closePopup() {
        // if (this.stop) return;
        this.codeIsShow = false;
      },
      clear(e) {},
      refresh() {
        // this.closePopup();
        this.getNum();
      },
      success(e) {
        this.closePopup();
        this.GetsmsCode(e);
      }
    }
  };
</script>

<style>
  uni-page-body,
  page {
    padding-bottom: 0px !important;
    background-image: url('https://document.dxznjy.com/applet/newimages/login-banner.png');
    background-repeat: no-repeat;
    background-size: cover;
  }

  .loginTitle {
    font-weight: bold;
    padding: 0 50rpx;
    text-align: center;
  }
  .loginTitle image {
    width: 130rpx;
    height: 130rpx;
  }

  .container {
    width: 100%;
    height: 100%;
  }

  .from {
    width: 690rpx;
    min-height: 900rpx;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    background-color: #fff;
    border-radius: 33rpx;
    padding-top: 104rpx;
    top: 300rpx;
  }
  .bottom_center {
    position: absolute;
    margin-bottom: 50rpx;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 270px;
  }
  .login-top {
    display: block;
    width: 240rpx;
    height: 240rpx;
    margin: 0 auto !important;
  }

  .login-box {
    padding-top: 50rpx;
    padding-bottom: 40rpx;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#7Fd2f9f6, endColorstr=#7Fd2f9f6);
    border-radius: 10rpx;
    margin-top: 60rpx;
  }

  .phone-input {
    width: 560rpx;
    height: 90rpx;
    line-height: 90rpx;
    background-color: #f6f6f6;
    margin-left: 50rpx;
    margin-right: 50rpx;
    border-radius: 10rpx;
    display: flex;
    align-items: center;
    position: relative;
    padding-bottom: 16upx;
    padding: 0 20rpx 0 40rpx;
    /* border-bottom: 2rpx solid #ececea; */
  }

  .eyeicon {
    width: 40rpx;
    height: 30rpx;
  }

  .phClass {
    color: #c7c7c7;
    font-size: 30rpx;
  }

  .login {
    margin-left: 50rpx;
    margin-right: 50rpx;
    background: rgb(46, 137, 111) !important;
    color: #fff !important;
    font-size: 32rpx;
    border-radius: 45rpx;
    text-align: center;
    height: 90rpx;
    line-height: 90rpx;
    margin-top: 80rpx;
  }

  .login.default {
    background: #c8c8c8 !important;
  }

  .touying-box {
    margin-left: 40rpx;
    margin-right: 40rpx;
    margin-top: -8rpx;
  }

  .touying {
    height: 28rpx;
    width: 100%;
  }

  .phone {
    width: 34rpx;
    height: 52rpx;
    margin-left: 20rpx;
    margin-right: 20rpx;
  }

  .input {
    flex-grow: 1;
  }

  .ml-20 {
    margin-left: 30rpx;
  }

  button::after {
    border: none;
  }

  .mt-20 {
    margin-top: 20rpx;
  }

  .text {
    padding: 0 50rpx;
    height: 120rpx;
    line-height: 120rpx;
    display: flex;
    justify-content: space-between;
    color: #0398ef;
    font-size: 28rpx;
  }

  .agreement {
    display: flex;
    align-items: center;
    font-size: 26rpx;
    padding: 0 0 0 40rpx;
    box-sizing: border-box;
    color: #c7c7c7;
  }

  .agreement text {
    color: #225449;
  }

  .marginright16 {
    margin-right: 16rpx;
  }

  .review_btn {
    width: 240upx;
    height: 80upx;
    margin: 0 !important;
    border-radius: 45upx;
    font-size: 30upx;
    color: #ffffff;
    line-height: 80upx;
    justify-content: center;
    text-align: center;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
  }

  .close_btn {
    width: 240upx;
    height: 80upx;
    color: #2e896f;
    font-size: 30upx;
    line-height: 80upx;
    text-align: center;
    border-radius: 45upx;
    box-sizing: border-box;
    border: 1px solid #2e896f;
  }

  .loadingImg {
    width: 120rpx;
    height: 120rpx;
  }

  .loadingpadding {
    padding: 85rpx 250rpx;
  }

  .agreementContent {
    padding: 35rpx 0 32rpx 0;
    margin: 0 24rpx 0 24rpx;
  }

  .agreementTitle {
    text-align: center;
    font-weight: bold;
    font-size: 34rpx;
    margin-bottom: 35rpx;
  }

  .agreementText {
    padding: 0 108rpx 0 108rpx;
    margin-top: 44rpx;
    text-align: center;
    font-size: 32rpx;
  }

  .ok_btn {
    width: 400rpx;
    height: 80rpx;
    color: #ffffff;
    font-size: 30rpx;
    text-align: center;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 40rpx;
  }

  .no_ok_btn {
    margin-top: 24rpx;
    color: #666666;
    text-align: center;
  }

  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 185rpx;
    z-index: -1;
  }

  .uni-combox {
    padding: 0rpx 0rpx !important;
  }
  .uni-combox__input-plac {
    color: #c7c7c7;
    font-size: 30rpx;
  }

  .get-yzm {
    position: relative;
    right: 0;
    height: 50rpx;
    line-height: 45rpx;
    background-color: #2b8b6e;
    color: #fff;
    font-size: 24rpx;
    border: 2rpx solid #0398ef;
    z-index: 99;
    border-radius: 10rpx;
  }

  .get-yzm.disableCodeBtn {
    border-color: #cccccc !important;
    color: #fff !important;
    background-color: #cccccc !important;
  }

  .yanzhengma {
    width: 38rpx;
    height: 42rpx;
    /* margin-left: 20rpx; */
    margin-right: 20rpx;
  }
  .phClass {
    color: #999999;
    font-size: 30rpx;
  }

  .marginright16 {
    margin-right: 16rpx;
  }
  .code {
    position: absolute;
    left: 50%;
    top: 50%;
    width: 96vw;
    background-color: #fff;
    transform: translate(-50%, -50%);
    padding: 20rpx 0 40rpx;
    border-radius: 28rpx;
  }
</style>

@font-face {
  font-family: "iconfont"; /* Project id 3795659 */
  src: url('/static/alifont/iconfont.ttf') format('woff2'),
       url('/static/alifont/iconfont.ttf') format('woff'),
       url('/static/alifont/iconfont.ttf') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-nan:before {
  content: "\e608";
}

.icon-nv1:before {
  content: "\e611";
}

.icon-lishijilu:before {
  content: "\e61d";
}

.icon-xiugai:before {
  content: "\e8cf";
}

.icon-baogao:before {
  content: "\e62d";
}


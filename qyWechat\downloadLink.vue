<template>
  <view>
    <view class="bg-ff radius-15 mlr-30" :style="{ height: useHeight + 'rpx' }">
      <u-navbar title="教练管理" bgColor="#f3f8fc" placeholder>
        <view class="" slot="left">
          <u-icon name="arrow-left" size="26" bold color="#000" @click="goBack"></u-icon>
        </view>
        <view class="u-nav-slot" slot="center" style="font-weight: 600">鼎校会议下载</view>
      </u-navbar>
      <view class="pd-30">
        <view class="item">
          <view class="title">电脑端下载</view>
          <view class="value">
            <view class="" style="display: flex; align-items: center; margin: 10rpx 0">
              <view>请用电脑浏览器打开链接</view>
              <view class="btn" @click="buttonClicked(pcUrl)">复制链接</view>
            </view>
            <text class="text-button" @click="buttonClicked(pcUrl)">{{ pcUrl || '' }}</text>
            <text style="margin-left: 10rpx">进行下载</text>
          </view>
        </view>
        <view class="item">
          <view class="title">安卓下载</view>
          <view class="value">
            <view class="" style="display: flex; align-items: center; margin: 10rpx 0">
              <view>请用手机浏览器打开链接</view>
              <view class="btn" @click="buttonClicked(andUrl)">复制链接</view>
            </view>
            <text class="text-button" @click="buttonClicked(andUrl)">{{ andUrl || '' }}</text>
            <text style="margin-left: 10rpx;line-height: 40rpx;">进行下载</text>
          </view>
        </view>
        <view class="item">
          <view class="title">ios下载</view>
          <view class="value">
            <text>请用前往App Store搜索</text>
            <text class="text-button" @click="buttonClicked('鼎校会议')">{{ '鼎校会议' }}</text>
            <text style="margin-left: 10rpx;line-height: 40rpx;">进行下载</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      useHeight: 0,
      pcUrl: 'https://document.dxznjy.com/app/kingmeeting/win/Kingmeeting.exe',
      // andUrl: 'https://www.pgyer.com/4eWRrjWz',
      andUrl: 'https://document.dxznjy.com/app/kingmeeting/and/app-release.apk',
      iosUrl: ''
    };
  },
  onReady() {
    uni.getSystemInfo({
      success: (res) => {
        // 可使用窗口高度，将px转换rpx
        let h = res.windowHeight * (750 / res.windowWidth);
        this.useHeight = h - 30;
      }
    });
  },
  onLoad() {},
  onShow() {},
  methods: {
    buttonClicked(item) {
      if (item) {
        uni.setClipboardData({
          data: item,
          success: (res) => {
            console.log(res);
            uni.showToast({
              title: '链接已复制至剪贴板',
              icon: 'none',
  duration: 3000, // 延长显示时间
  mask: true // 防止用户操作
            });
          }
        });
      }
    },
    goBack() {
      uni.navigateBack();
    }
  }
};
</script>
<style lang="scss">
page {
  background-color: #f3f8fc;
}
.pd-30 {
  padding: 30rpx;
}
.item {
  margin-top: 30rpx;
  .title {
    color: #000;
    font-size: 30rpx;
    font-weight: 700;
    margin-bottom: 30rpx;
  }
  .value {
    color: #000000;
    font-size: 28rpx;
    word-wrap: break-word;
  }
}
.text-button {
  color: #f2a58b;
  cursor: pointer;
  word-break: break-all;
  white-space: normal;
  line-height: 40rpx;
}
.text-button:hover {
  color: #007aff;
}
.btn {
  width: 130rpx;
  height: 50rpx;
  line-height: 50rpx;
  text-align: center;
  font-size: 28rpx;
  color: #ffffff;
  background: #2e896f;
  border-radius: 8rpx;
  margin-left: 60rpx;
}
</style>

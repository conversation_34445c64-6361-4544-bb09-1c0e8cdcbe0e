*{
	margin: 0;
	padding: 0;
}
.nodata { text-align: center; }
.nodata image { width: 380rpx;height: 252rpx;margin: 116rpx auto 90rpx auto; }
.nodata text { display: block; font-size: 24rpx;color: #959595; }
span { display: inline-block; }


textarea { width: 100%; } 
.positionRelative{
	position: relative;
}
.positionAbsolute{
	position: absolute;
}

/* 字体大小 */
.font18 { font-size: 18rpx!important; }
.font20 { font-size: 20rpx!important; }
.font24 { font-size: 24rpx!important; }
.font26 { font-size: 26rpx!important; }
.font28 { font-size: 28upx!important; }
.font30 { font-size: 30upx!important; }
.font32 { font-size: 32upx!important; }
.font34 { font-size: 34upx!important; }
.font36 { font-size: 36upx!important; }
.font38 { font-size: 38upx!important; }

/* 字体 */
.fontAliR{
	font-family: "ALBBR"!important;
}
/* 底部灰色边框 */
.bottomBorder{
	border-bottom: 1px solid #EEEEEE;
}

/* margin居中 */
.marginCenter { margin: 0 auto; }

/* 文字居中 */
.textCenter { text-align: center; }
.textRight { text-align: right; }

/* 超出十个字省略 */
.overTenEllipsis{
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipisis;	
}

/* 下划线 */
.textLineCenter {
	text-decoration: line-through;
}

/* 屏幕高度100% */
.overHeight{
		height: 100vh;
		overflow: hidden;
}
.overPaddingBottom {
	padding-bottom: 180upx;
	box-sizing: border-box;
}

/* 字体样式 */
.t-l { text-align: left; } 
.t-c { text-align: center; } 
.t-r { text-align: right; }
.bold{ font-weight: bold; }
.fixed { position: fixed; }
.relative { position: relative; }
.absolute { position: absolute; }
.h1 { font-size: 34rpx; font-weight: bold; }
.h2 { font-size: 32rpx; font-weight: bold; }
.line-t{text-decoration: line-through;}
.placeholder{color: #a8a8a8;font-size: 28rpx;}
.show, .block { display: block; }
.hide { display: none; }
.inline{ display: inline-block; }
.height { height: 120rpx; }
.sizing{box-sizing: border-box;}
.monospace{font-family: monospace;}
.w100 {width: 100%;}
.w610 {width: 305px;}
.h100 {height: 100%;}
.shadow {box-shadow:0px 0px 15rpx 0px rgba(0,0,0,0.1);}
.shadow-t{box-shadow: 0 0 10rpx 5rpx rgba(138,138,137,.1);}

.col-1 {width: 62rpx;}
.col-2 {width: 125rpx;}
.col-3 {width: 187rpx;}
.col-4 {width: 250rpx;}
.col-5 {width: 321rpx;}
.col-6 {width: 375rpx;}
.col-7 {width: 437rpx;}
.col-8 {width: 500rpx;}
.col-9 {width: 562rpx;}
.col-10 {width: 625rpx;}
.col-11 {width: 687rpx;}
.col-12 {width: 750rpx;}

/* 弹性盒子 */
.flex { display: flex; align-items: center; justify-content: space-between; flex-flow: row wrap; }
.flex-box { flex: 1; overflow: hidden; }
.flex-c { display: flex; align-items: center; justify-content: center;  }
.flex-s { display: flex; align-items: center; justify-content: space-between;  }
.flex-dir-row { display: flex;flex-direction: row; }
.flex-dir-col { display: flex;flex-direction: column; }
.flex-a-c { display: flex;align-items: center; }

.flex-col{display: flex;flex-direction: column;align-items: center;justify-content: center;}
.flex-x-s { justify-content: flex-start; }
.flex-x-c { justify-content: center; }
.flex-x-b { justify-content: space-between; }
.flex-x-a { justify-content: space-around; }
.flex-x-e { justify-content: flex-end; }

.flex-y-s { align-items: flex-start; }
.flex-y-st { align-items: stretch; }
.flex-y-c { align-items: center; }
.flex-y-e { align-items: flex-end; }

.flex-three { box-sizing: border-box; flex: 0 0 33.3%; }
.flex-four { box-sizing: border-box; flex: 0 0 25%; }
.flex-five { box-sizing: border-box; flex: 0 0 20%; }
.flex-six { box-sizing: border-box; flex: 0 0 16.6%; }

.flex-self-s { display: flex; align-self: flex-start;}
.flex-self-c { display: flex; align-self: center;}
.flex-self-e { display: flex; align-self: flex-end;}

.flex-k{ flex-shrink: 0; }

.flex-wrap{ display: flex; flex-wrap: wrap; }
.flex-nowrap{ display: flex; flex-wrap: nowrap; }


/* radius */
.radius-3 { border-radius: 3rpx; overflow: hidden; }
.radius-5 { border-radius: 5rpx; overflow: hidden; }
.radius-6 { border-radius: 6rpx; overflow: hidden; }
.radius-8 { border-radius: 8rpx; overflow: hidden; }
.radius-10 { border-radius: 10rpx; overflow: hidden; }
.radius-12 { border-radius: 12rpx; overflow: hidden; }
.radius-15 { border-radius: 15rpx; overflow: hidden; }
.radius-20 { border-radius: 20rpx; overflow: hidden; }
.radius-30 { border-radius: 30rpx; overflow: hidden; }
.radius-50 { border-radius: 500rpx; overflow: hidden; }
.radius-all { border-radius: 50%; overflow: hidden; }

/*正方形盒子*/
.box-30{width: 30rpx;height: 30rpx;}
.box-50{width: 50rpx;height: 50rpx;}
.box-60{width: 60rpx;height: 60rpx;}
.box-70{width: 70rpx;height: 70rpx;}
.box-80{width: 80rpx;height: 80rpx;}
.box-90{width: 90rpx;height: 90rpx;}
.box-100{width: 100rpx;height: 100rpx;}
.box-110{width: 110rpx;height: 110rpx;}
.box-120{width: 120rpx;height: 120rpx;}
.box-136{width: 136rpx;height: 136rpx;}
.box-140{width: 140rpx;height: 140rpx;}
.box-150{width: 150rpx;height: 150rpx;}
.box-160{width: 160rpx;height: 160rpx;}
.box-180{width: 180rpx;height: 180rpx;}
.box-200{width: 200rpx;height: 200rpx;}
.box-210{width: 200rpx;height: 210rpx;}
.box-220{width: 220rpx;height: 220rpx;}
.box-240{width: 240rpx;height: 240rpx;}
.box-250{width: 240rpx;height: 250rpx;}

/* 长方形 */
.boxl-180{width: 180rpx;height: 80rpx;}

/* 超出换行 */
.onelist { text-overflow: ellipsis; overflow: hidden; white-space: nowrap; }
.twolist { display: -webkit-box; word-break: break-all; text-overflow: -o-ellipsis-lastline; overflow: hidden; text-overflow: ellipsis; -webkit-line-clamp: 2; -webkit-box-orient: vertical; }
.thrlist { display: -webkit-box; word-break: break-all; text-overflow: -o-ellipsis-lastline; overflow: hidden; text-overflow: ellipsis; -webkit-line-clamp: 3; -webkit-box-orient: vertical; }
.elli {width:100%;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;}
.elli-2 {text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;line-clamp: 2;-webkit-box-orient: vertical;}
.elli-3 {text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 3;line-clamp: 3;-webkit-box-orient: vertical;}

/* 字体大小 */
.f-72 { font-size: 72rpx; } 
.f-70 { font-size: 70rpx; } 
.f-60 { font-size: 60rpx; } 
.f-58 { font-size: 58rpx; } 
.f-56 { font-size: 56rpx; } 
.f-54 { font-size: 54rpx; } 
.f-52 { font-size: 52rpx; } 
.f-50 { font-size: 50rpx; } 
.f-48 { font-size: 48rpx; } 
.f-46 { font-size: 46rpx; } 
.f-44 { font-size: 44rpx; } 
.f-42 { font-size: 42rpx; } 
.f-40 { font-size: 40rpx; } 
.f-38 { font-size: 38rpx; } 
.f-36 { font-size: 36rpx; } 
.f-34 { font-size: 34rpx; } 
.f-32 { font-size: 32rpx; } 
.f-30 { font-size: 30rpx; } 
.f-28 { font-size: 28rpx; } 
.f-26 { font-size: 26rpx; } 
.f-24 { font-size: 24rpx; } 
.f-22 { font-size: 22rpx; } 
.f-20 { font-size: 20rpx; } 
.f-18 { font-size: 18rpx; } 
.f-16 { font-size: 16rpx; }
.f-14 { font-size: 14rpx; }

/* 字体颜色 */
.c-00 { color: #000000; } 
.c-1a { color: #1a1a1a; }
.c-33 { color: #333333; } 
.c-44 { color: #444444; } 
.c-29 { color: #292929; } 
.c-5c { color: #5c5c5c; } 
.c-66 { color: #666666; } 
.c-77 { color: #777777; } 
.c-88 { color: #888888; } 
.c-99 { color: #999999; }
.c-96 { color: #969696; }
.c-aa { color: #aaaaaa; }
.c-ab { color: #ababab; }
.c-a5 { color: #a5a5a5; }
.c-a8 { color: #a8a8a8; }
.c-c2 { color: #C2C2C2; }
.c-e5 { color: #e5e5e5; }
.c-da { color: #DADADA; }
.c-ea { color: #EAEAEA; }
.c-ff { color: #ffffff; } 
.c-f2 { color: #F2F2F2; } 
.c-f0 { color: #F01010; }
.c-a2 { color: #FFA200;}
.c-81 { color: #81E2AF;}

.c-m{ color: var(--main)!important; }
.c-mr{ color: var(--minor)!important; }
.c-s{ color: var(--second)!important; }
.c-orange{ color: var(--orange)!important; }
.c-olive{ color: var(--olive)!important; }
.c-green{ color: var(--green)!important; }
.c-cyan{ color: var(--cyan)!important; }
.c-blue{ color: var(--blue)!important; }
.c-purple{ color: var(--purple)!important; }
.c-red{ color: var(--red)!important; }
.c-page{ color: var(--page)!important; }
.c-font{ color: var(--font)!important; }
.c-sub{ color: var(--sub)!important; }
.c-price{ color: var(--price)!important; }
.c-red {color: #FF0000;}
.c-fa {color: #FAA314;}
.c-fea {color: #EA6031;}
.c-fe5 {color: #E57126;}
.c-blue {color: #007EFF;}
.c-faa {color: #FAA115;}
.c-ffe {color: #FFEAD5;}
.c-fde {color: #DEDEDE;}
.c-499 {color: #499D1C;}
.c-ebd {color: #EDBC73;}
.c-2e8 {color: #2E896F;}
.c-f9c {color: #96c4b7;}
.c-f1a {color: #F1A667;}
.c-f76 {color: #7676E9;}
.c-fba {color: #babaf4;}
.c-fe9 {color: #E97676;}
.c-ff4 {color: #f4baba;}
.c-f1d {color: #1D755C;}
.c-fe5 {color: #E57126;}
/* 背景色颜色 */
.bg-00 { background: #000; } 
.bg-33 { background: #333; } 
.bg-23 { background: #232221; } 
.bg-66 { background: #666; } 
.bg-77 { background: #777; } 
.bg-88 { background: #888; } 
.bg-96 { background: #969696; }
.bg-99 { background: #999; }
.bg-a8 { background: #a8a8a8; }
.bg-1a { background: #1a1a1a; }
.bg-c2 { background: #C2C2C2; }
.bg-cc { background: #cccccc; }
.bg-dd { background: #dddddd; }
.bg-ee { background: #eeeeee; }
.bg-e5 { background: #e5e5e5; }
.bg-f4 { background: #f4f4f4; }
.bg-f5 { background: #f5f5f5; }
.bg-f7 { background: #f7f7f7; } 
.bg-f3 { background: #f3f8fc; } 
.bg-ff { background: #ffffff; } 
.bg-m{ background: var(--main)!important; }
.bg-mr{ background: var(--minor)!important; }
.bg-s{ background: var(--second)!important; }
.bg-orange{ background: var(--orange)!important; }
.bg-olive{ background: var(--olive)!important; }
.bg-green{ background: var(--green)!important; }
.bg-cyan{ background: var(--cyan)!important; }
.bg-blue{ background: var(--blue)!important; }
.bg-purple{ background: var(--purple)!important; }
.bg-red  { background: var(--red)!important; }
.bg-page{ background: var(--page)!important; }
.bg-font{ background: var(--font)!important; }
.bg-sub{ background: var(--sub)!important; }
.bg-linear-r{ background: linear-gradient(to right,var(--main),var(--minor)); }
.bg-linear-b{ background: linear-gradient(to bottom,var(--main),var(--minor)); }
.bg-a2 {background-color: #FFA200;}

/* 行高 */
.lh-12 { line-height: 1.2; }
.lh-13 { line-height: 1.3; }
.lh-14 { line-height: 1.4; }
.lh-15 { line-height: 1.5; }
.lh-16 { line-height: 1.6; }
.lh-17 { line-height: 1.7; }
.lh-18 { line-height: 1.8; }
.lh-20 { line-height: 20rpx; }
.lh-24 { line-height: 24rpx; }
.lh-28 { line-height: 28rpx; }
.lh-30 { line-height: 30rpx; }
.lh-32 { line-height: 32rpx; }
.lh-36 { line-height: 36rpx; }
.lh-40 { line-height: 40rpx; }
.lh-44 { line-height: 44rpx; }
.lh-50 { line-height: 50rpx; }
.lh-56 { line-height: 56rpx; }
.lh-60 { line-height: 60rpx; }
.lh-64 { line-height: 64rpx; }
.lh-70 { line-height: 70rpx; }
.lh-72 { line-height: 72rpx; }
.lh-80 { line-height: 80rpx; }
.lh-90 { line-height: 90rpx; }
.lh-98 { line-height: 98rpx; }
.lh-100 { line-height: 100rpx; }

/* 高度 */
.h-20 { height: 20rpx; }
.h-30 { height: 30rpx; }
.h-40 { height: 40rpx; }
.h-50 { height: 50rpx; }
.h-60 { height: 60rpx; }
.h-70 { height: 70rpx; }
.h-80 { height: 80rpx; }
.h-90 { height: 90rpx; }
.h-100 { height: 100rpx; }
.h-110 { height: 110rpx; }
.h-115 { height: 115rpx!important; }
.h-120 { height: 120rpx; }
.h-130 { height: 130rpx; }
.h-140 { height: 140rpx; }
.h-160 { height: 160rpx; }

/* 内边距 */
.p-5{ padding: 5rpx; }
.p-8{ padding: 8rpx; }
.p-10{ padding: 10rpx; }
.p-12{ padding: 12rpx; }
.p-15{ padding: 15rpx; }
.p-20{ padding: 20rpx; }
.p-25{ padding: 25rpx; }
.p-30{ padding: 30rpx; }
.p-35{ padding: 35rpx; }
.p-40{ padding: 40rpx; }
.p-45{ padding: 45rpx; }
.p-50{ padding: 50rpx; }

.pl-5 { padding-left: 5rpx;  } 
.pl-8 { padding-left: 8rpx; }
.pl-10 { padding-left: 10rpx; } 
.pl-12 { padding-left: 12rpx; } 
.pl-15 { padding-left: 15rpx; } 
.pl-20 { padding-left: 20rpx; } 
.pl-25 { padding-left: 25rpx; } 
.pl-30 { padding-left: 30rpx; } 
.pl-35 { padding-left: 35rpx; } 
.pl-40 { padding-left: 40rpx; }
.pl-45 { padding-left: 45rpx; } 
.pl-50 { padding-left: 50rpx; }
.pl-100 { padding-left: 100rpx; }

.pr-5  { padding-right: 5rpx;  } 
.pr-8  { padding-right: 8rpx;  } 
.pr-10 { padding-right: 10rpx; } 
.pr-12 { padding-right: 12rpx; } 
.pr-15 { padding-right: 15rpx; } 
.pr-20 { padding-right: 20rpx; } 
.pr-25 { padding-right: 25rpx; } 
.pr-30 { padding-right: 30rpx; } 
.pr-35 { padding-right: 35rpx; } 
.pr-40 { padding-right: 40rpx; }
.pr-45 { padding-right: 45rpx; } 
.pr-50 { padding-right: 50rpx; }
.pr-100 { padding-right: 100rpx; }

.pt-5  { padding-top: 5rpx;  }
.pt-8  { padding-top: 8rpx;  }
.pt-10 { padding-top: 10rpx; }
.pt-12 { padding-top: 12rpx; }
.pt-15 { padding-top: 15rpx; }
.pt-20 { padding-top: 20rpx; }
.pt-25 { padding-top: 25rpx; }
.pt-30 { padding-top: 30rpx; }
.pt-35 { padding-top: 35rpx; }
.pt-40 { padding-top: 40rpx; }
.pt-45 { padding-top: 45rpx; }
.pt-50 { padding-top: 50rpx; }
.pt-60 { padding-top: 60rpx; }
.pt-70 { padding-top: 70rpx; }
.pt-75 { padding-top: 75rpx; }
.pt-110 { padding-top: 110rpx; }
.pt-140 { padding-top: 140rpx; }

.pb-5  { padding-bottom: 5rpx;  } 
.pb-8  { padding-bottom: 8rpx;  } 
.pb-10 { padding-bottom: 10rpx; } 
.pb-12 { padding-bottom: 12rpx; } 
.pb-15 { padding-bottom: 15rpx; } 
.pb-20 { padding-bottom: 20rpx; } 
.pb-25 { padding-bottom: 25rpx; } 
.pb-30 { padding-bottom: 30rpx; } 
.pb-35 { padding-bottom: 35rpx; } 
.pb-40 { padding-bottom: 40rpx; }
.pb-45 { padding-bottom: 45rpx; } 
.pb-50 { padding-bottom: 50rpx; }
.pb-55 { padding-bottom: 55rpx; }
.pb-60 { padding-bottom: 60rpx; }
.pb-70 { padding-bottom: 70rpx; }
.pb-150 { padding-bottom: 150rpx; }

.ptb-5  { padding-top: 5rpx;padding-bottom: 5rpx; } 
.ptb-8  { padding-top: 8rpx;padding-bottom: 8rpx; } 
.ptb-10 { padding-top: 10rpx;padding-bottom: 10rpx; } 
.ptb-12 { padding-top: 12rpx;padding-bottom: 12rpx; } 
.ptb-15 { padding-top: 15rpx;padding-bottom: 15rpx; } 
.ptb-20 { padding-top: 20rpx;padding-bottom: 20rpx; } 
.ptb-25 { padding-top: 25rpx;padding-bottom: 25rpx; } 
.ptb-30 { padding-top: 30rpx;padding-bottom: 30rpx; } 
.ptb-35 { padding-top: 35rpx;padding-bottom: 35rpx; } 
.ptb-40 { padding-top: 40rpx;padding-bottom: 40rpx; }
.ptb-45 { padding-top: 45rpx;padding-bottom: 45rpx; } 
.ptb-50 { padding-top: 50rpx;padding-bottom: 50rpx; }
.ptb-55 { padding-top: 55rpx;padding-bottom: 55rpx; }
.ptb-60 { padding-top: 60rpx;padding-bottom: 60rpx; }

.plr-5  { padding-left: 5rpx;padding-right: 5rpx;} 
.plr-8  { padding-left: 8rpx;padding-right: 8rpx; } 
.plr-10 { padding-left: 10rpx;padding-right: 10rpx; } 
.plr-12 { padding-left: 12rpx;padding-right: 12rpx; } 
.plr-15 { padding-left: 15rpx;padding-right: 15rpx; } 
.plr-20 { padding-left: 20rpx;padding-right: 20rpx; } 
.plr-25 { padding-left: 25rpx;padding-right: 25rpx; } 
.plr-30 { padding-left: 30rpx;padding-right: 30rpx; } 
.plr-32 { padding-left: 32rpx;padding-right: 32rpx; } 
.plr-35 { padding-left: 35rpx;padding-right: 35rpx; } 
.plr-40 { padding-left: 40rpx;padding-right: 40rpx; }
.plr-45 { padding-left: 45rpx;padding-right: 45rpx; } 
.plr-50 { padding-left: 50rpx;padding-right: 50rpx; }
.plr-60 { padding-left: 60rpx;padding-right: 60rpx; }
.plr-80 { padding-left: 80rpx;padding-right: 80rpx; }

/* 外边距 */
.m-5{ margin: 5rpx; }
.m-8{ margin: 8rpx; }
.m-10{ margin: 10rpx; }
.m-12{ margin: 12rpx; }
.m-15{ margin: 15rpx; }
.m-20{ margin: 20rpx; }
.m-25{ margin: 25rpx; }
.m-30{ margin: 30rpx; }
.m-35{ margin: 35rpx; }
.m-40{ margin: 40rpx; }
.m-45{ margin: 45rpx; }
.m-50{ margin: 50rpx; }

.ml-5  { margin-left: 5rpx;  } 
.ml-8  { margin-left: 8rpx;  } 
.ml-10 { margin-left: 10rpx; } 
.ml-12 { margin-left: 12rpx; } 
.ml-15 { margin-left: 15rpx; } 
.ml-20 { margin-left: 20rpx; } 
.ml-25 { margin-left: 25rpx; } 
.ml-30 { margin-left: 30rpx; } 
.ml-35 { margin-left: 35rpx; } 
.ml-40 { margin-left: 40rpx; }
.ml-45 { margin-left: 45rpx; } 
.ml-50 { margin-left: 50rpx; }
.ml-55 { margin-left: 55rpx; }
.ml-60 { margin-left: 60rpx; }

.mr-5  { margin-right: 5rpx;  } 
.mr-8  { margin-right: 8rpx;  } 
.mr-10 { margin-right: 10rpx; } 
.mr-12 { margin-right: 12rpx; } 
.mr-15 { margin-right: 15rpx; } 
.mr-20 { margin-right: 20rpx; } 
.mr-25 { margin-right: 25rpx; } 
.mr-30 { margin-right: 30rpx; } 
.mr-35 { margin-right: 35rpx; } 
.mr-40 { margin-right: 40rpx; }
.mr-45 { margin-right: 45rpx; } 
.mr-50 { margin-right: 50rpx; }
.mr-55 { margin-right: 55rpx; }
.mr-60 { margin-right: 60rpx; }
.mr-65 { margin-right: 65rpx; }
.mr-70 { margin-right: 70rpx; }
.mr-75 { margin-right: 75rpx; }
.mr-80 { margin-right: 80rpx; }

.mt-5  { margin-top: 5rpx;  }
.mt-8  { margin-top: 8rpx;  }
.mt-10 { margin-top: 10rpx; }
.mt-12 { margin-top: 12rpx; }
.mt-15 { margin-top: 15rpx; }
.mt-20 { margin-top: 20rpx; }
.mt-25 { margin-top: 25rpx; }
.mt-30 { margin-top: 30rpx; }
.mt-35 { margin-top: 35rpx; }
.mt-40 { margin-top: 40rpx; }
.mt-45 { margin-top: 45rpx; }
.mt-50 { margin-top: 50rpx; }
.mt-55 { margin-top: 55rpx; }
.mt-60 { margin-top: 60rpx; }
.mt-65 { margin-top: 65rpx; }
.mt-70 { margin-top: 70rpx; }
.mt-75 { margin-top: 75rpx; }
.mt-80 { margin-top: 80rpx; }

.mb-5  { margin-bottom: 5rpx;  } 
.mb-8  { margin-bottom: 8rpx;  } 
.mb-10 { margin-bottom: 10rpx; } 
.mb-12 { margin-bottom: 12rpx; } 
.mb-15 { margin-bottom: 15rpx; } 
.mb-20 { margin-bottom: 20rpx; } 
.mb-25 { margin-bottom: 25rpx; } 
.mb-30 { margin-bottom: 30rpx; } 
.mb-35 { margin-bottom: 35rpx; } 
.mb-40 { margin-bottom: 40rpx; }
.mb-45 { margin-bottom: 45rpx; } 
.mb-50 { margin-bottom: 50rpx; }

.mtb-5  { margin-top: 5rpx;margin-bottom: 5rpx;} 
.mtb-8  { margin-top: 8rpx;margin-bottom: 8rpx; } 
.mtb-10 { margin-top: 10rpx;margin-bottom: 10rpx; } 
.mtb-12 { margin-top: 12rpx;margin-bottom: 12rpx; } 
.mtb-15 { margin-top: 15rpx;margin-bottom: 15rpx; } 
.mtb-20 { margin-top: 20rpx;margin-bottom: 20rpx; } 
.mtb-25 { margin-top: 25rpx;margin-bottom: 25rpx; } 
.mtb-30 { margin-top: 30rpx;margin-bottom: 30rpx; } 
.mtb-35 { margin-top: 35rpx;margin-bottom: 35rpx; } 
.mtb-40 { margin-top: 40rpx;margin-bottom: 40rpx; }
.mtb-45 { margin-top: 45rpx;margin-bottom: 45rpx; } 
.mtb-50 { margin-top: 50rpx;margin-bottom: 50rpx; }
.mtb-55 { margin-top: 55rpx;margin-bottom: 55rpx; }
.mtb-60 { margin-top: 60rpx;margin-bottom: 60rpx; }

.mlr-5  { margin-left: 5rpx ;margin-right: 5rpx ; } 
.mlr-8  { margin-left: 8rpx ;margin-right: 8rpx ; } 
.mlr-10 { margin-left: 10rpx;margin-right: 10rpx; } 
.mlr-12 { margin-left: 12rpx;margin-right: 12rpx; } 
.mlr-15 { margin-left: 15rpx;margin-right: 15rpx; } 
.mlr-20 { margin-left: 20rpx;margin-right: 20rpx; } 
.mlr-25 { margin-left: 25rpx;margin-right: 25rpx; } 
.mlr-30 { margin-left: 30rpx;margin-right: 30rpx; } 
.mlr-35 { margin-left: 35rpx;margin-right: 35rpx; } 
.mlr-40 { margin-left: 40rpx;margin-right: 40rpx; }
.mlr-45 { margin-left: 45rpx;margin-right: 45rpx; } 
.mlr-50 { margin-left: 50rpx;margin-right: 50rpx; }
.mlr-55 { margin-left: 55rpx;margin-right: 55rpx; }
.mlr-60 { margin-left: 60rpx;margin-right: 60rpx; }


.margin50 { margin: 50upx; }


/* 上间距 */
.marginTop8{ margin-top: 8upx!important; }
.marginTop10{ margin-top: 10upx!important; }
.marginTop12{ margin-top: 12upx!important; }
.marginTop14{ margin-top: 14upx!important; }
.marginTop16{ margin-top: 16upx!important; }
.marginTop18{ margin-top: 18upx!important; }
.marginTop20{ margin-top: 20upx!important; }
.marginTop22{ margin-top: 22upx!important; }
.marginTop25{ margin-top: 25upx!important; }
.marginTop27{ margin-top: 27upx!important; }
.marginTop30{ margin-top: 30upx!important; }
.marginTop34{ margin-top: 34upx!important; }
.marginTop36{ margin-top: 36upx!important; }
.marginTop40{ margin-top: 40upx!important; }
.marginTop42{ margin-top: 42upx!important; }
.marginTop47{ margin-top: 47upx!important; }
.marginTop50{ margin-top: 50upx!important; }
.marginTop54{ margin-top: 54upx!important; }
.marginTop58{ margin-top: 58upx!important; }
.marginTop62{ margin-top: 62upx!important; }
.marginTop64{ margin-top: 64upx!important; }
.marginTop80{ margin-top: 80upx!important; }
.marginTop100 { margin-top: 100upx!important; }

/* 左间距 */
.marginLeft10{ margin-left: 10upx; }
.marginLeft12{ margin-left: 12upx; }
.marginLeft14{ margin-left: 14upx; }
.marginLeft16{ margin-left: 16upx; }
.marginLeft17{ margin-left: 17upx; }
.marginLeft18{ margin-left: 18upx; }
.marginLeft19{ margin-left: 19upx; }
.marginLeft20{ margin-left: 20upx; }
.marginLeft23{ margin-left: 23upx; }
.marginLeft28{ margin-left: 28upx; }
.marginLeft30{ margin-left: 30upx; }
.marginLeft32{ margin-left: 32upx; }
.marginLeft40{ margin-left: 40upx; }
.marginLeft46{ margin-left: 46upx; }
.marginLeft60{ margin-left: 60upx; }
/* 右间距 */
.marginRight7{ margin-right: 7upx; }
.marginRight10{ margin-right: 10upx; }
.marginRight12{ margin-right: 12upx; }
.marginRight16{ margin-right: 16upx; }
.marginRight20{ margin-right: 20upx; }
.marginRight28{ margin-right: 28upx; }
.marginRight50{ margin-right: 50upx; }
.marginRight170{ margin-right: 170upx; }


/* 底部间距 */
.marginBottom8{margin-bottom: 8upx;}
.marginBottom10{margin-bottom: 10upx;}
.marginBottom16{margin-bottom: 16upx;}
.marginBottom18{margin-bottom: 18upx;}
.marginBottom20{margin-bottom: 20upx;}
.marginBottom25{margin-bottom: 25upx;}
.marginBottom26{margin-bottom: 26upx;}
.marginBottom30{margin-bottom: 30upx;}
.marginBottom42{margin-bottom: 42upx;}
.marginBottom53{margin-bottom: 53upx;}
.marginBottom56{margin-bottom: 56upx;}
.marginBottom87{margin-bottom: 87upx;}

/* margin水平居中 */
.displayBlock { display: block; }


/* padding */
.paddingTop10 {
	padding-top: 10upx!important;
}
.paddingTop20 {
	padding-top: 20upx!important;
}
.paddingTop30 {
	padding-top: 30upx!important;
}
.paddingTop36 {
	padding-top: 36upx!important;
}
.paddingTop100 {
	padding-top: 100upx!important;
}
.padding11 {
	padding: 11upx 0!important;
}
.paddingTopBottom30 {
	padding: 30upx 0!important;
	box-sizing: border-box;
}
.paddingTopBottom20 {
	padding: 20upx 0!important;
	box-sizing: border-box;
}
.paddingLeftRight9{
	padding: 0 9upx;
	box-sizing: border-box;
}
.paddingLeftRight20{
	padding: 0 20upx;
	box-sizing: border-box;
}
.paddingLeftRight30{
	padding: 0 30upx;
	box-sizing: border-box;
}
.padding20 {
	padding: 20upx;
	box-sizing: border-box;
}
.padding30 {
	padding: 30upx;
	box-sizing: border-box;
}
.padding36 {
	padding: 36upx 0!important;
}
.paddingBottom6{
	padding-bottom: 6upx!important;
}
.paddingBottom20{
	padding-bottom: 20upx!important;
}
.paddingBottom30{
	padding-bottom: 30upx!important;
}
.paddingBottom35{
	padding-bottom: 35upx!important;
}
.paddingBottom40{
	padding-bottom: 40upx!important;
}

/* 底部虚线边框 */
.borderBottomDash {
	border-bottom: 1upx dashed #F6F6F6;
}
.borderBottomDashBlack {
	border-bottom: 1upx dashed #000000;
}
.borderBottomDashD5 {
	border-bottom: 1upx dashed #D5D5D5;
}
.borderBottomSolidD5 {
	border-bottom: 1upx solid #D5D5D5;
}
.borderTopD5{
	border-top: 1upx dashed #D5D5D5;
}

.borderRadiusLeft16 {
	border-radius: 16upx 0 0 16upx;
}
.borderRadiusRight16 {
	border-radius: 0 16upx 16upx 0;
}

.borerRadiusTop14{
	border-radius: 14upx 14upx 0 0;
}
.borerRadiusBottom14{
	border-radius: 0 0 14upx 14upx;
}


/* 模块宽度 */
.width30 {width: 30upx;height: 30upx;}
.width70 {width: 70upx;height: 70upx;}
.width104 {width: 104upx;height: 104upx;}
.width308 {width: 308upx;}
.width650 {width: 650upx;}

/* 高度 */
.height36 {
	height: 36upx;
	line-height: 36upx;
}
.height50 {
	height: 50upx;
	line-height: 50upx;
}
.height78 {
	height: 78upx;
	line-height: 78upx;
}
.height80 {
	height: 80upx;
	line-height: 80upx;
}
.height85 {
	height: 85upx;
	line-height: 85upx;
}
.height90 {
	height: 90upx;
	line-height: 90upx;
}
.height100 {
	height: 100upx;
	line-height: 100upx;
}
.height110 {
	height: 110upx;
	line-height: 110upx;
}


.deleteTask{
	position: absolute;
	top: -52upx;
	right: 0;
}


/* 按钮 */
.tag_grey {
	padding: 0 8upx;
	height: 36upx;
	border-radius: 4upx;
	border: 1upx solid #999999;
}
.commonBtn{
	display: flex;
	align-items: center;
	justify-items: center;
	padding: 2upx 8upx;
	border-radius: 8upx;
	text-align: center;
	color: #ffffff;
	font-size: 26upx;
}
.commonTwoBtn{
	height: 90upx;
	line-height: 90upx;
	border-radius: 45upx;
	text-align: center;
	color: #ffffff;
	font-size: 30upx;
}


/* 悬浮按钮 */
.float_btn {
	width: 200upx;
	height: 200upx;
	color: #ffffff;
	background: #0398EF;
	line-height: 200upx;
	border-radius: 50%;
	position: absolute;
	bottom: 300upx;
	right: 100upx;
	font-size: 30upx;
}
/* 圆角两个按钮切换 */
.change_time_type {
	border-radius: 8upx;
	font-size: 30upx;
	color: #979797;
	height: 50upx;
	line-height: 50upx;
	text-align: center;
}
.change_time_type .item_left{
	width: 80upx;
	border-radius: 8upx 0upx 0upx 8upx;
	border: 1upx solid #979797;
	border-right: none;
}
.change_time_type .item_right{
	width: 80upx;
	border-radius: 0upx 8upx 8upx 0upx;
	border: 1upx solid #979797;
	border-left: none;
}
.change_time_type .left_active{
	background: #0398EF;
	color: #ffffff;
}
.change_time_type .right_active{
	background: #0398EF;
	color: #ffffff;
}

/* 圆形 */
.circleOverhidden{
	border-radius: 50%;
	overflow: hidden;
}


/* flex布局 */
/*两端对齐*/
.displayFlex {
	display: flex;
}
.displayInlineFlex {
	display: inline-flex;
	flex-wrap: wrap;
}
.displayFlexSB {
	display: flex;
	justify-content: space-between;
}
.displayFlexTop{ align-items: flex-start; }
.displayFlexCenter{ align-items: center; }
.displayFlexVCenter{ justify-content: center;text-align: center; }
.displayflexWrap { flex-wrap: wrap; }

.fixedBottom {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	z-index: 22;
}






/* 字体颜色 */
.color_black { color: #000000; }
.color_red { color: #ff0000; }
.color_grey66 { color: #666666; }
.color_grey { color: #999999; }
.color_grey97 { color: #979797; }
.color_grey98 { color: #98A9B2; }
.color_white { color: white; }
.color_black30{ color: #303133; }
.color_blue { color: #0398EF; }
.color_green { color: #2DC032; }
.color_orange { color: #EB7E2D; }
.color_yellow { color: #F8B342; }
.color_black33 { color: #333333; }

/* 背景色 */
.background_green{ background: #33C378; }
.background_purple{ background: #6C71E6; }
.background_blue1{ background: #3482F6; }
.background_e8{ background: #E8E8E8; }

/* 字体 */
.fontFamilyAR{
	/* font-family: "ALIBBR"; */
}
.fontFamilyAM{
	/* font-family: "ALIBBM"; */
}

/* 自定义头部 */
.userHead { height: 80rpx;position: relative; line-height: 80rpx;text-align: center;color: #ffffff;padding-top: 40rpx;background: #0a6658;font-size: 30rpx;font-weight: bold; }
.userHead .iconBack { position: absolute; left: 20rpx;top: 40rpx; }


/* input 光标居右 */
.inputRight { 
	text-align: right;
}


/* 搜索框样式 */
.usersearch{
	background-color: #f9f9f9;
	width:620rpx;
	height: 58rpx;
	border-radius:100rpx;
	margin: 20rpx auto;
	padding: 4rpx;
	display: flex;
	justify-content: center;
	align-items: center;	 
}
.searchIcon {
	width: 26rpx;
	height: 26rpx;
}
 .usersearch input{
	    line-height: 60rpx;
		width: 90%;
	    text-align: left;
		padding-left: 20rpx;
	  }	
.searchText{ font-size: 24rpx; color: #a0a0a0;margin: 0 20rpx; }

/* 弹窗 */
.userPopup {
	width: 670upx;
	background: #FFFFFF;
	border-radius: 14upx;
	position: relative;
}
.popupClose {
	position: absolute;
	right: 20upx;
	top: 20upx;
}
.popupTitle { width: 100%;text-align: center;padding-top: 40upx; }
.user_popup_text {
	padding: 0 85upx;
	margin-bottom: 80upx;
	height: 280upx;
	overflow-y: auto;
	line-height: 52upx;
}
.user_popup_rule {
	width: 440upx;
	line-height: 52upx;
}


/* picker弹窗确定颜色 */
.uni-picker-container .uni-picker-action.uni-picker-action-confirm {
    float: right;
    color: #0a6658;
}
/* 弹窗关闭close图标 */
.closePop { display: block; margin: 70rpx auto; }

/* 自定义card类似阴影 */
.userCard{
	width: 690upx;
	padding: 30rpx 30rpx 10rpx 30rpx;
	box-shadow: 0upx 10upx 60upx #e3e3e3;
	margin-top: 30rpx;
	box-sizing: border-box;
	border-radius: 20rpx;
	background-color: #ffffff;
}
.userCardTwo{
	width: 710upx;
	padding: 30upx 20upx;
	margin-top: 20upx;
	box-sizing: border-box;
	border-radius: 14upx;
	background-color: #ffffff;
	margin: 20upx auto 0 auto;
}
.userCardThree{
	width: 710upx;
	padding: 0;
	margin-top: 20upx;
	box-sizing: border-box;
	border-radius: 14upx;
	background-color: #ffffff;
	margin: 20upx auto 0 auto;
}

/*公共蓝色按钮*/
.blueBtn{
	display: inline-block;
	text-align: center;
	font-size: 34upx;
	padding: 16upx 54upx;
	color: #0398EF;
	height: 80upx;
	font-family: "ALBBR";
	border: 1px solid #0398EF;
	overflow: visible;
	border-radius: 14upx;
	box-sizing: border-box;
	margin: 0 auto;
	transform: scale(0.995); /* 解决ios上圆角失效 */
}

.activeBtn {
	font-size: 34upx;
	padding: 16upx 88upx;
	color: #FFFFFF;
	background-color: #0398EF;
	height: 80upx;
	font-family: "ALBBR";
	border-radius: 14upx;
	box-sizing: border-box;
	margin: 0 auto;
}
.dealtBtn{
	background-color: #e4e4e4!important;
}

.blueBtn_two{
	display: inline-block;
	text-align: center;
	font-size: 34upx;
	color: #0398EF;
	height: 80upx;
	line-height: 80upx;
	border: 1px solid #0398EF;
	overflow: visible;
	box-sizing: border-box;
	border-radius: 14upx;
	transform: scale(0.995); /* 解决ios上圆角失效 */
}
.greyBtn_two{
	display: inline-block;
	text-align: center;
	font-size: 34upx;
	color: #B7BDC6;
	height: 80upx;
	line-height: 80upx;
	border: 1px solid #B7BDC6;
	overflow: visible;
	box-sizing: border-box;
	border-radius: 14upx;
	transform: scale(0.995); /* 解决ios上圆角失效 */
}


.activeBtn_two {
	font-size: 34upx;
	text-align: center;
	color: #FFFFFF;
	background-color: #0398EF;
	height: 80upx;
	line-height: 80upx;
	border-radius: 14upx;
	margin: 0 auto;
}


.popContent{
	width: 565rpx;
	background: #FFFFFF;
	border-radius: 16rpx;
}
.popContTitle{text-align: center;height: 80rpx;line-height: 80rpx;font-size: 32rpx; }
.termSelect {
	padding: 0 45rpx;
	
}
.popGroupBox { border-top: 1rpx solid #e5e5e5;display: flex;margin-top: 32rpx; }
.popGroupBox .popGroup{ width: 50%;text-align: center;height: 84rpx;line-height: 84rpx;font-size: 32rpx; }
.popGroupBox .popGroup:first-child{ border-right: 1rpx solid #e5e5e5; }
.popGroupBox .popGroup.cancel { color: #999999; }
.popGroupBox .popGroup.confirm { color: #0a6658; }


/* 按钮组 */
.btn_group{ padding: 96rpx 120rpx;width: 100%;height: 84rpx;box-sizing: border-box;display: flex;justify-content: space-between; }
.btn_groupList { width: 216rpx;height: 84rpx;border: 1rpx solid #0a6659;color: #0a6659; border-radius: 12rpx;text-align: center;line-height: 84rpx;box-sizing: border-box;font-size: 30rpx; }
.btn_groupList.active { background: #0A6659;color: #FFFFFF; }


/* 按钮样式 */
.defaultButton { 
	height: 40upx;
	background: #DFDFDF;
	border-radius: 32upx;
	 padding: 0 10upx;
	 box-sizing: border-box;
}


/* 发布任务 */
.textArea_title {
	height: 100upx;
	line-height: 100upx;
	color: ;
}

.taskItem { width: 100%;height: 100upx;line-height: 100upx; align-items: center;; border-bottom: 1px solid #EEEEEE;  }

.textArea_box {  
	width: 100%;
	height: 345upx;
	margin-top: 38upx;
	position: relative;
}
.fontNum {
	position: absolute;
	bottom: 20upx;
	right: 0upx;
}

.tipIcon {
	width: 30upx;
	height: 30upx;
	margin-left: 8upx;
}
.changeTable {
	width: 200upx;
	height: 60upx;
	overflow: hidden;
}
.changeTable .tast_rule{
	width: 100upx;
	height: 60upx;
	text-align: center;
	line-height: 60upx;
	color: #979797;
	border: 1upx solid #979797;
	box-sizing: border-box;
}
.changeTableLeft{
	border-radius: 8upx 0upx 0upx 8upx;
	border-right: none!important;
}
.changeTableRight{
	border-radius: 0 8upx 8upx 0upx;
	border-left: none!important;
}

.changeTable .tast_rule.active{
	color: white;
	background: #0398EF;
	border-color: #0398EF!important;
}
.dateChooseBtn {
	width: 0upx;
	overflow: hidden;
	position: absolute;
	right: 0;
	top: 0;
}
.radio_group_item{ 
	align-items: center;
	display: flex;
}

/* 日历 */
.task_detail_box { padding-bottom: 50upx; }
.task_detail_title {
	height: 88rpx;
	font-size: 32rpx;
	align-items: flex-end;	
}
.detailsItem {
	width: 100%;
	height: 100upx;
	align-items: center;
}


/* 学员列表 */
.studentList_box { margin-bottom: 20upx; }
.add_student_icon {
	width: 104upx;
	height: 104upx;
	border-radius: 50%;
	border: 1upx solid #979797;
	text-align: center;
	line-height: 104upx;
	margin-right: 30upx;
}
.allChooseBox{ 
	width: 160upx;
	float: right;
	display: flex;
	align-items: center;
	text-align: right;
}

.close-view {
		height: 40upx;
		width: 40upx;
		background: #BCBCBC;
		color: #FFFFFF;
		position: absolute;
		top: -12upx;
		right: -12upx;
		font-size: 40upx;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		padding-bottom: 8upx;
		padding-left: 2upx;
		box-sizing: border-box;
	}

.student_item {
	width: 100%;
	height: 90upx;
	margin-bottom: 30upx;
}
.student_header { 
	width: 90upx;
	height: 90upx;
	border-radius: 50%;
	margin-right: 20upx;
}

.colorGrade {
	display: inline-block;
	width: 100upx;
	height: 36upx;
	text-align: center;
	font-size: 30upx;
	line-height: 36upx;
	border-radius: 6upx;
	box-sizing: border-box;
}
.grade_red{
	color: #C2522D;
	border: 1px solid #C2522D;
}
.grade_purple{
	color: #6413B3;
	border: 1px solid #6413B3;
}
.grade_blue{
	color: #0398EF;
	border: 1px solid #0398EF;
}


.bottomFilter{width: 100%; height:74upx;background: #FFFFFF;filter: blur(12upx);margin-bottom:-16upx;}


.headerCommon{
	position: fixed;
	width: 100%;
	height: 160upx;
	top: 0;
	left: 0;
	text-align: center;
	background: #ffffff;
}
.headerBack {
	position: absolute;
	left: 10upx;
	top: 100upx;
	z-index: 22;
	font-weight: 600;
}
.headerTitle {
	display: inline-block;
	width: 100%;
	padding-top: 110upx;
	color: #666666;
	font-size: 28upx;
	font-family: 'ALBBR';
}

/* 横向两条数据分布样式 */
.progress_dis { 
	width: 100%;
	height: 26upx;
	background: #E8EEF1;
	border-radius: 4upx;
	overflow: hidden;
}
.progress_item{
	display: inline-block;
	height: 100%;
	float: left;
	text-align: center;
	line-height: 26upx;
	
}
.progress_dis>span{
	display: inline-block;
	height: 100%;
	float: left;
}
.progress_blue {
	background: linear-gradient(101deg, #A1DAFB 0%, #0398EF 100%);
}
.progress_org {
	background: linear-gradient(101deg, #FFE8C3 0%, #F9BD5A 100%);
}
.progress_purple {
	background: linear-gradient(101deg, #D2CDF9 0%, #8A80EA 100%);
}
.progress_green {
	background: linear-gradient(101deg, #C1FFCE 0%, #47C985 100%);
}

.progress_yellow {
	background: linear-gradient(101deg, #FDD99E 0%, #F9B64A 100%);
}
.background_blue{
	background: #5FBBF4;
}
.background_red{
	background: #C25220;
}
.background_grey{
	background: #999999;
}
.background_green{
	background: #2DC032;
}
.progress_grey {
	background: #E8EEF1;
}
.background_green_two{
	background: rgb(46, 137, 111)!important;
}
.width26{
	width: 26upx;
	height: 26upx;
	border-radius: 4upx;
}
.width508{
	width: 508upx;
	height: 26upx;
	border-radius: 4upx;
}


/* 托管师首页 */
.hostHeader{ 
	padding: 100upx 0 22upx 48upx;
	background: #fff;
}

.dealtList{
	width: 100%;
	height: 100upx;
	align-items: center;
}
.dealtStatus{
	width: 32upx;
	height: 32upx;
	text-align: center;
	line-height: 32upx;
	border-radius: 4upx;
	color: #fff;
	margin-left: 60upx;
	margin-right: 10upx;
	font-size: 26upx;
	font-family: "ALBBR";
}
.dealtText{
	width: 370upx;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
}

.bgRed{
	background-color: #CE1F1F;
}
.bgOrange{
	background-color: #EB7E2D;
}
.bgGreen{
	background-color: #2DC032;
}



.efficiency_box{
	width: 315upx;
	height: 152upx;
	border-radius: 10upx;
	opacity: 0.8;
	box-sizing: border-box;
	margin-bottom: 20upx;
	position: relative;
	top: 0;
}
.efficiency_bg1{
	background: #3482F6;
}
.efficiency_bg2{
	background: #33C378;
}
.efficiency_bg3{
	background: #6C71E6;
}
.efficiency_bg4{
	background: #F8B342;
}
.efficiency_bg5{
	background: #10C3E7;
}
.efficiency_title{
	position: absolute;
	left: 0;
	padding: 0 12upx;
	box-sizing: border-box;
	height: 50upx;
	line-height: 50upx;
	box-shadow: 0upx 2upx 5upx 0upx rgba(0,0,0,0.27);
	background-color: rgba(0,0,0,0.27);
	border-radius: 10upx 0upx 10upx 0upx;
}

.efficiency_text{
	width: 100%;
	margin-top: 74upx;
	line-height: 40upx;
	color: #fff;
	padding: 0 10upx;
	box-sizing: border-box;
	font-size: 26upx;
	opacity: 0.6;
	font-family: "ALBBR";
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
}


.hostAdd{
	width: 80upx;
	height: 80upx;
	color: #ffffff;
	line-height: 80upx;
	border-radius: 50%;
	position: fixed;
	bottom: 400upx;
	right: 50upx;
}
.host_add_btn {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	opacity: 0.7;
	background: #35ADF2;
	text-align: center;
}
.host_addList_box {
	position: absolute;
	width: 220upx;
	height: 272upx;
	background: #FFFFFF;
	box-shadow: 0upx 2upx 8upx 0upx rgba(0,0,0,0.13);
	border-radius: 16upx;
	right: 80upx;
	top: 23upx;
	opacity: 1;
}
.host_addList{
	width: 100%;
	height: 90upx;
	text-align: center;
	line-height: 90upx;
	border-bottom: 1upx solid #EEEEEE;
	font-size: 30upx;
	color: #303133;
	font-family: "ALBBR";
}
.host_addList:last-child{
	border: none!important;
}


/* 学情分析 */
.pageTitle{
	position: absolute;
	left: 0;
	top: 40upx;
	width: 209upx;
	height: 50upx;
	line-height: 50upx;
	font-size: 32upx;
	font-family: "ALBBM";
	color: white;
	padding-left: 31upx;
	box-sizing: border-box;
	/* background: url("/static/images/title_bg.png") 100% ; */
}
.studyLine {
	position: absolute;
	right: 30upx;
	top: 40upx;
	width: 200upx;
	height: 200upx;
	text-align: center;
}
.lineText{
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

.user_tabbar{
	width: 310upx;
	margin: 0 auto;
	display: flex;
	justify-content: space-between;
}
.tabbar_item {
	width: 130upx;
	text-align: center;	
}
.useTabText {
	display: block;
	height: 45upx;
	line-height: 45upx;
	padding-bottom: 6px;
}
.tabLine {
	width: 60upx;
	height: 4upx;
	background: #0398EF;
	border-radius: 2upx;
	margin: 6upx auto 0 auto;
}

.abnormal_box{
	width: 648upx;
	background: rgba(216,216,216,0.14);
	border-radius: 16upx;
	border: 1upx solid #D5D5D5;
	margin: 30upx auto;
	padding: 35upx;
	box-sizing: border-box;
}

/* 用户滑动 */
.swiper-item {
	display: flex;
	flex-wrap: wrap;
}
.swiper_student_item{
	width: 162upx;
	text-align: center;
	margin-top: 40upx;
}
.swiper_student_head {
	width: 100upx;
	height: 100upx;
	position: relative;
	border-radius: 50%;
	margin: 0 auto;
}
.student_list_head {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	overflow: hidden;
}
.positionChoose_student {
	position: absolute;
	top: 0upx;
	left: 0;
	width: 100upx;
	height: 110upx;
}

.always_feedback,.no_feedback {
	display: block;
	width: 100upx;
	height: 44upx;
	line-height:44upx;
	border-radius: 8upx;
	font-size: 28upx;
	font-family: "ALBBR";
	margin: 13upx auto 0 auto;
}



.always_feedback {
	background: #E5EDF6;
	color:black;
}
.no_feedback{
	background: #0398EF;
	color: white;
}

.remarks_reason{
	display: flex;
	flex-wrap: wrap;
	width: 100%;
	padding: 0 15upx;
	box-sizing: border-box;
	margin-top: 30upx;
}
.reason_item {
	width: 180upx;
	height: 70upx;
	border-radius: 8upx;
	border: 1upx solid #979797;
	box-sizing: border-box;
	font-size: 30upx;
	font-family: "ALBBR";
	color: #303133;
	margin: 16upx;
	text-align: center;
	line-height: 68upx;
}
.reason_item.active {
	background: rgba(3,152,239,0.1);
	border: 1px solid #0398EF;
	color: #0398EF;
}

.other_reason {
	width: 100%;
	height: 170upx;
	padding: 0 30upx;
	box-sizing: border-box;
	margin-top: 15upx;
	margin-bottom: 52upx;
}
.reason_input {
	width: 100%;
	height: 170upx;
	border-radius: 8upx;
	border: 1upx solid #979797;
	box-sizing: border-box;
	padding: 15upx 20upx;
}



.optionItem{

}
.optionContent {
	width: 100%;
	height: 172upx;
	display: flex;
	background: #FFFFFF;
	border-radius: 14upx;
	align-items: center;
	padding: 32upx;
	box-sizing: border-box;
}




/* tab学员 */
.tabStudentList_item{
	padding: 30upx 0 0 0!important;
}
.student_status{
	display: inline-block;
	width: 90upx;
	height: 36upx;
	border-radius: 4upx;
	font-size: 26upx;
	color: white;
	text-align: center;
	line-height: 36upx;
	margin-left: 22upx;
}
.btn_red{
	background: #CE1F1F;
	border: 1upx solid #CE1F1F;
	box-sizing: border-box;
}
.btn_orange{
	background: #EB7E2D;
	border: 1upx solid #EB7E2D;
	box-sizing: border-box;
}
.btn_green{
	background: #2DC032;
	border: 1upx solid #2DC032;
	box-sizing: border-box;
}
.btn_grey{
	background: #999999;
	border: 1upx solid #999999;
	box-sizing: border-box;
}
.btn_blue{
	background: #0398EF;
	border: 1upx solid #0398EF;
	box-sizing: border-box;
}

.student_abnolmar{
	position: absolute;
	right: 0upx;
	top: 0upx;
	width: 209upx;
	height: 50upx;
	line-height: 50upx;
	font-size: 32upx;
	font-family: "ALBBM";
	color: white;
	padding-left: 31upx;
	box-sizing: border-box;
}
.student_add_other{
	width: 50%;
	height: 95upx;
	line-height: 95upx;
	text-align: center;
	display: flex;
	align-items: center;
	justify-content: center;
	box-sizing: border-box;
}
.student_add_other:first-child{
	border-right: 1upx solid #D5D5D5
}
.sa_img {
	display: inline-flex;
	width: 30upx;
}


/* 新建待办 */
.addNeedDealtText{
	height: 540upx;
	padding-bottom: 30upx;
	box-sizing: border-box;
}
.addNeedDealtTextArea {
	width: 100%;
	height: 470upx;
	padding-bottom: 30upx;
	box-sizing: border-box;
}

.needDealtItem { 
	width: 100%;
	height: 100upx;
	border-top: 1upx dashed #D5D5D5;
	box-sizing: border-box;
	align-items: center;
}

.plan_detail_item {
	display: inline-flex;
	padding: 0 30upx;
	box-sizing: border-box;
	height: 60upx;
	line-height: 60upx;
	border-radius: 30upx;
	border: 1upx solid #979797;
	color: #979797;
	font-size: 30upx;
	font-family: "ALBBR";
	margin-top: 32upx;
	align-items: center;
}
.plan_detail_item_icon {
	margin-right: 19upx;
	width: 36upx;
}


/* 学员档案 */
.archives .student_status {
	border-radius: 14upx 4upx 14upx 4upx;
	position: absolute;
	top: -30upx;
	left: -20upx;
}
.archives .archive_time {
	display: inline-block;
	position: absolute;
	top: -30upx;
	right: 0upx;
	height: 36upx;
	line-height: 36upx;
	background: #999999;
	border-radius: 4upx 14upx 4upx 14upx;
	opacity: 0.5;
	border: 1px solid #999999;
	color: #fff;
	padding: 0 10upx;
	box-sizing: border-box;
}


.dot_item {
	width: 20upx;
	height: 20upx;
	border-radius: 50%;
}



/* main index */
.list_img{
	width: 188upx;
	height: 130upx;
	border-radius: 8upx;
	margin-right: 29upx;
}
.list_img image {
	width: 100%;
	height: 100%;
}
.index_list_text {
	flex-wrap: wrap;
	display:-webkit-box; 
	-webkit-box-orient:vertical; 
	-webkit-line-clamp:2; 
}
.line_title {
	display: block;
	width: 4upx;
	height: 32upx;
	background: #0398EF;
	border-radius: 2upx;
	margin-right: 13upx;
}




	.audio_box {
		width: 100%;
		height: 60upx;
		border-radius: 30upx;
		border: 1upx solid #979797;
		display: flex;
		align-items: center;
		padding: 0 20upx;
		box-sizing: border-box;
	}
	
	/* 回显textarea */
	.feedbackBox{
       border: 1upx solid #DFDFDF;
		 line-height: 40upx;
		 padding: 12upx 30upx;
		 height: 300upx;
		 overflow-y: scroll;
		 border-radius: 12upx;
		 box-sizing: border-box;
	}
    
    /* 回显textarea */
    .feedbackBoxNew{
        background-color: rgba(153, 153, 153, 0.08);
    	 line-height: 40upx;
    	 padding: 12upx 30upx;
    	 height: 300upx;
    	 overflow-y: scroll;
    	 border-radius: 12upx;
    	 box-sizing: border-box;
    }
    
    /* 21天抗遗忘   start*/
    .reviewBox { 
    	width: 100%;
    	height: 100%;
    	background: linear-gradient(178deg, #072021 0%, #3A8C59 50%, #508846 100%);
    	position: relative;
    	overflow: hidden;
    }
    .reviewUp_box {
    	width: 658rpx;
    	left: 45rpx;
        padding-bottom: 20rpx;
    	background: white;
    	margin-top: 44rpx;
    	margin-bottom: 44rpx;
    	box-shadow: 2px 4px 20px 1px rgba(0, 0, 0, 0.12);
    	border-radius: 14rpx;
    	position: relative;
    }
    
    .review_share {
    	position: absolute;
    	width: 50rpx;
    	height: 50rpx;
    	right: 14rpx;
    	top: 14rpx;
    }
    .review_share image {
    	width: 100%;
    	height: 100%;
    }
    .review_rate {
    	width: 586rpx;
    	height: 570rpx;
    	margin: 9rpx auto 60rpx auto;
    	position: relative;
    }
    .rate_bgImage {
    	position: absolute;
    	top: 0;
    	left: 0;
    	width: 100%;
    	height: 100%;
    }
    .rate_num { 
    	font-size: 60rpx;
    	color: #2ab3d8;
    	position: absolute;
    	z-index: 1;
    	width: 100%;
    	text-align: center;
    	top: 210rpx;
    }
    .rate_num text { font-size: 36rpx; }
    .review_rate_text { font-family: 'syhtR';font-size: 24rpx;color: #999999; }
    .review_task { width: 100%;
        text-align: center;
        position: absolute;
        bottom: 100rpx;
        font-weight: bold;
    	 font-size: 36rpx;
    	 }
    .review_census {
    	width: 606rpx;
    	height: 111rpx;
    	border-radius: 14rpx;
    	background: rgba(153,153,153,0.1);
    	margin: 0 auto;
    }
    .review_census>view{
    	width: 146rpx;
    	text-align: center;
    	float: left;
    	text-align: center;
    	padding-top: 8rpx;
    }
    /* .over_review_census>view{
    	width: 201rpx;
    	text-align: center;
    	float: left;
    	text-align: center;
    	padding-top: 8rpx;
    } */
    .rightBorder {
    	width: 1rpx!important;
    	height: 60rpx;
    	background-color: #DDDDDD;
    	margin-top: 26rpx;
    }
    .review_census,.over_review_census>view text { color: #999999;font-family: 'syhtR';font-size: 24rpx; }
    .review_litleTitle {font-size: 36rpx; }
    .review_litleTitle>view {
        margin-left: 30rpx;
    }
    .review_color1 {
    	background: #FEEFE4;
    	opacity: 0.8;
    }
    .review_color2 {
    	background: #DAFCE2;
    	opacity: 0.8;
    
    }
    .review_color3 {
    	background: #DBF4FA;
    	opacity: 0.8;
    }
    .review_color4 {
    	background: #E2E2FE;
    	opacity: 0.8;
    }
    .record_body { width: 100%;height: 1080rpx;overflow-y: auto;padding-bottom: 100rpx;box-sizing: border-box; }
    .reviewBottom { width: 100%;height: 140rpx;position: absolute;display: flex;justify-content: space-between; bottom: 0;z-index: 2;background: #FFFFFF;border-top-left-radius: 35rpx;border-top-right-radius: 35rpx;padding: 30rpx 55rpx;box-sizing: border-box; }
    .reviewBtn { width: 300rpx;height: 80rpx;border-radius: 40rpx;display: inline-flex;justify-content: center; line-height: 80rpx;font-size: 32rpx;color: #FFFFFF;}
    .reviewLeft_btn { background: #FDA324; }
    .reviewRight_btn { background: #007C72; }
    .reviewRight_btn_1 { background: linear-gradient(180deg, #88CFBA 0%, #1D755C 100%); }
    .study_record {
    	width: 658rpx;
    	margin-left: 45rpx;
    	background: white;
    	margin-top: 30rpx;
    	box-shadow: 2px 4px 20px 1px rgba(0, 0, 0, 0.12);
    	border-radius: 14rpx;
    	padding-left: 40rpx;
    	box-sizing: border-box;
    }
    .study_record_title { width: 100%;font-size: 24rpx;padding-top: 33rpx; font-size: 'syhtR';margin: 33rpx auto 4rpx auto; }
    .record_listBox { width: 100%;overflow-y: auto;padding-bottom: 20rpx; }
    .record_listBox .recordList:last-child { border: none!important; }
    .recordList { width: 100%;height: 122rpx;border-bottom: 1rpx dashed #DDDDDD; }
    .recordWord { width: 100%;height: 45rpx;line-height: 45rpx;padding-top: 18rpx;display: flex;justify-content: space-between;align-items: center; }
    .recordIcon { width: 14rpx;height: 14rpx;margin-right: 18rpx;display: inline-block; border-radius: 7rpx;background: #5CBE63; }
    .recordIcon1 { background: #FFBE00!important; }
    .record_title { font-size: 30rpx;}
    .recordTranst { margin-left: 32rpx;margin-top: 10rpx;color: #999999;font-family: 'syhtR';font-size: 24rpx; }
    /* 复习单词单词或文字过长 */
    .flHeight { display: inline-block;width: 300rpx;line-height: 45rpx; }
    /* 21天抗遗忘   end*/




     






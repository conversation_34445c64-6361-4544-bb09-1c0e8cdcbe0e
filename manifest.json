{
  "name": "助教端",
  "appid": "__UNI__201304B",
  "description": "",
  "versionName": "2.0.3",
  "versionCode": 203,
  "transformPx": false,
  "sassImplementationName": "node-sass",
  /* 5+App特有相关 */
  "app-plus": {
    "usingComponents": true,
    "nvueCompiler": "weex",
    "nvueStyleCompiler": "uni-app",
    "compilerVersion": 3,
    "splashscreen": {
      "alwaysShowBeforeRender": true,
      "waiting": true,
      "autoclose": true,
      "delay": 0
    },
    /* 模块配置 */
    "modules": {
      "Camera": {}
    },
    /* 应用发布信息 */
    "distribute": {
      /* android打包配置 */
      "android": {
        "permissions": [
          "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
          "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
          "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
          "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
          "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
          "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
          "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
          "<uses-permission android:name=\"android.permission.CAMERA\"/>",
          "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
          "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
          "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
          "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
          "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
          "<uses-feature android:name=\"android.hardware.camera\"/>",
          "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
        ]
      },
      /* ios打包配置 */
      "ios": {},
      /* SDK配置 */
      "sdkConfigs": {
        "oauth": {
          "weixin": {
            "appsecret": {
              "onlyAuthorize": true
            }
          }
        },
        "share": {
          "weixin": {
            "appid": "",
            "UniversalLinks": ""
          }
        },
        "payment": {
          "weixin": {
            "__platform__": ["ios", "android"],
            "appid": "",
            "UniversalLinks": ""
          }
        }
      }
    }
  },
  /* 快应用特有相关 */
  "quickapp": {},
  /* 小程序特有相关 */
  "mp-weixin": {
    "appid": "wxce2bd1113a024ff6",
    "permission": {},
    "setting": {
      "urlCheck": false,
      "minified": true,
      "postcss": true
    },
    "usingComponents": true,
    "optimization": {
      "subPackages": true
    }
  },
  "mp-alipay": {
    "usingComponents": true
  },
  "mp-baidu": {
    "usingComponents": true
  },
  "mp-toutiao": {
    "usingComponents": true
  },
  "uniStatistics": {
    "enable": false
  },
  "vueVersion": "2"
}
//...  
//iOS平台自定义InfoPlist.strings  
//...  
/* SDK配置 */
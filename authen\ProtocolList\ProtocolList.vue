<template>
  <div class="box">
    <div class="item" v-for="item in userList" :key="item.name" @click="goweburl(item)">
      <div class="">{{ item.name }}</div>
      <uni-icons type="right" size="16" color="#5a5a5a"></uni-icons>
    </div>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        app: 0,
        list: [],
        userList: [
          {
            key: '1',
            name: '用户服务协议'
          },
          {
            key: '2',
            name: '隐私协议'
          }
        ]
      };
    },
    onUnload() {
      // #ifdef APP-PLUS
      if (this.app) {
        plus.runtime.quit();
      }
      // #endif
    },
    onLoad(e) {
      this.app = e.app;
    },
    methods: {
      goweburl(item) {
        if (item.key == 1) {
          uni.navigateTo({
            url: '/authen/useragree'
          });
        } else {
          // #ifdef APP-PLUS
          uni.navigateTo({
            url: '/authen/PrivacyagreeApp'
          });
          // #endif
          // #ifdef MP-WEIXIN
          uni.navigateTo({
            url: '/authen/Privacyagree'
          });
          // #endif
        }
      }
    },
    components: {},
    created() {},
    mounted() {}
  };
</script>

<style lang="scss" scoped>
  .box {
    margin: 24rpx 32rpx;
    background-color: #fff;
    padding: 14rpx 40rpx 14rpx 24rpx;
    border-radius: 16rpx;
  }

  .item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100rpx;
    border-bottom: 3rpx solid #f5f5f5;
    font-size: 28rpx;
    color: #333333;
  }

  .item:nth-last-child(1) {
    border-bottom: 0;
  }
</style>

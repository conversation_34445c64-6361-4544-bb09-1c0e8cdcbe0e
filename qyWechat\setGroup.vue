<template>
  <view>
    <view class="info">
      <view class="">
        <view class="formItem">
          <view class="label">学员编号：</view>
          <input type="text" v-model="searchCode" style="flex: 1" placeholder="请输入学员编号" class="search-dialog-student-input" />
        </view>
        <view class="border"></view>
      </view>
      <view class="">
        <view class="formItem">
          <view class="label">群类型：</view>
          <u-radio-group v-model="searchType" placement="row" @change="changeRadio">
            <u-radio activeColor="#2E896F" label="上课群" name="2"></u-radio>
            <u-radio activeColor="#2E896F" label="试课群" name="1"></u-radio>
          </u-radio-group>
        </view>
        <view class="border"></view>
      </view>
      <view class="" v-if="searchType == 2 && needTime">
        <view class="formItem">
          <view class="label">首次上课时间：</view>
          <view class="" @click="chooseFitstTime">
            <view class="uni-list-cell-db positionRelative">
              <view :class="firstTime ? 'color000' : ''">
                {{ firstTime ? firstTime : '请选择' }}
              </view>
              <view class="time-icon">
                <u-icon name="arrow-right" color="#c7c7c7" size="26"></u-icon>
              </view>
            </view>
          </view>
        </view>
        <view class="border"></view>
      </view>
      <view class="" v-if="searchType == 2 && needTime">
        <view class="formItem">
          <view class="label">复习时间：</view>
          <view class="uni-list-cell-db positionRelative" @click="openReviewTime">
            <view
              style="position: absolute; right: 20rpx; width: 100rpx"
              v-if="comfireReviewData.week != undefined && comfireReviewData.week.length > 0 && comfireReviewData.startTime != undefined && comfireReviewData.startTime != ''"
            >
              <text style="font-size: 30rpx; color: #2e896f; line-height: 42rpx">编辑</text>
            </view>
            <view class="flex-s" v-else>
              <view class="flex-a-c"><text style="float: left">请选择</text></view>
              <view class="time-icon">
                <u-icon name="arrow-right" color="#c7c7c7" size="26"></u-icon>
              </view>
            </view>
          </view>
        </view>
        <view
          v-if="comfireReviewData.week != undefined && comfireReviewData.week.length > 0 && comfireReviewData.startTime != undefined && comfireReviewData.startTime != ''"
          class="formItem1"
        >
          <view class="mt-30 mb-25" v-for="(item, index) in comfireReviewData.week" :key="index">
            <text class="text-view">{{ getWeekName(item) }}</text>
            <text style="margin-left: 60rpx" class="text-view">{{ comfireReviewData.startTime }}</text>
          </view>
        </view>
      </view>
      <view class="border"></view>
      <!--      <view class="">
        <view class="formItem">
          <view class="label">复习时间：</view>
          <view class="value" @click="chooseReviewTime">
            {{ '请选择' }}
          </view>
        </view>
        <view class="border"></view>
      </view> -->
      <view class="bottom_btn">
        <view class="" style="display: flex; justify-content: center; align-items: center; width: 100%">
          <view class="bottom-button" @click="onSure">确定</view>
        </view>
      </view>
    </view>
    <u-datetime-picker
      :show="firstShow"
      ref="datetimePicker"
      :formatter="formatter"
      v-model="time1"
      mode="datetime"
      @confirm="confirmTime"
      @cancel="cancelTime"
      @change="changeTime"
    ></u-datetime-picker>

    <!-- <u-popup :show="reviewShow"> -->
    <!-- 复习时间-->
    <uni-popup ref="reviewShow" type="bottom" @maskClick="cancelAtion()">
      <view class="dialogBG pt-30 pb-40">
        <view class="top-close">
          <text>复习时间</text>
          <uni-icons type="clear" size="28" color="#B1B1B1" @click="cancelAtion"></uni-icons>
        </view>
        <view class="center-content">
          <view class="chose-week-text">
            <view
              class="chose-week-item"
              @click="reviewChoseWeekFunc(item, index)"
              v-for="(item, index) in normalWeekData"
              :class="reviewChoseWeek.indexOf(index) != -1 ? 'week-chose' : 'week-normal'"
            >
              {{ normalWeekData[index] }}
            </view>
          </view>
          <view class="start-time" @click="reviewStartTimeDialog()">
            <view>
              <text>开始时间</text>
              <text style="font-size: 32rpx; margin-left: 70rpx">
                {{ rewviewdatevalue.length <= 0 ? '请选择' : rewviewdatevalue }}
              </text>
            </view>
            <u-icon name="arrow-right" color="#c7c7c7" size="30"></u-icon>
          </view>
        </view>
        <view class="top-button">
          <button class="radius-50 confirm-button" @click="reviewConfirm()">确定</button>
        </view>
      </view>
    </uni-popup>
    <u-datetime-picker
      ref="reviewtimePicker"
      :show="showReviewTime"
      v-model="rewviewdatevalue"
      mode="time"
      itemHeight="60"
      confirmColor="#2e896f"
      @cancel="cancel"
      @confirm="reviewDateChange"
      :immediateChange="true"
    ></u-datetime-picker>
    <uni-popup ref="addPopup" type="bottom" :is-mask-click="false" :safe-area="false">
      <view class="bg-ff radius-15 mt-30">
        <view class="closeIcon">
          <image @click="addCancel" style="width: 42rpx; height: 42rpx" src="/static/images/icon_close.png" mode="aspectFill"></image>
        </view>
        <view class="f-30 mt-30 ml-30 mb-15" style="font-weight: 600">复习时间:</view>
        <view class="week">
          <view class="" v-for="(item, index) in weekdays" :key="index">
            <text class="weekItem f-30 ml-10" :class="item.select ? 'selected' : 'not-selected'" @click="chooseItem(item)">{{ item.label }}</text>
          </view>
        </view>
        <view>
          <view class="f-30 mt-30 ml-30 mb-15" style="font-weight: 600">开始时间:</view>
          <u-datetime-picker :show="reviewShow1" v-model="reviewTime1" mode="datetime"></u-datetime-picker>
        </view>
        <view style="height: 500rpx"></view>
        <view class="bottomFixed">
          <view class="reviewBottom">
            <view class="common-sure-btn" @click="addConfirm">确定</view>
          </view>
        </view>
      </view>
      <!-- </u-popup> -->
    </uni-popup>
  </view>
</template>

<script>
import dayjs from 'dayjs';
export default {
  data() {
    return {
      searchCode: '',
      searchType: '',
      formData: {},
      firstShow: false,
      reviewShow: true,
      reviewShow1: false,
      firstTime: '',
      time1: Number(new Date()),
      reviewTime: '',
      reviewTime1: '',
      normalWeekData: '周一_周二_周三_周四_周五_周六_周日'.split('_'),
      comfireReviewData: {},
      reviewChoseWeek: [], //复习选择的星期
      rewviewdatevalue: '',
      showReviewTime: false,
      weekdays: [
        {
          label: '周一',
          select: false
        },
        {
          label: '周二',
          select: false
        },
        {
          label: '周三',
          select: false
        },
        {
          label: '周四',
          select: false
        },
        {
          label: '周五',
          select: false
        },
        {
          label: '周六',
          select: false
        },
        {
          label: '周日',
          select: false
        }
      ],
      needTime: false
    };
  },
  onLoad() {},
  onShow() {},
  methods: {
    changeRadio(e) {
      let tel = uni.getStorageSync('tel');
      if (!this.searchCode) {
        return uni.showToast({
          title: '请先填写学员编号',
          icon: 'none'
        });
      }
      if (e == 2) {
        let obj = {
          studentCode: this.searchCode,
          mobile: tel
        };
        this.$http.post('/deliver/web/experience/needContactInfo', JSON.stringify(obj)).then(({ data }) => {
          console.log(data);
          this.needTime = data.data;
        });
      }
      // console.log(e);
    },
    onSure() {
      let that = this;
      console.log(this.comfireReviewData);
      if (!that.searchCode) {
        return uni.showToast({
          title: '请输入学员编号',
          icon: 'none'
        });
      }
      if (!that.searchType) {
        return uni.showToast({
          title: '请选择群类型',
          icon: 'none'
        });
      }
      if (that.needTime && !that.firstTime) {
        return uni.showToast({
          title: '请填写上课时间',
          icon: 'none'
        });
      }
      if (
        that.needTime &&
        (that.comfireReviewData.week == undefined ||
          that.comfireReviewData.week.length <= 0 ||
          that.comfireReviewData.startTime == undefined ||
          that.comfireReviewData.startTime == '')
      ) {
        return uni.showToast({
          title: '请填写复习时间',
          icon: 'none'
        });
      }
      let mobile = uni.getStorageSync('tel');
      that.$http
        .get('/deliver/web/experience/getHistoryChatInfo', {
          studentCode: that.searchCode.trim(),
          type: that.searchType,
          mobile: mobile,
          firstTime: that.needTime ? that.timestampToYMDHMS1(that.time1) : '',
          reviewTime: that.needTime ? that.comfireReviewData.startTime : '',
          reviewWeek: that.needTime ? that.comfireReviewData.week : ''
        })
        .then(({ data }) => {
          console.log(data, '==========================');
          if (!data.data) {
            // that.searchCode = '';
            return uni.showToast({
              title: '您不是该学员的上课教练',
              icon: 'none'
            });
          } else {
            console.log(data.data);
            let courseList = data.data;
            let item = courseList[0];
            item.studentCode = that.searchCode;
            let sendData = encodeURIComponent(JSON.stringify(item));
            console.log(sendData);
            uni.navigateTo({
              url: `/qyWechat/judgeCode?sendData=${sendData}`
            });
          }
        })
        .catch((err) => {
          console.log(err, '===================');
        });
    },
    getWeekName(week) {
      return this.normalWeekData[week];
    },
    openReviewTime() {
      this.$refs.reviewShow.open();
      this.showDialog = true;
    },
    reviewConfirm() {
      if (this.reviewChoseWeek.length == 0 && this.rewviewdatevalue.length > 0) {
        uni.showToast({
          icon: 'none',
          title: '请选择星期'
        });
        return;
      }
      if (this.reviewChoseWeek.length > 0 && this.rewviewdatevalue.length <= 0) {
        uni.showToast({
          icon: 'none',
          title: '请选择开始时间'
        });
        return;
      }
      this.reviewChoseWeek.sort((a, b) => a - b);
      this.comfireReviewData = {
        week: this.reviewChoseWeek,
        startTime: this.rewviewdatevalue
      };
      this.$refs.reviewShow.close();
      this.showDialog = false;
    },
    ////////Start////
    // 复习开始时间选择
    reviewDateChange: function (e) {
      this.showReviewTime = false;
      this.$forceUpdate();
    },
    //显示时间
    reviewStartTimeDialog() {
      this.showReviewTime = true;
    },
    //复习时间--开始时间
    reviewChoseWeekFunc(item, index) {
      let weelIndex = this.reviewChoseWeek.indexOf(index);
      if (weelIndex == -1) {
        this.reviewChoseWeek.push(index);
      } else {
        this.reviewChoseWeek.splice(weelIndex, 1);
      }
    },
    cancelAtion() {
      this.$refs.reviewShow.close();
      this.showDialog = false;
    },
    addConfirm() {
      console.log('123');
      let that = this;
      let arr = this.weekdays.filter((i) => i.select === true);
      console.log(arr);
    },
    addCancel() {
      this.$refs.addPopup.close();
    },
    chooseItem(item) {
      item.select = !item.select;
    },
    chooseReviewTime() {
      // this.reviewShow = true
      this.$refs.reviewShow.open();
    },
    chooseFitstTime() {
      this.firstShow = true;
    },
    changeTime(e) {
      console.log(e, '==========');
      if (!dayjs(e.value).isAfter(dayjs(Date.now()).add(3, 'hour'))) {
        return uni.showToast({
          title: '请选择三小时后的时间',
          icon: 'none'
        });
      } else {
        this.firstTime = this.timestampToYMDHMS(e.value);
      }
    },
    confirmTime() {
      console.log(this.time1);
      if (!dayjs(this.time1).isAfter(dayjs(Date.now()).add(3, 'hour'))) {
        return uni.showToast({
          title: '请选择三小时后的时间',
          icon: 'none'
        });
      } else {
        this.firstTime = this.timestampToYMDHMS(this.time1);
        setTimeout(() => {
          this.firstShow = false;
        }, 100);
      }
    },
    timestampToYMDHMS(timestamp) {
      const date = new Date(timestamp);
      const year = date.getFullYear();
      const month = ('0' + (date.getMonth() + 1)).slice(-2); // 月份是从0开始的
      const day = ('0' + date.getDate()).slice(-2);
      const hours = ('0' + date.getHours()).slice(-2);
      const minutes = ('0' + date.getMinutes()).slice(-2);
      return `${year}年${month}月${day}日 ${hours}时${minutes}分`;
    },
    timestampToYMDHMS1(timestamp) {
      const date = new Date(timestamp);
      const year = date.getFullYear();
      const month = ('0' + (date.getMonth() + 1)).slice(-2); // 月份是从0开始的
      const day = ('0' + date.getDate()).slice(-2);
      const hours = ('0' + date.getHours()).slice(-2);
      const minutes = ('0' + date.getMinutes()).slice(-2);
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    },
    cancelTime() {
      this.time1 = Number(new Date());
      this.firstShow = false;
    },

    formatter(type, value) {
      if (type === 'year') {
        return `${value}年`;
      }
      if (type === 'month') {
        return `${value}月`;
      }
      if (type === 'day') {
        return `${value}日`;
      }
      if (type === 'hour') {
        return `${value}时`;
      }
      if (type === 'minute') {
        return `${value}分`;
      }

      return value;
    }
  }
};
</script>

<style lang="scss">
page {
  background: #f3f8fc;
}

.info {
  width: 690rpx;
  min-height: 1100rpx;
  background: #ffffff;
  border-radius: 14rpx;
  margin: 30rpx auto;
}
.formItem1 {
  padding: 0 30rpx;
}
.formItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  /deep/.uni-icons {
    color: #fff !important;
  }
  .label {
    width: 220rpx;
  }

  .value {
    flex: 1;
  }
}
.color000 {
  color: #000;
}
.border {
  width: 650rpx;
  height: 0;
  border: 1rpx solid #efefef;
  margin: 0 auto;
}

::v-deep .u-radio {
  margin-right: 80rpx;
}

.week {
  margin-top: 30px;
  padding-left: 30rpx;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  /* 将容器分为4列，每列平均占据剩余空间 */
  grid-gap: 10rpx;
  /* 设置格子之间的间隔 */
  grid-row-gap: 50rpx;
}

.weekItem {
  font-size: 28rpx;
  border-radius: 12rpx;
  padding: 10rpx 20rpx;
  // width: 120rpx;
  // height: 65rpx;
}

.selected {
  // background-color: #eaf3f0;
  border: 1rpx solid #2e896f;
  background: rgba(46, 137, 111, 0.1);
  color: #2e896f;
}

.not-selected {
  background: rgba(177, 177, 177, 0.2);
  // background-color: #efefef;
  border: 1rpx solid #b1b1b1;
  color: #000;
}

.bottomFixed {
  z-index: 999;
  position: fixed;
  bottom: 50rpx;
  width: 690rpx;
  /* background-color: #fff; */
  /* border-radius: 20rpx 20rpx 0 0; */
}

.reviewBottom {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.common-sure-btn {
  width: 250rpx;
  height: 80rpx;
  background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
  border-radius: 40rpx;
  font-size: 30rpx;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
}
.closeIcon {
  position: absolute;
  top: 30rpx;
  right: 20rpx;
}
.dialogBG {
  margin: 0 20rpx 20rpx 20rpx;
  height: 961rpx;
  background-color: #fff;
  border-radius: 12rpx;
}

.top-close {
  color: black;
  font-size: 30rpx;
  margin: 0 35rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.center-content {
  margin-top: 40rpx;
  height: 760rpx;
}

.start-time {
  margin: 30rpx 35rpx 0rpx 35rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: black;
}

.top-button {
  text-align: center;
  height: 80rpx;
  display: flex;
  justify-content: center;
}

.confirm-button {
  width: 250rpx;
  height: 80rpx;
  background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
  color: #fff;
  font-size: 32rpx;
  display: flex;
  justify-content: center;
  /* 文本水平居中对齐 */
  align-items: center;
  /* 文本垂直居中对齐 */
}

.chose-week-text {
  width: 100%;
  margin: 0 15rpx;
  display: flex;
  align-content: flex-start;
  flex-flow: row wrap;
}

.chose-week-item {
  margin: 0 20rpx 30rpx 20rpx;
}

.week-normal {
  width: 120rpx;
  height: 65rpx;
  background: rgba(177, 177, 177, 0.2);
  border-radius: 12rpx;
  border: 1rpx solid #b1b1b1;
  color: #000000;
  font-size: 30rpx;
  display: flex;
  align-items: center;
  /* 垂直居中 */
  justify-content: center;
  /* 水平居中 */
}

.week-chose {
  width: 120rpx;
  height: 65rpx;
  background: rgba(46, 137, 111, 0.1);
  border-radius: 12rpx;
  border: 1rpx solid #2e896f;
  color: #2e896f;
  font-size: 30rpx;
  display: flex;
  align-items: center;
  /* 垂直居中 */
  justify-content: center;
  /* 水平居中 */
}

.text-view {
  font-size: 32rpx;
  color: #333333;
}

.text-more-view {
  display: flex;
  flex-direction: row;
  /* 设置为水平排列 */
}

.course-bg-view {
  width: 530rpx;
  height: 60rpx;
  line-height: 60rpx;
  background: rgba(153, 153, 153, 0.1);
  border-radius: 12rpx;
  // padding-top: 24rpx;
  padding-left: 24rpx;
  padding-right: 24rpx;
  font-size: 26rpx;
  color: #666666;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.remake-bg-view {
  background: rgba(153, 153, 153, 0.1);
  border-radius: 12rpx;
  padding: 24rpx 35rpx;
  font-size: 28rpx;
  color: #000;
}

.time-flag {
  width: 38rpx;
  height: 38rpx;
  margin-top: 5rpx;
  margin-left: 20rpx;
}

.week-view {
  margin-left: 35rpx;
  margin-right: 24rpx;
  display: flex;
  justify-content: space-between;
}

.time-view {
  display: flex;
  justify-content: center;
  align-items: center;
}

.time-text-view-normal {
  color: #999999;
  width: 116rpx;
  font-size: 28rpx;
}

.time-text-view-normal-1 {
  border-radius: 12rpx;
  border: 1px solid #c8c8c8;
  background-color: #f4f4f4;
  color: #000;
  width: 116rpx;
  padding: 10rpx 0;
  font-size: 28rpx;
  overflow: visible;
}

.time-text-view {
  color: #000000;
  width: 116rpx;
  font-size: 30rpx;
}

.thesaurus {
  background-color: #1d755c;
  padding: 5rpx 8rpx;
}

.first-class-vocabulary {
  position: absolute;
  top: 10rpx;
  right: 2rpx;
  color: #fff;
  height: 40rpx;
  font-size: 24rpx;
  line-height: 40rpx;
  padding: 0 5rpx;
  background-color: #2f8069;
  border-radius: 10rpx 0 0 10rpx;
}

.course {
  width: 420rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.borderB {
  border-bottom: 1px solid #efefef;
}
.information {
  display: flex;
  justify-content: space-between;
  align-items: center;

  /deep/.uni-icons {
    color: #fff !important;
  }
}
.uni-list-cell-db {
  background: #fff;
  border-radius: 8rpx;
  // width: 100%;
  // height: 70rpx;
  font-size: 28rpx;
  color: #999;
  display: flex;
  padding-left: 20rpx;
  align-items: center;
}
.bottom_btn {
  position: absolute;
  bottom: 100rpx;
  width: 690rpx;
}

.bottom-button {
  width: 586rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  color: #fff;
  background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
  border-radius: 40rpx;
}
</style>

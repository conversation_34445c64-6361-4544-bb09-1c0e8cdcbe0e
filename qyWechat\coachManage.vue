<template>
  <view>
    <view class="bg-ff radius-15 mlr-30" :style="{ height: useHeight + 'rpx' }">
      <!--      <u-navbar title="教练管理" bgColor="#f3f8fc" placeholder>
        <view class="" slot="left">
        </view>
        <view class="u-nav-slot" slot="center" style="font-weight: 600">教练管理</view>
      </u-navbar> -->
      <view class="pd-30">
        <view class="" v-for="(item, index) in itemList" :key="index">
          <view class="item">
            <view class="title">{{ item.label }}</view>
            <view class="value" v-if="item.name || item.id">
              <view class="">{{ item.name }}</view>
              <u-icon name="edit-pen" size="20" color="#999999" @click="eidtTeacher(item)"></u-icon>
            </view>
            <view class="btn" v-else>
              <view class="btn" @click="addTeacher(item)">
                <u-icon name="plus-circle" size="20" color="#2E896F"></u-icon>
                <view class="btn-title">添加</view>
              </view>
              <view class="" style="flex: 1"></view>
            </view>
          </view>
          <view class="line"></view>
        </view>
      </view>
    </view>
    <!-- 选择角色弹窗 -->
    <uni-popup ref="popopChooseStudent" type="center" :safe-area="false" :isMaskClick="false">
      <view class="dialogBG">
        <view class="dialog-all">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="dialog-small-close" @click="cancelStudent">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="dialog-center">
            <view class="dialog-title pb-10">{{ dialogTitle }}</view>
            <view class="dialog-list">
              <u-empty v-if="teacherList.length < 1" mode="list" text="暂无学员" icon="https://document.dxznjy.com/automation/1728442200000"></u-empty>
              <view
                v-else
                class="dialog-item"
                @click="chooseStudentlist(item, index)"
                :key="index"
                v-for="(item, index) in teacherList"
                :class="isactive == index ? 'selected' : 'not-selected'"
              >
                {{ item.name }}
              </view>
            </view>
            <view class="flex-c mt-40" v-if="teacherList.length > 0">
              <view class="common-sure-btn-1" @click="confirmStudent()">确定</view>
              <view class="common-cancel-btn ml-40" @click="cancelStudent()">取消</view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import { Debounce } from '@/utils/debounce.js';
  export default {
    data() {
      return {
        useHeight: 0,
        itemList: [
          {
            type: 1,
            label: '上课教练'
          },
          {
            type: 3,
            label: '代课教练'
          },
          {
            type: 2,
            label: '复习教练'
          }
        ],
        dialogTitle: '新增教练',
        chatId: '',
        teacher: '',
        teacherList: [],
        isactive: -1,
        changeForm: {
          newTeacherId: '',
          oldTeacherId: '',
          chatId: '',
          type: '',
          mobile: ''
        },
        flag: false,
        id: '',
        experienceType: ''
      };
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 30;
        }
      });
    },
    onLoad(e) {
      this.id = e.id;
      this.experienceType = e.experience;
      let that = this;
      // #ifdef MP-WEIXIN
      // wx.qy.checkSession({
      //   success: function () {
      //     //session_key 未过期，并且在本生命周期一直有效
      //     // that.initData();
      //     that.getRole();
      //   },
      //   fail: function () {
      //     uni.removeStorageSync('token');
      //     uni.removeStorageSync('scrm_token');
      //     uni.setStorageSync('userRole', 'notLogin');
      //     uni.setStorageSync('isLogin', false);
      //     that.$store.commit('setType', 'notLogin');
      //     uni.navigateTo({
      //       url: '/qyWechat/login_qywx'
      //     });
      //   }
      // });
      // #endif
    },
    onShow() {
      let loginType = uni.getStorageSync('token');
      if (loginType) {
        this.getRole();
      }
    },
    onHide() {
      this.chatId = '';
    },
    onUnload() {
      this.chatId = '';
    },
    methods: {
      getRole() {
        let that = this;
        // #ifdef APP-PLUS
        that.initData1();
        // #endif
        // #ifdef MP-WEIXIN
        that.initData();
        // #endif
        //    let mobile = uni.getStorageSync('tel');
        // console.log(mobile,'9999999999999999999999999999999999999')
        //    that.$http
        //      .get('/deliver/web/teacher/role/check', {
        //        mobile
        //      })
        //      .then(({ data }) => {
        //        console.log(data.data, '====================');
        //        if (data.data) {
        //          // #ifdef APP-PLUS
        //            that.initData1();
        //          // #endif
        //          // #ifdef MP-WEIXIN
        //            that.initData();
        //          // #endif
        //        } else {
        //          uni.showToast({
        //            title: '请用小组组长身份进行修改',
        //            icon: 'none'
        //          });
        //          setTimeout(() => {
        //            uni.reLaunch({
        //              url: '/pages/index/index'
        //            });
        //          }, 1000);
        //        }
        //      });
      },
      chooseStudentlist(item, index) {
        this.isactive = index;
        this.teacher = item.id;
        this.changeForm.newTeacherId = item.id;
      },
      cancelStudent: Debounce(function () {
        this.isactive = -1;
        this.$refs.popopChooseStudent.close();
      }),
      confirmStudent: Debounce(function () {
        let that = this;
        if (that.isactive == -1) {
          uni.showToast({
            icon: 'none',
            title: '请先选择~'
          });
          return;
        } else {
          uni.showLoading();
          let mobile = uni.getStorageSync('tel');
          that.changeForm.id = this.id;
          that.changeForm.experienceType = this.experienceType == 0 ? 1 : 0;
          // that.changeForm.chatId = 'wr6t28KgAAkb4Rg8s4zLtdQOD_vIJXhA';
          // that.changeForm.chatId = that.chatId;
          console.log(that.changeForm, '=====================');
          that.$http
            .post('/deliver/web/teacher/app/changeTeacher', that.changeForm)
            .then(({ data }) => {
              uni.hideLoading();
              console.log(data, '======================================');
              let userId = data.data;
              // #ifdef MP-WEIXIN
              wx.qy.updateEnterpriseChat({
                chatId: that.chatId, //通过企业微信创建群聊接口返回的chatId
                userIdsToAdd: userId, //参与会话的企业成员列表，格式为userid1;userid2;...，用分号隔开。
                success: (res) => {
                  console.log(JSON.stringify(res));
                  uni.showToast({
                    title: '操作成功'
                  });
                  that.cancelStudent();
                  that.initData();
                  window.close();
                  // that.initData1();
                },
                fail: (res) => {
                  console.log(JSON.stringify(res));
                  that.cancelStudent();
                  // that.initData();
                  that.initData1(true);
                }
              });
              // #endif
              // #ifdef APP-PLUS
              that.cancelStudent();
              that.initData1();
              uni.showModal({
                title: '提示',
                content: '自行邀请新教练入群',
                showCancel: false
              });
              window.close();
              //#endif
            })
            .catch((err) => {
              uni.hideLoading();
              console.log(err);
            });
          // let role = this.teacher;
        }
      }),
      // confirmStudent: Debounce(function () {
      //   let that = this;
      //   if (that.isactive == -1) {
      //     uni.showToast({
      //       icon: 'none',
      //       title: '请先选择~'
      //     });
      //     return;
      //   } else {
      //     uni.showLoading();
      //     let mobile = uni.getStorageSync('tel');
      //     that.changeForm.id = mobile;
      //     // that.changeForm.chatId = 'wr6t28KgAAkb4Rg8s4zLtdQOD_vIJXhA';
      //     // that.changeForm.chatId = that.chatId;
      //     console.log(that.changeForm, '=====================');
      //     that.$http
      //       .post('/deliver/web/teacher/changeTeacher', that.changeForm)
      //       .then(({ data }) => {
      //         uni.hideLoading();
      //         console.log(data, '======================================');
      //         let userId = data.data;
      //         wx.qy.updateEnterpriseChat({
      //           chatId: that.chatId, //通过企业微信创建群聊接口返回的chatId
      //           userIdsToAdd: userId, //参与会话的企业成员列表，格式为userid1;userid2;...，用分号隔开。
      //           success: (res) => {
      //             console.log(JSON.stringify(res));
      //             uni.showToast({
      //               title: '操作成功'
      //             });
      //             that.cancelStudent();
      //             that.initData();
      //             window.close();
      //             // that.initData1();
      //           },
      //           fail: (res) => {
      //             console.log(JSON.stringify(res));
      //             that.cancelStudent();
      //             that.initData();
      //             // that.initData1();
      //           }
      //         });
      //       })
      //       .catch((err) => {
      //         uni.hideLoading();
      //         console.log(err);
      //       });
      //     // let role = this.teacher;
      //   }
      // }),
      getTeacherList: Debounce(function (add, type) {
        let that = this;
        let title = add ? '没有可新增的教练' : '没有可更换的教练';
        // let chatId = 'wr6t28KgAAkb4Rg8s4zLtdQOD_vIJXhA';
        let chatId = that.chatId;
        uni.showLoading({ title: '加载中' });
        this.$http
          .get('/deliver/web/teacher/add/appTeacher', {
            id: this.id,
            type: type,
            experienceType: this.experienceType == 0 ? 1 : 0
          })
          .then(({ data }) => {
            // console.log(data, '========================');
            that.teacherList = data.data;
            uni.hideLoading();
            if (this.teacherList.length < 1) {
              return uni.showToast({
                title: title,
                icon: 'none'
              });
            } else {
              that.$refs.popopChooseStudent.open();
            }
          })
          .catch((err) => {
            // console.log(err, '========================');
            uni.hideLoading();
          });
      }),
      eidtTeacher: Debounce(function (item) {
        // console.log(item);
        this.dialogTitle = '更换教练';
        this.changeForm.type = item.type;
        this.changeForm.oldTeacherId = item.id;
        this.getTeacherList(false, item.type);
      }),
      addTeacher: Debounce(function (item) {
        // console.log(item);
        this.dialogTitle = '新增教练';
        this.changeForm.type = item.type;
        this.getTeacherList(true, item.type);
      }),
      initData1() {
        let that = this;
        uni.showLoading({ title: '加载中' });
        that.$http
          .get('/deliver/web/teacher/changeTeacher/appTab', {
            id: this.id,
            type: this.experienceType == 0 ? 1 : 0
          })
          .then(({ data }) => {
            // console.log(data, '========================');
            let arr1 = data.data;
            that.itemList = that.itemList.map((item, index) => {
              const person = arr1.find((val, idx) => item.type == val.type);
              // console.log(person);
              return { ...item, name: person ? person.name : '', id: person ? person.id : '' };
            });

            // console.log(that.itemList);
            uni.hideLoading();
          })
          .catch((err) => {
            console.log(err, '========================');
            uni.hideLoading();
          });
      },
      initData() {
        let that = this;
        if (that.chatId) {
          that.$http
            .get('/deliver/web/teacher/changeTeacher/tab', {
              chatId: that.chatId
            })
            .then(({ data }) => {
              // console.log(data, '========================');
              let arr1 = data.data;
              that.itemList = that.itemList.map((item, index) => {
                const person = arr1.find((val, idx) => item.type == val.type);
                return { ...item, name: person ? person.name : '', id: person ? person.id : '' };
              });
              uni.hideLoading();
            })
            .catch((err) => {
              console.log(err, '========================');
              uni.hideLoading();
            });
        } else {
          wx.qy.getCurExternalChat({
            success: (res) => {
              uni.showLoading({ title: '加载中' });
              let chatId = res.chatId; //返回当前外部群的群聊ID
              that.chatId = chatId;
              that.changeForm.chatId = chatId;
              that.$http
                .get('/deliver/web/teacher/changeTeacher/tab', {
                  chatId: chatId
                })
                .then(({ data }) => {
                  // console.log(data, '========================');
                  let arr1 = data.data;
                  that.itemList = that.itemList.map((item, index) => {
                    const person = arr1.find((val, idx) => item.type == val.type);
                    return { ...item, name: person ? person.name : '', id: person ? person.id : '' };
                  });
                  uni.hideLoading();
                })
                .catch((err) => {
                  console.log(err, '========================');
                  uni.hideLoading();
                });
            },
            fail: (err) => {
              console.log(err, 'errrrrrrrrrrrrrrrrrrrrrrrrrrrr');
              // uni.showToast({
              //   title: err.errMsg,
              //   icon: 'none'
              // });
            }
          });
        }
      }
    }
  };
</script>
<style lang="scss">
  page {
    background-color: #f3f8fc;
  }
  .pd-30 {
    padding: 30rpx;
  }
  .item {
    .title {
      color: #666666;
      font-size: 28rpx;
      margin-bottom: 30rpx;
    }
    .value {
      color: #000000;
      font-size: 30rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .btn {
      display: flex;
      align-items: center;
      color: #2e896f;
    }
    .btn-title {
      font-size: 30rpx;
      margin-left: 12rpx;
    }
  }
  .line {
    width: 630rpx;
    border: 1rpx solid #eeeeee;
    margin: 30rpx auto;
  }
  .dialogBG {
    margin: 0 30rpx 30rpx 30rpx;
    background-color: #fff;
    border-radius: 14rpx;
  }
  .dialog-all {
    width: 670rpx;
    position: relative;
  }

  .dialog-all image {
    width: 100%;
    height: 100%;
  }
  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 170rpx;
    z-index: -1;
  }
  .dialog-small-close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .dialog-center {
    position: relative;
    width: 100%;
    height: 100%;

    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 50upx 55upx;
    box-sizing: border-box;
  }
  .dialog-title {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    font-weight: 600;
    justify-content: center;
  }
  .dialog-list {
    height: 450rpx;
    overflow-y: auto;
  }
  .dialog-item {
    box-sizing: border-box;
    font-size: 32upx;
    line-height: 45upx;
    text-align: center;
    margin-top: 40rpx;
  }

  .selected {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #2e896f;
    border: 1px solid #2e896f;
    border-radius: 35rpx;
    overflow: visible;
  }

  .not-selected {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #000;
    border: 1px solid #c8c8c8;
    overflow: visible;
    border-radius: 35rpx;
    transform: scale(0.995); /* 解决ios上圆角失效 */
  }
  .common-sure-btn-1 {
    width: 250rpx;
    height: 80rpx;
    background: #2e896f;
    border-radius: 40rpx;
    font-size: 30rpx;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(0.995); /* 解决ios上圆角失效 */
  }
  .common-cancel-btn {
    width: 250rpx;
    height: 80rpx;
    border-radius: 40rpx;
    border: 1rpx solid #2e896f;
    font-size: 30rpx;
    color: #2e896f;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(0.995); /* 解决ios上圆角失效 */
  }
</style>

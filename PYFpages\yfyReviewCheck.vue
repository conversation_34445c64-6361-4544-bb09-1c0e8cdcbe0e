<template>
  <view class="ctxt plr-30 bg-ff">
    <view class="bg-ff ptb-30">
      <view class="binggo_c">
        <view class="view_box" :class="{active:index==activeIndex}" v-for="(item,index) in yfyList" :key="index" @click="sayWord(index,item,item.wordSyllableAudioUrl)">
          <view class="box_l">
            {{getType(item.wordSyllableType)}}
          </view>
          <view class="box_m" :class="{ ytxt: item.knowType === 0 }">
            {{ item.word }}
          </view>
          <view class="box_r">
            <image @click.stop="correctWord(index, item, item.word, item.scheduleCode, 0)" :src="errorimage(index)">
            </image>
            <image @click.stop="correctWord(index, item, item.word, item.scheduleCode, 1)" :src="rightimage(index)">
            </image>
          </view>
        </view>
        <!-- <uni-load-more class="mb-65" :status="loadingType"></uni-load-more> -->
        <view class="zanwu">没有更多数据了~</view>
      </view>
      <view v-if="chunks.length==0" class="t-c flex-col mt-30 bg-ff radius-15 mlr-30" :style="{height: useHeight+'rpx'}">
        <image src="https://document.dxznjy.com/alading/correcting/no_data.png" class="mb-20" style="width: 160rpx;" mode="widthFix"></image>
        <view style="color: #BDBDBD;">暂无数据</view>
      </view>

      <view class="botBtn">
        <view class="btn_b b_r" @click="goWord"> 下一环节 </view>
      </view>
    </view>
  </view>
</template>

<script>
var innerAudioContext;
export default {
  data() {
    return {
      activeIndex: null,
      // wordSyllableType:null,//-1辅音 0元音 1单词音 2基础音节 3重音 4单词拼读 5划弱音 6定长短
      chunks: [],
      yfyList: [], //元辅音单词列表
      yfyPages: [], //分页单词列表
      studentCode: "", //学员Code
      pageindex: 1, //当前页
      pageSize: 20, //页数
      loadingType: "more", //加载更多状态
      planReviewId: '', //复习计划
      wordList: [], //单词集合
      errorIndex: [], //错误标记
      rightIndex: [], //正确标记
    };
  },
  onLoad(options) {
    var that = this;
    innerAudioContext = uni.createInnerAudioContext();
    that.planReviewId = options.planReviewId
    innerAudioContext.onPlay(() => {
      console.log('开始播放')
    });
    innerAudioContext.onStop(function () {
      console.log('播放结束')
      that.isplay = false;
    });
    innerAudioContext.onPause(function () {
      console.log('播放暂停')
      that.isplay = false;
    });
    innerAudioContext.onError((res) => {
      console.log(res.errMsg);
      console.log(res.errCode);
      that.isplay = false;
    });
    that.getWords();
  },
  onUnload() {
    var that = this;
    // 停止音频播放
    console.log('停止音频播放pyf--------')
    const resetAudioContext = () => {
      innerAudioContext.stop();
      innerAudioContext.offEnded();
      innerAudioContext.offCanplay();
    };
    resetAudioContext(); // 先重置音频上下文，避免事件冲突
  },
  methods: {
    errorimage(index) {
      return this.errorIndex.indexOf(index) > -1 ?
        "https://document.dxznjy.com/course/514e9457e3fb414ab67eaf715604e1db.png" :
        "https://document.dxznjy.com/course/a1b3b1a6db0a4288bcbaa53273e117d2.png";
    },
    rightimage(index) {
      return this.rightIndex.indexOf(index) > -1 ?
        "https://document.dxznjy.com/course/2dd08c3d20b0467f9f5e0b7f23a3e566.png" :
        "https://document.dxznjy.com/course/7aa8bc20f68245d68692050675f27841.png";
    },
    //区分单词音节类型
    getType(type) {
      switch (type) {
        case -1:
          return '辅音';
        case 0:
          return '元音';
        case 1:
          return '单词音';
        case 2:
          return '基础音节';
        case 3:
          return '重音';
        case 4:
          return '单词拼读';
        case 5:
          return '划弱音';
        case 6:
          return '定长短';
      }
    },
    goWord() {
      // console.log('this.yfyPages---',this.yfyPages,this.errorIndex,)
      innerAudioContext.stop();
      if (this.errorIndex.length + this.rightIndex.length < this.yfyPages.length) {
        uni.showToast({
          icon: 'none',
          title: '还有题目未完成哦',
          duration: 2000
        });

      } else {
        this.yfyPages.forEach((item, index) => {
          // knowType 1:学会0:不会
          //检查 index 是否存在于 this.rightIndex 数组中 errorIndex对的  rightIndex错的
          item.knowType = this.rightIndex.includes(index) ? 0 : 1;
        })
        console.log('this.yfyPages========', this.yfyPages)
        uni.navigateTo({
          url: "/PYFpages/dcReviewCheck?yfyList=" + encodeURIComponent(JSON.stringify(this.yfyPages)) + '&planReviewId=' + this.planReviewId,
        });
      }


    },
    correctWord(index, val, word, scheduleCode, correct) {
      //单词、标记、对错 0是错 1是对
      // innerAudioContext.src = word;
      innerAudioContext.stop();
      if (correct == 0) {
        if (this.errorIndex != null && this.errorIndex.indexOf(index) == -1) {
          this.errorIndex.push(index);
        }
        if (this.rightIndex.indexOf(index) > -1) {
          this.rightIndex.splice(this.rightIndex.indexOf(index), 1);
        }
      }
      if (correct == 1) {
        if (this.rightIndex != null && this.rightIndex.indexOf(index) == -1) {
          this.rightIndex.push(index);
        }
        if (this.errorIndex.indexOf(index) > -1) {
          this.errorIndex.splice(this.errorIndex.indexOf(index), 1);
        }
      }
      console.log('errorIndex,this.rightIndex', this.errorIndex, this.rightIndex)
    },
    sayWord(index, wordList, word) {
      var that = this
      // 在每次播放之前先解绑所有事件
      const resetAudioContext = () => {
        innerAudioContext.stop();
        innerAudioContext.offEnded();
        innerAudioContext.offCanplay();
      };
      const hasEmptyAudioUrl = () => {
        let hasEmpty = false;
        if (!wordList.wordSyllableAudioUrl || wordList.wordSyllableAudioUrl.length === 0) {
          word = '1'//让语音断
          uni.showToast({
            title: '该单词无音频哦',
            icon: 'error',
            duration: 2000
          });
          hasEmpty = true;
          return;
        }
        return hasEmpty;
      };

      that.activeIndex = index
      // 重置 activeIndex 以移除样式
      setTimeout(() => {
        this.activeIndex = null;
      }, 2000);
      // #ifdef MP-WEIXIN
      innerAudioContext.obeyMuteSwitch = false;
      // #endif
      if (hasEmptyAudioUrl()) {
        console.log('word', word)
        return;
      }
      resetAudioContext(); // 先重置音频上下文，避免事件冲突
      innerAudioContext.src = word;
      innerAudioContext.play();
    },
    loadWordList(type = "add") {
      uni.showLoading({
        title: "加载中",
      });

      if (type === "add") {
        this.pageindex++;
        this.getWords();
      }
      if (type === "refresh") {
        this.pageindex = 1;
        this.yfyList = [];
        this.getWords();
      }
      uni.hideLoading();

    },
    async getWords() {
      let result = await this.$http.get('/znyy/pd/planReviewDetails/getPdPlanReviewDetails', {
        planReviewId: this.planReviewId
        // planReviewId: '1279082733194072064'
      })
      // this.yfyList = result.data.data
      //元辅音页面分页
      this.yfyPages = result.data.data.syllableVos //[]
      this.yfyPages.totalItems = this.yfyPages.length //30
      this.yfyPages.size = this.pageSize //7
      this.yfyPages.totalPage = Math.ceil(this.yfyPages.totalItems / this.yfyPages.size)
      const chunks = []
      let pageNum = 1
      for (let i = 0; i < this.yfyPages.totalItems; i += this.yfyPages.size) {
        const chunk = this.yfyPages.slice(i, i + this.yfyPages.size)
        chunks.push({
          chunk,
          size: chunk.length,
          totalItems: this.yfyPages.length,
          totalPage: pageNum
        })
        this.chunks = chunks
        pageNum++
      }
      const currentChunk = this.chunks[this.pageindex - 1];
      if (currentChunk) {
        // 如果是首次加载，直接赋值；如果是加载更多，追加数据
        if (this.pageindex === 1) {
          this.yfyList = currentChunk.chunk;
        } else {
          this.yfyList = this.yfyList.concat(currentChunk.chunk);

        }
        console.log('当前显示的数据:', this.yfyList);
      }
    },
  },


  onPullDownRefresh() {
    this.loadWordList("refresh");
  },

  onReachBottom() {
    if (this.pageindex < this.chunks.length) {
      this.loadWordList();
      // this.loadingType = 'more';
      console.log("加载更多！！！！");
    } else {
      // this.loadingType === 'no-more'
      console.log("没有更多数据");
    }
  }
};
</script>

<style scoped>
.ctxt {
  height: 100vh;
}

.binggo_c {
  font-size: 30rpx;
  color: #555555;
  margin-top: 10rpx;
}

.view_box {
  width: 686rpx;
  /* height: 92rpx; */
  height: 92rpx;
  font-size: 28rpx;
  border-radius: 56rpx;
  border: 1px solid #d8d8d8;
  overflow: hidden;
  display: flex;
  align-items: center;
  text-align: center;
  margin-bottom: 40rpx;
}

.ytxt {
  color: #fea858;
}

.view_box:last-of-type {
  margin-bottom: 190rpx;
  /* 最后一项的底部间距设置为 104rpx (40rpx + botBtn 高度) */
}

.view_box.active {
  background-color: #fbfff9;
  border: 1px solid #3a9483;
}

.box_l {
  color: #77d977;
  width: 92rpx;
}

.box_m {
  width: 442rpx;
}

.box_r {
  width: 164rpx;
  line-height: 0;
}

.box_r image {
  width: 64rpx;
  height: 64rpx;
}

.lc_yellow {
  color: #fd9b2a;
  text-align: center;
  width: 116rpx;
  border: 1px solid #ffe1be;
  background-color: #fdf6ed;
  border-radius: 8rpx;
  margin-left: 32rpx;
}

.flex-self-s {
  margin-bottom: 40rpx;
}

.botBtn {
  position: fixed;
  bottom: 0rpx;
  right: 32rpx;
  padding-bottom: 64rpx;
  box-sizing: border-box;
  background-color: #ffffff;
}

.btn_b {
  width: 686rpx;
  height: 74rpx;
  border-radius: 60rpx;
  line-height: 74rpx;
  text-align: center;
}

.b_r {
  background-image: linear-gradient(to bottom, #88cfba, #1d755c);
  color: #ffffff;
  margin-left: 32rpx;
  letter-spacing: 3px;
}
.zanwu {
  margin: 0 auto;
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  text-align: center;
  font-size: 36rpx;
  color: #b3b7ba;
  height: 180rpx;
}
</style>
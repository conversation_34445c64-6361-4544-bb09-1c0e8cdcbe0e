<template>
  <view>
    <view class="" :style="{ height: useHeight + 'rpx' }">
      <u-navbar leftText="" title=" " :placeholder="true" bgColor="#f3f8fc">
        <view class="u-nav-slot" slot="left" @click="goBack"></view>
        <view class="" slot="center">绑定群聊</view>
      </u-navbar>
      <block v-if="bindInfo && bindInfo.id">
        <view class="box">
          <view class="pd-30" style="position: relative">
            <view class="tag">
              <view class="tag-box">已绑定</view>
              <!--       <image :src="img1" style="width: 143rpx" mode="widthFix"></image> -->
            </view>
            <view class="item" v-for="(val, idx) in arr" :key="idx">
              <view class="label">
                <text class="text_13">{{ val.label }}</text>
              </view>
              <view class="value">
                <text class="text_16">{{ bindInfo[val.value] || '' }}</text>
              </view>
            </view>
          </view>
        </view>
        <view style="height: 200rpx"></view>
      </block>
      <block v-else>
        <block v-if="Data.length == 0">
          <u-empty mode="list" icon="https://document.dxznjy.com/automation/1728442200000"></u-empty>
        </block>
        <block v-else>
          <view class="box1" v-for="(item, index) in Data" :key="index" @click.prevent="goDetail(item)">
            <view class="pd-30">
              <view class="item" v-for="(val, idx) in arr" :key="idx">
                <view class="label">
                  <text class="text_13">{{ val.label }}</text>
                </view>
                <view class="value">
                  <text class="text_16">{{ item[val.value] || '' }}</text>
                </view>
                <view class="btn" v-if="val.value == 'expectTime'" @click.stop="bindGroup(item)">绑定</view>
              </view>
            </view>
          </view>
        </block>
      </block>
    </view>
    <uni-popup ref="addPopup" type="center" :is-mask-click="false" :safe-area="false">
      <view class="dialogBG pt-25 pb-45 pl-20 pr-20">
        <view class="dialog-all">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="flex-s" style="margin-bottom: 60rpx">
            <view class="" style="width: 42rpx; height: 42rpx"></view>
            <view style="font-size: 30rpx; font-weight: 650; flex: 1; text-align: center">温馨提示</view>
            <image @click="addCancel" style="width: 42rpx; height: 42rpx" src="/static/images/icon_close.png" mode="aspectFill"></image>
          </view>
          <view class="flex-center1" style="margin-bottom: 30rpx">您确定将 {{ bindForm.studentName }}学员({{ bindForm.studentCode }}) 与该群聊进行绑定吗</view>
          <view class="flex-center2" style="margin-bottom: 30rpx">请您仔细查看并确认,如果绑定错误将会影响学员正常上课</view>
          <view class="flex-s">
            <view class="common-sure-btn" @click="addSure">确定绑定</view>
            <view class="common-cancel-btn" @click="addCancel">取消</view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import { Debounce } from '@/utils/debounce.js';
  export default {
    data() {
      return {
        currentIndex: 1,
        tabs: [
          {
            name: '未接单'
          },
          {
            name: '已接单'
          },
          {
            name: '已建群'
          },
          {
            name: '已退单'
          }
        ],
        Data: [],
        arr: [
          {
            label: '学员：',
            value: 'studentName'
          },
          {
            label: '学员编号：',
            value: 'studentCode'
          },
          {
            label: '课程类型：',
            value: 'type'
          },
          {
            label: '首次课时间：',
            value: 'expectTime'
          }
        ],
        sendData: {},
        useHeight: 0,
        chatId: '',
        bindInfo: {},
        bindForm: {},
        img1: 'https://document.dxznjy.com/automation/1721732040000',
        img2: 'https://document.dxznjy.com/automation/1721732073000'
      };
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 30;
        }
      });
    },
    onLoad(e) {
      if (e.chatId) {
        this.chatId = e.chatId;
      }
      let that = this;
      // #ifdef MP-WEIXIN
      wx.qy.checkSession({
        success: function () {
          // that.getNowChatInfo();
          that.getChatId();
        },
        fail: function () {
          that.$store.commit('quit');

          uni.navigateTo({
            url: '/qyWechat/login_qywx'
          });
        }
      });
      // #endif
      uni.removeStorageSync('isCreate');
    },
    onShow() {
      uni.removeStorageSync('isCreate');
      let loginType = uni.getStorageSync('token');
      if (loginType) {
        this.getRole();
      }
    },
    onHide() {
      this.chatId = '';
    },
    onUnload() {
      this.chatId = '';
    },
    methods: {
      getRole() {
        let that = this;
        let mobile = uni.getStorageSync('tel');
        that.$http
          .get('/deliver/web/teacher/role/check', {
            mobile
          })
          .then(({ data }) => {
            if (!data.data) {
              that.getNowChatInfo();
            } else {
              uni.showToast({
                title: '您暂无权限修改',
                icon: 'none',
                duration: 3000
              });
              setTimeout(() => {
                uni.reLaunch({
                  url: '/pages/index/index'
                });
              }, 3000);
            }
          });
      },
      bindGroup(item) {
        let that = this;
        that.bindForm = item;
        that.$refs.addPopup.open();
      },
      addSure() {
        let that = this;
        let mobile = uni.getStorageSync('tel');
        let obj = {
          chatId: that.chatId,
          mobile: mobile,
          boundId: that.bindForm.id,
          type: that.bindForm.orderType
        };
        that.$http.post('/deliver/web/learnManager/bindGroup', obj).then(({ data }) => {
          console.log(data, '========================');
          if (data.success) {
            // return console.log(data.data);
            uni
              .showToast({
                title: '绑定成功',
                duration: 3000
              })
              .then(() => {
                if (data.data.referrerUserId || data.data.teamLeaderUserId || data.data.assetTransferUserId) {
                  let userId = [data.data.referrerUserId, data.data.teamLeaderUserId, data.data.assetTransferUserId].join(';');
                  console.log(userId, '11111111111111111111111');
                  wx.qy.updateEnterpriseChat({
                    chatId: that.chatId, //通过企业微信创建群聊接口返回的chatId
                    userIdsToAdd: userId, //参与会话的企业成员列表，格式为userid1;userid2;...，用分号隔开。
                    success: (res) => {
                      console.log(JSON.stringify(res));
                      uni.showToast({
                        title: '操作成功'
                      });
                      setTimeout(() => {
                        that.addCancel();
                        that.getNowChatInfo();
                      }, 1500);
                    },
                    fail: (res) => {
                      console.log(JSON.stringify(res));
                      setTimeout(() => {
                        that.addCancel();
                        that.getNowChatInfo();
                      }, 1500);
                    }
                  });
                }
              });
          } else {
            uni.showModal({
              title: '提示',
              content: data.message,
              showCancel: false,
              success: function (res) {
                if (res.confirm) {
                  console.log('用户点击确定');
                  that.addCancel();
                  that.getNowChatInfo();
                } else if (res.cancel) {
                  console.log('用户点击取消');
                  that.addCancel();
                  that.getNowChatInfo();
                }
              }
            });
          }
        });
      },
      addCancel() {
        let that = this;
        that.bindForm = {};
        that.$refs.addPopup.close();
      },
      getChatId() {
        wx.qy.getCurExternalChat({
          success: (res) => {
            let chatId = res.chatId; //返回当前外部群的群聊ID
            this.chatId = res.chatId;
          },
          fail: (err) => {
            uni.showToast({
              title: '获取群聊信息失败',
              icon: 'none'
            });
            console.log(err, 'errrrrrrrrrrrrrrrrrrrrrrrrrrrr');
          }
        });
      },
      getNowChatInfo() {
        let that = this;
        if (that.chatId) {
          that.$http.get('/deliver/web/learnManager/getOrderByChatId', { chatId: that.chatId }).then(({ data }) => {
            console.log(data.data, '==================');
            if (!data.data) {
              that.initData();
            } else {
              that.bindInfo = data.data;
              that.bindInfo.type = that.bindInfo.orderType == 1 ? '试课' : '正式课';
            }
          });
        } else {
          wx.qy.getCurExternalChat({
            success: (res) => {
              let chatId = res.chatId; //返回当前外部群的群聊ID
              that.chatId = res.chatId;
              that.$http.get('/deliver/web/learnManager/getOrderByChatId', { chatId: chatId }).then(({ data }) => {
                console.log(data.data, '==================');
                if (!data.data) {
                  that.initData();
                } else {
                  that.bindInfo = data.data;
                  that.bindInfo.type = that.bindInfo.orderType == 1 ? '试课' : '正式课';
                }
              });
            },
            fail: (err) => {
              console.log(err, 'errrrrrrrrrrrrrrrrrrrrrrrrrrrr');
            }
          });
        }
        // let chatId = 'wr6t28KgAAO5wSwKEI68YehwJgd_dNVw';
      },
      initData() {
        let that = this;
        that.$http
          .get('/deliver/web/experience/orderList', {
            type: 1
          })
          .then(({ data }) => {
            if (data.success) {
              console.log(data.data.data);
              that.Data = data.data.data;
              that.Data.forEach((i) => {
                i.type = i.orderType == 1 ? '试课' : '正式课';
              });
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      goDetail: Debounce(function (item) {
        let that = this;
        if (item.orderType == 1) {
          uni.navigateTo({
            url: `/qyWechat/testClass?orderId=${item.id}&orderType=1`
          });
        } else {
          uni.navigateTo({
            url: `/qyWechat/formalClass?orderId=${item.id}&orderType=1`
          });
        }
      })
    }
  };
</script>

<style lang="scss">
  page {
    background-color: #f3f8fc;
    padding-top: 40rpx;
  }
  .box {
    width: 680rpx;
    height: 320rpx;
    background: #ffffff;
    border-radius: 12rpx;
    margin: 10rpx auto;
    position: reactive;
    .tag {
      width: 152rpx;
      height: 70rpx;
      background: #e60012;
      opacity: 0.56;
      background: #e60012;
      // opacity: 0.56;
      // border: 5rpx solid #e2e2e2;
      // opacity: 0.56;
      position: absolute;
      right: 30rpx;
      top: 30rpx;
    }
    .tag-box {
      width: 143rpx;
      height: 62rpx;
      line-height: 62rpx;
      text-align: center;
      font-size: 36rpx;
      border: 5rpx solid #e2e2e2;
      // opacity: 0.56;
      color: #fff;
    }
    .item {
      display: flex;
      margin-top: 20rpx;
      color: #000;
      font-size: 28rpx;
      .label {
        min-width: 160rpx;
        margin-right: 40rpx;
      }
      .value {
        overflow: auto;
        flex: 1;
      }
    }
  }
  .box1 {
    width: 680rpx;
    height: 300rpx;
    background: #ffffff;
    border-radius: 12rpx;
    margin: 10rpx auto;
    position: relative;
    .poIcon {
      position: absolute;
      right: 0;
      width: 89rpx;
      height: 89rpx;
    }
    .item {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;
      color: #000;
      font-size: 28rpx;
      .label {
        min-width: 160rpx;
        margin-right: 10rpx;
      }
      .value {
        overflow: auto;
        flex: 1;
      }
    }
    .btn {
      width: 130rpx;
      height: 50rpx;
      line-height: 50rpx;
      text-align: center;
      font-size: 28rpx;
      color: #ffffff;
      background: #2e896f;
      border-radius: 8rpx;
      margin-left: 20rpx;
    }
  }
  .title {
    font-size: 28rpx;
    color: #000;
    font-weight: 600;
    margin-bottom: 30rpx;
  }

  .pd-30 {
    padding: 30rpx;
  }
  .color1 {
    color: #000;
  }
  .color2 {
    color: #ff4949;
  }
  .color3 {
    color: #2e896f;
  }
  .color4 {
    color: #f5a53a;
  }
  .dialogBG {
    margin: 200rpx 30rpx 30rpx 30rpx;
    background-color: #fff;
    border-radius: 14rpx;
  }
  .dialog-all {
    width: 560rpx;
    position: relative;
  }

  .dialog-all image {
    width: 100%;
    height: 100%;
  }
  .input-border {
    height: 80rpx;
    border-radius: 14rpx;
    border: 1rpx solid rgba(0, 0, 0, 0.2);
  }
  .flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .flex-center1 {
    // display: flex;
    text-align: center;
    // align-items: center;
    // justify-content: center;
    color: #000;
    font-size: 30rpx;
    padding: 0 40rpx;
    line-height: 1.2;
  }
  .flex-center2 {
    margin-top: 20rpx;
    text-align: center;
    padding: 0 40rpx;
    color: #f99a4b;
    font-size: 28rpx;
  }
  .common-sure-btn {
    width: 250rpx;
    height: 80rpx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 40rpx;
    font-size: 30rpx;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 25rpx;
    transform: scale(0.995); /* 解决ios上圆角失效 */
  }
  .common-cancel-btn {
    width: 250rpx;
    height: 80rpx;
    border-radius: 40rpx;
    border: 1rpx solid #2e896f;
    font-size: 30rpx;
    color: #2e896f;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 25rpx;
    transform: scale(0.995); /* 解决ios上圆角失效 */
  }
  .search-dialog-student-input {
    color: #666666;
    margin-left: 20rpx;
    width: 440rpx;
    height: 40rpx;
    font-size: 28rpx;
  }
  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 100rpx;
    z-index: -1;
  }
</style>

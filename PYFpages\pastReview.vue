<template>
  <view class="contaiter">
    <view class="bg-h">
      <view class="positioning" @click="goback">
        <uni-icons type="left" size="24" color="#000"></uni-icons>
      </view>
      <view class="word-position t-c col-12" style="">
        <view class="f-34">往期复习</view>
      </view>
    </view>
    <view class="paddingb mt-180">
      <view class="potop">
        <view class="top">
          <text>复习课程</text>
          <text>日期</text>
          <text>查看详情</text>
        </view>
      </view>
      <u-empty v-if="historylist.length == 0" mode="history" icon="http://cdn.uviewui.com/uview/empty/history.png"></u-empty>
      <view class="history-item" style="padding-bottom: 100rpx" v-else>
        <view class="list" v-for="(item, index) in historylist" :key="index">
          <view class="courseLevel" v-if="item.courseLevel">{{ courseLevelList.find((e) => e.value == item.courseLevel).label }}</view>
          <text>{{ item.pdCourseName }}</text>
          <text>{{ item.reviewTime.split(' ')[0] }}</text>
          <image src="https://document.dxznjy.com/course/1cebe1a9493c416db68cea8233704f95.png" mode="" @click="goUrl(item.id)" class="list-img"></image>
        </view>
        <view class="zanwu" :status="loadingType">没有更多数据了~</view>
      </view>
    </view>
    <!-- <uni-load-more :status="loadingType"></uni-load-more> -->
  </view>
</template>

<script>
  export default {
    components: {},
    data() {
      return {
        loadingType: 'more', //加载更多状态
        historylist: [],
        pageindex: 1,
        pageSize: 20,
        studentCode: '',
        sendType: false,
        queryData: {},
        isShare: false,
        courseLevelList: [
          { value: '0', label: '低年级' },
          { value: '1', label: '基础' },
          { value: '2', label: '高年级' }
        ]
      };
    },
    methods: {
      goback() {
        console.log('dongdddd');
        // uni.navigateBack()
        uni.navigateTo({
          url: '/PYFpages/forgetReview'
        });
      },
      sendMsg() {
        let that = this;
        // that.sendData.isSend = 0;
        // 发文字
        wx.qy.getContext({
          success: () => {
            //    var entry = data.entry, //返回进入小程序的入口类型
            // var shareTicket = data.shareTicket;
            wx.qy.sendChatMessage({
              msgtype: 'miniprogram', //消息类型，必填
              enterChat: false, //为true时表示发送完成之后顺便进入会话，仅移动端3.1.10及以上版本支持该字段
              text: {
                content: '' //文本内容
              },
              image: {
                mediaid: '' //图片的素材id
              },
              video: {
                mediaid: '' //视频的素材id
              },
              file: {
                mediaid: '' //文件的素材id
              },
              news: {
                link: '', //H5消息页面url 必填
                title: '', //H5消息标题
                desc: '', //H5消息摘要
                imgUrl: '' //H5消息封面图片URL
              },
              miniprogram: {
                appid: 'wxce2bd1113a024ff6', //小程序的appid
                title: '往期复习记录', //小程序消息的title
                imgUrl: 'https://document.dxznjy.com/manage/1716255999000', //小程序消息的封面图
                page: `/antiForgetting/history.html?studentCode=${that.studentCode}&isSend=0` //小程序消/小程序消息打开后的路径，注意要以.html作为后缀，否则在微信端打开会提示找不到页面
                // page: `/pages/index/study_feedback?sendData=${encodeURIComponent(JSON.stringify(that.sendData))}` //小程序消息打开后的路径，注意要以.html作为后缀，否则在微信端打开会提示找不到页面
              },
              success: (res) => {
                that.$http
                  .get('/znyy/pd/planReview/queryReviewedParents', {})
                  .then(() => {
                    uni.showToast({
                      title: '发送成功'
                    });
                    setTimeout(() => {
                      uni.redirectTo({
                        url: '/qyWechat/notice'
                      });
                    }, 800);
                  })
                  .catch((err) => {
                    uni.showToast({
                      title: '发送失败',
                      icon: 'none'
                    });
                  });
              },
              fail: (err) => {
                // console.log(err, '=============================')
              }
            });
          },
          fail: (err) => {
            // console.log(err, '111111111111111')
          }
        });
      },
      async loadMyMember(type = 'add', loading) {
        if (type === 'add') {
          if (this.loadingType === 'nomore') {
            return;
          }
          this.loadingType = 'loading';
        } else {
          this.loadingType = 'more';
        }
        var pageNum = this.pageindex;
        var pageSize = this.pageSize;
        // let result = await uni.$http.get('/znyy/review/query/student/word/review/' + mindex + '/' + this.pageSize + '/' + 62404928588;
        let result = await uni.$http.post(`/znyy/pd/planReview/queryReviewedParents?${pageNum}/${pageSize}`, {
          studentCode: this.studentCode
          // studentCode: 6231216111,
        });
        console.log(result, '拼音法往期复习');
        if (type === 'refresh') {
          this.historylist = [];
        }
        if (result) {
          // if (mindex <= 1 && result.data.data.data.length == 0) {
          if (result.data.data.data.length == 0) {
            // this.historylist = [];
            this.loadingType = 'nodata';
          } else {
            if (result.data.data.data.length) {
              this.historylist = this.historylist.concat(result.data.data.data);
            }
            this.loadingType = this.pageindex >= result.data.data.totalPage ? 'nomore' : 'more';
          }
          if (type === 'refresh') {
            if (loading == 1) {
              uni.hideLoading();
            } else {
              uni.stopPullDownRefresh();
              this.loadingType = 'nomore';
            }
          }
        }
      },
      goUrl(id) {
        let url = '';
        console.log(this.sendType);
        if (this.sendType) {
          url = `/PYFpages/reviewReport?reviewId=${id}&isShare=true&isSend=0`;
        } else {
          url = `/PYFpages/reviewReport?planReviewId=${id}&toIndex=2`;
        }
        // 使用 navigateTo 进入复习报告页后 ，可以返回往期复习页
        // uni.navigateTo({
        uni.redirectTo({
          url: url
        });
      }
    },
    onLoad(options) {
      let that = this;
      that.sendType = options.isSend == 1 ? true : false;
      that.isShare = options.isShare == 'true' ? true : false;
      if (options.item) {
        that.queryData = JSON.parse(decodeURIComponent(options.item));
      }
      // this.studentCode = options.studentCode
      this.studentCode = uni.getStorageSync('pyfStudentCode');
      console.log(this.studentCode);

      this.loadMyMember();
    },
    onPageScroll(e) {
      if (e.scrollTop >= 0) {
        this.headerPosition = 'fixed';
      } else {
        this.headerPosition = 'absolute';
      }
    },
    onPullDownRefresh() {
      this.pageindex = 1;
      this.loadMyMember('refresh');
    },
    //加载更多
    onReachBottom() {
      this.pageindex++;
      this.loadMyMember();
    }
  };
</script>

<style>
  .contaiter {
    /* 		margin-top: 10rpx; */
    /* '		height: 1311rpx;' */
  }

  page {
    background: #f3f8fc !important;
  }
  .bg-h {
    position: fixed; /* 确保背景颜色显示 */
    top: 0; /* 固定在页面顶部 */
    left: 0;
    width: 100%; /* 使背景颜色覆盖整个宽度 */
    z-index: 8; /* 高于其他内容 */
  }
  .positioning {
    position: fixed;
    top: 110rpx;
    left: 30rpx;
    z-index: 9;
  }

  .word-position {
    position: fixed;
    top: 0;
    left: 0;
    background-color: #f3f8fc;
    height: 190rpx;
    padding-top: 110rpx;
    box-sizing: border-box;
  }

  .paddingb {
    padding: 100rpx 20rpx;
    width: 690rpx;
    /* position: relative; */
    margin: 0 auto;
    background: #ffffff;
    border-radius: 14rpx;
    min-height: 80vh;
  }

  .potop {
    /* position: absolute; */
    top: 0;
    width: 690rpx;
    height: 100rpx;
    background-color: #fff;
    z-index: 999;
  }

  .top {
    margin-top: 20rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: space-around;
    font-size: 30rpx;
    background: #fbfbfb;
    font-weight: bold;
    /* border-bottom: 1px solid #e5e5e5; */
    padding-left: 70rpx;
  }

  .list {
    position: relative;
    display: flex;
    align-items: center;
    font-size: 30rpx;
    margin-top: 18rpx;
    min-height: 131rpx;
    background-color: #f9f9f9;
    /* height: 300rpx; */
    color: #666;
    padding-left: 100rpx;
  }

  .list-img {
    width: 52rpx;
    height: 52rpx;
    margin-left: 88rpx;
  }

  .list text {
    display: block;
    width: 33%;
    text-align: center;
  }
  .courseLevel {
    position: absolute;
    width: 89rpx;
    height: 52rpx;
    left: 0;
    top: 0;
    background: url('https://document.dxznjy.com/course/fdb49a412f4c483e80b1c69e005b3a8d.png') no-repeat;
    background-size: contain;
    color: #fff;
    font-size: 23rpx;
    line-height: 52rpx;
    text-align: center;
  }
  .zanwu {
    margin: 0 auto;
    margin-top: 20rpx;
    margin-bottom: 20rpx;
    text-align: center;
    font-size: 28rpx;
    color: #b3b7ba;
  }

  .timeFn {
    width: 80rpx;
    margin-left: 40rpx;
  }

  .wordsFn {
    margin-left: 39rpx;
  }

  .list-timer {
    width: 182rpx !important;
  }

  .detail_btn {
    width: 140rpx;
    height: 58rpx;
    background-color: #007aff;
    text-align: center;
    line-height: 58rpx;
    color: #fff;
    border-radius: 40rpx;
  }

  .bottomFixed {
    z-index: 999;
    position: fixed;
    bottom: 10rpx;
    width: 690rpx;
    /* background-color: #fff; */
    /* border-radius: 20rpx 20rpx 0 0; */
  }

  .sendButton {
    width: 586rpx;
    height: 90rpx;
    line-height: 90rpx;
    color: #ffffff;
    font-size: 30rpx;
    text-align: center;
    background: #2e896f;
    border-radius: 45rpx;
    margin: 10rpx auto 0;
  }
  .mt-180 {
    margin-top: 87rpx;
  }
</style>

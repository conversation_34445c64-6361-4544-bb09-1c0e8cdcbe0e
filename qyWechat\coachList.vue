<template>
    <view>
        <view class="radius-15">
            <view class="coach-title-css">
                <u-tabs   :list="list4" @click="checkIndex"
                    lineWidth="40rpx"
                    lineColor="#339378"
                    :activeStyle="{ color: '#000',  fontSize: '28rpx' }"
                    :inactiveStyle="{ color: '#000', fontSize: '30rpx' }"
                ></u-tabs>
            </view>
            <scroll-view  v-if="coachList.length > 0" class="scoll-view-css"   scroll-y="true"
                :scroll-top="scrollTop"   @scrolltolower="scrolltolower"  >
                <view class="plr-32">
                    <view v-for="item in coachList" :key="item.id"  style="padding:32rpx;width:620rpx;" class="bg-ff radius-15 mt-20 flex flex-x-between">
                        <view class="flex flex-y-center">
                            <image src="https://document.dxznjy.com/course/baeb40d8197542bb808e3c5c599f399f.png" class="image-header-css"></image>
                            <view class="text-css ml-20">
                                <view >{{ item.studentName }}</view>
                                <view class="code-css mt-12">{{ item.studentCode }}</view>
                            </view>
                        </view>
                        <view class="rigth-css" @click="changeCoach(item)">
                            <span>更换</span>
                            <image class="image-css" src="../static/images/right-icon.png"></image>
                        </view>
                    </view>
                </view>
                <view v-if="no_more && coachList.length > 0">
                    <u-divider text="到底了"></u-divider>
                </view>
            </scroll-view>
            <view  v-if="coachList.length == 0" class="flex flex-x-center flex-y-center" style="height: 98vh">
                <view class="flex flex-x-center flex-y-center">
                    <image :src="imgHost + '/app/images/zhujiaoduan/zhujiao_img_student_no.png'" class="empty-img"></image>
                    <view class="mt-40 no-text-css">暂无数据</view>
                </view>
            </view>
        </view>
    </view>
   
</template>
<script>
export default {
    data() {
        return {
            list4:[
                {
                    name: '正式课学员'
                },
                {
                    name: '试课学员'
                }
            ],
            imgHost: 'https://document.dxznjy.com/',
            no_more:false,
            coachList:[],
            infoLists:{},
            page:1,
            scrollTop:0,
            experienceIndex:0,
        }
    },
    onLoad() {
       this.getCoach()
    },
 
    methods: {
        scrolltolower() {
            if (this.page >= this.infoLists.totalPage) {
              this.no_more = true;
             return false;
            }
            this.page++;
            this.getCoach(true, this.page);
        },
        getCoach(isPage, page){
            this.page = page || 1;
            this.$http.get('/deliver/web/experience/getAppLeaderOrderList',{
                pageNum: page||1,
                pageSize:10,
                experience:this.experienceIndex
            }).then(({ data }) => {
                this.infoLists=data.data.data
                if(data.success){
                    if (isPage) {
                        let old = this.coachList;
                        this.coachList = [...old, ...data.data.data.data];
                    } else {
                        this.coachList=data.data.data.data
                    }
                    
                }
            })
        },
        checkIndex(e) {
            this.experienceIndex=e.index
            this.getCoach()
            this.$nextTick(()=>{
                this.scrollTop=0
			});
        },
        changeCoach(item){
            console.log(this.experienceIndex)
            uni.navigateTo({
                url: '/qyWechat/coachManage?id='+item.id+'&experience='+this.experienceIndex
            });
        }
    }
}
</script>
<style scoped lang="scss">
.image-header-css{
    width: 96rpx;
    height: 96rpx;
}
.coach-title-css{
    width:80%;
    margin-left:60rpx;
    position: fixed;
}
.scoll-view-css{
    height: calc(100vh - 90rpx);
    position: absolute;
    top: 110rpx;
    left:0rpx;
}
.empty-img{
    display: block;
    width: 200rpx;
    height: 200rpx;
    margin: 0 auto;
}
.no-text-css{
    width: 750rpx;
    text-align: center;
}
/* #339378 */
.rigth-css{
    color: #339378;
    font-size: 24rpx;
	.image-css{
		width: 20rpx;
		height: 20rpx;
	}
}
.text-css{
    font-size: 28rpx;
    color:#555;
}
.code-css{
    color:rgba(51, 51, 51, 0.5)
}
.mt-12{
    margin-top: 12rpx;
}
</style>
<template>
  <view>
    <view class="bg-ff radius-15 mlr-30" :style="{ height: useHeight + 'rpx' }">
      <view class="pd-30">
        <view class="title">{{
          messageTemplate.messageTitle || "请您对本次试课满意度打分："
        }}</view>
        <view class="star">
          <view class="item" v-for="(item, index) in messageEvaluateOptions" :key="index">
            <view class="text">{{ item.optionText || "" }}:</view>
            <u-rate
              :count="count"
              :value="item.star"
              activeColor="#F2CB51"
              :key="item.id"
              :touchable="false"
              @change="changeRate($event, item, index)"></u-rate>
            <!-- :readonly="!canSubmit" -->
          </view>
        </view>
        <u--textarea
          v-model="content"
          height="600rpx"
          placeholder="写点反馈吧"
          count
          :maxlength="200"
          v-if="messageTemplate.needText == 1"></u--textarea>
      </view>
      <block v-if="isQy">
        <view class="button" @click="onSubmit" v-if="!sendType">提交</view>
        <view class="button" @click="sendMsg" v-if="sendType">发送</view>
      </block>
      <block v-else>
        <view class="button" @click="onSubmit">提交</view>
      </block>
    </view>
  </view>
</template>

<script>
  import { Debounce } from "@/utils/debounce.js";
  export default {
    data() {
      return {
        useHeight: 0,
        count: 5,
        content: "",
        messageEvaluateOptions: [],
        messageTemplate: {},
        hasSubmitted: false,
        evaluateList: [],
        boundId: "",
        templateId: "",
        type: 1,
        codeShow: false,
        queryData: {},
        sendType: false,
        canSubmit: false,
        isQy: true,
      };
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 30;
        },
      });
    },
    onLoad(e) {
      let that = this;
      that.isQy = uni.getStorageSync("isQy");
      if (e.templateId) {
        // console.log(e);
        that.sendType = e.isSend == 1 ? true : false;
        that.templateId = e.templateId;
        that.boundId = e.boundId;
        that.type = e.type ? e.type : that.type;
        that.getDetail(e.templateId, e.boundId);
      }
      if (e.item) {
        that.queryData = JSON.parse(decodeURIComponent(e.item));
      }
      that.canSubmit = e.canSubmit == 1 ? true : false;
      // this.getDetail('1237830663061917696', 1);
    },
    methods: {
      changeRate(e, item, index) {
        let that = this;
        if (that.isQy && !that.canSubmit) {
          uni.showToast({
            title: "非家长不可以在群里点评价哦~",
            icon: "none",
          });
          return (that.messageEvaluateOptions[index].star = 0);
        } else {
          that.messageEvaluateOptions[index].star = e;
        }
      },
      sendMsg: Debounce(function () {
        let that = this;
        // 发文字
        wx.qy.getContext({
          success: () => {
            //    var entry = data.entry, //返回进入小程序的入口类型
            // var shareTicket = data.shareTicket;
            wx.qy.sendChatMessage({
              msgtype: "miniprogram", //消息类型，必填
              enterChat: false, //为true时表示发送完成之后顺便进入会话，仅移动端3.1.10及以上版本支持该字段
              text: {
                content: "", //文本内容
              },
              image: {
                mediaid: "", //图片的素材id
              },
              video: {
                mediaid: "", //视频的素材id
              },
              file: {
                mediaid: "", //文件的素材id
              },
              news: {
                link: "", //H5消息页面url 必填
                title: "", //H5消息标题
                desc: "", //H5消息摘要
                imgUrl: "", //H5消息封面图片URL
              },
              miniprogram: {
                appid: that.$config.WXAppId, //小程序的appid
                title: that.queryData.msgTypeName, //小程序消息的title
                imgUrl: "https://document.dxznjy.com/manage/1716255999000", //小程序消息的封面图
                page: `/qyWechat/CommentStar.html?templateId=${that.templateId}&boundId=${that.boundId}&type=2&canSubmit=0`, //小程序消/小程序消息打开后的路径，注意要以.html作为后缀，否则在微信端打开会提示找不到页面
              },
              success: (res) => {
                that.$http
                  .get("/deliver/teacher/notify/details", {
                    id: that.queryData.id,
                    isSend: 1,
                    boundInfo: that.queryData.boundInfo,
                    message: that.queryData.msgType,
                  })
                  .then(() => {
                    uni.showToast({
                      title: "发送成功",
                    });
                    setTimeout(() => {
                      uni.redirectTo({
                        url: "/qyWechat/notice",
                      });
                    }, 800);
                  })
                  .catch((err) => {
                    uni.showToast({
                      title: "发送失败",
                      icon: "none",
                    });
                  });
              },
              fail: (err) => {
                console.log(err, "=============================");
              },
            });
          },
          fail: (err) => {
            console.log(err, "111111111111111");
          },
        });
      }),
      close() {
        this.codeShow = false;
      },
      onSubmit: Debounce(function () {
        let that = this;
        uni.showLoading({ title: "加载中" });
        if (!that.isQy) {
          let evaluateList = that.messageEvaluateOptions.map((i) => {
            return {
              id: i.id,
              star: i.star,
            };
          });
          let isZero = evaluateList.some((i) => i.star == 0);
          // return console.log(isZero, '==================');
          if (isZero) {
            uni.hideLoading();
            return uni.showToast({
              title: "不可以评0星哦",
              icon: "none",
            });
          } else {
            let data = {
              content: that.content,
              evaluateList: evaluateList,
              boundId: that.boundId,
              type: 2,
            };
            that.$scrmHttp.post("/scrm/evaluate/submit", data).then(({ data }) => {
              console.log(data);
              if (data.success) {
                uni.hideLoading();
                uni.showToast({
                  title: "提交成功",
                });
              } else {
                uni.hideLoading();
                uni.showToast({
                  title: "您已评价过",
                  icon: "none",
                });
              }
            });
          }
        } else {
          if (that.canSubmit) {
            let evaluateList = that.messageEvaluateOptions.map((i) => {
              return {
                id: i.id,
                star: i.star,
              };
            });
            let isZero = evaluateList.some((i) => i.star == 0);
            if (isZero) {
              uni.hideLoading();
              return uni.showToast({
                title: "不可以评0星哦",
                icon: "none",
              });
            } else {
              let data = {
                content: that.content,
                evaluateList: evaluateList,
                boundId: that.boundId,
                type: 1,
              };
              that.$scrmHttp
                .post("/scrm/evaluate/submit", data)
                .then(({ data }) => {
                  console.log(data);
                  if (data.success) {
                    uni.hideLoading();
                    uni.showToast({
                      title: "提交成功",
                    });
                  } else {
                    uni.hideLoading();
                    uni.showToast({
                      title: "您已评价过",
                      icon: "none",
                    });
                  }
                })
                .catch((err) => {
                  uni.hideLoading();
                  uni.showToast({
                    title: "评价失败",
                    icon: "none",
                  });
                });
            }
          } else {
            uni.hideLoading();
            let userRole = uni.getStorageSync("userRole");
            if (userRole == "assistant") {
              return uni.showToast({
                title: "教练不可以给自己评分哦",
                icon: "none",
              });
            } else {
              uni.showToast({
                title: "您不可评价",
                icon: "none",
              });
            }
          }
        }
      }),
      getDetail(templateId, boundId) {
        let that = this;
        this.$scrmHttp
          .get("/scrm/evaluate/detail", {
            templateId: templateId,
            boundId: boundId,
            type: that.type,
          })
          .then(({ data }) => {
            console.log(data);
            data.data.messageEvaluateOptions.forEach((i) => (i.star = 0));
            this.messageEvaluateOptions = data.data.messageEvaluateOptions;
            this.messageTemplate = data.data.messageTemplate;
            this.hasSubmitted = data.data.hasSubmitted;
          })
          .catch((err) => {
            uni.showToast({
              title: "加载出错",
              icon: "none",
            });
          });
      },
    },
  };
</script>
<style lang="scss">
  page {
    background-color: #f3f8fc;
  }
  .title {
    color: #000;
    font-size: 32rpx;
    font-weight: 600;
  }
  .pd-30 {
    padding: 30rpx;
  }
  .img {
    width: 300rpx;
    height: 300rpx;
  }
  .star {
    margin: 50rpx auto;
    .item {
      display: flex;
      margin-top: 26rpx;
      .text {
        margin-right: 50rpx;
        font-size: 28rpx;
        color: #000;
        width: 30%;
      }
      .u-rate {
        flex: 1;
      }
    }
  }
  .u-textarea {
    background-color: #f7f7f7 !important;
  }
  .button {
    margin: 50rpx auto;
    width: 586rpx;
    height: 90rpx;
    line-height: 90rpx;
    text-align: center;
    color: #fff;
    background: #2e896f;
    border-radius: 45rpx;
  }
  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 140rpx;
    z-index: -1;
  }
  .orderinfo {
    position: relative;
    width: 650rpx;
    height: 460rpx;
    font-size: 30rpx;
    border-radius: 24rpx;
    // padding: 80rpx 0 50rpx 0;
    padding-top: 80rpx;
    background-color: #fff;
    font-size: 28rpx;
    .color {
      color: #666666;
    }
    .btns {
      display: flex;
      justify-content: center;
      margin-top: 40rpx;
    }
    .btn {
      width: 250rpx;
      height: 80rpx;
      border-radius: 40rpx;
      line-height: 80rpx;
      text-align: center;
      font-size: 30rpx;
    }
    .btn1 {
      color: #64a795;
      background-color: #ffffff;
      border: 1px solid #64a795;
      margin-right: 40rpx;
      // margin-left: 40rpx;
    }
    .btn2 {
      // background-color: #469880;
      // background-image: linear-gradient(60deg, #64b3f4 0%, #c2e59c 100%);
      background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
      color: #fff;
      // border: 1px solid transparent;
    }
  }
</style>

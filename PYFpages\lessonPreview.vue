<template>
  <view>
    <!-- <view class="header2">
			<uni-icons v-if="!isShare" type="arrowleft" class="backIcon" size="20" color="#000"
				@click="back()"></uni-icons>
		</view> -->
    <view>
      <view class="list_box sizing">
        <view>
          <uv-list v-if="historylist.length !== 0">
            <uv-list-item>
              <view class="listBorder plr-30 sizing" v-for="(item, index) in historylist" :key="index" @tap="gotoLesson(item, index)" :class="{ bold: selectedItem === index }">
                <view class="courseLevel" v-if="item.courseLevel">
                  {{ courseLevelList.find((e) => e.value == item.courseLevel).label }}
                </view>
                <view class="flex-a-c">
                  <text class="item">{{ item.pdCourseName }}</text>
                </view>
                <view class="flex-a-c">
                  <u-icon name="arrow-right"></u-icon>
                </view>
              </view>
            </uv-list-item>
          </uv-list>
          <view v-else class="t-c flex-col zw bg-ff sizing" :style="{ height: useHeight + 'rpx' }">
            <image src="https://document.dxznjy.com/alading/correcting/no_data.png" class="mb-20" style="width: 160rpx" mode="widthFix"></image>
            <view style="color: #bdbdbd">暂无数据</view>
          </view>
          <uni-load-more class="mb-65" :status="loadingType"></uni-load-more>
          <!-- <view v-else>
						<u-empty mode="data">
						</u-empty>
					</view> -->
        </view>
      </view>
    </view>
  </view>
</template>
<script>
  export default {
    onLoad(options) {
      this.studentCode = options.studentCode;
    },
    data() {
      return {
        loadingType: 'more', //加载前
        historylist: [], //复习课程列表
        studentCode: '',
        pdCourseCode: '1',
        planReviewId: '', //复习计划
        pageindex: 1, //当前页码
        pageSize: 20,
        vid: '', //保存体验课视频vid
        selectedItem: null,
        courseLevelList: [
          { value: '0', label: '低年级' },
          { value: '1', label: '基础' },
          { value: '2', label: '高年级' }
        ]
      };
    },
    onLoad(options) {
      console.log('options', options);
      // this.studentCode = options.studentCode;
      this.studentCode = uni.getStorageSync('pyfStudentCode');
      // this.studentCode = '6231216111'
      console.log(this.studentCode);
      this.loadMyMember();
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 560;
        }
      });
    },
    methods: {
      async loadMyMember(type = 'add', loading) {
        if (type === 'add') {
          if (this.loadingType === 'no-more') {
            return;
          }
          this.loadingType = 'loading'; //加载中
        } else {
          this.loadingType = 'more'; //加载前
        }
        this.$http
          .post('/znyy/pd/planReview/queryReviewedCourses', {
            studentCode: this.studentCode,
            // studentCode: '6231216111',
            pageNum: this.pageNum,
            pageSize: this.pageSize
          })
          .then((res) => {
            console.log(res.data.data.data, '课程信息');
            // this.listKnow = res.data.data.data
            if (type === 'refresh') {
              this.historylist = [];
            }

            if (res.data.data.data.length == 0) {
              this.loadingType = 'no-more';
            } else {
              if (res.data.data.data.length) {
                this.historylist = this.historylist.concat(res.data.data.data);
                console.log('this.historylist', this.historylist);
              }
              this.loadingType = this.pageindex >= res.data.data.totalPage ? 'no-more' : 'more';
            }
          });

        // 	let result = await this.$httpUser.post('znyy/pd/planReview/queryReviewedCourses', {
        // 		pageNum: this.pageindex,
        // 		pageSize: this.pageSize,
        // 		studentCode: this.studentCode
        // 	});
        // 	if (result) {
        // 		if (type === 'refresh') {
        // 			this.historylist = [];
        // 		}

        // 		if (result.data.data.data.length == 0) {
        // 			this.loadingType = 'no-more';
        // 		} else {
        // 			if (result.data.data.data.length) {
        // 				this.historylist = this.historylist.concat(result.data.data.data);
        // 				console.log("this.historylist",this.historylist);
        // 			}
        // 			this.loadingType = this.pageindex >= result.data.data.totalPage ? 'no-more' : 'more';
        // 		}
        // 	}
      },
      //判断是否为体验课
      async judgeIdentity() {
        uni.navigateTo({
          url: '/PYFpages/yfyReviewCheck?planReviewId=' + this.planReviewId
        });
        // let res = await this.$http.post('/znyy/pd/planReview/queryPreschoolVideo',{
        // 	pdCourseCode:this.pdCourseCode
        // })
        // console.log(res)
        // this.vid=res.data.data
        // // 等下改成!==
        // if(res.data.data!==null){
        // 	//正式课
        // 	console.log('res---------正式课')
        // 	uni.navigateTo({
        // 		// url:'/PYFforget/preschoolVideo?vid='+this.vid
        // 		url:'/Coursedetails/pinYin/preschoolVideo?vid='+this.vid
        // 	})
        // }else{
        // 	//体验课
        // 	uni.navigateTo({
        // 		url:'/PYFpages/yfyReviewCheck?planReviewId='+this.planReviewId
        // 	})
        // }
      },
      // 错题本跳转题目 view 中
      gotoLesson(know, index) {
        if (!uni.getStorageSync('token')) {
          this.goToLogin();
          return;
        }
        console.log('know========', know.id);
        this.selectedItem = index;
        console.log('this.selectedItem----------', this.selectedItem);
        this.pdCourseCode = know.pdCourseCode;
        this.planReviewId = know.id;
        uni.setStorageSync('pyfPlanReviewId', this.planReviewId); //保存一下复习计划id到本地
        this.judgeIdentity();
      },
      onPageScroll(e) {
        if (e.scrollTop >= 0) {
          this.headerPosition = 'fixed';
        } else {
          this.headerPosition = 'absolute';
        }
      },
      onPullDownRefresh() {
        console.log('下拉刷新触发');
        this.pageindex = 1;
        this.loadMyMember('refresh');
      },
      //加载更多
      onReachBottom() {
        this.pageindex++;
        this.loadMyMember();
      }
    }
  };
</script>

<style>
  .header2 {
    height: 172rpx;
    background-color: #fff;
  }
  .list_box {
    /* border-radius: 14upx; */
    background: linear-gradient(#ecfff7 0%, #f6f7fc 100%);
    box-shadow: 0rpx 4rpx 16rpx 0rpx #f5f7f9;
    padding: 30rpx;
    font-size: 26upx;
    color: #555555;
    position: relative;
    min-height: 99vh;
  }
  .courseLevel {
    position: absolute;
    width: 89rpx;
    height: 52rpx;
    left: 0;
    top: 0;
    background: url('https://document.dxznjy.com/course/fdb49a412f4c483e80b1c69e005b3a8d.png') no-repeat;
    background-size: contain;
    color: #fff;
    font-size: 23rpx;
    line-height: 52rpx;
    text-align: center;
  }
  .iconButton {
    width: 154rpx;
    height: 76rpx;
    background: #555555;
    border-radius: 46rpx;
    border: 2rpx solid #e1e1e1;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 10rpx;
  }
  .item {
    margin-left: 126rpx;
    font-size: 28rpx;
    font-weight: bold;
  }
  .iconB {
    width: 154rpx;
    height: 76rpx;
    background: rgba(225, 225, 225, 0.15);
    border-radius: 46rpx;
    border: 2rpx solid #e1e1e1;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 10rpx;
  }

  .pick {
    flex: 1;
    height: 60upx;
    line-height: 60upx;
    border: 1px solid #c8c8c8;
    border-radius: 20upx;
    text-align: center;
    padding: 0 30upx;
    position: relative;
  }

  .pick_right {
    position: absolute;
    right: 20upx;
    top: -5upx;
  }

  .pick {
    flex: 1;
    height: 60upx;
    line-height: 60upx;
    border: 1px solid #c8c8c8;
    border-radius: 20upx;
    overflow: visible;
    text-align: center;
    padding: 0 30upx;
    position: relative;
    transform: scale(0.995); /* 解决ios上圆角失效 */
  }
  /* 弹层宽度 */
  .uv-dp__container {
    height: 150rpx;
    width: 690rpx;
    margin-left: 30rpx;
  }

  button {
    width: 40% !important;
  }

  .dropButton {
    display: flex;
    justify-content: space-around;
    align-items: center;
  }

  .tagItem {
    display: flex;
    margin: 20rpx 30rpx;
    flex-wrap: wrap;
  }

  .listBorder {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    /* width: 716rpx; */
    height: 83rpx;
    background: #ffffff;
    border-radius: 30rpx;
    border: 2rpx solid #e2eee2;
    flex-flow: row wrap;
    margin-bottom: 20rpx;
  }

  .badge {
    width: 50rpx;
    height: 50rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    background-color: #edeced;
    font-size: 18rpx;
  }
  .backIcon {
    position: absolute;
    left: 30rpx;
    top: 90rpx;
  }
  .bold {
    font-weight: 900 !important;
    color: #3bbf9f;
    border-color: #3bbf9f;
  }
  .listBorder.active {
    font-weight: 900;
  }
  .zw {
    padding-top: 45%;
    box-sizing: border-box;
  }
</style>

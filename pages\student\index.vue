<template>
  <!-- <page-meta style="overflow: hidden;"></page-meta> -->
  <view>
    <view class="hear_img">
      <image src="https://document.dxznjy.com/app/images/zhujiaoduan/parent-bgc.png" class="wallet-bgc"></image>
      <view class="search-view">
        <view class="search-student">
          <view style="display: flex">
            <image style="height: 30rpx; width: 30rpx; margin-top: 8rpx" src="/static/images/create_img_seacher.png"></image>
            <input type="text" v-model="searchName" placeholder="请输入学员姓名" class="search-student-input" />
          </view>
          <view class="search-student-text" @click="nameSearchFunc">搜索</view>
        </view>
      </view>
      <view class="nav-title">学员管理</view>
      <!--#ifdef APP-PLUS  -->
      <view class="backImg" @click="goBack">
        <image lazy-load src="/static/images/left-icon.png" style="width: 32rpx; height: 32rpx; display: block" mode=""></image>
      </view>
      <!-- #endif -->
    </view>
    <view class="tab-bg">
      <u-tabs
        :list="list4"
        @click="checkIndex"
        lineWidth="40rpx"
        lineColor="#2E896F"
        :activeStyle="{ color: '#000000', fontWeight: 'bold', fontSize: '32rpx' }"
        :inactiveStyle="{ color: '#666666', fontSize: '30rpx' }"
      ></u-tabs>
    </view>

    <scroll-view
      v-if="studentListData.data.length > 0"
      :style="'height: ' + (useHeight - 504) + 'rpx;'"
      style="background-color: #f3f8fc"
      scroll-y="true"
      :scroll-top="scrollTop"
      @scrolltolower="scrolltolower"
    >
      <view class="scroll-box">
        <view class="list-bg">
          <view v-for="(item, index) in studentListData.data" :key="index" class="c-33">
            <view v-if="navIndex == 0" class="list-item-bg-0">
              <view class="flex-between pt-40">
                <text style="font-size: 30rpx">学员：{{ getMaxLengthText(item.studentName) }}（{{ item.studentCode }}）</text>
                <view class="flex-c">
                  <image @click="vocabulary(item)" style="width: 45rpx; height: 50rpx" src="/static/images/img_student_report.png" mode="aspectFill"></image>
                  <!-- <view :class="item.reviewType==1?'studyClass-y':'studyClass-g'" style="margin-left: 14rpx;">{{item.reviewType==1?'教练带复习':'自行复习'}}</view> -->
                </view>
              </view>
              <view class="flex-between pt-30 pb-40">
                <text style="font-size: 30rpx">剩余可排交付课时：{{ getSchoolHourVos(item.studentSchoolHourVos) }}</text>
              </view>
              <view class="flex-between pt-30 pb-40" v-if="false">
                <text style="font-size: 30rpx">剩余复习时长：{{ getHaveReviewMinutes(item.haveReviewMinutes) }}</text>
              </view>
              <view class="pb-40" @click="voiceSet(item)" style="font-size: 30rpx; color: #2e896f; margin-left: 20rpx">发音设置></view>
            </view>
            <view v-else-if="navIndex == 1" :class="index == 0 ? 'list-item-bg-1-top' : index != studentListData.data.length - 1 ? 'list-item-bg-1' : 'list-item-bg-1-end'">
              <view class="flex-between pt-30 pb-20" :class="index == 0 ? '' : 'border-top'">
                <text style="font-size: 30rpx">学员：{{ getMaxLengthText(item.studentName) }}（{{ item.studentCode }}）</text>
                <image @click="vocabulary(item)" style="width: 45rpx; height: 50rpx" src="/static/images/img_student_report.png" mode="aspectFill"></image>
              </view>
              <view class="pb-30" style="margin-left: 8rpx">
                <text @click="clickInterest(item)" style="font-size: 30rpx; color: #2e896f; margin-left: 14rpx">趣味复习</text>
                <text style="margin-left: 40rpx; margin-right: 40rpx; color: #2e896f">|</text>
                <text @click="voiceSet(item)" style="font-size: 30rpx; color: #2e896f; margin-left: 14rpx">发音设置</text>
              </view>
              <view class="flex-between pt-20 pb-20" v-if="false">
                <text style="font-size: 30rpx">剩余复习时长：{{ getHaveReviewMinutes(item.haveReviewMinutes) }}</text>
              </view>
            </view>
            <view v-else :class="index == 0 ? 'list-item-bg-2-top' : index != studentListData.data.length - 1 ? 'list-item-bg-2' : 'list-item-bg-2-end'">
              <view class="flex-between pt-30" :class="index == 0 ? '' : 'border-top'">
                <text style="font-size: 30rpx">学员：{{ getMaxLengthText(item.studentName) }}（{{ item.studentCode }}）</text>
                <image @click="vocabulary(item)" style="width: 45rpx; height: 50rpx" src="/static/images/img_student_report.png" mode="aspectFill"></image>
              </view>
            </view>
          </view>
          <view v-if="no_more && studentListData.data.length > 0">
            <u-divider text="到底了"></u-divider>
          </view>
        </view>
      </view>
    </scroll-view>

    <view v-else style="background-color: #f3f8fc; padding-bottom: 30rpx" :style="'height: ' + (useHeight - 614) + 'rpx;'">
      <view class="empty-content">
        <image :src="imgHost + '/app/images/zhujiaoduan/zhujiao_img_student_no.png'" class="empty-img"></image>
        <view style="color: #bdbdbd; margin-top: 28rpx">暂无学员</view>
      </view>
    </view>

    <!--        <view class="course-btn-bg">
            <view class="course-btn" @click="addStudent">
                添加学员
            </view>
        </view> -->
    <!--        
        <uni-popup ref="addPopup" type="bottom" :is-mask-click="false" :safe-area = "false">
            <view class="dialogBG pt-25 pb-45 pl-40 pr-20 ">
                <view class="flex-s" style="margin-bottom: 60rpx;">
                    <text style="font-size: 30rpx;font-weight: 650;">添加学员</text>
                    <image @click="addCancel" style="width: 42rpx;height: 42rpx;" src="/static/images/icon_close.png" mode="aspectFill"></image>
                </view>
                <view class="input-border mb-40 flex-center">
                    <image style="width: 38rpx;height: 38rpx;" src="/static/images/create_img_user.png" mode="aspectFill"></image>
                    <input type="text" v-model="searchPhone" placeholder="请输入学员编号或家长手机号" 
                    class="search-dialog-student-input"/>
                    <view class="flex-c" style="height: 80rpx;width: 80rpx;" @click="dialogSearchPhone">
                        <image style="height: 40rpx;width: 40rpx;" src="/static/images/create_img_seacher.png"></image>
                    </view>
                </view>
                <view class="flex-center" style="margin-bottom: 160rpx;">
                    <image @click="dialogChose(1)" style="width: 36rpx;height: 36rpx;" 
                    :src="getChoseDisable(1,3)" mode="aspectFill"></image>
                    <text style="font-size: 30rpx;color: #666666;margin-left: 18rpx;">上课学员</text>
                    <image @click="dialogChose(2)" class="ml-40" style="width: 36rpx;height: 36rpx;" 
                    :src="getChoseDisable(2,0)" mode="aspectFill"></image>
                    <text style="font-size: 30rpx;color: #666666;margin-left: 18rpx;">复习学员</text>
                    <image @click="dialogChose(3)" class="ml-40" style="width: 36rpx;height: 36rpx;" 
                    :src="getChoseDisable(3,1)" mode="aspectFill"></image>
                    <text style="font-size: 30rpx;color: #666666;margin-left: 18rpx;">代课学员</text>
                </view>
               <view class="flex-center">
                   <view class="common-cancel-btn" @click="addCancel">取消</view>
                   <view class="common-sure-btn" style="margin-left: 30rpx;" @click="addSure">确定</view>
               </view>
            </view>
        </uni-popup> -->

    <uni-popup ref="addStudentSuccess" type="center" @maskClick="closeAddSucc">
      <view class="bg-ff radius-15 mlr-30 flex-col ptb-45 plr-45">
        <image style="width: 110rpx; height: 110rpx; margin-bottom: 30rpx" src="https://document.dxznjy.com/app/images/zhujiaoduan/create_img_success.png"></image>
        <view style="font-size: 32rpx; margin-bottom: 20rpx; text-align: center">您已成功添加：{{ addStudentRequest.studentName }}（{{ addStudentRequest.studentCode }}）</view>
        <view style="font-size: 32rpx; text-align: center">
          为您的
          <text style="color: #ea6031">{{ getSuccessText(choseIndexArr[0]) }}</text>
          <text v-if="choseIndexArr.length > 1">与</text>
          <text v-if="choseIndexArr.length > 1" style="color: #ea6031">{{ getSuccessText(choseIndexArr[1]) }}</text>
          ！
        </view>
      </view>
    </uni-popup>

    <!-- 选择校区弹窗 -->
    <uni-popup ref="popopChooseSchool" type="center">
      <view class="dialogBG">
        <view class="dialog-all">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="dialog-small-close" @click="cancelSchool">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="dialog-center">
            <view class="dialog-title bold pb-20">选择校区</view>
            <view
              class="dialog-item"
              @click="chooseSchoollist(item, index)"
              v-for="(item, index) in arraySchool"
              :key="index"
              :class="isactive == index ? 'selected' : 'not-selected'"
            >
              {{ item.merchantName }}
            </view>
            <view class="flex-c mt-40">
              <view class="common-sure-btn-1" @click="confirmSchool()">确定</view>
              <view class="common-cancel-btn ml-40" @click="cancelSchool()">取消</view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 选择学员弹窗 -->
    <uni-popup ref="popopChooseStudent" type="bottom" :safe-area="false">
      <view class="dialogBG">
        <view class="dialog-all">
          <view class="dialog-small-close" @click="cancelStudent">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="dialog-center">
            <view class="dialog-title pb-10">选择学员</view>
            <view
              class="dialog-item"
              @click="chooseStudentlist(item, index)"
              v-for="(item, index) in arrayStudent"
              :key="index"
              :class="isactive == index ? 'selected' : 'not-selected'"
            >
              {{ item.realName + '（' + item.studentCode + '）' }}
            </view>
            <view class="flex-c mt-40">
              <view class="common-sure-btn-1" @click="confirmStudent()">确定</view>
              <view class="common-cancel-btn ml-40" @click="cancelStudent()">取消</view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>

    <uni-popup ref="loadingPopup" type="center" :is-mask-click="false">
      <view class="bg-ff radius-15 t-c">
        <view class="loadingpadding">
          <image src="https://document.dxznjy.com/Assistant/loading.png" class="loadingImg"></image>
          <view class="mt-30">加载中...</view>
          <view>请耐心等候</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import dayjs from 'dayjs';
  export default {
    data() {
      return {
        imgHost: 'https://document.dxznjy.com/',
        useHeight: 0,
        list4: [{ name: '上课学员' }, { name: '复习学员' }, { name: '代课学员' }],
        navIndex: 0, // tabs栏索引

        searchName: '', //搜索名字
        studentListData: {
          data: []
        },

        scrollTop: 0,
        no_more: false,
        page: 1,
        pageSize: 20,

        isactive: -1,

        arraySchool: [],
        cacheDialogMerchantCode: '',
        choseInterestStudentItem: null,
        dialogMerchantCode: '',

        arrayStudent: [],
        searchPhone: '', //弹框搜索手机号学生code
        choseIndexArr: [], //添加学员
        bindInfoFromCode: [],
        cacheAddStudentRequest: {},
        addStudentRequest: {},

        //教练个人信息
        userlist: {}
      };
    },
    onLoad() {},
    onReady() {
      let that = this;
      uni.getSystemInfo({
        //调用uni-app接口获取屏幕高度
        success(res) {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          that.useHeight = h;
        }
      });
    },
    watch: {
      searchName(newName, oldName) {
        if (newName.length == 0) {
          console.log('searchName watch');
          this.getNormalList();
        }
      },

      searchPhone(newPhone, oldPhone) {
        if (newPhone.length == 0) {
          this.choseIndexArr = [];
          this.bindInfoFromCode = [];
          this.arrayStudent = [];
          this.addStudentRequest = {};
        }
      }
    },

    onShow() {
      this.searchName = '';

      uni.removeStorage({ key: 'logintokenReview' });

      this.getPersonalList();
      this.clickHandle();
      this.getNormalList();
    },
    methods: {
      goBack() {
        plus.runtime.quit();
      },
      //发音设置 START
      async voiceSet(item) {
        let that = this;
        try {
          let res = await uni.$http.get('/znyy/course/info', { studentCode: item.studentCode });
          that.$refs.loadingPopup.close();
          if (res && res.data.success) {
            let data = {
              studentCode: item.studentCode,
              v: '1',
              rq: '0',
              sex: 'W'
            };
            if (res.data.data.voiceModel != '') {
              let list = res.data.data.voiceModel.split('#');
              data = {
                studentCode: item.studentCode,
                v: list[0],
                rq: list[1],
                sex: list[2]
              };
            }
            console.log(1111);
            uni.navigateTo({
              url: '/pages/my/pronunciationSettings/index?list=' + encodeURIComponent(JSON.stringify(data))
            });
          }
        } catch {
          that.$refs.loadingPopup.close();
        }
      },
      //发音设置 END
      getMaxLengthText(str) {
        if (str.length > 3) {
          let val = str.substr(0, 3);
          return val + '...';
        }
        return str;
      },
      getHaveReviewMinutes(haveReviewMinutes) {
        let str = '';
        if (!haveReviewMinutes) {
          return str;
        }
        if (haveReviewMinutes.length == 1) {
          str = haveReviewMinutes[0].haveDeliverHours + '分钟';
        } else {
          for (var i = 0; i < haveReviewMinutes.length; i++) {
            str += haveReviewMinutes[i].merchantName + '：' + haveReviewMinutes[i].haveDeliverHours + '分钟';
            if (i != haveReviewMinutes.length - 1) {
              str += '，';
            }
          }
        }
        if (str == '') {
          str = '0分钟';
        }
        return str;
      },
      getSchoolHourVos(studentSchoolHourVos) {
        let str = '';
        if (!studentSchoolHourVos) {
          return str;
        }
        if (studentSchoolHourVos.length == 1) {
          str = studentSchoolHourVos[0].haveDeliverHours;
        } else {
          for (var i = 0; i < studentSchoolHourVos.length; i++) {
            str += studentSchoolHourVos[i].merchantName + '：' + studentSchoolHourVos[i].haveDeliverHours;
            if (i != studentSchoolHourVos.length - 1) {
              str += '，';
            }
          }
        }
        if (str == '') {
          str = '0';
        }
        return str;
      },
      getNormalList() {
        this.no_more = false;
        this.page = 1;
        this.studentListData.data = [];
        this.getBindStudentList();
      },
      // 获取个人信息(账号，姓名)
      async getPersonalList() {
        let { data } = await uni.$http.get('/deliver/app/teacher/info');
        this.userlist = data.data;
        uni.getStorageSync('teacherName', this.userlist.name);
        uni.getStorageSync('teacherCode', this.userlist.teacherCode);
        uni.getStorageSync('merchantName', this.userlist.merchantName);
        uni.getStorageSync('merchantCode', this.userlist.merchantCode);
      },
      // 滚动条回到顶部
      clickHandle() {
        this.scrollTop = this.scrollTop === 0 ? 1 : 0;
      },
      scrolltolower() {
        if (this.page >= this.studentListData.totalPage) {
          this.no_more = true;
          return false;
        }
        ++this.page;
        this.getBindStudentList(true);
      },
      nameSearchFunc() {
        if (this.searchName.length == 0) {
          uni.showToast({
            icon: 'none',
            title: '请输入学员姓名',
            duration: 2000
          });
          return;
        }
        this.getNormalList();
      },
      //1 上课学员 2复习学员 3代课学员
      async getBindStudentList(isPage) {
        let _this = this;
        // _this.$refs.loadingPopup.open();
        try {
          let data = {
            studentName: _this.searchName,
            pageNum: _this.page,
            pageSize: _this.pageSize,
            type: _this.navIndex + 1
          };
          let res;
          if (_this.navIndex == 0) {
            res = await uni.$http.get('/deliver/app/student/bind/selStudentBindingPage', data);
            if (_this.navIndex != 0) {
              return;
            }
          } else if (_this.navIndex == 1) {
            res = await uni.$http.get('/deliver/app/student/bind/selStudentBindingPage', data);
            if (_this.navIndex != 1) {
              return;
            }
          } else if (_this.navIndex == 2) {
            res = await uni.$http.get('/deliver/app/student/bind/selStudentBindingPage', data);
            if (_this.navIndex != 2) {
              return;
            }
          }
          // _this.$refs.loadingPopup.close();
          if (res.data.success) {
            if (isPage) {
              let old = _this.studentListData.data;
              _this.studentListData.data = [...old, ...res.data.data.data];
            } else {
              _this.studentListData = res.data.data;
            }
          } else {
            console.log('___err___' + res.data.message);
          }
        } catch {
          // _this.$refs.loadingPopup.close();
        }
      },

      checkIndex(e) {
        this.navIndex = e.index;
        this.getNormalList();
      },
      vocabulary(item) {
        uni.navigateTo({
          url: '/vocabulary/vocabulary?studentCode=' + item.studentCode + '&studentName=' + item.studentName
        });
      },
      addStudent() {
        this.cleanAddData();
        this.$refs.addPopup.open();
      },
      getChoseDisable(self, other) {
        if (this.choseIndexArr.indexOf(self) != -1) {
          return '/static/images/icon_chose.png';
        } else if (this.choseIndexArr.indexOf(other) != -1) {
          return '/static/images/icon_chose_disable.png';
        } else {
          return '/static/images/icon_chose_normal.png';
        }
      },

      addSure() {
        if (!this.addStudentRequest.studentCode) {
          uni.showToast({
            icon: 'none',
            title: '请点击搜索按钮查询'
          });
          return;
        }
        if (this.choseIndexArr.length <= 0) {
          uni.showToast({
            icon: 'none',
            title: '请选择绑定类型'
          });
          return;
        }
        this.studentBinding();
        this.$refs.addPopup.close();
      },
      addCancel() {
        this.$refs.addPopup.close();
      },
      getSuccessText(index) {
        if (index == 1) {
          return '上课学员';
        } else if (index == 2) {
          return '复习学员';
        }
        if (index == 3) {
          return '代课学员';
        }
        return '';
      },
      cleanAddData() {
        this.arrayStudent = [];
        this.searchPhone = '';
        this.bindInfoFromCode = [];
        this.choseIndexArr = [];
        this.addStudentRequest = {};
      },
      closeAddSucc() {
        this.cleanAddData();
        this.$refs.addStudentSuccess.close();
      },
      async studentBinding() {
        let that = this;
        that.$refs.loadingPopup.open();
        try {
          let res = await uni.$http.post('/deliver/app/student/bind/studentBinding', {
            studentCode: that.addStudentRequest.studentCode,
            teacherId: that.userlist.teacherCode,
            type: that.choseIndexArr
          });
          that.$refs.loadingPopup.close();
          if (res && res.data.success) {
            this.getNormalList();
            this.$refs.addStudentSuccess.open();
          } else {
            uni.showToast({
              icon: 'none',
              title: res.data.message
            });
          }
        } catch {
          that.$refs.loadingPopup.close();
        }
      },

      //查询学员绑定信息
      async selStudentBindInfo() {
        let that = this;
        that.$refs.loadingPopup.open();
        try {
          let res = await uni.$http.get('/deliver/app/student/bind/selStudentBindInfo', { studentCode: that.addStudentRequest.studentCode });
          console.log(res);
          that.$refs.loadingPopup.close();
          if (res) {
            that.bindInfoFromCode = res.data.data;
          }
        } catch {
          that.$refs.loadingPopup.close();
        }
      },
      async dialogSearchPhone() {
        let that = this;
        if (that.searchPhone == '') {
          uni.showToast({
            icon: 'none',
            title: '搜索信息不能为空'
          });
          return;
        }
        if (that.addStudentRequest.studentCode) {
          return;
        }
        that.$refs.loadingPopup.open();
        try {
          let res = await uni.$http.get('/znyy/areas/student/getStudentInfo', { value: that.searchPhone });
          that.$refs.loadingPopup.close();
          if (res.data.data.length > 0) {
            that.arrayStudent = res.data.data;
            if (that.arrayStudent.length > 1) {
              that.isactive = -1;
              that.$refs.popopChooseStudent.open();
            } else {
              that.addStudentRequest.studentCode = that.arrayStudent[0].studentCode;
              that.addStudentRequest.studentName = that.arrayStudent[0].realName;
              that.searchPhone = that.addStudentRequest.studentName + ' ' + that.addStudentRequest.studentCode;
              that.selStudentBindInfo();
            }
          } else {
            uni.showToast({
              icon: 'none',
              title: '暂无学员，请重新搜索'
            });
          }
        } catch {
          that.$refs.loadingPopup.close();
        }
      },
      //1上课  2复习 3代课
      dialogChose(index) {
        if (!this.addStudentRequest.studentCode) {
          this.$util.alter('请点击搜索按钮查询');
          return;
        }
        if (index == 1 && this.choseIndexArr.indexOf(3) != -1) {
          return;
        } else if (index == 3 && this.choseIndexArr.indexOf(1) != -1) {
          return;
        }
        let i = this.choseIndexArr.indexOf(index);
        if (i != -1) {
          if (this.bindInfoFromCode.indexOf(2) == -1 && index == 2) {
            return;
          }
          this.choseIndexArr.splice(i, 1);
        } else {
          this.choseIndexArr.push(index);
          if (this.choseIndexArr.indexOf(2) == -1 && this.bindInfoFromCode.indexOf(2) == -1 && (index == 1 || index == 3)) {
            this.choseIndexArr.push(2);
          }
        }
      },
      //选择学生
      chooseStudentlist(item, index) {
        this.isactive = index;
        this.cacheAddStudentRequest.studentCode = item.studentCode;
        this.cacheAddStudentRequest.studentName = item.realName;
      },
      confirmStudent() {
        if (this.isactive == -1) {
          uni.showToast({
            icon: 'none',
            title: '请先选择~'
          });
          return;
        }
        this.addStudentRequest = this.cacheAddStudentRequest;
        this.searchPhone = this.addStudentRequest.studentName + ' ' + this.addStudentRequest.studentCode;
        this.selStudentBindInfo();
        this.$refs.popopChooseStudent.close();
        this.isactive = -1;
        this.cacheAddStudentRequest = {};
      },
      cancelStudent() {
        this.cacheAddStudentRequest = {};
        this.$refs.popopChooseStudent.close();
        this.isactive = -1;
      },

      //选择校区
      chooseSchoollist(item, index) {
        this.isactive = index;
        this.cacheDialogMerchantCode = item.merchantCode;
      },
      confirmSchool() {
        if (this.isactive == -1) {
          uni.showToast({
            icon: 'none',
            title: '请先选择~'
          });
          return;
        }
        this.dialogMerchantCode = this.cacheDialogMerchantCode;
        this.getPadToken();
        this.$refs.popopChooseSchool.close();
        this.isactive = -1;
        this.cacheDialogMerchantCode = '';
      },
      cancelSchool() {
        this.$refs.popopChooseSchool.close();
        this.isactive = -1;
        this.cacheDialogMerchantCode = '';
      },

      //趣味复习
      interest() {
        console.log(2222);
        if (this.choseInterestStudentItem) {
          console.log(1111);
          uni.navigateTo({
            url: '/interestModule/funReview?studentCode=' + this.choseInterestStudentItem.studentCode + '&merchantCode=' + this.dialogMerchantCode
          });
        }
      },
      clickInterest(item) {
        this.choseInterestStudentItem = item;
        this.getSchoolList(item.studentCode);
      },
      //获取校区
      async getSchoolList(studentCode) {
        let that = this;
        that.$refs.loadingPopup.open();
        try {
          let result = await uni.$http.get('/v2/mall/getStudentMerchantList?studentCode=' + studentCode);
          that.$refs.loadingPopup.close();
          if (result.data.success) {
            that.arraySchool = result.data.data;
            if (result.data.data == null) {
              that.$util.alter('您还没有学习过课程');
            } else {
              if (result.data.data.length == 1) {
                that.dialogMerchantCode = that.arraySchool[0].merchantCode;
                that.getPadToken();
              } else if (result.data.data.length > 1) {
                that.$refs.popopChooseSchool.open();
              }
            }
          } else {
            that.$util.alter(result.data.message);
          }
        } catch (e) {
          that.$refs.loadingPopup.close();
        }
      },
      // 获取pad token
      async getPadToken() {
        let that = this;
        var logintoken = uni.getStorageSync('token');
        that.$refs.loadingPopup.open();
        try {
          let res = await uni.$http.get(
            `/new/security/v2/login/student/member/token?memberToken=${logintoken}&studentCode=${that.choseInterestStudentItem.studentCode}&merchantCode=${that.dialogMerchantCode}`
          );
          that.$refs.loadingPopup.close();
          if (res.data.success) {
            console.log(res.data.data.token);
            uni.setStorageSync('logintokenReview', res.data.data.token);
            that.interest();
          } else {
            that.$util.alter(res.data.message);
          }
        } catch (e) {
          that.$refs.loadingPopup.close();
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .scroll-box {
    margin: 0 auto;
  }
  .hear_img {
    position: relative;
    width: 100%;
    height: 340rpx;
    z-index: 99;
  }
  .wallet-bgc {
    width: 100%;
    height: 340rpx;
  }
  .nav-title {
    position: absolute;
    font-weight: bold;
    font-size: 34rpx;
    top: 130rpx;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 999;
  }
  .backImg {
    position: absolute;
    top: 130rpx;
    left: 5%;
    transform: translate(-50%, -50%);
    z-index: 999;
  }
  .search-view {
    position: absolute;
    left: 50%;
    transform: translate(-50%, -50%);
    top: 270rpx;
    width: 690rpx;
    height: 140rpx;
    background-color: #fff;
    border-radius: 14rpx;
  }
  .search-student {
    padding: 0 20rpx;
    margin-top: 30rpx;
    margin-left: 20rpx;
    width: 610rpx;
    height: 80rpx;
    display: flex;
    border-radius: 8rpx;
    background-color: rgba(200, 200, 200, 0.18);
    justify-content: space-between;
    align-items: center;
  }
  .search-student-input {
    color: #999999;
    margin-left: 10rpx;
    width: 410rpx;
    height: 40rpx;
    font-size: 30rpx;
  }
  .search-student-text {
    padding-left: 20rpx;
    color: #2e896f;
    font-size: 30rpx;
    border-left: 1rpx solid #d9d9d9;
  }

  .tab-bg {
    background-color: #f3f8fc;
    width: 100%;
    height: 64rpx;
    padding-top: 10rpx;
    padding-bottom: 50rpx;
    display: flex;
    justify-content: center;
  }
  .list-bg {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #f3f8fc;
    width: 100%;
  }
  .list-item-bg-0 {
    width: 690rpx;
    background: #ffffff;
    border-radius: 14rpx;
    margin-bottom: 30rpx;
  }
  .list-item-bg-1 {
    width: 690rpx;
    background: #ffffff;
  }
  .list-item-bg-1-top {
    width: 690rpx;
    background: #ffffff;
    border-top-left-radius: 14rpx;
    border-top-right-radius: 14rpx;
  }
  .list-item-bg-1-end {
    width: 690rpx;
    background: #ffffff;
    border-end-start-radius: 14rpx;
    border-end-end-radius: 14rpx;
  }
  .list-item-bg-2 {
    width: 690rpx;
    height: 108rpx;
    background: #ffffff;
  }
  .list-item-bg-2-top {
    width: 690rpx;
    height: 108rpx;
    background: #ffffff;
    border-top-left-radius: 14rpx;
    border-top-right-radius: 14rpx;
  }
  .list-item-bg-2-end {
    width: 690rpx;
    height: 108rpx;
    background: #ffffff;
    border-end-start-radius: 14rpx;
    border-end-end-radius: 14rpx;
  }
  .flex-between {
    margin-right: 20rpx;
    margin-left: 20rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .border-top {
    border-top: 2rpx dashed #eeeeee;
  }
  .studyClass-g {
    padding-left: 6rpx;
    padding-right: 6rpx;
    height: 36rpx;
    background: #2dc032;
    border-radius: 4rpx;
    font-size: 26rpx;
    color: #ffffff;
    line-height: 35rpx;
    text-align: center;
  }
  .studyClass-y {
    padding-left: 8rpx;
    padding-right: 8rpx;
    height: 36rpx;
    background: #edbd58;
    border-radius: 4rpx;
    font-size: 26rpx;
    color: #ffffff;
    text-align: center;
    line-height: 35rpx;
  }
  .loadingImg {
    width: 120rpx;
    height: 120rpx;
  }
  .loadingpadding {
    padding: 85rpx 250rpx;
  }
  .course-btn-bg {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 120rpx;
    background-color: #ffffff;
  }
  .course-btn {
    color: #ffffff;
    font-size: 30rpx;
    width: 586rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 40rpx;
  }
  .flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .dialogBG {
    margin: 0 30rpx 30rpx 30rpx;
    background-color: #fff;
    border-radius: 14rpx;
  }
  .input-border {
    height: 80rpx;
    border-radius: 14rpx;
    border: 1rpx solid rgba(0, 0, 0, 0.2);
  }
  .search-dialog-student-input {
    color: #666666;
    margin-left: 30rpx;
    width: 440rpx;
    height: 40rpx;
    font-size: 28rpx;
  }
  .common-sure-btn {
    width: 250rpx;
    height: 80rpx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 40rpx;
    font-size: 30rpx;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .common-sure-btn-1 {
    width: 250rpx;
    height: 80rpx;
    background: #2e896f;
    border-radius: 40rpx;
    font-size: 30rpx;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(0.995); /* 解决ios上圆角失效 */
  }
  .common-cancel-btn {
    width: 250rpx;
    height: 80rpx;
    border-radius: 40rpx;
    border: 1rpx solid #2e896f;
    font-size: 30rpx;
    color: #2e896f;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(0.995); /* 解决ios上圆角失效 */
  }
  .empty-content {
    height: 100%;
    margin: 0 30rpx 0 30rpx;
    z-index: 9;
    border-radius: 14rpx;
    background-color: #fff;
    display: flex;
    flex-direction: column; /* 垂直布局，子视图按列排列 */
    justify-content: center; /* 水平居中 */
    align-items: center; /* 垂直居中 */
  }
  /deep/.u-tabs__wrapper__nav__item {
    height: 80rpx !important;
  }
  .empty-img {
    width: 114rpx;
    height: 107rpx;
  }
  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 170rpx;
    z-index: -1;
  }

  .dialog-all {
    width: 670rpx;
    position: relative;
  }

  .dialog-all image {
    width: 100%;
    height: 100%;
  }

  .dialog-center {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 50upx 55upx;
    box-sizing: border-box;
  }
  .dialog-small-close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .dialog-title {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    font-weight: 600;
    justify-content: center;
  }
  .dialog-item {
    box-sizing: border-box;
    font-size: 32upx;
    line-height: 45upx;
    text-align: center;
    margin-top: 40rpx;
  }

  .selected {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #2e896f;
    border: 1px solid #2e896f;
    overflow: visible;
    border-radius: 35rpx;
  }

  .not-selected {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #000;
    border: 1px solid #c8c8c8;
    overflow: visible;
    border-radius: 35rpx;
    transform: scale(0.995); /* 解决ios上圆角失效 */
  }
</style>

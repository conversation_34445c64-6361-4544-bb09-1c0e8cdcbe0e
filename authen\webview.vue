<template>
  <view class="web-view-container">
    <web-view :src="url"></web-view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        url: ''
      };
    },
    onLoad(options) {
      // 获取传递过来的url参数
      if (options.url) {
        this.url = uni.getStorageSync('signurl');
        console.log(this.url)
        console.log('mmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm')
      }
    },
    onUnload() {
      uni.removeStorageSync('signurl')
    }
  };
</script>
<style lang="scss">
  .web-view-container {
    width: 100%;
    height: 100vh;
  }
</style>
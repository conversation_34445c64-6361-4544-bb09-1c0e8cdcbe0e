<template>
  <page-meta :page-style="'overflow:' + (showReviewTimeDialog ? 'hidden' : 'visible')"></page-meta>
  <view>
    <view class="nav-title">
      <view class="status_bar">
        <uni-icons @click="Back" type="left" color="#a8a8a8" size="24" class="icon_img"></uni-icons>
        {{ trialclass ? (triallist.curriculumName | safe) + '体验结果反馈' : '学习反馈' }}
      </view>
    </view>

    <view class="container">
      <!-- 试课反馈详情 -->
      <view class="card" v-if="trialclass">
        <view class="white-content">
          <view class="text-content" v-if="triallist.moduleType !== 4">课程类型：{{ triallist.curriculumName | safe }}</view>
          <view class="text-content">日期：{{ triallist.dateTime | safe }}</view>
          <view class="text-content">姓名：{{ triallist.studentName | safe }}</view>
          <view class="text-content">年级：{{ triallist.gradeName | safe }}</view>
          <view class="text-content">学员编号：{{ triallist.studentCode | safe }}</view>
          <view class="text-content" v-if="triallist.moduleType !== 4">时间：{{ triallist.studyTime | safe }}</view>
          <!-- 实际时间 -->
          <view v-if="!Feedback || (timelist.actualStart && timelist.actualEnd)" class="text-content reality">
            实际时间：
            <view class="begin">{{ timelist.actualStart | safe }}</view>
            至
            <view class="finish">{{ timelist.actualEnd | safe }}</view>
          </view>
          <view v-else class="text-content flexs">
            实际时间：
            <dateTime ref="chanelTimetry" @getStart="changenum" @getEnd="changetime"></dateTime>
          </view>
          <!-- TODO 新增超级阅读类型不同字段 -->
          <view v-if="triallist.moduleType === 3">
            <view class="text-content">所学课程类型：{{ triallist.superReadCourseStatistics && triallist.superReadCourseStatistics.courseName }}</view>
            <view class="text-content">所学课程名称：{{ mapArray(triallist.superReadCourseStatistics.courseList) }}</view>
            <view class="text-content">学习进度：{{ triallist.superReadCourseStatistics && triallist.superReadCourseStatistics.learningProgress }}%</view>
            <view class="text-content">学习关卡（正确率)：{{ rateMapArray(triallist.superReadCourseStatistics.checkpointList) }}</view>
          </view>
          <view v-if="triallist.moduleType === 2">
            <view class="text-content">所学语法：{{ triallist.grammarName | safe }}</view>
            <view class="text-content">学习进度：{{ triallist.learningProgress | safe }}%</view>
            <view class="text-content">
              <span>复习知识点：{{ triallist.reviewKnowledgeNum | safe }}个</span>
              <span>新学知识点：{{ triallist.newKnowledgeNum | safe }}个</span>
            </view>
            <view class="text-content">语法点：{{ triallist.grammarNum | safe }}个</view>
            <view class="text-content">结业检测：{{ triallist.graduationNum | safe }}个</view>

            <view class="text-content">
              <!-- 复习知识点(正确率)：-- -->
              <view>复习知识点(正确率)：</view>
              <view>
                <template v-for="(item, index) in triallist.reviewKnowledgeList">
                  <view class="pb-20">{{ item.name || '--' }} ({{ item.accuracyRate || '--' }}%)</view>
                </template>
                <span v-if="triallist.reviewKnowledgeList && triallist.reviewKnowledgeList.length <= 0">--</span>
              </view>
            </view>

            <view class="text-content">
              <!-- 新学知识点(正确率)： -->
              <view>新学知识点(正确率)：</view>
              <view>
                <template v-for="(item, index) in triallist.newKnowledgeList">
                  <view class="pb-20">{{ item.name || '--' }} ({{ item.accuracyRate || '--' }}%)</view>
                </template>
                <span v-if="triallist.newKnowledgeList && triallist.newKnowledgeList.length <= 0">--</span>
              </view>
            </view>

            <view class="text-content">
              <!-- 语法点(正确率)： -->
              <view>语法点(正确率)：</view>
              <view>
                <template v-for="(item, index) in triallist.grammarList">
                  <view class="pb-20">{{ item.name | safe }} ({{ item.accuracyRate | safe }}%)</view>
                </template>
                <span v-if="triallist.grammarList && triallist.grammarList.length <= 0">--</span>
              </view>
            </view>

            <view class="text-content">
              <!-- 结业检测(正确率)： -->
              <view>结业检测(正确率)：</view>
              <view>
                <template v-for="(item, index) in triallist.graduationList">
                  <view class="pb-20">{{ item.name | safe }} ({{ item.accuracyRate | safe }}%)</view>
                </template>
                <span v-if="triallist.graduationList.length <= 0">--</span>
              </view>
            </view>
          </view>
          <view v-if="triallist.moduleType === 4">
            <view class="text-content">学习学时：{{ triallist.studyHour | safe }}个小时</view>
            <view class="text-content">课程类型：{{ triallist.listeningStatisticsDto.courseName | safe }}</view>
            <view class="text-content">课程名称：{{ listenName(triallist.listeningStatisticsDto.courseList) }}</view>
            <view class="text-content">学习进度：{{ listenProgress(triallist.listeningStatisticsDto.progressList) }}</view>
            <view class="text-content">听力名称（正确率)：{{ listenArray(triallist.listeningStatisticsDto.listeningList) }}</view>
          </view>
          <view v-if="triallist.moduleType !== 2 && triallist.moduleType !== 3 && triallist.moduleType !== 4">
            <view class="text-content">试学学时：{{ triallist.studyHour | safe }}小时</view>
            <view class="text-content">词汇测试水平：{{ triallist.vocabularyLevel | safe }}</view>
            <view class="text-content">首测词汇量：{{ triallist.expWords | safe }}个</view>
            <view class="text-content">识记词汇数量：{{ triallist.todayWords | safe }}个</view>
            <view class="text-content">遗忘数量：{{ triallist.forgetWords | safe }}个</view>
            <view class="text-content">记忆率：{{ triallist.wordsRate | safe }}%</view>
            <view class="text-content">体验词库：{{ triallist.studyBooks | safe }}</view>
            <view class="text-content" v-if="Feedback || isEdit" style="display: flex; line-height: 60rpx">
              记忆情况：
              <input type="text" class="borderInput" v-model="recall.minutes" maxlength="4" />
              分钟记住
              <input type="text" class="borderInput" v-model="recall.words" maxlength="4" />
              个单词
            </view>
            <view class="text-content" v-else style="display: flex; line-height: 60rpx">
              记忆情况： {{ triallist.memoryTime | safe }}分钟记住{{ triallist.memoryNum | safe }}个单词
            </view>
          </view>

          <!-- 体验后学习意愿 -->
          <view class="text-content" style="align-items: center">
            体验后学习意愿：
            <uni-data-select
              v-if="Feedback || isEdit"
              :disabled="!Feedback && !isEdit"
              :clear="false"
              v-model="Intention"
              :localdata="intendToStudy"
              @change="changeIntendTo"
              style="width: 165upx"
            ></uni-data-select>
            <text v-else>{{ triallist.studyIntention }}</text>
          </view>
        </view>

        <!-- 复习时间-->
        <view class="bg-ff radius-15 mt-30 pb-30" v-if="triallist.moduleType !== 3 && triallist.moduleType !== 4">
          <view class="f-30 mt-30 ml-30 mb-15" style="font-weight: 600">复习时间:</view>
          <view class="flex-s mr-20 ml-20 pb-35 border-bottom mt-35" v-for="(item, index) in reviewTimeArr">
            <view style="width: 500rpx" @click="choseReview(index)">
              <text class="c-99 f-30 ml-10">
                {{ item ? item : Feedback || isEdit ? '请选择时间' : '暂无时间' }}
              </text>
            </view>
            <view v-if="Feedback || isEdit">
              <uni-icons v-if="!item" @click="choseReview(index)" class="mr-10" type="right" color="#C7C7C7" size="20"></uni-icons>
              <uni-icons v-else @click="cleanReview(index)" class="mr-10" type="close" color="#C7C7C7" size="20"></uni-icons>
            </view>
          </view>
        </view>

        <view class="white-content marginTop30" style="height: 420rpx">
          <!-- 学员学习状况反馈 -->
          <view class="flex-view">
            <view class="text-content">学员学习状况反馈：</view>
            <view v-if="Feedback || isEdit" class="flex-view-text" @click="cleanFeedback()">清空</view>
          </view>
          <view class="marginTop10" style="max-height: 300upx">
            <u--textarea
              v-if="Feedback || isEdit"
              :disabled="!Feedback && !isEdit"
              class="feedback"
              v-model="feedback"
              count
              :value="feedback"
              placeholder="请输入"
              placeholderStyle="color:#b0b0b6"
              maxlength="200"
              height="140"
            ></u--textarea>
            <view class="font26 color_grey66 feedbackBox marginBottom10" v-else>
              {{ feedback }}
            </view>
          </view>
        </view>

        <!-- 按钮 -->
        <view class="button-sp-area marginTop40 flexs">
          <button v-if="!Feedback && !isEdit" class="mini-btn refresh" type="default" size="mini" @click="editOption()">修改</button>
          <button v-if="Feedback || isEdit" class="mini-btn background_green_two" type="default" size="mini" @click="addOrLook(id)">确定</button>
          <button v-else class="mini-btn background_green_two" type="default" size="mini" @click="shareJump">分享</button>
        </view>
      </view>
      <!-- 正式学员反馈 -->
      <view class="card" v-else>
        <view class="white-content" style="position: relative">
          <view @click="refresh(id)" class="refresh-view">
            <image class="width30" src="/static/images/shuaxin.png" mode=""></image>
            <view class="f-28" style="color: #2e896f; margin-left: 4rpx">刷新</view>
          </view>
          <view class="text-content" v-if="backlist.moduleType !== 4">课程类型：{{ backlist.curriculumName | safe }}</view>
          <view class="text-content">日期：{{ backlist.dateTime | safe }}</view>
          <view class="text-content">姓名：{{ backlist.studentName | safe }}</view>
          <view class="text-content">年级：{{ backlist.gradeName | safe }}</view>
          <view class="text-content">学员编号：{{ subject.studentCode | safe }}</view>
          <view class="text-content" v-if="backlist.moduleType !== 4">时间：{{ backlist.studyTime | safe }}</view>

          <!-- 实际时间 -->
          <view class="text-content reality" v-if="backlist.moduleType == 3 || backlist.moduleType == 4">
            实际时间：
            <view>
              <view class="begin" @click="isStartshow = true">{{ timelist.actualStart | safe }}</view>
              <w-picker
                v-if="isFeedback"
                :visible.sync="isStartshow"
                mode="date"
                :startYear="new Date(date).getFullYear()"
                endYear="2040"
                :value="timelist.actualStart"
                fields="minute"
                @confirm="onStartConfirm"
                :disabled-after="false"
                ref="dateTimeFlag"
              ></w-picker>
            </view>
            至
            <view>
              <view class="finish" @click="isEndShow = true">{{ timelist.actualEnd | safe }}</view>
              <w-picker
                v-if="isFeedback"
                :visible.sync="isEndShow"
                mode="date"
                :startYear="new Date(date).getFullYear()"
                endYear="2040"
                :value="timelist.actualEnd"
                fields="minute"
                @confirm="onEndConfirm"
                :disabled-after="false"
                ref="dateTimeFlag"
              ></w-picker>
            </view>
          </view>
          <!-- 实际时间 -->
          <!--          <view class="text-content reality">实际时间：
                    	<view><view class="begin">{{timelist.actualStart}}</view></view>
                    	<view><view class="finish">{{timelist.actualEnd}}</view></view>
                    </view> -->
          <view class="text-content">学习学时：{{ backlist.studyHour | safe }}个小时</view>
          <view v-if="backlist.moduleType === 3">
            <view class="text-content">所学课程类型：{{ backlist.superReadCourseStatistics.courseName | safe }}</view>
            <view class="text-content">所学课程名称：{{ mapArray(backlist.superReadCourseStatistics.courseList) }}</view>
            <view class="text-content">学习进度：{{ backlist.superReadCourseStatistics.learningProgress | safe }}%</view>
            <view class="text-content">学习关卡（正确率)：{{ rateMapArray(backlist.superReadCourseStatistics.checkpointList) }}</view>
          </view>
          <view v-if="backlist.moduleType === 2">
            <!-- 2222 -->
            <!-- <view class="text-content">时间：{{ backlist.studyTime }}</view> -->
            <view class="text-content">所学语法：{{ backlist.grammarName | safe }}</view>
            <view class="text-content">学习进度：{{ backlist.learningProgress ? backlist.learningProgress : 0 }}%</view>
            <view class="text-content">
              <view class="mr-20">复习知识点：{{ backlist.reviewKnowledgeNum | safe }}个</view>
              <view>新学知识点：{{ backlist.newKnowledgeNum | safe }}个</view>
            </view>
            <view class="text-content">语法点：{{ backlist.grammarNum | safe }}个</view>
            <view class="text-content">结业检测：{{ backlist.graduationNum | safe }}个</view>
            <view class="text-content">
              <!-- 复习知识点(正确率)：-- -->
              <view>复习知识点(正确率)：</view>
              <view>
                <template v-for="(item, index) in backlist.reviewKnowledgeList">
                  <view class="pb-20">{{ item.name || '--' }} ({{ item.accuracyRate || '--' }}%)</view>
                </template>
                <span v-if="backlist.reviewKnowledgeList.length <= 0">--</span>
              </view>
            </view>

            <view class="text-content">
              <!-- 新学知识点(正确率)： -->
              <view>新学知识点(正确率)：</view>
              <view>
                <template v-for="(item, index) in backlist.newKnowledgeList">
                  <view class="pb-20">{{ item.name || '--' }} ({{ item.accuracyRate || '--' }}%)</view>
                </template>
              </view>
              <span v-if="backlist.newKnowledgeList.length <= 0">--</span>
            </view>

            <view class="text-content">
              <!-- 语法点(正确率)： -->
              <view>语法点(正确率)：</view>
              <view>
                <template v-for="(item, index) in backlist.grammarList">
                  <view class="pb-20">{{ item.name | safe }} ({{ item.accuracyRate | safe }}%)</view>
                </template>
                <span v-if="backlist.grammarList && backlist.grammarList.length <= 0">：--</span>
              </view>
            </view>

            <view class="text-content">
              <!-- 结业检测(正确率)： -->
              <view>结业检测(正确率)：</view>
              <view>
                <template v-for="(item, index) in backlist.graduationList">
                  <view class="pb-20">{{ item.name | safe }} ({{ item.accuracyRate | safe }}%)</view>
                </template>
                <span v-if="backlist.graduationList && backlist.graduationList.length <= 0">--</span>
              </view>
            </view>
          </view>
          <view v-if="backlist.moduleType === 4">
            <view class="text-content">课程类型：{{ backlist.listeningStatisticsDto.courseName | safe }}</view>
            <view class="text-content">课程名称：{{ listenName(backlist.listeningStatisticsDto.courseList) }}</view>
            <view class="text-content">学习进度：{{ listenProgress(backlist.listeningStatisticsDto.progressList) }}</view>
            <view class="text-content">听力名称（正确率)：{{ listenArray(backlist.listeningStatisticsDto.listeningList) }}</view>
          </view>
          <view v-if="backlist.moduleType !== 2 && backlist.moduleType !== 3 && backlist.moduleType !== 4">
            <view class="text-content">已购鼎英语学时：{{ backlist.totalCourseHours | safe }}小时</view>
            <view class="text-content">剩余鼎英语学时：{{ backlist.leaveCourseHours | safe }}小时</view>
            <view class="text-content" v-if="backlist.isWord && backlist.studyBooks">
              所学词库：
              <view class="text_content_inner">
                {{ backlist.studyBooks && backlist.studyBooks.indexOf('、') !== -1 ? replaceDelimiter(backlist.studyBooks) : backlist.studyBooks }}
              </view>
            </view>

            <view class="displayFlex">
              <view class="text-content widthAHalf" v-if="backlist.isWord">复习词汇：{{ backlist.reviewWords | safe }}</view>
              <view class="text-content widthAHalf" v-if="backlist.isWord">复习遗忘词汇：{{ backlist.forgetWords }}</view>
            </view>

            <view class="text-content" v-if="backlist.isWord">复习遗忘率：{{ backlist.forgetRate }}%</view>

            <view class="displayFlex">
              <view class="text-content widthAHalf" v-if="backlist.isWord">学新词汇：{{ backlist.newWords | safe }}个</view>
              <view class="text-content widthAHalf" v-if="backlist.isWord">学新遗忘词汇：{{ backlist.newForget | safe | safe }}个</view>
            </view>

            <view class="text-content" v-if="backlist.isWord">学新遗忘率：{{ backlist.newForgetRate | safe }}%</view>

            <view class="text-content" v-if="backlist.isWord && backlist.learnSchedule">
              学习进度：

              <view class="text_content_inner">
                <!-- {{ backlist.learnSchedule&&backlist.learnSchedule.indexOf(',') !== -1 ? replaceDelimiter(backlist.learnSchedule) : backlist.learnSchedule }} -->
                {{ backlist.learnSchedule && backlist.learnSchedule.indexOf(',') !== -1 ? backlist.learnSchedule.replace(/,/g, '\n') : backlist.learnSchedule | safe }}
              </view>
            </view>

            <view class="text-content" v-if="backlist.isWord">
              今日共识记词汇
              <text class="color_grey font26">(复习遗忘词汇+学新词汇)</text>
              ：{{ backlist.todayWords | safe }}个
            </view>
            <view class="text-content" style="display: flex" v-if="backlist.isWord">
              学习效率：
              <input type="number" class="borderInput" v-model="backlist.studyRate" :disabled="!isFeedback" maxlength="3" />
            </view>
          </view>
        </view>

        <view class="white-content marginTop30" style="height: 420rpx">
          <view class="flex-view">
            <view class="text-content">教练评语：</view>
            <view v-if="isFeedback || isEdit" class="flex-view-text" @click="cleanFeedback()">清空</view>
          </view>
          <!-- 反馈 -->
          <view style="max-height: 300upx">
            <u--textarea
              v-if="isFeedback || isEdit"
              :disabled="!isFeedback && !isEdit"
              class="feedback"
              v-model="feedback"
              :value="feedback"
              placeholder="请输入"
              count
              placeholderStyle="color:#b0b0b6"
              height="165"
              maxlength="200"
            ></u--textarea>
            <view v-else class="font26 color_black33 feedbackBoxNew marginBottom10">
              {{ feedback | safe }}
            </view>
          </view>
        </view>
        <view class="button-sp-area marginTop40 flexs">
          <button v-if="!isFeedback && !isEdit" class="mini-btn refresh" type="default" size="mini" @click="editOption()">修改</button>
          <button v-if="isFeedback || isEdit" class="mini-btn background_green_two" type="default" size="mini" @click="addOrLook(id)">确定</button>
          <button v-else class="mini-btn background_green_two" type="default" size="mini" @click="shareJump">分享</button>
        </view>
      </view>
    </view>

    <uni-popup ref="choseReviewDate" type="bottom" @maskClick="cancelChoseReview()">
      <view class="m-20 ptb-20 bg-ff radius-12" style="height: 548rpx">
        <view class="mt-20 t-c mb-20" style="border-bottom: 1rpx solid rgba(153, 153, 153, 0.3); height: 80rpx; display: flex; justify-content: space-between">
          <view class="ml-30 c-99 f-32" @click="cancelChoseReview()">取消</view>
          <view class="mr-30 c-2e8 f-32" @click="confirmChoseReview()">确定</view>
        </view>
        <long-date ref="longDate" chooseNum="30" @select="seletReviewTime"></long-date>
      </view>
    </uni-popup>

    <uni-popup ref="loadingPopup" type="center" :is-mask-click="false">
      <view class="bg-ff radius-15 t-c">
        <view class="loadingpadding">
          <image src="https://document.dxznjy.com/Assistant/loading.png" class="loadingImg"></image>
          <view class="mt-30">加载中...</view>
          <view>请耐心等候</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>
<script>
  import dateTime from '@/components/lanxiujuan-dyDateTime/lanxiujuan-dyDateTime.vue';
  import dayjs from 'dayjs';
  import longDate from '@/components/long-date/long-date.vue';
  export default {
    name: 'StudyDetails',
    components: {
      dateTime,
      longDate
    },
    filters: {
      safe(value) {
        return value == null ? '' : value;
      }
    },
    data() {
      return {
        // 实际时间
        isStartshow: false,
        isEndShow: false,

        backlist: '', // 获取反馈详情列表
        subject: '', // 课程列表详情
        timelist: {
          actualStart: '',
          actualEnd: ''
        },
        triallist: '', // 试课反馈详情

        isFeedback: false, // 是否显示反馈详情确定按钮
        //反馈详情列表
        trialclass: false, // 是否是试课反馈
        Feedback: false, // 是否显示试课反馈详情确定按钮

        feedback: '', // 弹出层文本框输入的内容

        Intention: '', // 学习意愿
        recall: {
          // 记忆情况
          minutes: '',
          words: ''
        },
        //试课反馈记忆特点下拉框
        range: [
          {
            value: 1,
            text: '弱'
          },
          {
            value: 2,
            text: '正常'
          },
          {
            value: 3,
            text: '强'
          }
        ],
        intendToStudy: [
          //学习意愿
          {
            value: '0',
            text: '愿意'
          },
          {
            value: '1',
            text: '不愿意'
          }
        ],
        dates: '',
        flag: false, // 防止多次请求

        isEdit: false, //是否修改

        //复习时间
        showReviewTimeDialog: false,
        reviewTimeArr: ['', '', ''],
        curChoseReviewItem: '',
        curChoseReviewIndex: -1
      };
    },

    onLoad(e) {
      console.log('[Shine] study_feedback onload e:', e);
      let that = this;
      this.dates = e.dates;
      console.log(decodeURIComponent(e.sendData), '1111111111111111111111111111111111111111111111111111111');
      let sendData = JSON.parse(decodeURIComponent(e.sendData));
      console.log(sendData, '222222222222222222222222222222222222222222222222222');
      if (sendData != null && sendData != undefined) {
        console.log('sendData.trialclass222222222222222222222222222222', sendData.trialclass);
        this.trialclass = sendData.trialclass;
        this.isFeedback = sendData.isFeedback;
        this.triallist = sendData.triallist;
        this.subject = sendData.subject;
        this.backlist = sendData.backlist;
        // this.getDetail();
        console.log(this.trialclass, '333333333333333333333333333333333333333333333');
        if (this.trialclass) {
          if (this.triallist != null && this.triallist != undefined) {
            // that.selExperienceMemoryRecord();
            this.Feedback = this.triallist.feedback === '' || this.triallist.feedback == null || this.triallist.feedback == undefined;
            this.feedback = this.triallist.feedback;
            this.Intention = this.triallist.studyIntention;
            this.timelist.actualStart = this.triallist.actualStart;
            this.timelist.actualEnd = this.triallist.actualEnd;
            this.reviewTimeArr = this.getNormalReviewTime(this.triallist.reviewDateList);
          }
        } else {
          if (this.backlist != null && this.backlist != undefined) {
            this.feedback = this.backlist.feedback;
            this.isFeedback = this.backlist.feedback === '' || this.backlist.feedback == null || this.backlist.feedback == undefined;
            this.timelist.actualStart = this.backlist.actualStart;
            this.timelist.actualEnd = this.backlist.actualEnd;
          }
          // 学习效率
          if (this.isFeedback) {
            this.backlist.studyRate = '';
          }
        }

        // setTimeout(function () {
        //   that.$refs.chanelTimetry.setLists(that.timelist);
        // }, 500);
      }
    },
    onReady() {},

    methods: {
      // replaceDelimiter(str, delimiter = '、', replacement = '\n') {
      //   return str ? str.replace(new RegExp(delimiter, 'g'), replacement) : '';
      // },
      escapeRegExp(str) {
        return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      },

      replaceDelimiter(str, delimiter = '、', replacement = '\n') {
        if (!str) return '';
        const safeDelimiter = this.escapeRegExp(delimiter);
        return str.replace(new RegExp(safeDelimiter, 'g'), replacement);
      },
      rateMapArray(arr) {
        if (arr && arr.length > 0) {
          let newArr = [];
          newArr = arr.map((i) => {
            let str = '';
            let accuracyRate = i.accuracyRate ? i.accuracyRate + '%' : '-';
            let checkpointName = i.checkpointName;
            str = `${checkpointName}(${accuracyRate})`;
            return str;
          });
          return newArr.join(',');
        } else {
          return '-';
        }
      },
      mapArray(arr) {
        if (arr && arr.length > 0) {
          return arr.map((i) => i.courseName + '（' + i.checkpointNum + '）').join(',');
        } else {
          return '-';
        }
      },
      changeMin(val) {
        console.log(val.target.value);
        let value = val.target.value;
        const regex = /^[1-9]\d*$/;
        // 如果输入值不符合正则表达式，则设置为上一个合法的值
        if (!regex.test(value)) {
          uni.showToast({
            title: '请输入正整数的分钟数',
            icon: 'none',
            duration: 3000
          });
          this.recall.minutes = '';
        } else {
          this.recall.minutes = value;
        }
      },
      getNormalReviewTime(arr) {
        if (!arr) {
          arr = [];
        }
        if (arr.length >= 3) {
          return arr;
        }
        for (let i = 0; i < 3; i++) {
          if (!arr[i]) {
            arr.push('');
          }
        }
        return arr;
      },
      //听力数据处理
      listenProgress(progressList) {
        return progressList.map((item) => `${parseFloat(item.learningProgress)}%`).join(' , ');
      },
      listenName(progressList) {
        return progressList.map((item) => item.courseName).join(' , ');
      },
      listenArray(arr) {
        if (arr && arr.length > 0) {
          return arr.map((i) => i.listeningName + '（' + i.accuracyRate + '%）').join(', ');
        } else {
          return '-';
        }
      },
      choseReview(index) {
        if (!this.Feedback && !this.isEdit) {
          return;
        }
        this.showReviewTimeDialog = true;
        this.curChoseReviewIndex = index;
        this.$refs.choseReviewDate.open();
      },
      seletReviewTime(e) {
        console.log(e);
        console.log('--------=========================================');
        this.curChoseReviewItem = e.time;
      },
      cleanReview(index) {
        this.reviewTimeArr.splice(index, 1, '');
      },
      cancelChoseReview() {
        this.showReviewTimeDialog = false;
        this.$refs.choseReviewDate.close();
      },
      getSameDate() {
        for (let i = 0; i < this.reviewTimeArr.length; i++) {
          if (i == this.curChoseReviewIndex) {
            continue;
          }
          if (dayjs(this.reviewTimeArr[i]).format('YYYY-MM-DD') == dayjs(this.curChoseReviewItem).format('YYYY-MM-DD')) {
            return true;
          }
        }
        return false;
      },
      confirmChoseReview() {
        console.log(this.reviewTimeArr);
        console.log('---------------------------------------------------');
        if (this.reviewTimeArr.indexOf(this.curChoseReviewItem) != -1) {
          uni.showToast({
            icon: 'none',
            title: '请选择不一样的时间',
            duration: 2000
          });
          return;
        }
        if (this.getSameDate(this.curChoseReviewIndex)) {
          uni.showToast({
            icon: 'none',
            title: '复习时间不能选择同一天',
            duration: 2000
          });
          return;
        }
        this.reviewTimeArr[this.curChoseReviewIndex] = this.curChoseReviewItem;
        this.showReviewTimeDialog = false;
        this.$refs.choseReviewDate.close();
      },
      //获取多少分钟多少词 注释
      async selExperienceMemoryRecord() {
        let that = this;
        let res = await uni.$http.get('/deliver/app/teacher/selExperienceMemoryRecord', {
          endTime: that.triallist.actualEnd + ':59',
          startTime: that.triallist.actualStart + ':00',
          studentCode: that.triallist.studentCode,
          merchantCode: that.triallist.merchantCode
        });
        if (res.data) {
          // that.recall.minutes = res.data.data.memoryTime
          // that.recall.words = res.data.data.memoryNum
        }
      },
      //清空
      cleanFeedback() {
        this.feedback = '';
      },
      //修改按钮
      editOption() {
        this.recall.minutes = this.triallist.memoryTime;
        this.recall.words = this.triallist.memoryNum;
        this.Intention = this.getIntentionIndex(this.triallist.studyIntention);
        this.isEdit = true;
      },
      getIntentionIndex(date) {
        for (let i = 0; i < this.intendToStudy.length; i++) {
          if (this.intendToStudy[i].text == date) {
            return this.intendToStudy[i].value;
          }
        }
        return '0';
      },
      // 没有学习记录打开反馈
      changenum(val) {
        console.log(val, '1111111111');
        this.timelist.actualStart = val;
        if (this.timelist.actualStart != '' && this.timelist.actualEnd != '') {
          // this.backData();
          this.getDetail();
        }
      },
      changetime(time) {
        //debugger
        this.timelist.actualEnd = time;
        if (this.timelist.actualStart != '' && this.timelist.actualEnd != '') {
          this.getDetail();
        }
      },

      // 获取试课详情
      getDetail() {
        if (this.trialclass) {
          //试课
          this.trialData(this.subject);
        } else {
          //学习反馈
          this.backData();
        }
      },

      // 实际开始时间弹窗确定
      async onStartConfirm(val) {
        let time = await dayjs(val.value).format('YYYY-MM-DD HH:mm');
        console.log('confirm', time);
        this.timelist.actualStart = time;
        this.isStartshow = false;
        if (this.timelist.actualStart != '' && this.timelist.actualEnd != '') {
          this.backData();
        }
      },
      // 实际结束时间弹窗确定
      async onEndConfirm(val) {
        let time = await dayjs(val.value).format('YYYY-MM-DD HH:mm');
        this.timelist.actualEnd = time;
        this.isEndShow = false;
        if (this.timelist.actualStart != '' && this.timelist.actualEnd != '') {
          this.backData();
        }
      },

      // 确定按钮（关闭弹框）
      async refresh() {
        let that = this;
        if (that.flag) return;
        that.flag = true;
        this.$refs.loadingPopup.open();
        try {
          let res = await uni.$http.get('/deliver/app/teacher/refreshFeedbackInfo', {
            id: this.subject.id,
            actualStart: this.timelist.actualStart,
            actualEnd: this.timelist.actualEnd,
            type: 1
          });
          this.$refs.loadingPopup.close();
          setTimeout(() => {
            that.flag = false;
          }, 60000);
          // if (res.data.code == 70001) {}
          if (res.data.success) {
            that.backlist = res.data.data;
            that.isFeedback = that.backlist.feedback === '' || that.backlist.feedback == null || that.backlist.feedback == undefined;
            that.feedback = that.backlist.feedback;
            that.timelist.actualStart = that.backlist.actualStart;
            that.timelist.actualEnd = that.backlist.actualEnd;
          }
          that.$refs.chanelTime.setLists(that.timelist);
        } catch {
          this.$refs.loadingPopup.close();
        }
      },

      // 确定按钮（关闭弹框）
      addOrLook() {
        if (this.isEdit) {
          this.editFeedback();
          return;
        }
        if (this.subject.experience == false) {
          console.log(this.trialclass);
          this.addbackData(false); // 新增反馈  // experience = true 是试课反馈
        } else {
          this.addtrialData(); // 新增试课反馈  // experience = false 是学习反馈
        }
      },

      getValidReviewArr() {
        let dateArr = [];
        for (var i = 0; i < this.reviewTimeArr.length; i++) {
          if (this.reviewTimeArr[i]) {
            dateArr.push(this.reviewTimeArr[i]);
          }
        }
        console.log(this.reviewTimeArr);
        console.log(dateArr);
        return dateArr;
      },

      async editFeedback() {
        let that = this;
        that.$refs.loadingPopup.open();
        let postUrl = {};
        if (!this.trialclass) {
          postUrl = {
            feedback: that.feedback,
            studyId: that.subject.id,
            studyIntention: '',
            memoryNum: '',
            memoryTime: '',
            reviewDateList: null
          };
        } else {
          if (that.triallist.moduleType != 3 && that.triallist.moduleType != 2 && that.triallist.moduleType != 4) {
            if (that.recall.minutes == '' || that.recall.words == '') {
              that.$util.alter('记忆特点字段未填写');
              return;
            }
          }
          if (that.Intention == '') {
            that.$util.alter('体验后学习意愿未填写');
            return;
          }
          let reviewArr = this.getValidReviewArr();
          let intention = that.intendToStudy[that.Intention].text;
          postUrl = {
            feedback: that.feedback,
            studyId: that.subject.id,
            studyIntention: intention,
            memoryNum: that.recall.words,
            memoryTime: that.recall.minutes,
            reviewDateList: reviewArr
          };
        }
        try {
          let res = await uni.$http.post('/deliver/app/teacher/updateFeedback', postUrl);
          console.log(res);
          that.$refs.loadingPopup.close();
          if (res.data.success) {
            uni.showToast({
              title: '修改反馈成功',
              duration: 1000
            });
            that.isEdit = false;
            that.getDetail();
          }
        } catch {
          that.$refs.loadingPopup.close();
        }
      },

      // 试课选择学习意愿
      changeIntendTo(e) {
        if (this.intendToStudy[e]) {
          this.Intention = this.intendToStudy[e].value;
        }
      },

      // 新增学习反馈
      async addbackData(isConfirm) {
        let that = this;
        if (that.backlist.moduleType === 1) {
          if (that.backlist.isWord && that.backlist.studyRate == '') {
            that.$util.alter('学习效率字段未填写');
            return;
          }
        }
        that.$refs.loadingPopup.open();
        let postUrl = `/deliver/app/teacher/addFeedback?actualStart=${that.timelist.actualStart}&actualEnd=${that.timelist.actualEnd}&feedBack=${encodeURI(
          encodeURI(that.feedback)
        )}&id=${that.subject.id}&type=1&studyRate=${that.backlist.studyRate}`;
        // let data = {
        //   actualStart: that.timelist.actualStart,
        //   actualEnd: that.timelist.actualEnd,
        //   feedBack: that.feedback,
        //   id: that.subject.id,
        //   type: 1,
        //   studyRate: that.backlist.studyRate
        // };
        if (isConfirm) {
          postUrl = postUrl + '&confirm=true';
          // data.confirm = true;
        }
        try {
          let res = await uni.$http.post(postUrl);
          // let res = await that.$http.post('/deliver/app/teacher/addFeedback', data);
          that.$refs.loadingPopup.close();
          if (res.data.code == 40047) {
            uni.showModal({
              title: '提示',
              content: res.data.message,
              success: function (res) {
                if (res.confirm) {
                  that.addbackData(true);
                } else if (res.cancel) {
                  console.log('用户点击取消');
                }
              }
            });
            return;
          }
          // 成功提示
          if (res.data.success) {
            uni.showToast({
              title: '反馈成功',
              duration: 1000
            });
            that.backlist.feedback = that.feedback;
            // that.recall = ''
            // that.feedback = '';
            // that.timelist.actualStart=''
            // that.timelist.actualEnd=''
            //显示分享按钮
            that.isFeedback = false;
            that.getDetail();
            // that.$refs.popup.close()
          }
        } catch {
          that.$refs.loadingPopup.close();
        }
      },

      // 新增试课反馈
      async addtrialData() {
        let that = this;
        if (that.triallist.moduleType != 3 && that.triallist.moduleType != 2 && that.triallist.moduleType != 4) {
          if (that.recall.minutes == '' || that.recall.words == '') {
            that.$util.alter('记忆特点字段未填写');
            return;
          }
        }
        if (that.Intention == '') {
          that.$util.alter('体验后学习意愿未填写');
          return;
        }
        that.$refs.loadingPopup.open();
        let reviewArr = this.getValidReviewArr();
        let intention = that.intendToStudy[that.Intention].text;
        let memoryTime = '';
        let memoryNum = '';
        if (that.triallist.moduleType == 3 || that.triallist.moduleType == 2 || that.triallist.moduleType == 4) {
          memoryTime = 0;
          memoryNum = 0;
        } else {
          memoryTime = that.recall.minutes;
          memoryNum = that.recall.words;
        }
        let data = {
          feedback: that.feedback,
          memoryTime: memoryTime,
          memoryNum: memoryNum,
          studyId: that.subject.id,
          studyIntention: intention,
          actualStart: that.timelist.actualStart,
          actualEnd: that.timelist.actualEnd,
          reviewDateList: reviewArr
        };
        let postUrl = `/deliver/app/teacher/addExperienceFeedback`;
        try {
          let res = await uni.$http.post(postUrl, data);
          console.log(res);
          that.$refs.loadingPopup.close();
          if (res.data.success) {
            uni.showToast({
              title: '反馈成功',
              duration: 1000
            });
            //显示分享按钮
            that.Feedback = false;
            that.getDetail();
          }
        } catch {
          that.$refs.loadingPopup.close();
        }
      },

      //分享
      shareJump() {
        let sendData = {};
        sendData.trialclass = this.trialclass;
        sendData.isStudy = true;
        if (this.trialclass) {
          this.triallist.reviewTimeArr = this.reviewTimeArr;
          sendData.detailsData = this.triallist;
        } else {
          sendData.detailsData = this.backlist;
        }
        let newstr = '';
        // #ifdef MP-WEIXIN
        newstr = JSON.stringify(sendData);
        // #endif
        // #ifdef APP-PLUS
        newstr = JSON.stringify(sendData).replace(/%/g, '%25');
        // #endif
        uni.navigateTo({
          url: '/share/share_feedback?sendData=' + encodeURIComponent(newstr)
        });
      },

      Back() {
        uni.$emit('onCalendarRefresh', this.dates);
        uni.navigateBack();
      },

      // 获取反馈详情
      async backData() {
        let that = this;
        let res = await uni.$http.get('/deliver/app/teacher/getFeedbackInfo', {
          id: this.subject.id,
          actualStart: this.timelist.actualStart,
          actualEnd: this.timelist.actualEnd,
          type: 1
        });
        console.log('获取反馈详情', res.data.data);
        // if (res.data.code == 70001) {}

        if (res.data.success) {
          that.backlist = res.data.data;
          that.isFeedback = that.backlist.feedback === '' || that.backlist.feedback == null || that.backlist.feedback == undefined;
          that.feedback = that.backlist.feedback;
          that.timelist.actualStart = that.backlist.actualStart;
          that.timelist.actualEnd = that.backlist.actualEnd;
        }
        // that.$refs.chanelTime.setLists(that.timelist);
      },

      // 试课反馈详情
      async trialData(item) {
        let that = this;
        let res = await uni.$http.post(`/deliver/app/teacher/getExperienceInfo/${that.subject.id}?actualStart=${that.timelist.actualStart}&actualEnd=${that.timelist.actualEnd}`);
        that.triallist = res.data.data;
        if (res.data.code == 70001) {
          // that.$refs.chanelTime.setLists(that.timelist);
          return;
        }
        if (res.data.success) {
          that.Feedback = that.triallist.feedback === '' || that.triallist.feedback == null || that.triallist.feedback == undefined;
          that.feedback = that.triallist.feedback;
          that.Intention = that.triallist.studyIntention;
          that.timelist.actualStart = that.triallist.actualStart;
          that.timelist.actualEnd = that.triallist.actualEnd;
          setTimeout(function () {
            that.$refs.chanelTimetry.setLists(that.timelist);
          }, 500);
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .nav-title {
    background-color: #f3f8fc;
    position: fixed;
    height: 150rpx;
    width: 100%;
    z-index: 999;
  }

  .status_bar {
    text-align: center;
    font-size: 40rpx;
    color: #000;
    line-height: 220rpx;
  }

  .icon_img {
    position: absolute;
    left: 10rpx;
  }

  .container {
    background-color: #f3f8fc;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 170rpx;
    padding-bottom: 70rpx;
  }

  .page {
    width: 100%;
  }

  .details {
    display: flex;
    font-size: 25rpx;
    padding: 0 30rpx 50rpx;
    margin-top: 20rpx;

    /deep/.u-row {
      margin-bottom: 20rpx !important;
    }

    /deep/.u-demo-block__content {
      width: 98% !important;
      margin-left: 5rpx;
    }

    /deep/.u-col {
      padding: 0 !important;
    }
  }

  .left {
    font-size: 26rpx;
    align-items: center;
  }

  .right {
    position: relative;
    padding: 20rpx 20rpx 40rpx;
    border-radius: 30rpx;
    background-color: #fff;
  }

  .pic {
    position: absolute;
    left: 300rpx;
    top: 30rpx;
    height: 50rpx;
    width: 50rpx;
  }

  .rightbtn {
    float: right;
    position: absolute;
    top: 40rpx;
    right: 40rpx;
  }

  .icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 10rpx;
  }

  .right-content {
    margin-top: 10rpx;
  }

  .tickling {
    text-align: center;
    font-weight: 700;
    font-size: 30rpx;
  }

  .text-content {
    font-size: 28rpx;
    color: #000;
    margin-bottom: 30rpx;
    display: flex;
  }

  .widthAHalf {
    width: 50%;
  }

  .text_content_inner {
    width: 450rpx;
    white-space: pre-line;
    line-height: 45upx;
  }

  /deep/.u-textarea__field {
    background-color: #f7f7f7;
    border: 1rpx solid #d9d9d9;
    padding: 10rpx;
  }

  /deep/.u-textarea--disabled {
    background-color: #fff !important;
  }

  .button-sp-area {
    display: flex;
  }

  .cancel-btn {
    font-size: 24rpx;
    width: 120rpx;
    height: 55rpx;
    margin-bottom: 30rpx;
  }

  .refresh {
    margin-right: 30rpx;
    color: #2e896f !important;
    border: 1px solid #2e896f;
    overflow: visible;
  }

  .refresh-view {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    right: 30rpx;
    top: 40rpx;
  }

  .mini-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 45rpx;
    font-size: 30rpx;
    color: #fff;
    width: 690rpx;
    height: 90rpx;
    line-height: 90rpx;
  }

  .border-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 30rpx;
    font-size: 24rpx;
    color: #1a8eff;
    background-color: #fff;
    border: 1.4rpx solid #1a8eff;
    width: 160rpx;
    height: 60rpx;
    transform: scale(0.995);
    /* 解决ios上圆角失效 */
  }

  // 弹框的样式设置
  /deep/.card {
    width: 680rpx;

    .white-content {
      background-color: #fff;
      border-radius: 14rpx;
      padding: 40rpx 30rpx 40rpx 30rpx;
      // overflow-y: auto;
    }

    .reality {
      display: flex;
      align-items: center;
      margin-top: 10rpx;
      line-height: 40rpx;
      font-size: 26rpx;
    }

    .begin {
      border: 1px solid #999;
      border-radius: 10rpx;
      height: 40rpx;
      line-height: 40rpx;
      width: 220rpx;
      text-align: center;
      margin-right: 6upx;
      font-size: 24rpx;
      overflow: visible;
      transform: scale(0.995);
      /* 解决ios上圆角失效 */
    }

    .finish {
      border: 1px solid #999;
      overflow: visible;
      margin-left: 6upx;
      border-radius: 10rpx;
      height: 40rpx;
      line-height: 40rpx;
      width: 220rpx;
      text-align: center;
      font-size: 24rpx;
      transform: scale(0.995);
      /* 解决ios上圆角失效 */
    }
  }

  .flexs {
    display: flex;
    align-items: center;
  }

  .borderInput {
    width: 100upx;
    border-bottom: 1upx solid #b1b1b1;
    text-align: center;
  }

  .border-bottom {
    border-bottom: 1rpx solid #efefef;
  }

  .loadingImg {
    width: 120rpx;
    height: 120rpx;
  }

  .loadingpadding {
    padding: 85rpx 250rpx;
  }

  .flex-view {
    display: flex;
    justify-content: space-between;
  }

  .flex-view-text {
    font-size: 28rpx;
    color: rgb(46, 137, 111);
  }
</style>

<template>
  <view class="bg-ff" v-if="showFalse">
    <view class="bg-h">
      <view class="positioning" @click="goback">
        <!-- <uni-icons type="left" size="24" color="#000"></uni-icons> -->
        <image lazy-load src="/static/images/left-icon.png" style="width:32rpx; height:32rpx;display:block;" mode=""></image>
      </view>
      <view class="word-position t-c col-12 f-34" style="">
        <!-- <view  class="f-34">复习报告{{reportList.planReviewVo.reviewTime.split(' ')[0]?reportList.planReviewVo.reviewTime.split(' ')[0]:''}}</view> -->
        <template v-if="
            reportList && reportList.planReviewVo && reportList.planReviewVo.reviewTime
          ">
          复习报告 {{ reportList.planReviewVo.reviewTime.split(" ")[0] }}
        </template>
        <template v-else> 复习报告 信息不可用 </template>
      </view>
    </view>
    <view class="bg-ff mt-180">
      <view class="binggo_c">
        <view class="top_txt">
          学生姓名：{{
            reportList.planReviewVo.studentName
              ? reportList.planReviewVo.studentName
              : ""
          }}<br />
          学生编号：{{
            reportList.planReviewVo.studentCode ? reportList.planReviewVo.studentCode : ""
          }}
        </view>
        <view class="top_mid">
          <view class="head_t">
            <image lazy-load class="head_txt" style="height: 44rpx;" src="https://document.dxznjy.com/course/e250ad45e4df4bd5a6235ea622f0d1f3.png" mode="aspectFill"></image>
            <view class="bor-line"> </view>
          </view>
          <view class="flex-s">
            <view class="top_box" style="height: 226rpx;">
              <view class="bold green"> 需复习 </view>
              <view class="bot_box f-36">
                <span class="f-60 bold">{{
                  reportList.planReviewVo.needReviewNum
                    ? reportList.planReviewVo.needReviewNum
                    : ""
                }}</span>
                节
              </view>
            </view>
            <view class="top_box top_box2" style="height: 226rpx;">
              <view class="bold green"> 已复习 </view>
              <view class="bot_box f-36">
                <span class="f-60 bold">{{
                  reportList.planReviewVo.reviewedNum
                    ? reportList.planReviewVo.reviewedNum
                    : ""
                }}</span>
                节
              </view>
            </view>
            <view class="top_box top_box3" style="height: 226rpx;">
              <view class="bold green"> 未复习 </view>
              <view class="bot_box f-36">
                <span class="f-60 bold">{{
                  reportList.planReviewVo.notReviewNum
                    ? reportList.planReviewVo.notReviewNum
                    : ""
                }}</span>
                节
              </view>
            </view>
          </view>
        </view>
        <view class="head_t">
          <!-- <view class="head_txt bold"> 正确率 </view> -->
          <image class="head_txt w-110" style="height: 44rpx;" lazy-load src="https://document.dxznjy.com/course/48220ada96aa4ffaab35043200832c5f.png" mode="aspectFill"></image>
          <view class="box_zql">
            <view class="zql_t">
              元辅音<text class="bold">{{ reportList.planReviewVo.consonantAccuracyRate != null ? reportList.planReviewVo.consonantAccuracyRate : '0' }}%</text>
            </view>
            <view class="progress-box">
              <progress :percent="pgList[0]" activeColor="#36957A" stroke-width="16" border-radius="20" />
            </view>
          </view>
          <view class="box_zql">
            <view class="zql_t">
              单词
              <text class="bold">
                {{ reportList.planReviewVo.wordsAccuracyRate != null ? reportList.planReviewVo.wordsAccuracyRate : '0' }}%</text>
            </view>
            <view class="progress-box">
              <progress :percent="pgList[1]" activeColor="#B5EAA9" stroke-width="16" border-radius="20" />
            </view>
          </view>
        </view>
      </view>
      <view class="h_view"> </view>
      <view class="cthg_c">
        <view class="head_t">
          <image class="head_txt" style="height: 44rpx;" lazy-load src="https://document.dxznjy.com/course/dcbf952e588b40699685344a2a54bde2.png" mode="widthFix"></image>
        </view>
        <view class="">
          <view class="green bold"> 元辅音 </view>
          <view class="cthg_t">
            <view class="ct_box" v-for="(item, index) in reportList.syllableVos" :key="index" v-if="item.knowType == 0">
              {{ item.word }}
            </view>
          </view>
        </view>
        <view class="mb-150">
          <view class="green bold"> 单词 </view>
          <view class="flex-dir-row" v-for="(item, index) in reportList.detailsVos" :key="index" v-if="item.knowType == 0">
            <view class="ct_box dc" v-if="item.wordSplitList.length === 0">
              {{ item.word }}
            </view>
            <view class="ct_box dc" v-else v-for="(char, i) in item.wordSplitList" :key="i">
              <text v-if="char.wordSyllableType == 3">'</text>
              <text v-if="char.wordSyllable">{{ char.wordSyllable }}</text>
            </view>
          </view>
        </view>
      </view>
      <!-- <view class="height"></view> -->
      <view class="botBtn plr-30" v-if="!isShare">
        <!-- <view class="btn_b b_r  f-34" @click="createOrder"> 分享链接给家长 </view> -->
        <!-- #ifdef MP-WEIXIN -->
        <!-- <button class="btn_b b_r  f-34" hover-class="none" open-type="share">
          分享链接给家长
        </button> -->
        <!-- #endif -->
        <!-- #ifdef APP-PLUS -->
        <!-- <button class="btn_b b_r  f-34" hover-class="none" @click="linkShareApp">
          分享链接给家长
        </button> -->
        <!-- #endif -->
        <view class="btn_b b_l f-34" @click="goPast"> 往期复习 </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      pgList: [0, 0],
      planReviewId: null,
      reportList: [],
      showFalse: false,
      isShare: false, //分享链接参数
      toIndex: 0,
    };
  },
  onShareAppMessage() {
    return {
      title: "复习报告",
      imageUrl: "", //分享封面
      path:
        "/PYFpages/reviewReport?planReviewId=" +
        this.planReviewId +
        "&isShare=true&isType=1",
    };
  },
  onLoad(options) {
    this.planReviewId = options.planReviewId;
    this.toIndex = options.toIndex;
    // this.planReviewId='1279082997728825344'
    this.isShare = options.isShare;
    // console.log("isShare", this.isShare);
    // console.log("toIndex", this.toIndex);
    this.loadReport();
  },
  methods: {
    linkShareApp() {
      let shareInfo = {
        title: '复习报告',
        imageUrl: '', //分享封面
        path:
          "/PYFpages/reviewReport?planReviewId=" +
          this.planReviewId +
          "&isShare=true&isType=1",
      };
      uni.$appShare(shareInfo, 2);
      plus.runtime.quit();
    },
    async loadReport() {
      // 显示加载中的提示框
      uni.showLoading({
        title: "加载中...",
        mask: true,
      });

      this.$http
        .get("/znyy/pd/planReviewDetails/getReviewReport?planReviewId=" + this.planReviewId)
        .then((res) => {
          this.reportList = res.data.data;
          this.showFalse = true
          if (this.reportList && this.reportList.planReviewVo) {
            this.pgList = [
              this.reportList.planReviewVo.consonantAccuracyRate || 0,
              this.reportList.planReviewVo.wordsAccuracyRate || 0,
            ];
          }
        })
        .catch((err) => {
          console.error("加载失败", err);
        })
        .finally(() => {
          // 隐藏加载中的提示框
          uni.hideLoading();
        });
    },
    splitItem(item) {
      return item.split("+");
    },
    goPast() {
      if (this.toIndex == 1) {
        uni.navigateBack();
      } else {
        uni.redirectTo({
          url: "/PYFpages/pastReview",
        });
      }
    },
    goback() {
      if (this.toIndex == 1) {
        uni.navigateBack();
      } else {
        uni.navigateTo({
          url: "/PYFpages/todayReview",
        });
      }
    },
  },
};
</script>

<style scoped>
.mb-150 {
  margin-bottom: 150rpx;
}
.bg-h {
  position: fixed; /* 确保背景颜色显示 */
  top: 0; /* 固定在页面顶部 */
  left: 0;
  width: 100%; /* 使背景颜色覆盖整个宽度 */
  z-index: 8; /* 高于其他内容 */
}
.positioning {
  position: fixed;
  top: 110rpx;
  left: 30rpx;
  z-index: 9;
}

.word-position {
  position: fixed;
  top: 0;
  left: 0;
  background-color: #f3f8fc;
  height: 190rpx;
  padding-top: 110rpx;
  box-sizing: border-box;
}
.mt-180 {
  margin-top: 180rpx;
}
.binggo_c {
  height: 934rpx;
  background: url('https://document.dxznjy.com/course/ccce5573819e4e868dfdffb3ff24fef1.png') no-repeat;
  background-size: 100% 100%;
  font-size: 30rpx;
  color: #555555;
  margin-top: 10rpx;
  padding: 45rpx 32rpx;
  box-sizing: border-box;
}
.green {
  color: #349479;
  margin-bottom: 24rpx;
}
.top_txt {
  color: #fff;
  font-weight: 600;
  height: 222rpx;
  line-height: 80rpx;
}
.top_mid {
  height: 324rpx;
}
.head_t {
  margin-bottom: 28rpx;
  color: #333333;
}

.head_txt {
  width: 152rpx;
  height: 44rpx;
  will-change: transform; /* 提前告知浏览器会变化 */
  /* border-bottom: 10rpx solid #66bfa6;
  border-radius: 15rpx;
  display: inline-block; */
}
.bor-line {
}
.box_zql {
  width: 686rpx;
  height: 136rpx;
  padding: 22rpx 24rpx;
  box-sizing: border-box;
}
.zql_t {
  margin-bottom: 16rpx;
}
.top_box {
  width: 238rpx;
  height: 226rpx;
  background: url('https://document.dxznjy.com/course/6a9aa3225d3344dba1d2757910f06106.png') no-repeat;
  background-size: 100% 100%;
  padding: 34rpx;
  box-sizing: border-box;
}
.top_box2 {
  background: url('https://document.dxznjy.com/course/b92753fa23f44ea7b8c365ea3590b2ae.png') no-repeat;
  background-size: 100% 100%;
}
.top_box3 {
  background: url('https://document.dxznjy.com/course/1e95d7c29b474b4fb557ca8d777d2514.png') no-repeat;
  background-size: 100% 100%;
}
.bot_box {
}
.botBtn {
  display: flex;
  text-align: center;
  width: 100vw;
  justify-content: space-around;
  /* flex-direction: row-reverse; */
  height: 140rpx;
  padding-top: 14rpx;
  box-sizing: border-box;
  background-color: #ffffff;
  box-shadow: 0px -5px 10px 0px #ededed;
  position: fixed;
  bottom: 0;
}
.btn_b {
  width: 628rpx;
  height: 92rpx;
  border-radius: 60rpx;
  line-height: 86rpx;
  text-align: center;
}
.b_l {
  background-color: #fff;
  color: #4e9f87;
  border: 1px solid #7baea0;
}
.b_r {
  background-image: linear-gradient(to bottom, #88cfba, #1d755c);
  color: #ffffff;
  margin-left: 32rpx;
}
.h_view {
  background-color: #f3f8fc;
}
.cthg_c {
  padding: 45rpx 32rpx;
  box-sizing: border-box;
  margin-top: 24rpx;
  border-radius: 10rpx 10rpx 0rpx 0rpx;
}
.cthg_t {
  display: flex;
  flex-wrap: wrap;
}
.ct_box {
  /* width: 98rpx; */
  height: 70rpx;
  border: 1rpx solid #b1cdb1;
  border-radius: 8rpx;
  text-align: center;
  line-height: 70rpx;
  margin-right: 34rpx;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
  box-sizing: border-box;
}
.dc {
  margin-right: 10rpx;
}
.w-110 {
  width: 110rpx;
}
</style>

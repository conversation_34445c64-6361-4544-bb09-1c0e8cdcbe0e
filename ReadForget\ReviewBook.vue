<template>
  <view>
    <view class="bg-h">
      <view class="word-position t-c col-12" style="">
        <view @click="goback">
          <uni-icons type="left" size="24" color="#000"></uni-icons>
        </view>
        <view class="f-26">{{ title }}</view>
        <view></view>
      </view>
    </view>
    <view class="contain" :style="{ height: useHeight + 'rpx' }">
      <view class="work" v-if="active == 0">
        <view v-if="workList.length">
          <view class="workItem" v-for="(item, index) in workList" :key="item.id" @click="sayWord(item.word)">
            <view class="seperate">{{ item.word }}</view>
            <view class="chinese" v-if="item.checked">{{ item.chinese }}</view>
            <view class="flag">
              <image @click.stop="change(1, index)" :src="rightimage(item.checked)" style="width: 52rpx; height: 52rpx"></image>
              <image @click.stop="change(2, index)" :src="errorimage(item.checked)" style="width: 52rpx; height: 52rpx"></image>
            </view>
          </view>
        </view>
        <view v-else class="empty">
          <image src="https://document.dxznjy.com/course/445806b8053f4246b0897dca73f40629.png" style="width: 122rpx; height: 122rpx" mode=""></image>
          <view class="emptyText">没有生词可复习</view>
        </view>
      </view>
      <view class="knowLedge" v-if="active == 1">
        <view v-if="knowledgeList.length">
          <view class="knowledgeItem" v-for="(item, index) in knowledgeList" :key="item.id">{{ index + 1 }}. {{ item.knowledgeExplain }}</view>
        </view>
        <view v-else class="empty">
          <image src="https://document.dxznjy.com/course/445806b8053f4246b0897dca73f40629.png" style="width: 122rpx; height: 122rpx" mode=""></image>
          <view class="emptyText">没有知识点可复习</view>
        </view>
      </view>
      <view class="article" v-if="active == 2">
        <view v-if="content" style="height: 100%; overflow-y: auto">
          <view class="articleTitle">
            {{ articleTitle }}
          </view>
          <view class="articleContent" v-html="content"></view>
        </view>
      </view>
    </view>
    <view class="btn" @click="next">{{ active == 2 ? '提交' : '下一环节' }}</view>
  </view>
</template>

<script>
  import CryptoJS from 'crypto-js';
  var innerAudioContext;
  export default {
    data() {
      return {
        timbre: 'W', // 音色默认女声 M  W
        pronunciationType: 0, // 1英式  0美式  默认美式
        playType: 2, // 版本
        active: 0,
        title: '复习生词',
        useHeight: 0,
        isplay: false,
        flag: 0,
        linkUrl: '',
        studentCode: '',
        merchantCode: '',
        sg: '',
        workList: [],
        workObj: {},
        knowledgeList: [],
        content: '',
        articleTitle: '',
        courseId: '', //课程id
        forgettingId: '', //抗遗忘id
        articleId: '' //关卡id
      };
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          this.useHeight = res.windowHeight * (750 / res.windowWidth) - 240;
        }
      });
    },
    watch: {
      active(e) {
        if (e == 0) {
          this.title = '复习生词';
        } else if (e == 1) {
          this.title = '复习知识点';
        } else {
          this.title = '复习文章';
        }
      }
    },
    onLoad(options) {
      let that = this;
      innerAudioContext = uni.createInnerAudioContext();
      innerAudioContext.onPlay(() => {});
      innerAudioContext.onStop(function () {
        that.isplay = false;
      });
      innerAudioContext.onPause(function () {
        that.isplay = false;
      });
      innerAudioContext.onError((res) => {
        that.isplay = false;
      });
      this.studentCode = options.studentCode;
      this.merchantCode = options.merchantCode;
      this.articleId = options.articleId;
      this.courseId = options.courseId;
      this.forgettingId = options.forgettingId;
    },
    onShow() {
      this.getWordversion();
      this.homeData();
      this.init();
    },
    methods: {
      goback() {
        // if (this.active == 0) {
        uni.navigateBack();
        // } else {
        // this.active--;
        // }
      },
      next() {
        if (this.active == 0) {
          this.active++;
        } else if (this.active == 1) {
          this.active++;
        } else {
          this.submit();
        }
      },
      async submit() {
        uni.showLoading({
          title: '提交中...'
        });
        let obj = {
          studentCode: this.studentCode,
          merchantCode: this.merchantCode,
          forgettingId: this.forgettingId,
          courseId: this.courseId,
          articleId: this.articleId,
          total: this.workList.length,
          reportSource: 1,
          total: this.workList.length
        };
        obj.wordList = this.workList

          .filter((o) => o.checked != 1)
          .map((e) => {
            return {
              word: e.word,
              chinese: e.chinese
            };
          });
        let { data } = await uni.$http.post('/znyy/superReadReview/saveReviewReport', obj);
        if (data.success) {
          uni.hideLoading();
          uni.redirectTo({
            url: `/ReadForget/readReport?studentCode=${this.studentCode}&merchantCode=${this.merchantCode}&forgettingId=${this.forgettingId}&type=1`
          });
        } else {
          uni.hideLoading();
          uni.showToast({
            icon: 'none',
            title: '提交失败'
          });
        }
      },
      async init() {
        this.active = 0;
        let { data } = await uni.$http.get(
          `/znyy/superReadReview/getReviewContentList?studentCode=${this.studentCode}&merchantCode=${this.merchantCode}&articleId=${this.articleId}`
        );
        if (data.success) {
          this.workObj = data.data;
          console.log(data.data);
          this.workList = data.data.wordListList.map((e) => {
            return {
              ...e,
              checked: 0
            };
          });
          // #ifdef MP-WEIXIN
          this.content = data.data.content.replaceAll(/#/g, '_');
          this.content = this.content.replaceAll(/@&/g, '  ');
          this.content = this.content.replaceAll(/@%/g, '\n');
          this.content = this.content.replaceAll('@=', '<span style="text-decoration: underline;">');
          this.content = this.content.replaceAll('@*', '</span>');
          // #endif
          // #ifdef APP-PLUS
          this.content = data.data.content.replace(/#/g, '_');
          this.content = this.content.replace(/@&/g, '  ');
          this.content = this.content.replace(/@%/g, '\n');
          this.content = this.content.replace(/@=/g, '<span style="text-decoration: underline;">');
          this.content = this.content.replace(/@*/g, '</span>');
          //#endif
          this.knowledgeList = data.data.knowledgeList;
          this.articleTitle = data.data.title;
        } else {
        }
      },
      change(e, i) {
        this.workList[i].checked = e;
      },
      errorimage(flag) {
        return flag == 2 ? 'https://document.dxznjy.com/course/80bdcb7a08684f598e0a911207297d9c.png' : 'https://document.dxznjy.com/course/67a1f5c776b84b57948722f24415f2d5.png';
      },
      rightimage(flag) {
        return flag == 1 ? 'https://document.dxznjy.com/course/bedb8bb2e49e41e99acdd9943a3afe26.png' : 'https://document.dxznjy.com/course/c14bb768d0d94543821273a6e4dea9b4.png';
      },
      async homeData() {
        let { data } = await uni.$http.get('/deliver/app/teacher/info');
        this.userinfo = data.data;
        let list = this.userinfo.teacherCode + 'L0anhf';
        this.sg = CryptoJS.SHA1(list).toString();
      },
      // 播放音频
      sayWord(word) {
        var that = this;
        uni.$http
          .get('/znyy/app/query/word/voice', {
            word: word,
            v: that.playType,
            rp: that.pronunciationType == 1 ? true : false,
            sex: that.timbre,
            sg: that.sg
          })
          .then((result) => {
            if (result.data.success) {
              let voiceUrl;
              let url;
              if (that.playType == 1) {
                voiceUrl = 'https://document.dxznjy.com/' + encodeURIComponent(result.data.data);
                that.linkUrl = voiceUrl;
              } else {
                voiceUrl = result.data.data;
                that.linkUrl = voiceUrl;
              }
              // #ifdef MP-WEIXIN
              innerAudioContext.obeyMuteSwitch = false;
              // #endif
              innerAudioContext.src = that.linkUrl;
              innerAudioContext.play();
            } else {
              that.$util.alter(result.data.message);
            }
          });
      },
      // 获取当前学员设置的语音版本
      getWordversion() {
        var that = this;
        uni.$http
          .get('/znyy/course/info', {
            studentCode: that.studentCode
          })
          .then((res) => {
            if (res.data.success) {
              console.log(res.data.data);
              let name = res.data.data.voiceModel.split('#');
              that.pronunciationType = name[1];
              that.timbre = name[2];
              that.playType =1;
              // #ifdef MP-WEIXIN
              that.playType = name[0];
              // #endif
            } else {
              that.$util.alter(res.data.message);
            }
          });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .btn {
    width: 686rpx;
    height: 74rpx;
    background: #339378;
    border-radius: 38rpx;
    position: fixed;
    color: #fff;
    line-height: 74rpx;
    bottom: 60rpx;
    left: 50%;
    transform: translateX(-50%);
    font-size: 28rpx;
    text-align: center;
  }
  .line {
    text-decoration: underline;
  }
  .empty {
    flex-direction: column;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    .emptyText {
      margin-top: 40rpx;
      font-size: 34rpx;
      color: #bababa;
      text-align: center;
    }
  }
  .contain {
    margin-top: 180rpx;
    padding-bottom: 100rpx;
    background-color: #fff;
    box-sizing: border-box;
    .article {
      height: 100%;
      // width: 100%;
      padding: 20rpx 32rpx;
      .articleTitle {
        font-size: 28rpx;
        line-height: 42rpx;
        text-align: center;

        color: #555555;
        margin-bottom: 10rpx;
      }
      .articleContent {
        white-space: pre-wrap;
        font-size: 28rpx;
        color: #555555;
        line-height: 42rpx;
      }
    }
    .knowLedge {
      height: 100%;
      padding: 14rpx 32rpx;
      overflow-y: auto;
      box-sizing: border-box;
      .knowledgeItem {
        line-height: 56rpx;
        margin-bottom: 24rpx;
        font-size: 28rpx;
        color: #555555;
      }
    }
    .work {
      height: 100%;
      padding: 20rpx 32rpx;
      overflow-y: auto;

      .workItem {
        display: flex;
        height: 92rpx;
        box-sizing: border-box;
        border: 2rpx solid #e1e0e0;
        border-radius: 16rpx;
        margin: 32rpx 0;
        align-items: center;
        justify-content: space-between;
        padding-left: 24rpx;
        padding-right: 28rpx;
        .flag {
          display: flex;
          width: 148rpx;
          justify-content: space-between;
        }
        .seperate {
          font-size: 32rpx;
          color: #555555;
          font-weight: bold;
        }
        .chinese {
          max-width: 220rpx;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
  .word-position {
    position: fixed;
    top: 0;
    left: 0;
    background-color: #f3f8fc;
    // height: 190rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 110rpx;
    box-sizing: border-box;
  }
</style>
